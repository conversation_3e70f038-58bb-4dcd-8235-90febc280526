declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void
      execute: (siteKey: string, options: { action: string }) => Promise<string>
      render: (containerId: string, options: any) => number
      getResponse: (widgetId: number) => string
      reset: (widgetId: number) => void
    }
    onRecaptchaLoaded?: () => void
  }
}

// Add this empty export to make the file a module
export {}
