
mutation CustomerLogin($email: String!, $password: String!) {
    customerLogin(email: $email, password: $password) {
        ...CustomerAuthData
    }
}

mutation CustomerRegister($data: CustomerRegistrationData!) {
    customerRegister(data: $data) {
        ...SignUpResponse
    }
}

mutation CustomerAuth {
    customerLoginRefresh {
        ...CustomerAuthData
    }
}

mutation CustomerLogout {
    customerLogout
}