mutation CartAdd($cartToken: String!, $sku: String!, $quantity: Float!) {
    cartItemAdd(cartToken: $cartToken, item: { baseQty: $quantity, sku: $sku }) {
        ...CartFull
    }
}

mutation CartUpdate($cartToken: String!, $sku: String!, $quantity: Float!) {
    cartItemUpdate(cartToken: $cartToken, item: { baseQty: $quantity, sku: $sku}) {
        ...CartFull
    }
}

mutation CartRemove($cartToken: String!, $sku: String!) {
    cartItemRemove(cartToken: $cartToken, sku: $sku) {
        ...CartFull
    }
}

mutation ApplyCouponCode($cartToken: String!, $couponCode: String!) {
    cartApplyCoupon(cartToken: $cartToken, couponCode: $couponCode) {
        ...CartFull
    }
}

mutation Subscribe($email: String!) {
    storeNewsletterSubscribe(email: $email)
}