mutation UpdateCustomer($data: CustomerUpdateInput!) {
    customerUpdateInfo(data: $data) {
        ...CustomerFull
    }
}

mutation UpdateCustomerPassword($oldPassword: String!, $newPassword: String!) {
    customerUpdatePassword(oldPassword: $oldPassword, newPassword: $newPassword) {
        id
    }
}

mutation CreateCustomerAddress($data: CustomerAddressInput!) {
    customerAddressAdd(data: $data) {
        ...CustomerAddressFull
    }
}

mutation UpdateCustomerAddress($data: CustomerAddressInput!, $addressID: ID!) {
    customerAddressUpdate(data: $data, addressID: $addressID) {
        ...CustomerAddressFull
    }
}

mutation DeleteCustomerAddress($addressID: ID!) {
    customerAddressRemove(addressID: $addressID) {
        ...CustomerAddressFull
    }
}

mutation ForgotPassword($email: String!) {
    customerPasswordForgot(email: $email)
}

mutation CustomerPasswordReset($customerId: ID!, $password: String!, $resetToken: String!) {
    customerPasswordReset(customerId: $customerId, password: $password, resetToken: $resetToken)
}
