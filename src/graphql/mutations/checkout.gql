mutation CartSaveClient($cartToken: String!, $data: ClientInput!) {
    cartSaveClient(cartToken: $cartToken, data: $data) {
        ...CartFull
    }
}

mutation GetShippingMethods($cartToken: String!, $data: ShippingInput!) {
    cartAvailableShippingMethods(cartToken: $cartToken, data: $data) {
        ...AvailableShippingMethod
    }
}

mutation CartSaveShippingMethod($cartToken: String!, $data: ShippingInput!, $code: String!) {
    cartSaveShipping(cartToken: $cartToken, shippingMethodCode: $code,data: $data) {
        ...CartFull
    }
}

mutation CartSavePaymentMethod($cartToken: String!, $code: String!) {
    cartSavePayment(cartToken: $cartToken, paymentMethodCode: $code) {
        ...CartFull
    }
}

mutation GetPaymentMethods($cartToken: String!) {
    cartAvailablePaymentMethods(cartToken: $cartToken) {
        ...AvailablePaymentMethod
    }
}

mutation CreateOrder($cartToken: String!, $data: NewOrderInput!) {
    placeOrder(cartToken: $cartToken, data: $data) {
        ...PlaceOrder
    }
}