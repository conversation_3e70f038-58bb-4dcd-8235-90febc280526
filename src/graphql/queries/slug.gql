query GetRouteData($url: String!, $query: [QueryParam]) {
  route(url: $url, query: $query) {
    ...DynamicRouteData
  }
}

fragment DynamicRouteData on Page {
  breadcrumbs {
    label
    url
    children {
      label
      url
    }
    siblings {
      label
      url
    }
  }
  status {
    redirectUrl
    statusCode
  }
  data {
    ...DynamicPageData
  }
}

fragment DynamicPageData on PageData {
  __typename
  ... on CatalogPage {
    ...CatalogPageData
  }
  ... on CMSPage {
    ...CMSPageData
  }
  ... on ProductPage {
    ...ProductPageData
  }
  ... on ErrorData {
    code
    message
  }
}


fragment CatalogPageData on CatalogPage {
  __typename
  layout
  state {
    ...AppFilterState
  }
  category {
    ... on SplashPage {
      ...AppSplashView
    }
    ... on Category {
      ...AppCategoryView
    }
    ... on SearchCategory {
      ...AppSearchCategory
    }
  }
  products {
    ...ProductView
  }
}

fragment CMSPageData on CMSPage {
  __typename
  links {
    ...AppLink
  }
  identifier
  title
  content
}

fragment ProductView on Product {
  __typename
  ... on SimpleProduct {
    ...SimpleProductView
  }
  ... on BundleProduct {
    ...BundleProductView
  }
}

fragment ProductPageData on ProductPage {
  __typename
  product {
    ...ProductView
  }
  widgets {
    __typename
    ...AppProductsSliderWidget
  }
  boughtTogether {
    ...AppProductCard
  }
}
