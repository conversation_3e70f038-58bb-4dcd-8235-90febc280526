query GetStoreStaticData {
  getStaticContent {
    brochureLink
    logo {
      ...AppLogo
    }
    apiKeys {
      facebookLoginAppId
      googleLoginClientId
      googleMaps
      googleRecaptchaKey
    }
    gdpr {
      __typename
      rootId
      clarityId
      pixelId
      gtagId
      extraGTagsIds
      modal {
        __typename
        title
        content
        cookieGroups {
          __typename
          id
          title
          content
          cookiePatterns
          grants
          vendors {
            __typename
            keys
            config {
              __typename
              id
              name
              url
              cookieCount
              cookieDetails {
                __typename
                id
                name
                type
                description
                expiration
              }
            }
          }
        }
      }
    }
    menu {
      ...AppMenu
    }
    footer {
      ...AppFooter
    }
    messages {
      newsletter
      sendInquiryMessage
    }
    store {
      baseUrl
      contacts {
        ...AppContacts
      }
    }
  }
}

query GetGoogleApiKey {
  getStaticContent {
    apiKeys {
      googleMaps
    }
  }
}

query GetGoogleClientId {
  getStaticContent {
    apiKeys {
      googleLoginClientId
    }
  }
}
