# BNP Paribas queries

query GetBNPPricingSchemes($goodTypeIds: String!, $principal: Float!, $downPayment: Float!) {
    getBNPPricingSchemes(
        goodTypeIds: $goodTypeIds
        principal: $principal
        downPayment: $downPayment
    ) {
        ...BNPPricingScheme
    }
}

query GetCreditCalculatorBNPParibas($sku: String!, $downPayment: Float!, $qty: Int!) {
    getCreditCalculatorBNPParibas(
        sku: $sku
        downPayment: $downPayment
        qty: $qty
    ) {
        ...BNPVariantGroup
    }
}

query GetCreditCalculatorQuoteBNPParibas($cartToken: String!, $downPayment: Float!) {
    getCreditCalculatorBNPParibasForQuote(
        cartToken: $cartToken
        downPayment: $downPayment
    ) {
        ...BNPVariantGroup
    }
}


query CalculateBNPLoan($cartToken: String!, $downPayment: Float!, $pricingVariantId: Int!) {
    calculateBNPLoan(
        cartToken: $cartToken
        downPayment: $downPayment
        pricingVariantId: $pricingVariantId
    ) {
        ...LoanCalculation
    }
}
