query GetHomePageData {
  getHomepage {
    heroSlider {
      ...CarouselWidgetFragment
    }
    widgets {
      __typename
      ... on CarouselWidget {
        ...CarouselWidgetFragment
      }
      ... on ProductsSliderWidget {
        ...AppProductsSliderWidget
      }
      ... on TilesWidget {
        ...TilesWidgetFragment
      }
      ... on CategoryLinkWidget {
        ...AppCategoryLinkWidget
      }
      ... on ServiceWidget {
        ...ServiceWidgetFragment
      }
      ... on NewsWidget {
        ...NewsWidgetFragment
      }
      ... on HtmlWidget {
        ...AppHtmlWidget
      }
    }
  }
  getBrands(featured: true) {
    ...AppBrand
  }
}

fragment AppCategoryLinkWidget on CategoryLinkWidget {
  title
  url {
    href
    text
    title
  }
  image {
    alt
    src
    title
    mobileSrc
  }
}

fragment AppHtmlWidget on HtmlWidget {
  html
}
