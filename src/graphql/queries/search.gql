fragment SearchResultProduct on Product {
  ... on SimpleProduct {
    sku
    shortDescription
    name
    url<PERSON>ey
    image {
      src
      title
    }
    price {
      price {
        value
        currency
      }
      special {
        value
        currency
      }
      specialFrom
      specialTo
    }
  }
  ... on BundleProduct {
    sku
    shortDescription
    name
    urlKey
    image {
      src
      title
    }
    price {
      price {
        value
        currency
      }
      special {
        value
        currency
      }
      specialFrom
      specialTo
    }
  }
}



query SearchField($text: String!) {
  search(searchQuery: $text) {
    categories {
      name
      url
    }
    popularTerms
    totalItems

    products {
      ...ProductView
    }
  }
}

fragment SearchPageResults on SearchResults {
  totalItems
  block {
    title
    content
  }
  categories {
    name
    url
  }
  popularTerms
  products {
    ...ProductView
  }
}

query SearchPage($searchQuery: String!, $query: [QueryParam]) {
  searchPage(query: $query, searchQuery: $searchQuery) {
    status {
      statusCode
    }
    state {
      ...AppFilterState
    }
    title
    data {
      ...SearchPageResults
    }
  }
}