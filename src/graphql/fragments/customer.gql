fragment CustomerAddressFull on CustomerAddress {
    id
    firstName
    lastName
    phone
    companyName
    country
    city
    cityID
    postCode
    street
}

fragment CustomerFull on Customer {
    id,
    token,
    isSubscribed,
    firstName,
    lastName,
    email,
    defaultBillingAddressID
    defaultShippingAddressID,
    addresses {
        ...CustomerAddressFull
    }
#    orders {
#        ...OrderFull
#    }
}