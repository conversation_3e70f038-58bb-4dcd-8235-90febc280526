fragment AppProductPrice on ProductPrice {
  price {
    ...AppPrice
  }
  special {
    ...AppPrice
  }
}

fragment AppEnergyLabel on EnergyLabel {
  image {
    ...AppImage
  }
  infoUrl
  labelUrl
}

fragment AppLabels on MagentoLabels {
  warrantyMonths
  buyCheap
  freeDelivery
  fromBrochure
  other
}

fragment AppProductCard on Product {
  __typename
  ... on SimpleProduct {
    id
    sku
    name
    urlKey
    image {
      ...AppImage
    }
    price {
      ...AppProductPrice
    }
    energyLabel {
      ...AppEnergyLabel
    }
    labels {
      ...AppLabels
    }
    stock {
      ...ProductStockFull
    }
  }
  ... on BundleProduct {
    id
    sku
    name
    urlKey
    image {
      ...AppImage
    }
    price {
      ...AppProductPrice
    }
    labels {
      ...AppLabels
    }
  }
}

fragment ProductStockFull on ProductStock {
  blockForSale
  hasImages
  inStock
  manageStock
  qty
  minQty
  zeronBlockedDelivery
  zeronSiteStatus
}

fragment StoreAvailability on StoreAvailabilityItem {
  available
  sample
  store {
    identity
    warehouseCode
    name
    displayOrder
    acceptOrders
    location {
      lat
      lng
    }
  }
}

fragment SimpleProductView on SimpleProduct {
  __typename
  id
  sku
  name
  urlKey
  image {
    ...AppImage
  }
  gallery {
    image {
      ...AppImage
    }
    position
  }
  videos {
    ...Video
  }
  measures {
    base
    secondary
    secondaryQty
    secondaryMeasureUsed
  }
  price {
    ...AppProductPrice
  }
  description
  shortDescription
  skuAvailability {
    ...StoreAvailability
  }
  stock {
    ...ProductStockFull
  }
  brand {
    ...AppBrand
  }
  energyLabel {
    ...AppEnergyLabel
  }
  labels {
    ...AppLabels
  }
}

fragment AppBundleItem on SimpleProduct {
  id
  sku
  name
  measures {
    base
    secondary
    secondaryQty
  }
  image {
    ...AppImage
  }
  price {
    ...AppProductPrice
  }
  description,
  gallery {
    image {
      ...AppImage
    }
    position
  }
  labels {
    ...AppLabels
  }
  shortDescription
  skuAvailability {
    ...StoreAvailability
  }
  urlKey
}

fragment BundleProductView on BundleProduct {
  __typename
  id
  sku
  name
  gallery {
    image {
      ...AppImage
    }
    position
  }
  price {
    ...AppProductPrice
  }
  description
  shortDescription
  brand {
    ...AppBrand
  }
  labels {
    ...AppLabels
  }
  bundled {
    ...AppBundleItem
  }
  urlKey,
  image {
    ...AppImage
  }
  skuAvailability {
    ...StoreAvailability
  }
}
