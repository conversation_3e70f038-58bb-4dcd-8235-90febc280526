fragment AppCategoryView on Category {
  __typename
  id
  name
  url
  description
  image {
    ...AppImage
  }
  icon {
    ...AppImage
  }
  banner {
    image {
      ...AppImage
    }
    url
  }
  widgets {
    ...AppCategoryViewWidget
  }
}

fragment AppSearchCategory on SearchCategory {
  __typename
  ... on SearchCategory {
    name
    url
  }
}

fragment AppSplashView on SplashPage {
  __typename
  ... on SplashPage {
    splashDescription: description
    title
    url
  }
}

fragment AppCategoryViewWidget on CategoryWidget {
  __typename
  ... on CategoryLinkWidget {
    title
    url {
      href
      text
      title
    }
    image {
      alt
      src
      title
      mobileSrc
    }
  }
}
