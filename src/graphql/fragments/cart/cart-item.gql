fragment CartItem on StoreCartItem {
    id
    sku
    labels {
        ...StoreCartItemLabels
    }
    product {
        ...ProductView
    }
    baseQty
    discountAmount {
        ...AppPrice
    }
    discountPercent
    price {
        ...AppPrice
    }
#    priceWithoutDiscount {
#        ...AppPrice
#    }
    rowTotal {
        ...AppPrice
    }
#    rowTotalWithoutDiscount {
#        ...AppPrice
#    }
}