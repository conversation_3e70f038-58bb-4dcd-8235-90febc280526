fragment CartFull on StoreCart {
    id
    token
    storeCode
    currency
    couponCode
    note
    customer {
        ...OrderCustomer
    }
    items {
        ...CartItem
    }
    shipping {
        ...CartShippingFull
    }
    paymentMethod
    totals {
        ...CartTotal
    }
}
fragment CartView on StoreCart {
    id
    token
    totals {
        code
        label
        amount {
            value,
            currency
        }
    }
    items {
        id
        sku
        product {
            ...ProductView
        }
        labels {
            freeShipping
            usePallet
        }
        baseQty
        price {
            value,
            currency
        }
        rowTotal {
            value,
            currency
        }
        discountAmount {
            value,
            currency
        }
        discountPercent
    }
    storeCode
    couponCode
    currency
}
