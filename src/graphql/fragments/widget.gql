fragment ServiceWidgetFragment on ServiceWidget {
  link {
    ...AppLink
  }
  title
  subtitle
  services {
    ...Service
  }
  availableStores {
    ...AppStorePreview
  }
  form {
    formId
    text
    title
  }
}

fragment NewsWidgetFragment on NewsWidget {
  title
  subtitle
  identifier
  link {
    href
    text
    title
  }
  articles {
    title
    description
    image {
      alt
      mobileSrc
      src
      title
    }
    link {
      href
      text
      title
    }
  }
}

fragment CarouselWidgetFragment on CarouselWidget {
  identifier
  slides {
    ...WidgetSlide
  }
}

fragment WidgetSlide on CarouselSlide {
  brand {
    name
    url {
      ...AppLink
    }
    image {
      ...AppImage
    }
  }
  title
  description
  features {
    ...AppImage
  }
  image {
    ...AppImage
  }
  link {
    ...AppLink
  }
  price {
    ...AppPrice
  }
  priceLabels {
    ...AppLabels
  }
}

fragment AppProductsSliderWidget on ProductsSliderWidget {
  identifier
  title
  subtitle
  tabs {
    title
    products {
      ...AppProductCard
    }
  }
}

fragment TilesWidgetFragment on TilesWidget {
  identifier
  viewMore {
    href
    text
    title
  }
  title
  subtitle
  rows {
    cols {
      __typename
      ... on ImageTile {
        ...AppImageTile
      }
      ... on DoubleImageTile {
        ...AppDoubleImageTile
      }
      ... on CategoryTile {
        ...AppCategoryTile
      }
    }
  }
}

fragment AppImageTile on ImageTile {
  bgColor
  image {
    ...AppImage
  }
  position
  content {
    ...AppTileContent
  }
}

fragment AppDoubleImageTile on DoubleImageTile {
  imageOnePosition
  imageOne {
    ...AppImage
  }
  contentOne {
    ...AppTileContent
  }
  imageTwoPosition
  imageTwo {
    ...AppImage
  }
  contentTwo {
    ...AppTileContent
  }
}

fragment AppCategoryTile on CategoryTile {
  image {
    ...AppImage
  }
  title
  bgColor
  textColor
  categories {
    ...AppLink
  }
}

fragment AppTileContent on TileContent {
  icon {
    ...AppImage
  }
  link {
    ...AppLink
  }
  title
  text
}
