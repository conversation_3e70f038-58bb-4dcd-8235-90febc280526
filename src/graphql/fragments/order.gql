fragment OrderFull on StoreOrder {
    incrementId,
    state,
    status {
        ...OrderStatusFull
    }
    couponCode,
    protectCode,
    createAt,
    items {
        ...OrderItemFull
    },
    totals {
        ...CartTotal
    },
    shippingMethod {
        ...ShippingMethod
    },
    paymentMethod {
        ...PaymentMethod
    },
    note,
    shippingAddress {
        ...OrderAddressFull
    },
    invoice {
        ...CustomerInvoice
    }
}

fragment OrderAddressFull on OrderAddress {
    firstName
    lastName
    email
    telephone
    city
    postcode
    street
}

fragment OrderItemFull on OrderItem {
    id,
    sku,
    product {
        ...ProductView
    },
    baseQty,
    discountAmount {
        ...AppPrice
    }
    discountPercent,
    price {
        ...AppPrice
    }
    priceWithoutDiscount {
        ...AppPrice
    }
    rowTotal {
        ...AppPrice
    }
    rowTotalWithoutDiscount {
        ...AppPrice
    }
}

fragment MapValueFull on MapValue {
    key
    value
}

fragment OrderRedirectFull on OrderRedirect {
    url
    data {
        ...MapValueFull
    }
}

fragment OrderStatusFull on OrderStatus {
    label
    code
}

fragment PlaceOrder on PlaceOrderResponse {
    orderNumber
    status {
        ...OrderStatusFull
    }
    redirect {
        ...OrderRedirectFull
    }
}