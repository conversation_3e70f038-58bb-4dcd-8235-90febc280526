fragment StoreGalleryImage on StoreImage {
  ID
  praktisStoreID
  position
  alt
  src
  title
  mobileSrc
  isPrimary
}

fragment AppStorePreview on PraktisStore {
  name
  identity
  address
  email
  phone
  city
  displayOrder
  gallery {
    ...StoreGalleryImage
  }
  businessHours {
    open
    close
    day
  }
}

fragment StorePageData on StorePageData {
  messageBlock
  store {
    ...AppStore
  }
}

fragment AppStore on PraktisStore {
  name
  address
  city
  email
  phone
  virtualTour
  transportInformation

  gallery {
    ...StoreGalleryImage
  }
  location {
    lat
    lng
  }
  businessHours {
    open
    close
    day
  }
  services {
    ...Service
  }

  descriptionArea {
    backgroundImage
    content
    formID
    title
  }

  metaDescription
  metaKeywords
  metaTitle
}
