fragment EcontCountry on EcontCountry {
    code2
    code3
    id
    isEU
    name
    nameEn
}

fragment EcontOfficeLocation on EcontOfficeLocation {
    latitude
    longitude
}

fragment EcontOfficeAddress on EcontOfficeAddress {
    city {
        ...EcontCity
    }
    fullAddress
    fullAddressEn
    location {
        ...EcontOfficeLocation
    }
}

fragment EcontOffice on EcontOffice {
    id
    code
    name
    nameEn
    phones
    isAPS
    isMPS
    adderess {
        ...EcontOfficeAddress
    }
}

fragment EcontCity on EcontCity {
    country {
        ...EcontCountry
    }
    id
    name
    nameEn
    postCode
    regionCode
    regionName
    regionNameEn
    type
}