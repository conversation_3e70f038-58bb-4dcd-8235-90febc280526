fragment AppAppliedFilter on AppliedFilter {
  label
  value
  requestVar
  attributeCode
}

fragment AppFilterOption on AttributeOption {
  label
  value
  order
}

fragment AppAvailableFilter on AvailableFilter {
  position
  type
  requestVar
  attributeCode
  label
  options {
    ...AppFilterOption
  }
}

fragment AppFilterState on CatalogState {
  filters {
    applied {
      ...AppAppliedFilter
    }
    available {
      ...AppAvailableFilter
    }
  }
  pager {
    page
    pageSize
    totalPages
    totalItems
  }
  sort {
    value
    dir
  }
}
