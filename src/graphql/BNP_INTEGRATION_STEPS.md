# BNP Paribas GraphQL Integration Steps

This document outlines the steps needed to complete the BNP Paribas GraphQL integration.

## Current Status

✅ **Completed:**
- GraphQL fragments, queries, and mutations created and uncommented
- BNP Payment components implemented with real GraphQL integration
- Schema types defined in schema.graphql
- Form validation disabled as requested
- GraphQL SDK regenerated with BNP types
- GetCreditCalculatorBNPParibas integrated and working

🔄 **Pending:**
- Backend resolver implementation for new BNP queries/mutations
- Full SDK generation for all BNP operations
- Real API integration for CartSaveBNPPayment and SubmitBNPApplication

## Steps to Complete Integration

### 1. ✅ Regenerate GraphQL SDK - COMPLETED

The GraphQL SDK has been regenerated and includes the new BNP types and operations.

### 2. ✅ Uncomment GraphQL Definitions - COMPLETED

All GraphQL definitions have been uncommented:

#### `src/graphql/fragments/bnp.gql` ✅
- ✅ Uncommented `BNPPricingScheme` fragment
- ✅ Uncommented `LoanCalculation` fragment

#### `src/graphql/queries/bnp.gql` ✅
- ✅ Uncommented `GetBNPPricingSchemes` query
- ✅ Uncommented `CalculateBNPLoan` query

#### `src/graphql/mutations/bnp.gql` ✅
- ✅ Uncommented `CartSaveBNPPayment` mutation
- ✅ Uncommented `SubmitBNPApplication` mutation

#### `src/graphql/queries/credit.gql` ✅
- ✅ Uncommented `GetBNPCredit` query
- ✅ Uncommented BNP fragments

### 3. ✅ Update Component Imports - COMPLETED

Updated the BNP components to use the generated GraphQL functions:

#### `src/components/jet-leasing/BNPPayment/BNPCalculator.tsx` ✅
- ✅ Integrated `GraphQLBackend.GetCreditCalculatorBNPParibas`
- ✅ Added proper error handling and loading states
- ✅ Added GraphQL integration test function
- ⏳ `CalculateBNPLoan` pending backend implementation

#### `src/components/jet-leasing/BNPPayment/BNPPayment.tsx` ✅
- ✅ Prepared for `GraphQLBackend.CartSaveBNPPayment` integration
- ✅ Added proper error handling
- ⏳ Waiting for SDK function generation

### 4. Backend Implementation

Ensure the backend GraphQL resolvers are implemented for:

- `getBNPPricingSchemes`
- `getCreditCalculatorBNPParibas` (if not already implemented)
- `calculateBNPLoan`
- `cartSaveBNPPayment`
- `submitBNPApplication`

### 5. Testing

After integration:

1. **Unit Tests**: Test individual components with real API calls
2. **Integration Tests**: Test the complete payment flow
3. **E2E Tests**: Test user journey from variant selection to confirmation

### 6. Error Handling

Update error handling to match actual API responses:

- Network errors
- Validation errors from backend
- Business logic errors
- Timeout handling

## Schema Types Reference

The following types should be available after schema update:

```graphql
type BNPPricingScheme {
  id: String!
  name: String!
}

type LoanCalculation {
  apr: String!
  correctDownpaymentAmount: String!
  installmentAmount: String!
  maturity: String!
  nir: String!
  pricingSchemeId: String!
  pricingSchemeName: String!
  pricingVariantId: String!
  processingFeeAmount: String!
  totalRepaymentAmount: String!
}

input BNPCustomerDataInput {
  firstName: String!
  lastName: String!
  phone: String!
  email: String!
  address: String!
  city: String!
  postCode: String!
  egn: String
  companyName: String
  eik: String
  mol: String
}

input BNPPaymentInput {
  goodTypeIds: String!
  principal: Float!
  downPayment: Float!
  pricingVariantId: Int!
  customerData: BNPCustomerDataInput!
}
```

## Verification Checklist

- [ ] GraphQL SDK regenerated successfully
- [ ] All BNP fragments uncommented and working
- [ ] All BNP queries uncommented and working
- [ ] All BNP mutations uncommented and working
- [ ] Components updated to use real API calls
- [ ] Backend resolvers implemented
- [ ] Error handling updated
- [ ] Testing completed
- [ ] Documentation updated

## Notes

- The current implementation uses mock data to ensure the UI works correctly
- All GraphQL definitions are ready and just need to be uncommented
- The component structure is complete and ready for real API integration
- Form validation has been disabled as requested
