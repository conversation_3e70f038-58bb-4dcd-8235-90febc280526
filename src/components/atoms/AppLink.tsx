import { LinkProps } from 'next/dist/client/link'
import Link from 'next/link'
import React from 'react'

import Text, { TextProps } from '@atoms/Text'
import type { Link as LinkType } from '@lib/_generated/graphql_sdk'

export interface AppLinkProps extends LinkProps {
  className?: string
  textProps?: Partial<TextProps>
  children?: React.ReactNode
  target?: string
  title?: string | null
  text?: string
  href: URL | string | LinkType
  dontAppendSlash?: boolean
}

const AppLink: React.FC<AppLinkProps> = ({ href, textProps, children, dontAppendSlash, ...props }) => {
  let _href = href
  let _children = children
  let _title = props.title ?? ''
  let _target = props.target ?? '_self'

  if (typeof _href === 'string') {
    if (!_href.startsWith('http') && !_href.startsWith('/') && !dontAppendSlash) {
      _href = `/${_href}`
    }
  }

  if (props.text) {
    _children = props.text
  }

  return (
    <Link {...props} href={_href} title={_title} target={_target}>
      {typeof children === 'string' ? <Text {...textProps}>{_children}</Text> : _children}
    </Link>
  )
}

export default AppLink
