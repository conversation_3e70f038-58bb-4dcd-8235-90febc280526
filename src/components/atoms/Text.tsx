import React from 'react'

import { ThemeColors } from '@/tailwind.config'
import { addClasses } from '@lib/style/style'

// need to include the full class names for compile purposes
export type TEXT_SIZES = 'large' | 'small' | 'xsmall' | 'medium' | 'base'

export interface TextProps {
  size?: TEXT_SIZES
  color?: ThemeColors
  component?: React.ElementType
  bold?: boolean
  thin?: boolean
  disable?: boolean
  className?: string
  children: React.ReactNode
  translateOnly?: boolean
  noTranslate?: boolean
  textOnly?: boolean
}

function translateText(text: string, t: any) {
  return text
}

const Text: React.FC<TextProps> = (props) => {
  const t = null
  let _text
  if (Array.isArray(props.children)) {
    _text = props.children.join('')
  } else {
    _text = props.children ? String(props.children) : ''
  }

  if (props.textOnly) {
    return <>{_text}</>
  }

  if (props.translateOnly) {
    return <>{translateText(_text, t)}</>
  }

  if (!props.noTranslate) {
    _text = translateText(String(_text), t)
  }

  const Element = props.component ?? 'span'

  let className = ''

  if (props.size) {
    className = addClasses(`text-${props.size}`, className)
  }

  if (props.disable) {
    className = addClasses(className, 'opacity-[0.48]')
  }

  if (props.bold) {
    className = addClasses(className, 'font-bold')
  } else if (props.bold === false) {
    className = addClasses(className, 'font-normal')
  }

  if (props.thin) {
    className = addClasses(className, 'font-light')
  }

  if (props.color) {
    className = addClasses(className, `text-${props.color}`)
  }

  className = addClasses(className, props.className)

  return <Element className={className}>{_text}</Element>
}

export default Text
