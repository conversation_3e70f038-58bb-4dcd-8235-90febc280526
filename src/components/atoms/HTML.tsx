'use client'

import DOMPurify from 'dompurify'
import React, { forwardRef } from 'react'

import { cn } from '@components/lib/utils'
import { PropsWithClassName } from '@lib/types/ClassName'

export const HTML = forwardRef<HTMLDivElement, PropsWithClassName<{ children: string }>>(
  ({ children, className }, ref) => {
    const sanitizedHtml = process.env.NODE_ENV === 'development' ? children : DOMPurify.sanitize?.(children) || children
    return (
      <div ref={ref} className={cn('html-content', className)} dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />
    )
  }
)

HTML.displayName = 'HTML'
