import Image, { ImageProps } from 'next/image'
import React from 'react'

interface Props extends ImageProps {
  mobileSrc?: string | null
  cover?: boolean
}

const Img: React.FC<Props> = (props) => {
  let { src, mobileSrc, ...rest } = props

  if (src == '') {
    return <></>
  }

  let _src = src
  if (typeof src == 'string') {
    _src = toImageURL(src)
  }

  let _mobileSrc = ''
  if (!!mobileSrc) {
    _mobileSrc = toImageURL(mobileSrc)
  }

  return (
    <>
      {_mobileSrc ? (
        <picture>
          <source media="(max-width:1280px)" srcSet={_mobileSrc} />
          <Image {...rest} alt={props.alt || ''} src={_src} />
        </picture>
      ) : (
        <Image {...rest} alt={props.alt || ''} src={_src} />
      )}
    </>
  )
}

export default Img

export function toImageURL(src: string): string {
  let _src = src
  if (typeof src === 'string') {
    if (!src.includes('http') && !src.startsWith('/images')) {
      // Always use NEXT_PUBLIC_IMAGE_DOMAIN for non-HTTP URLs that don't start with '/images'
      let domain = process?.env?.NEXT_PUBLIC_IMAGE_DOMAIN || ''
      // Remove trailing slash from domain if present
      if (domain.charAt(domain.length - 1) === '/') {
        domain = domain.slice(0, -1)
      }

      // Prepare the path
      let path = src
      // Remove leading slash from path if present
      if (path.startsWith('/')) {
        path = path.slice(1)
      }

      // Always ensure the path has 'media/' at the beginning
      if (!path.startsWith('media/')) {
        path = 'media/' + path
      }

      // Combine domain and path
      _src = domain + '/' + path
    }

    if (process?.env?.NEXT_IMAGE_PROXY === 'true') {
      if (typeof _src === 'string' && _src.includes('http') && !_src.startsWith('/') && !_src.includes('svg')) {
        _src = `/api/protected-image?url=${_src}`
      }
    }
  }

  return _src
}
