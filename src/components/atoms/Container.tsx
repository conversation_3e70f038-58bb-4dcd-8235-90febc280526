import React from 'react'

interface ContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' // Add more as needed
}

const Container: React.FC<ContainerProps> = ({ children, className = '', maxWidth }) => {
  const maxWidthClass = maxWidth ? `max-w-screen-${maxWidth}` : 'w-full'
  // container
  return <div className={`mx-auto px-1.5 sm:px-6 lg:px-8 xl:px-5 ${className} ${maxWidthClass}`}>{children}</div>
}

export default Container
