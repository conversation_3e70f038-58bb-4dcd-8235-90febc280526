import React, { PropsWithChildren } from 'react'
import { ClassNameValue } from 'tailwind-merge'

import { cn } from '@components/lib/utils'

export function HomePageContainer({ children, className }: PropsWithChildren & { className?: ClassNameValue }) {
  return (
    <div className={cn('w-full mt-6', className)}>
      {/*<div className={cn("mt-4 mb-2 lg:mt-6 lg:mb-0 xl:mt-8 2xl:mt-8 w-full", className)}>*/}
      {children}
    </div>
  )
}
