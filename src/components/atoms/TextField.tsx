'use client'
import { Field, ErrorMessage, useField } from 'formik'
import React from 'react'

import { addClasses } from '@lib/style/style'

export interface HtmlFieldProps {
  name: string
  required?: boolean
  type?: 'text' | 'email' | 'password' | 'tel' | 'file'
  asType?: 'select' | 'textarea'
  accept?: string
  loading?: boolean
  label?: string
  value?: string
  error?: string
  placeholder?: string
  grayFieldBg?: boolean
  children?: React.ReactNode
  className?: string
  disabled?: boolean
  onChange?: (value: React.ChangeEvent<any>) => void
  afterChange?: (value: React.ChangeEvent<any>) => void
}

const TextField: React.FC<HtmlFieldProps> = ({
  name,
  type,
  asType,
  accept,
  required,
  label,
  placeholder,
  grayFieldBg,
  children,
  className,
  loading,
  disabled,
  value,
  error,
  onChange,
  afterChange,
}) => {
  const [meta, field] = useField(name)

  return (
    <div className="flex flex-col gap-2">
      <label htmlFor={name}>
        {required && <em className="text-danger">* </em>}
        {label}
      </label>
      <div className="flex flex-col">
        <Field
          type={type}
          name={name}
          value={value && value}
          accept={accept && accept}
          as={asType ? asType : undefined}
          disabled={!!loading || disabled}
          onChange={onChange}
          className={addClasses(
            'border  rounded-md px-6 py-2 ',
            grayFieldBg && 'bg-gray-50',
            field.error ? 'border-danger' : 'border-gray-300',
            'focus:outline-none focus:ring-1 focus:ring-tertiary focus:border-transparent',
            'placeholder:text-paragraph-gray placeholder:text-sm ',
            'h-[42px]',
            `${className}`
          )}
          placeholder={placeholder}
        >
          {children}
        </Field>
        <ErrorMessage
          name={name}
          render={(errorMessage) => {
            return <div className="text-red text-small m-2 flex">{errorMessage}</div>
          }}
        />
      </div>
    </div>
  )
}

export default TextField
