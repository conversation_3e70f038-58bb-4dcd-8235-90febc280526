import React, { DOMAttributes } from 'react'

import ButtonContent from '@atoms/Button/Button.Content'
import { TextProps } from '@atoms/Text'
import { addClasses } from '@lib/style/style'
import { getStyleClassesFromProps, JustifyTypes, ThemeStyles } from '@lib/style/theme'
import { PreventDefaultCallback } from '@lib/utils/func'

import './Button.css'

export type ButtonVariants = 'primary' | 'black' | 'white' | 'border-primary' | 'border-black' | 'border-white'

export interface ButtonProps extends DOMAttributes<HTMLButtonElement>, ThemeStyles {
  style?: React.CSSProperties
  className?: string
  variant?: ButtonVariants
  type?: 'submit' | 'button'
  loading?: boolean
  disabled?: boolean
  children: React.ReactNode

  // icon configuration
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  justify?: JustifyTypes
  // text props
  textProps?: Partial<TextProps>
}

const CustomButton = React.forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const { type, loading, disabled, children, unstyled, style, className } = props

  let onClick = props.onClick
  if (loading || disabled) {
    onClick = PreventDefaultCallback
  }

  let classes = ''
  if (!unstyled) {
    classes = getButtonClassesFromProps(props)
  }

  return (
    <button ref={ref} onClick={onClick} style={style} type={type ?? 'button'} className={classes}>
      {unstyled ? children : <ButtonContent {...props}>{children}</ButtonContent>}
    </button>
  )
})
CustomButton.displayName = 'Button'

export default CustomButton

export function getButtonClassesFromProps(props: ButtonProps): string {
  return addClasses(
    'relative',
    `click-animation`,
    `btn-${props.variant ?? 'primary'}`,
    getStyleClassesFromProps(props),
    props.className
  )
}
