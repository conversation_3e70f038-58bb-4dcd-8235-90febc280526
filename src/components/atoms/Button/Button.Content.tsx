import React from 'react'

import { ButtonProps } from '@atoms/Button/CustomButton'
import ThemeIconSpinner from '@atoms/Icons/ThemeIconSpinner'
import Text from '@atoms/Text'
import { addClasses } from '@lib/style/style'

export const ButtonContent: React.FC<ButtonProps> = (props) => {
  const { loading, disabled, children, textProps, leftIcon, rightIcon } = props

  const _textProps = textProps

  let _rightIcon = rightIcon
  if (loading) {
    _rightIcon = <ThemeIconSpinner stroke={'white'} size={20} />
  }

  let _className = addClasses('flex items-center', `${props.justify ?? 'justify-center'}`)

  return (
    <>
      {(loading || disabled) && <div className="full-cover bg-white bg-opacity-40" />}
      <div className={_className}>
        {leftIcon && <div className="mr-3">{leftIcon}</div>}
        {typeof children === 'string' ? <Text {..._textProps}>{children}</Text> : children}
        {_rightIcon && <div className="ml-3">{_rightIcon}</div>}
      </div>
    </>
  )
}

export default ButtonContent
