'use client'
import { Form, Formik, FormikValues } from 'formik'
import { FormikConfig, FormikErrors, FormikHelpers } from 'formik/dist/types'
import React, { useMemo } from 'react'

import NotificationMessage from '@components/molecules/NotificationMessage'
import ThemeLoadingOverlay from '@components/molecules/ThemeLoadingOverlay'

interface Props<T> {
  data: T
  initialErrors?: FormikErrors<T>
  children: React.ReactNode
  onSubmit: (values: T, actions?: FormikHelpers<T>) => Promise<any> | void
  validationSchema?: any
  formError?: any
  className?: string
  useFormLoader?: boolean
  isLoading?: boolean
  formikProps?: Partial<FormikConfig<T>>
}

const ValidationDefault = {
  validateOnChange: true,
  validateOnBlur: false,
  validateOnMount: false,
}

function FormContext<T extends FormikValues>({
  data,
  initialErrors,
  validationSchema,
  onSubmit,
  children,
  className,
  useFormLoader,
  isLoading,
  formError,
  formikProps,
}: Props<T>): React.ReactElement<Props<T>> {
  const defaultFormikProps = useMemo(() => {
    return {
      ...ValidationDefault,
      ...formikProps,
    }
  }, [formikProps])

  return (
    <Formik
      {...defaultFormikProps}
      initialValues={data}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
      initialErrors={initialErrors}
    >
      {({ isSubmitting }) => (
        <Form className={className}>
          <ThemeLoadingOverlay loading={isLoading || (useFormLoader && isSubmitting)} />
          <NotificationMessage type={'error'}>{formError}</NotificationMessage>
          {children}
        </Form>
      )}
    </Formik>
  )
}

export default FormContext
