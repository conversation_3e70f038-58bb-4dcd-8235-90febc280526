import React from 'react'

import { addClasses } from '@lib/style/style'
import { ThemeStyles } from '@lib/style/theme'

interface Props extends ThemeStyles {
  children?: React.ReactNode
  style?: React.CSSProperties
}

const Paper: React.FC<Props> = (props) => {
  return (
    <div className={addClasses(`card`, props.className)} style={props.style}>
      {props.children}
    </div>
  )
}

export default Paper
