import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 20,
  height: 20,
}

interface Props extends IconProps {
  dark?: boolean
}

const HeadsetIcon: React.FC<Props> = (props) => {
  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1293_7330)">
        <path
          d="M1 16V10C1 5.03 5.03 1 10 1C14.97 1 19 5.03 19 10V16"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M19 17C19 18.1 18.1 19 17 19H16C14.9 19 14 18.1 14 17V14C14 12.9 14.9 12 16 12H19V17ZM1 17C1 18.1 1.9 19 3 19H4C5.1 19 6 18.1 6 17V14C6 12.9 5.1 12 4 12H1V17Z"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_1293_7330">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default HeadsetIcon
