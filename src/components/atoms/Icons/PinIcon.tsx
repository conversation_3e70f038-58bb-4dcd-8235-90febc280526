import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 22,
  height: 28,
}

const PinIcon: React.FC<IconProps> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 22 28"
      fill="none"
    >
      <path
        d="M21.2477 11.0132C21.2477 16.5396 11.1239 27.2642 11.1239 27.2642C11.1239 27.2642 1 16.5396 1 11.0132C1 5.48679 5.55021 1 11.1377 1C16.7252 1 21.2616 5.48679 21.2616 11.0132H21.2477Z"
        stroke="black"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.1377 14.4056C13.0549 14.4056 14.6091 12.8683 14.6091 10.9721C14.6091 9.0758 13.0549 7.53857 11.1377 7.53857C9.22047 7.53857 7.66626 9.0758 7.66626 10.9721C7.66626 12.8683 9.22047 14.4056 11.1377 14.4056Z"
        stroke="black"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default PinIcon
