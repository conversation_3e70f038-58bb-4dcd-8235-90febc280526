import React from 'react'

import { getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 30,
  height: 30,
}

const DesignByIcon: React.FC<IconProps> = (props) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" {...getIconSizes(props, DSize)} viewBox="0 0 29 29" fill="none">
      <g clipPath="url(#clip0_2392_6451)">
        <path
          d="M28.6067 4.51157C27.9217 4.20001 27.1859 4.3371 26.4248 4.91037C25.6636 5.4961 25.0547 6.70495 24.5853 8.57431C24.3443 11.2537 23.6212 12.4252 22.416 12.0513C21.2235 11.6774 20.2087 10.8175 19.4094 9.4716C18.1409 7.30315 17.2021 5.74535 16.6185 4.74835C16.0604 3.80121 15.6037 3.07839 15.2358 2.56743C14.8933 2.04401 14.6903 1.76984 14.6522 1.70753C14.4873 1.47074 14.3224 1.19657 14.1448 0.909936C14.056 0.747924 13.9545 0.598376 13.815 0.473752C13.4978 0.149729 13.1045 -0.0122819 12.6352 0.000180487C12.1658 -0.0122819 11.7725 0.149729 11.4554 0.461289C11.1382 0.772849 10.986 1.15918 11.0241 1.57044C11.0494 1.9817 11.1636 2.35557 11.392 2.6796C11.5061 2.84161 11.6203 3.04101 11.7345 3.22794C12.5464 4.63619 13.3583 6.04444 14.1702 7.45269C14.2463 7.60224 14.5 8.13813 14.944 9.06034C15.3626 9.92025 15.6037 10.4686 15.6671 10.6805C15.6544 11.0045 15.4641 11.1042 15.0836 10.9796C14.8933 10.8674 14.4366 10.5184 13.7388 9.95764C13.0284 9.38437 12.5083 8.93572 12.1531 8.57431C11.7852 8.20044 11.4046 7.86395 11.0114 7.53993C10.5927 7.21591 10.1741 6.82957 9.74278 6.39339C9.61592 6.28123 9.24803 5.93228 8.67717 5.37147C8.20779 4.91037 7.89064 4.61127 7.72572 4.44926C7.68766 4.41187 7.48469 4.22493 7.10411 3.87599C6.87577 3.66413 6.60936 3.42734 6.29221 3.16563C5.97507 2.87899 5.64523 2.70452 5.32808 2.64221C5.25197 2.62975 5.17585 2.62975 5.11242 2.62975C5.02362 2.62975 4.93482 2.62975 4.84602 2.64221C4.49081 2.69206 4.17367 2.84161 3.90726 3.11578C3.57743 3.4398 3.41251 3.85106 3.4252 4.31217C3.4252 4.52403 3.46325 4.73589 3.57743 4.94775C3.67892 5.13469 3.83115 5.34655 4.03412 5.53348C4.24978 5.73288 4.47813 5.96967 4.74453 6.23138C5.68329 7.17852 7.01531 8.64909 8.61374 10.182C10.5927 12.0762 11.5569 13.2726 12.0262 13.821C12.4956 14.3693 12.2673 14.7432 12.077 14.9426C11.8867 15.1295 11.6076 15.1046 11.2651 14.8678C10.9353 14.631 10.2629 14.1949 9.24803 13.5842C8.41076 13.0732 6.78696 11.9641 4.37664 10.2443C4.11024 10.0324 3.81846 9.80809 3.52668 9.55884C3.41251 9.4716 3.31102 9.38437 3.19685 9.28467C2.81627 9.01049 2.44838 8.87341 2.0678 8.87341C1.58574 8.87341 1.17979 9.03542 0.83727 9.35944C0.507437 9.68346 0.355206 10.0947 0.355206 10.5558C0.355206 10.9796 0.659668 11.4656 1.26859 12.0513C2.10586 12.7243 2.6133 13.1231 2.7909 13.2976L5.44226 15.1545C6.36833 15.8274 8.56299 17.1111 10.098 18.6813C10.5547 19.1549 9.78084 19.8403 8.3727 19.2795C5.91164 18.2327 4.30052 17.5472 3.53937 17.2108C2.94313 16.949 2.27078 16.6749 1.52231 16.4381C1.10367 16.4381 0.761155 16.5752 0.482065 16.8743C0.126859 17.1609 0 17.4974 0 17.8962C0 18.2825 0.126859 18.6315 0.431321 18.9181C0.596238 19.0801 0.811898 19.2297 1.10367 19.3917C1.15442 19.4291 1.26859 19.4914 1.47157 19.5786C1.75066 19.7157 2.39764 20.0771 3.39983 20.6379C4.38933 21.2112 5.86089 22.2331 7.81452 23.741C8.72791 24.4389 9.48906 25.0371 10.0853 25.5356L11.633 26.7819C11.9248 27.0062 12.2165 27.2056 12.4829 27.405C13.6247 28.2026 15.0709 28.726 16.7962 28.9753C18.7498 29.1622 20.3609 28.5515 21.6422 27.0934C22.9234 25.6353 24.1667 24.4016 25.4099 23.3921C26.6404 22.3702 27.4396 20.1145 27.4777 17.4102C27.5284 14.4316 28.0105 10.7552 28.4291 8.86095C28.9873 6.33108 29.2918 4.86052 28.6067 4.54896"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_2392_6451">
          <rect width="29" height="29" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default DesignByIcon
