import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 24,
  height: 20,
}

interface Props extends IconProps {
  dark?: boolean
}

const CompareIcon: React.FC<Props> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 24 20"
      fill="none"
    >
      <path
        d="M23 2V8H17"
        stroke={props.dark ? 'black' : 'white'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M1 18V12H7"
        stroke={props.dark ? 'black' : 'white'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.51 6.99991C5.17 2.30991 10.31 -0.140093 15 1.51991C16.27 1.96991 17.41 2.68991 18.36 3.63991L23 7.99991M1 11.9999L5.64 16.3599C9.15 19.8799 14.85 19.8799 18.37 16.3599C19.32 15.4099 20.04 14.2599 20.49 12.9999"
        stroke={props.dark ? 'black' : 'white'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default CompareIcon
