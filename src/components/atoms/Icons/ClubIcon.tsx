import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 28,
  height: 20,
}

const ClubIcon: React.FC<IconProps> = (props) => {
  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 28 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2638_3834)">
        <path
          d="M13.7999 15.11C16.6221 15.11 18.9099 12.8222 18.9099 10C18.9099 7.17784 16.6221 4.89001 13.7999 4.89001C10.9778 4.89001 8.68994 7.17784 8.68994 10C8.68994 12.8222 10.9778 15.11 13.7999 15.11Z"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M6.88 15.76C5.58 14.2 4.79 12.19 4.79 10C4.8 5.03 8.83 1 13.8 1C18.77 1 22.8 5.03 22.8 10C22.8 14.97 18.77 19 13.8 19H1"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M21.7798 19H26.5198" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_2638_3834">
          <rect width="27.52" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default ClubIcon
