import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 23,
  height: 29,
}

const LocationIcon: React.FC<IconProps> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 23 29"
      fill="none"
    >
      <g clipPath="url(#clip0_2659_4878)">
        <path
          d="M21.6169 11.3811C21.6169 16.9075 11.493 27.6321 11.493 27.6321C11.493 27.6321 1.36914 16.9075 1.36914 11.3811C1.36914 5.85471 5.91935 1.36792 11.5068 1.36792C17.0943 1.36792 21.6307 5.85471 21.6307 11.3811H21.6169Z"
          stroke="white"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.5071 14.7736C13.4243 14.7736 14.9785 13.2364 14.9785 11.3401C14.9785 9.44384 13.4243 7.90662 11.5071 7.90662C9.58986 7.90662 8.03564 9.44384 8.03564 11.3401C8.03564 13.2364 9.58986 14.7736 11.5071 14.7736Z"
          stroke="white"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2659_4878">
          <rect width="23" height="29" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default LocationIcon
