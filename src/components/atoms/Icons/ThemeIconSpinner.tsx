import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'
import { addClasses } from '@lib/style/style'

const DSize: IconSizes = {
  width: 25,
  height: 25,
}

const _classes = 'spinner stroke-primary store-background'

const ThemeIconSpinner: React.FC<IconProps> = (props) => {
  let _props = { ...props }
  if (!_props.stroke) {
    _props.stroke = 'primary'
  }

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...getIconSizes(_props, DSize)}
      className={addClasses(`spinner`, getClassName(_props))}
      viewBox="0 0 50 50"
    >
      <circle className="path" cx="25" cy="25" r="20" fill="none" strokeWidth="5"></circle>
    </svg>
  )
}

export default ThemeIconSpinner
