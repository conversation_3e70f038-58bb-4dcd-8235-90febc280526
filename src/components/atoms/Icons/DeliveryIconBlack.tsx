import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 55,
  height: 55,
}

const DeliveryIconBlack: React.FC<IconProps> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 55 55"
      fill="none"
    >
      <circle cx="27.5" cy="27.5" r="27.5" fill="black" />
      <g clipPath="url(#clip0_0_1)">
        <path
          d="M31.6092 17.8101H32.6492C33.4892 17.8101 34.2992 18.1101 34.9392 18.6501L42.7192 25.2301C43.7792 26.1301 44.3992 27.4501 44.3992 28.8501V33.1101C44.3992 34.1701 43.5392 35.0201 42.4892 35.0201H31.1992"
          fill="black"
        />
        <path
          d="M31.6092 17.8101H32.6492C33.4892 17.8101 34.2992 18.1101 34.9392 18.6501L42.7192 25.2301C43.7792 26.1301 44.3992 27.4501 44.3992 28.8501V33.1101C44.3992 34.1701 43.5392 35.0201 42.4892 35.0201H31.1992"
          stroke="#EE7F00"
          strokeMiterlimit="10"
        />
        <path
          d="M38.2395 38.6601C40.2001 38.6601 41.7895 37.0707 41.7895 35.1101C41.7895 33.1494 40.2001 31.5601 38.2395 31.5601C36.2788 31.5601 34.6895 33.1494 34.6895 35.1101C34.6895 37.0707 36.2788 38.6601 38.2395 38.6601Z"
          fill="black"
          stroke="#EE7F00"
          strokeMiterlimit="10"
        />
        <path d="M32.9004 28.86H42.3704" stroke="#EE7F00" strokeMiterlimit="10" strokeLinecap="round" />
        <path
          d="M31.19 35.02V17.98C31.19 16.61 30.08 15.5 28.71 15.5H12.15C10.69 15.5 9.5 16.69 9.5 18.15V32.31C9.5 33.8 10.71 35.01 12.2 35.01H31.19V35.02Z"
          fill="black"
          stroke="#EE7F00"
          strokeMiterlimit="10"
        />
        <path
          d="M20.9602 38.6601C22.9208 38.6601 24.5102 37.0707 24.5102 35.1101C24.5102 33.1494 22.9208 31.5601 20.9602 31.5601C18.9995 31.5601 17.4102 33.1494 17.4102 35.1101C17.4102 37.0707 18.9995 38.6601 20.9602 38.6601Z"
          fill="black"
          stroke="#EE7F00"
          strokeMiterlimit="10"
        />
      </g>
      <defs>
        <clipPath id="clip0_0_1">
          <rect width="35.89" height="24.16" fill="white" transform="translate(9 15)" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default DeliveryIconBlack
