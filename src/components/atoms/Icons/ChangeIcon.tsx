import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 20,
  height: 24,
}

interface Props extends IconProps {
  dark?: boolean
}

const ChangeIcon: React.FC<Props> = (props) => {
  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 20 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1293_7333)">
        <path d="M15 1L19 5L15 9" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path
          d="M1 11V9C1 6.79 2.79 5 5 5H19"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M5 23L1 19L5 15" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path
          d="M19 13V15C19 17.21 17.21 19 15 19H1"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_1293_7333">
          <rect width="20" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default ChangeIcon
