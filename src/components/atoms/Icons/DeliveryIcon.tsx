import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 31,
  height: 21,
}

const DeliveryIcon: React.FC<IconProps> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 31 21"
      fill="none"
    >
      <path
        d="M10 20C11.6569 20 13 18.6569 13 17C13 15.3431 11.6569 14 10 14C8.34315 14 7 15.3431 7 17C7 18.6569 8.34315 20 10 20Z"
        stroke="black"
        strokeWidth="1.2"
        strokeMiterlimit="10"
        strokeLinecap="round"
      />
      <path
        d="M24 20C25.6569 20 27 18.6569 27 17C27 15.3431 25.6569 14 24 14C22.3431 14 21 15.3431 21 17C21 18.6569 22.3431 20 24 20Z"
        stroke="black"
        strokeWidth="1.2"
        strokeMiterlimit="10"
        strokeLinecap="round"
      />
      <path
        d="M7.08121 17H3.38766C2.07201 17 1 15.8962 1 14.519V3.48101C1.00975 2.1038 2.07201 1 3.3974 1H16.6123C17.928 1 19 2.1038 19 3.48101V16.6253"
        stroke="black"
        strokeWidth="1.2"
        strokeMiterlimit="10"
        strokeLinecap="round"
      />
      <path d="M21 17H13" stroke="black" strokeWidth="1.2" strokeMiterlimit="10" strokeLinecap="round" />
      <path
        d="M27.7003 17H28.2752C29.2271 17 30 16.1972 30 15.2084V10.8126C30 9.84336 29.5476 8.93287 28.7936 8.36504L23.148 3.77343C22.5353 3.27413 21.7813 3 20.9991 3H20"
        stroke="black"
        strokeWidth="1.2"
        strokeMiterlimit="10"
        strokeLinecap="round"
      />
    </svg>
  )
}

export default DeliveryIcon
