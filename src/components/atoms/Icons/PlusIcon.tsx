import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 41,
  height: 41,
}

interface Props extends IconProps {
  invert?: boolean
}

const PlusIcon: React.FC<Props> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 41 41"
      fill="none"
    >
      <circle cx="20.5" cy="20.5" r="20.5" fill={props.invert ? '#fff' : '#EE7F00'} />
      <path
        d="M21 14V28"
        stroke={props.invert ? '#EE7F00' : '#fff'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14 21H28"
        stroke={props.invert ? '#EE7F00' : '#fff'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default PlusIcon
