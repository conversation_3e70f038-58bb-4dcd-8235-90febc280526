import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 14,
  height: 14,
}

interface Props extends IconProps {}

const CloseIcon: React.FC<Props> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...getIconSizes(props, DSize)}
      className={`${getClassName(props)}`}
      viewBox="0 0 14 14"
      fill="red"
    >
      <path stroke={props.stroke ? props.stroke : 'currentColor'} d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"></path>
    </svg>
  )
}

export default CloseIcon
