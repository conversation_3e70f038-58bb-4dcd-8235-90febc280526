import React from 'react'

import { ThemeColors } from '@/tailwind.config'
import { addClasses } from '@lib/style/style'

const DSize: IconSizes = {
  width: 71,
  height: 70,
}

export interface IconSizes {
  width: number
  height: number
}

export interface IconProps {
  width?: number
  height?: number
  size?: number
  className?: string
  color?: ThemeColors
  stroke?: ThemeColors
  style?: React.CSSProperties
}

export const getIconSizes = (p: IconProps, def?: IconSizes): IconSizes => {
  let _w = p.size || p.width || def?.width || 30
  let _h = p.size || p.height || def?.height || 30

  if (def) {
    if (p.width && p.height === undefined) {
      _h = (_w * def.height) / def.width
    } else if (p.width === undefined && p.height) {
      _w = (_h * def.width) / def.height
    } else {
      if (def.height === def.width) {
        if (_w != _h) {
          _w = Math.min(_w, _h)
          _h = _w
        }
      } else if (def.width > def.height) {
        _h = (_w * def.height) / def.width
      } else {
        _w = (_h * def.width) / def.height
      }
    }
  }

  return {
    width: _w,
    height: _h,
  }
}

export function getClassName(p: IconProps): string {
  let _className = p.className ?? ''

  p.color && (_className = addClasses(_className, `text-${p.color} fill-${p.color}`))

  p.stroke && (_className = addClasses(_className, `stroke-${p.stroke}`))

  return _className
}
