import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 17,
  height: 55,
}

const DividerArrowIcon: React.FC<IconProps> = (props) => {
  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 17 55"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M1 54.5L15.5 28L1 1" stroke="#E5E5E5" />
    </svg>
  )
}

export default DividerArrowIcon
