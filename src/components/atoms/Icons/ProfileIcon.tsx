import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 18,
  height: 21,
}

interface Props extends IconProps {
  fill?: string
}

const ProfileIcon: React.FC<Props> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 18 21"
      fill="none"
    >
      <g clipPath="url(#clip0_2392_5451)">
        <path
          d="M17.0822 20.0921V17.9585C17.0822 15.607 15.1546 13.7004 12.7772 13.7004H5.22292C2.84556 13.7004 0.917969 15.607 0.917969 17.9585V20.0921"
          stroke={props.fill ? props.fill : 'white'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.99635 9.42411C11.3739 9.42411 13.3013 7.51769 13.3013 5.166C13.3013 2.81432 11.3739 0.907898 8.99635 0.907898C6.6188 0.907898 4.69141 2.81432 4.69141 5.166C4.69141 7.51769 6.6188 9.42411 8.99635 9.42411Z"
          stroke={props.fill ? props.fill : 'white'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2392_5451">
          <rect width="18" height="21" fill={props.fill ? props.fill : 'white'} />
        </clipPath>
      </defs>
    </svg>
  )
}

export default ProfileIcon
