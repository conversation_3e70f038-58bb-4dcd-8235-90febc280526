import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 20,
  height: 22,
}

const HomeIcon: React.FC<IconProps> = (props) => {
  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 20 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1176_6708)">
        <path
          d="M1 8L10 1L19 8V19C19 20.1 18.1 21 17 21H3C1.9 21 1 20.1 1 19V8Z"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M7 21V11H13V21" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_1176_6708">
          <rect width="20" height="22" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default HomeIcon
