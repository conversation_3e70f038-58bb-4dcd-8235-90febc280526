import React from 'react'

import { addClasses } from '@/src/lib/style/style'

interface Props {
  months: number | string
}

const WarrantyIcon: React.FC<Props> = ({ months }) => {
  return (
    <div className="relative  w-[70px] h-[70px]">
      <div
        className={addClasses(
          'absolute w-[55px] h-[55px] bg-black rounded-full top-1/2 left-1/2',
          'transform -translate-x-1/2 -translate-y-1/2 flex items-center',
          'justify-center text-3xl text-primary '
        )}
      >
        {months}
      </div>
      <svg
        className="absolute w-[116px] h-[116px] top-[50%] left-[50%] -translate-x-1/2 -translate-y-1/2"
        viewBox="0 0 200 200"
      >
        <defs>
          <path id="circlePath" d="M 100, 100 m -54, 0 a 54,54 0 1,1 108,0 a 54,54 0 1,1 -108,0" />
        </defs>
        <text fontSize="20" fill="black" fontWeight="bold">
          <textPath href="#circlePath" startOffset="0%">
            Гаранция
          </textPath>
          <textPath href="#circlePath" startOffset="50%">
            месеца
          </textPath>
        </text>
      </svg>
    </div>
  )
}

export default WarrantyIcon
