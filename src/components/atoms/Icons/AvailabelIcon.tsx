import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 23,
  height: 22,
}

interface Props extends IconProps {
  fill?: string
}

const AvailabelIcon: React.FC<Props> = (props) => {
  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 23 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1339_9368)">
        <path
          d="M21 10.08V11.01C21 16.53 16.52 21.01 10.99 21C5.47002 21 0.990017 16.52 1.00002 10.99C1.00002 5.47002 5.48002 0.990017 11.01 1.00002C12.41 1.00002 13.8 1.30002 15.07 1.87002"
          stroke={props.fill ? props.fill : '#05CC19'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M22 2.01001L11 13.01L8 10.01"
          stroke={props.fill ? props.fill : '#05CC19'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_1339_9368">
          <rect width="23" height="22" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default AvailabelIcon
