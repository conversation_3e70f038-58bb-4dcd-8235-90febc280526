import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 7,
  height: 23,
}

const MenuIcon: React.FC<IconProps> = (props) => {
  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 7 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2648_644)">
        <path
          d="M3.27 5.54C4.52369 5.54 5.54 4.52369 5.54 3.27C5.54 2.01631 4.52369 1 3.27 1C2.01631 1 1 2.01631 1 3.27C1 4.52369 2.01631 5.54 3.27 5.54Z"
          stroke="#2D3132"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M3.27 13.52C4.52369 13.52 5.54 12.5037 5.54 11.25C5.54 9.99629 4.52369 8.97998 3.27 8.97998C2.01631 8.97998 1 9.99629 1 11.25C1 12.5037 2.01631 13.52 3.27 13.52Z"
          stroke="#2D3132"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M3.27 21.5C4.52369 21.5 5.54 20.4836 5.54 19.23C5.54 17.9763 4.52369 16.96 3.27 16.96C2.01631 16.96 1 17.9763 1 19.23C1 20.4836 2.01631 21.5 3.27 21.5Z"
          stroke="#2D3132"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2648_644">
          <rect width="6.55" height="22.5" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default MenuIcon
