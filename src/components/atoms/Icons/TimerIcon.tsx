import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 21,
  height: 24,
}

const TimerIcon: React.FC<IconProps> = (props) => {
  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 21 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 22.9858C14.9706 22.9858 19 18.8373 19 13.7197C19 8.60219 14.9706 4.45361 10 4.45361C5.02944 4.45361 1 8.60219 1 13.7197C1 18.8373 5.02944 22.9858 10 22.9858Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M10 8.57178V13.7196" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M10 4.45367V2.39453" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M7 1.36499H13" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M18 9.60137L20 7.54224" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  )
}

export default TimerIcon
