import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const ArrowRightCircle: React.FC<IconProps> = (props) => {
  const DSize: IconSizes = {
    width: 22,
    height: 22,
  }

  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="11" cy="11" r="11" fill="#EE7F00" />
      <path
        d="M17.3536 11.3536C17.5488 11.1583 17.5488 10.8417 17.3536 10.6464L14.1716 7.46447C13.9763 7.2692 13.6597 7.2692 13.4645 7.46447C13.2692 7.65973 13.2692 7.97631 13.4645 8.17157L16.2929 11L13.4645 13.8284C13.2692 14.0237 13.2692 14.3403 13.4645 14.5355C13.6597 14.7308 13.9763 14.7308 14.1716 14.5355L17.3536 11.3536ZM4 11.5H17V10.5H4V11.5Z"
        fill="white"
      />
    </svg>
  )
}

export default ArrowRightCircle
