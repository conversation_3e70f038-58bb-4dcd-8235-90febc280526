import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 41,
  height: 41,
}

interface Props extends IconProps {
  invert?: boolean
}

const MinusIcon: React.FC<Props> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 41 41"
      fill="none"
    >
      <circle cx="20.5" cy="20.5" r="20.5" fill={props.invert ? '#f3f3f3' : '#EE7F00'} />
      <path
        d="M14 21H28"
        stroke={props.invert ? '#EE7F00' : '#f3f3f3'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default MinusIcon
