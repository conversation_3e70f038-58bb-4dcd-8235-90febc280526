import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 18,
  height: 22,
}

const BrochureIcon: React.FC<IconProps> = (props) => {
  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 18 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2638_3856)">
        <path
          d="M1 18.5C1 17.12 2.12 16 3.5 16H17"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M3.5 1H17V21H3.5C2.12 21 1 19.88 1 18.5V3.5C1 2.12 2.12 1 3.5 1Z"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2638_3856">
          <rect width="18" height="22" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default BrochureIcon
