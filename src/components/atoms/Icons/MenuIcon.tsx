import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 24,
  height: 24,
}

const MenuIcon: React.FC<IconProps> = (props) => {
  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M3 12H21" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M3 6H21" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M3 18H21" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  )
}

export default MenuIcon
