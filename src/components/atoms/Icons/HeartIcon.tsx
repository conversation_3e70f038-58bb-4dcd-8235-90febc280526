import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

const DSize: IconSizes = {
  width: 22,
  height: 20,
}

interface Props extends IconProps {
  dark?: boolean
}

const HeartIcon: React.FC<Props> = (props) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 23 21"
      fill="none"
    >
      <g clipPath="url(#clip0_2638_3893)">
        <path
          d="M20.2901 2.61C18.1401 0.459998 14.6601 0.459998 12.5101 2.61L11.4501 3.67L10.3901 2.61C8.24006 0.459998 4.76006 0.459998 2.61006 2.61C0.460059 4.76 0.460059 8.24 2.61006 10.39L3.67006 11.45L11.4501 19.23L19.2301 11.45L20.2901 10.39C22.4401 8.24 22.4401 4.76 20.2901 2.61Z"
          stroke={props.dark ? 'black' : 'white'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2638_3893">
          <rect width="22.9" height="20.23" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default HeartIcon
