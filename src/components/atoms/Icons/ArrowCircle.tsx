import React from 'react'

import { getClassName, getIconSizes, IconProps, IconSizes } from '@atoms/Icons/Icon'

interface Props extends IconProps {
  dark?: boolean
  big?: boolean
}

const ArrowCircle: React.FC<Props> = (props) => {
  const DSize: IconSizes = {
    width: props.big ? 50 : 19,
    height: props.big ? 50 : 19,
  }

  return (
    <svg
      {...getIconSizes(props, DSize)}
      className={getClassName(props)}
      viewBox="0 0 19 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="9.5" cy="9.5" r="9.5" fill={props.dark ? '#2D3132' : '#F3F3F3'} />
      <path d="M9.49998 12L7.33491 8.25L11.665 8.25L9.49998 12Z" fill="#EE7F00" />
    </svg>
  )
}

export default ArrowCircle
