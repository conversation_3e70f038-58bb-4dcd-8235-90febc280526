// Define the available icon types
export const AVAILABLE_ICONS = [
  'card',
  'cube3d',
  'drill',
  'key',
  'lamp',
  'machine',
  'paint',
  'painting',
  'paper',
  'saw',
  'scissors',
  'shower',
  'tiles',
  'tools',
  'truck',
  'wallpaper',
  'xxx',
] as const

export type IconType = (typeof AVAILABLE_ICONS)[number]

// Import icons dynamically with type safety
const importIcons = (type: IconType) => {
  try {
    return [
      require(`@icons/minimalistic/hoverable/default/${type}.inline.svg`).default,
      require(`@icons/minimalistic/hoverable/hover/${type}.inline.svg`).default,
    ]
  } catch {
    return [null, null] // Gracefully handle missing icons
  }
}

interface HoverableIconProps {
  className?: string
  onClick?: () => void
}

// HOC to create hoverable icon components
const createHoverableIcon = (type: IconType) => {
  const IconComponent: React.FC<HoverableIconProps> = ({ className, onClick }) => {
    const [Normal, Hovered] = importIcons(type)

    if (!Normal || !Hovered) return null

    return (
      <div className={`relative h-12  aspect-square  ${className}`} onClick={onClick}>
        <Normal className="transition-opacity duration-300 opacity-100 group-hover:opacity-0" />
        <Hovered className="absolute top-0 left-0 transition-opacity duration-300 opacity-0 group-hover:opacity-100" />
      </div>
    )
  }

  return IconComponent
}

// Create individual icon components
export const CardIcon = createHoverableIcon('card')
export const Cube3DIcon = createHoverableIcon('cube3d')
export const DrillIcon = createHoverableIcon('drill')
export const KeyIcon = createHoverableIcon('key')
export const LampIcon = createHoverableIcon('lamp')
export const MachineIcon = createHoverableIcon('machine')
export const PaintIcon = createHoverableIcon('paint')
export const PaintingIcon = createHoverableIcon('painting')
export const PaperIcon = createHoverableIcon('paper')
export const SawIcon = createHoverableIcon('saw')
export const ScissorsIcon = createHoverableIcon('scissors')
export const ShowerIcon = createHoverableIcon('shower')
export const TilesIcon = createHoverableIcon('tiles')
export const ToolsIcon = createHoverableIcon('tools')
export const TruckIcon = createHoverableIcon('truck')
export const WallpaperIcon = createHoverableIcon('wallpaper')
export const XXXIcon = createHoverableIcon('xxx')

// Generic component that can render any of the icons by type
export const HoverableIcon: React.FC<{ type: IconType } & HoverableIconProps> = ({ type, ...props }) => {
  const [Normal, Hovered] = importIcons(type)

  if (!Normal || !Hovered) return null

  return (
    <div className={`relative h-12 group`} onClick={props.onClick}>
      <Normal className="transition-opacity duration-300 opacity-100 group-hover:opacity-0" />
      <Hovered className="absolute top-0 left-0 transition-opacity duration-300 opacity-0 group-hover:opacity-100" />
    </div>
  )
}
export const HoverIcons = {
  CardIcon,
  Cube3DIcon,
  DrillIcon,
  KeyIcon,
  LampIcon,
  MachineIcon,
  PaintIcon,
  PaintingIcon,
  PaperIcon,
  SawIcon,
  ScissorsIcon,
  ShowerIcon,
  TilesIcon,
  ToolsIcon,
  TruckIcon,
  WallpaperIcon,
  XXXIcon,
  HoverableIcon,
}
