import React from 'react'

import Img from '@atoms/Img'
import { addClasses } from '@lib/style/style'

interface Props {
  checked?: boolean
  onClick?: () => void
}

const CheckboxElement: React.FC<Props> = ({ checked = false, onClick }) => {
  return (
    <div
      className={addClasses(
        'min-w-[18px] h-[18px] flex rounded bg-background',
        'border border-gray-300 items-center justify-center',
        `${checked ? 'bg-primary border-none' : ''}`,
        `${onClick ? 'cursor-pointer' : ''}`
      )}
      onClick={onClick}
    >
      <Img
        alt="checkbox"
        src={'/images/icons/checkbox-white.svg'}
        width={11}
        height={11}
        className={checked ? '' : 'hidden'}
      />
    </div>
  )
}

export default CheckboxElement
