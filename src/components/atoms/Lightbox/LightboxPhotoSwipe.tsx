'use client'
import PhotoSwipeLightbox from 'photoswipe/lightbox'
import React, { useEffect } from 'react'

import 'photoswipe/style.css'
import './photoswipe.css'
import { addClasses } from '@lib/style/style'

interface Props {
  children: React.ReactNode
  galleryId: string
}

const LightboxPhotoSwipe: React.FC<Props> = ({ children, galleryId }) => {
  useEffect(() => {
    let lightbox = new PhotoSwipeLightbox({
      gallery: '#' + galleryId,
      children: 'a',
      pswpModule: () => import('photoswipe'),
    })
    lightbox.init()

    return () => {
      lightbox.destroy()
    }
  }, [galleryId])
  return (
    <div
      className={addClasses(
        'pswp-gallery flex overflow-auto gap-4 xl:grid xl:grid-cols-3 4xl:grid-cols-4 6xl:grid-cols-6'
      )}
      id={galleryId}
    >
      {children}
    </div>
  )
}

export default LightboxPhotoSwipe
