import { useField } from 'formik'
import React from 'react'

interface CheckboxProps {
  label: string
  name: string
  required?: boolean
  onChange?: () => void
}

const Checkbox: React.FC<CheckboxProps> = ({ label, name, required, onChange }) => {
  const [field, meta] = useField(name)

  return (
    <>
      <div className="flex items-center mb-3">
        <input
          type="checkbox"
          id={name}
          {...field}
          className="form-checkbox h-5 w-5 transition duration-150 ease-in-out"
          onChange={onChange}
        />
        <label htmlFor={name} className="ml-2 block text-sm leading-5 text-gray-900">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      </div>
      {meta.touched && meta.error ? <div className="text-red text-small ml-6 mb-4 flex">{meta.error}</div> : null}
    </>
  )
}

export default Checkbox
