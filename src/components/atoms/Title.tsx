import React from 'react'

import { addClasses } from '@lib/style/style'

import Text, { TextProps } from './Text'

interface Props extends Omit<TextProps, 'size'> {
  size?: 'h1' | 'h2' | 'h3' | 'h4' | 'sub-header' | 'small'
}

const Title: React.FC<Props> = ({ size, className, children, color, ...props }) => {
  let Element = (size ?? 'h1') as React.ElementType
  let _className = ''

  if (size === 'sub-header') {
    Element = 'h5'
  } else if (size === 'small') {
    Element = 'h5'
  }

  _className = addClasses(_className, className ?? '')

  return (
    <Text {...props} component={Element} color={color} className={_className}>
      {children}
    </Text>
  )
}

export default Title
