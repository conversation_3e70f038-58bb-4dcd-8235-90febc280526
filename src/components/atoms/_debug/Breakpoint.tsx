'use client'
import { useEffect, useState } from 'react'
import { useLocalStorage } from 'usehooks-ts'
import { createPortal } from 'react-dom'
import useBreakpoint, { breakpoints } from '@/src/hooks/useBreakpoint'
import { cn } from '@components/lib/utils'

export const Breakpoint = () => {
  const { breakpoint, width, innerWidth } = useBreakpoint()
  const [mount, setMount] = useState(false)

  const [isDebugging, setIsDebugging] = useLocalStorage('debugger', false)

  useEffect(() => {
    console.log('Breakpoint', mount)
    if (!mount) {
      setMount(true)
    }
  }, [mount])

  useEffect(() => {
    if (isDebugging) {
      document.documentElement.classList.add('debug-outline')
    } else {
      document.documentElement.classList.remove('debug-outline')
    }
  }, [isDebugging])

  const toggleDebugging = () => {
    setIsDebugging(!isDebugging)
  }

  if (!mount) return null

  return createPortal(
    <div
      className="fixed flex flex-col gap-2 bg-primary text-white p-2 bottom-0 right-0 cursor-pointer group z-110"
      onClick={toggleDebugging}
    >
      <div className="hidden group-hover:block">
        <table>
          <thead>
            <tr>
              <th className="text-left">Breakpoint</th>
              <th className="text-left">Width</th>
              <th className="text-left">Description</th>
            </tr>
          </thead>
          <tbody>
            {breakpoints.map((bp) => (
              <tr
                key={bp.name}
                className={cn(breakpoint === bp.name ? 'bg-secondary text-primary' : 'hover:bg-secondary/50')}
              >
                <td>{bp.name}</td>
                <td>{bp.width}</td>
                <td>{bp.description}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex gap-2">
        <input type="checkbox" checked={isDebugging} onChange={toggleDebugging} />
        {breakpoint?.toUpperCase()} - {width}px -{breakpoint === 'xs' && 'Phones in portrait mode'}
        {breakpoint === 'sm' && 'Tablets or phones in landscape mode'}
        {breakpoint === 'md' && 'Laptops or small desktops'}
        {breakpoint === 'lg' && 'Desktops'}
        {breakpoint === 'xl' && 'Large screens'}
        {breakpoint === '2xl' && 'Extra Large screens'} ({innerWidth}px)
        {breakpoint === '3xl' && 'Full HD (1080p)'}
        {breakpoint === '4xl' && '2.5K / WQHD'}
        {breakpoint === '5xl' && 'Ultra wide'}
        {breakpoint === '6xl' && '4K UHD'}
      </div>
    </div>,
    document.body
  )
}
