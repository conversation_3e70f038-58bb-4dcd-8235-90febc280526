import React, { PropsWithChildren } from 'react'

import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from '@components/theme/ui/tooltip'

interface NAProps {
  message?: string
}
export const NA: React.FC<PropsWithChildren<NAProps>> = ({ children, message }) => {
  return (
    <Tooltip>
      <TooltipTrigger className="hover:bg-primary/10">{children || 'N/A'}</TooltipTrigger>
      <TooltipContent>{message || 'Not available in the backend response'}</TooltipContent>
    </Tooltip>
  )
}
