import React, { useRef, ReactNode, TouchEvent } from 'react'

interface OnTapProps {
  onTap: (event: TouchEvent<HTMLDivElement>) => void
  children: ReactNode
  threshold?: number
  [key: string]: any // For rest props
}

const OnTap: React.FC<OnTapProps> = ({ onTap, children, threshold = 10, ...restProps }) => {
  const touchStartPos = useRef<{ x: number; y: number }>({ x: 0, y: 0 })
  const isSwiping = useRef<boolean>(false)

  const handleTouchStart = (e: TouchEvent<HTMLDivElement>) => {
    const touch = e.touches[0]
    touchStartPos.current = { x: touch.clientX, y: touch.clientY }
    isSwiping.current = false
  }

  const handleTouchMove = (e: TouchEvent<HTMLDivElement>) => {
    const touch = e.touches[0]
    const dx = Math.abs(touch.clientX - touchStartPos.current.x)
    const dy = Math.abs(touch.clientY - touchStartPos.current.y)

    // If moved more than the threshold, consider it a swipe
    if (dx > threshold || dy > threshold) {
      isSwiping.current = true
    }
  }

  const handleTouchEnd = (e: TouchEvent<HTMLDivElement>) => {
    if (!isSwiping.current && onTap) {
      onTap(e)
    }
  }

  return (
    <div onTouchStart={handleTouchStart} onTouchMove={handleTouchMove} onTouchEnd={handleTouchEnd} {...restProps}>
      {children}
    </div>
  )
}

export default OnTap
