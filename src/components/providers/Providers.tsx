import React from 'react'
import { TooltipProvider } from '@components/theme/ui/tooltip'
import { Toaster } from '@components/theme/ui/sonner'
import Static from '@features/static'
import { GDPR } from '@components/molecules/GDPR/GDPR'
import { StaticStoreInit } from '@features/static/StaticStoreInit'
import { getStaticContent } from '@features/static/api'
import { StaticContent } from '@lib/_generated/graphql_sdk'

export async function Providers({ children }: { children: React.ReactNode }) {
  let staticContent: StaticContent | null = null
  try {
    staticContent = await getStaticContent()
  } catch (e) {}

  return (
    <>
      <StaticStoreInit staticContent={staticContent} />
      <TooltipProvider delayDuration={100}>
        <Static component={GDPR} />
        {children}
        <Toaster
          theme="light"
          toastOptions={{
            classNames: {
              toast:
                'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',
              description: 'group-[.toast]:text-muted-foreground',
              actionButton: 'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',
              cancelButton: 'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground',
              success: 'group-[.toaster]:bg-green-50 group-[.toaster]:border-green-200 group-[.toaster]:text-green-800',
              error: 'group-[.toaster]:bg-red-50 group-[.toaster]:border-red-200 group-[.toaster]:text-red-800',
              warning:
                'group-[.toaster]:bg-yellow-50 group-[.toaster]:border-yellow-200 group-[.toaster]:text-yellow-800',
              info: 'group-[.toaster]:bg-blue-50 group-[.toaster]:border-blue-200 group-[.toaster]:text-blue-800',
            },
          }}
        />
      </TooltipProvider>
    </>
  )
}
