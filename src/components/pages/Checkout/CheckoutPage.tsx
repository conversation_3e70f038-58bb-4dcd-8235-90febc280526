'use client'

import { useRouter } from 'next/navigation'
import React, { useCallback, useEffect, useState } from 'react'

import Cookies from 'universal-cookie'
import { gtagTrack } from '@components/molecules/GDPR/GtagTrack'
import Container from '@atoms/Container'
import { EmptyCart } from '@components/pages/Cart/components/EmptyCart'
import { CheckoutFormData, FormState, FormType } from '@components/pages/Checkout/types'
import { scrollToElement } from '@context/ScrollUtils'
import { useCartStore } from '@features/cart/cart-state'
import { useEcontStore } from '@features/checkout/checkout.state'
import { FormWrapper } from '@features/checkout/components/forms/components/FormWrapper'
import { DeliverySchema } from '@features/checkout/components/forms/delivery/delivery.schema'
import { DeliveryData } from '@features/checkout/components/forms/delivery/DeliveryData'
import { PaymentSchema } from '@features/checkout/components/forms/payment/payment.schema'
import { PaymentData } from '@features/checkout/components/forms/payment/PaymentData'
import { PersonalSchema } from '@features/checkout/components/forms/personal/personal.schema'
import { PersonalData } from '@features/checkout/components/forms/personal/PersonalData'
import { MiniCheckout } from '@features/checkout/components/mini-checkout/MiniCheckout'
import { AvailableShippingMethodFragment, NewOrderInput, OrderRedirect } from '@lib/_generated/graphql_sdk'
import { showErrorToast, showSuccessToast } from '@lib/utils/toaster'
import { formToPayload, payloadToForm } from '@components/pages/Checkout/dataMapper'
import { EcontOffice } from '@features/checkout/service-providers/econt/types'
import useBreakpoint from '@/src/hooks/useBreakpoint'
import {
  trackAddPaymentInfo,
  trackCustomEvent,
  trackInitiateCheckout,
  trackPurchase,
} from '@lib/fbp/faceBookPixelHelper'
import { BNPPaymentInput } from '../../jet-leasing/BNPPayment/types'
import { GraphQLBackend } from '@lib/api/graphql'

const Cookie = new Cookies()

function createAndSubmitInvisibleForm(
  formData: Array<{ key: string; value: string }>,
  formAction: string,
  cb?: () => void
) {
  // Create a new form element
  const form = document.createElement('form')
  form.method = 'POST'
  form.action = formAction || 'https://ecg.test.upc.ua/go/pay' // Default URL if not provided
  form.style.display = 'none' // Make the form invisible

  console.log(JSON.stringify(formData), formAction)

  // Loop through the array and create inputs
  formData.forEach((item) => {
    // Create input element
    const input = document.createElement('input')
    input.type = 'hidden'
    input.name = item.key
    input.value = item.value //item.key === 'TotalAmount' ? getPriceInCoins(item.value).toString() : item.value

    // Add input to form
    form.appendChild(input)
  })

  // Add form to document body
  document.body.appendChild(form)

  // Submit the form after a short delay to allow the status message to be seen
  setTimeout(() => {
    cb && cb()
    form.submit()
  }, 500)
}

const CheckoutPage = () => {
  const router = useRouter()

  const { minBreakpoint } = useBreakpoint()
  const [isRedirecting, setIsRedirecting] = useState(false)

  const { list: cityList, loading: cityLoading } = useEcontStore((state) => state.cities)
  const officesList = useEcontStore((state) => state.offices.list)
  // Forms state
  const [formsState, setFormsState] = useState<FormState>({
    personal: {
      isAvailable: false,
      status: 'loading',
      data: null,
    },
    delivery: {
      isAvailable: false,
      status: false,
      data: null,
    },
    payment: {
      isAvailable: false,
      status: false,
      data: null,
    },
    finale: {
      isAvailable: false,
      status: false,
      data: null,
    },
  })
  const [createOrderLoading, setCreateOrderLoading] = useState(false)
  const [currentStep, setCurrentStep] = React.useState<FormType>('personal')
  const personalFormData = formsState.personal.data
  const deliveryFormData = formsState.delivery.data
  const paymentFormData = formsState.payment.data

  const personalFormStatus = formsState.personal.status
  const paymentFormStatus = formsState.payment.status

  const setFormStatus = (form: FormType, status: boolean | 'loading') => {
    setFormsState((prev) => ({
      ...prev,
      [form]: {
        ...prev[form],
        status: status,
      },
    }))
  }

  const setFormAvailable = (form: FormType, status: boolean) => {
    setFormsState((prev) => ({
      ...prev,
      [form]: {
        ...prev[form],
        isAvailable: status,
      },
    }))
  }

  const setFormData = (form: FormType, data: PersonalSchema | DeliverySchema | PaymentSchema) => {
    setFormsState((prev) => ({
      ...prev,
      [form]: {
        ...prev[form],
        data: data,
      },
    }))
  }

  const [hasMounted, setHasMounted] = useState(false)
  const [trackedInitiateCheckout, setTrackedInitiateCheckout] = useState(false)

  const getCities = useEcontStore((state) => state.getCities)
  const getOffices = useEcontStore((state) => state.getOffices)
  const {
    customer,
    saveClientData,
    shipping: { address, selectedMethod },
    shippingPrices,
    getShippingPrices,
    getPaymentMethods,
    createOrder,
    saveShippingMethod,
    ready,
    items,
    resetCart,
    paymentMethod,
    note,
    couponCode,
  } = useCartStore()

  // Form handlers
  const onPersonalFormSubmit = useCallback(
    async (data: PersonalSchema) => {
      scrollToForm('personal')
      setFormStatus('personal', 'loading')
      const response = await saveClientData(formToPayload.personal(data))
      if (cityList.length === 0 && !cityLoading) {
        await getCities({ countryCode: 'BGR' })
      }

      setFormData('personal', data)
      if (response) {
        setFormStatus('personal', true)
        setFormAvailable('delivery', true)
        setCurrentStep('delivery')
        scrollToForm('delivery', 300)
        trackCustomEvent('CHECKOUT_STEP_1', {
          custom_data: {
            firstName: data.firstName,
            lastName: data.lastName,
            phone: data.phone,
            email: data.email,
          },
        })
      } else {
        setFormStatus('personal', false)
      }
    },
    [cityList.length, cityLoading, getCities, saveClientData]
  )

  const onDeliveryFormSubmit = useCallback(
    async (data: DeliverySchema) => {
      scrollToForm('delivery')
      const deliveryPayload = formToPayload.delivery({ data, cityList, officesList })
      if (shippingPrices.data.length > 0) {
        setFormStatus('delivery', 'loading')
        setFormData('delivery', data)
        if (data.deliveryMethod !== deliveryFormData?.deliveryMethod) {
          await saveShippingMethod(data.deliveryMethod || '', deliveryPayload)
        }
        setFormStatus('payment', 'loading')
        getPaymentMethods().then(() => {
          trackCustomEvent('CHECKOUT_STEP_2', {
            custom_data: {
              deliveryMethod: data.deliveryMethod,
              deliveryStore: data.deliveryStore,
              deliveryType: data.deliveryType,
              deliveryCity: data.deliveryCity,
              deliveryOffice: data.deliveryOffice,
            },
          })
          setFormStatus('delivery', true)
          setFormStatus('payment', false)
          setFormAvailable('payment', true)
          setCurrentStep('payment')
          scrollToForm('payment', 300)
        })
        return
      }
      setFormData('delivery', data)
      setFormStatus('delivery', 'loading')
      await getShippingPrices(deliveryPayload)
      setFormStatus('delivery', true)
    },
    [
      cityList,
      deliveryFormData?.deliveryMethod,
      getPaymentMethods,
      getShippingPrices,
      officesList,
      saveShippingMethod,
      shippingPrices.data.length,
    ]
  )

  const redirectToPayment = useCallback(
    (params: OrderRedirect) => {
      createAndSubmitInvisibleForm(params.data, params.url, () => {
        resetCart()
      })
    },
    [resetCart]
  )

  const onCreateOrder = useCallback(
    async ({ personalFormData, deliveryFormData, paymentFormData }: CheckoutFormData) => {
      setCreateOrderLoading(true)
      setFormStatus('finale', 'loading')
      const orderPayload: NewOrderInput = {
        client: formToPayload.personal(personalFormData),
        shipping: formToPayload.delivery({ data: deliveryFormData, cityList, officesList }),
        ...formToPayload.payment(paymentFormData),
        shippingMethodCode: deliveryFormData.deliveryMethod as string,
      }

      if (paymentFormData.paymentMethod === 'stenik_leasingjetcredit') {
        const paymentData: BNPPaymentInput = {
          downPayment: paymentFormData.bnpDownPayment || 0,
          pricingVariantId: parseInt(paymentFormData.bnpSelectedVariantId as string),
          customerData: {
            firstName: personalFormData.firstName,
            lastName: personalFormData.lastName,
            phone: personalFormData.phone,
            email: personalFormData.email,
            egn: paymentFormData.bnpEgn as string,
            // address: paymentFormData.bnpAddress as string, // REMOVED - Address field no longer used
          },
        }

        await GraphQLBackend.CartSaveBNPPayment({
          cartToken: Cookie.get('cartToken') || '',
          paymentData,
        })
      }

      const createOrderResponse = await createOrder(orderPayload)

      setFormStatus('payment', true)
      if (createOrderResponse.status && createOrderResponse.data) {
        Cookie.set('order_id', createOrderResponse.data.orderNumber, {
          expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
          path: '/',
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
        })
        setIsRedirecting(true)
        if (paymentFormData.paymentMethod === 'paymentmethodbnpl') {
          // reset card if tbi payment method
          resetCart()
        }
        if (createOrderResponse.data.redirect.url.length > 0) {
          redirectToPayment(createOrderResponse.data.redirect)
        } else {
          router.push(`/checkout/onepage/success`)
        }
      }
      setCreateOrderLoading(false)
    },
    [cityList, createOrder, officesList, redirectToPayment, resetCart, router]
  )

  const onPaymentSubmit = useCallback(
    async (data: PaymentSchema) => {
      scrollToForm('payment')

      // Track AddPaymentInfo event
      trackAddPaymentInfo({
        content_ids: items?.map((item) => item.product.id) || [],
        contents: items?.map((item) => ({ id: item.product.id, quantity: item.baseQty })) || [],
        currency: items?.[0]?.price?.currency || 'BGN',
        value: items?.reduce((acc, item) => acc + Number(item.price.value) * item.baseQty, 0) || 0,
      })

      // GA4 add_payment_info event
      gtagTrack({
        eventName: 'add_payment_info',
        properties: {
          currency: items?.[0]?.price?.currency || 'BGN',
          value: items?.reduce((acc, item) => acc + Number(item.price.value) * item.baseQty, 0) || 0,
          payment_type: data.paymentMethod || 'DEFAULT',
          items:
            items?.map((item) => ({
              item_id: item.product.id,
              item_name: item.product.name,
              currency: item.price.currency,
              price: Number(item.price.value),
              quantity: item.baseQty,
            })) || [],
        },
      })

      trackCustomEvent('CHECKOUT_STEP_3', {
        custom_data: {
          paymentMethod: data.paymentMethod,
          promoCode: data.promoCode,
          subscribe: data.subscribe,
        },
      })
      setFormData('payment', data)
      if (minBreakpoint('lg')) {
        setFormStatus('payment', 'loading')
        scrollToForm('personal')
        if (personalFormData && deliveryFormData) {
          await onCreateOrder({ personalFormData, deliveryFormData, paymentFormData: data })
        }
        setFormStatus('payment', true)
        setCurrentStep('finale')
      } else {
        showSuccessToast({ description: 'Информация за плащане е запазена' })
        setFormStatus('payment', true)
        setCurrentStep('finale')
        scrollToForm('finale', 300)
      }
    },
    [deliveryFormData, items, minBreakpoint, onCreateOrder, personalFormData]
  )

  const fetchShippingPrices = useCallback(
    (data: DeliverySchema) => {
      const deliveryPayload = formToPayload.delivery({ data, cityList, officesList })
      getShippingPrices(deliveryPayload)
    },
    [cityList, getShippingPrices, officesList]
  )

  useEffect(() => {
    ;(async () => {
      if (!hasMounted && ready) {
        trackInitiateCheckout({
          content_ids: items.map((item) => String(item.product.id)),
          contents: items.map((item) => ({ id: item.product.id, quantity: item.baseQty })),
          currency: items?.[0]?.price?.currency || 'BGN',
          value: items.reduce((acc, item) => acc + Number(item.price.value) * item.baseQty, 0),
          num_items: items.length,
        })

        gtagTrack({
          eventName: 'begin_checkout',
          properties: {
            currency: items?.[0]?.price?.currency || 'BGN',
            value: items.reduce((acc, item) => acc + Number(item.price.value) * item.baseQty, 0),
            items: items.map((item) => ({
              item_id: item.product.id,
              item_name: item.product.name,
              currency: item.price.currency,
              price: Number(item.price.value),
              quantity: item.baseQty,
            })),
          },
        })

        setHasMounted(true)
        setFormData('personal', payloadToForm.personal(customer))
        setFormStatus('personal', false)
        setFormAvailable('personal', true)
        if (address) {
          setFormStatus('delivery', 'loading')
          const cityResponse = await getCities({ countryCode: 'BGR' })
          if (!cityResponse.success) {
            showErrorToast({
              title: 'Грешка',
              description: 'Списъкът с градове не може да бъде зареден',
            })
          }

          const addressCity = cityResponse.data.find((city) => city.id === Number(address.cityId))

          let officeList: EcontOffice[] = []
          if (address.method === 'ECONT_TO_OFFICE') {
            if (addressCity) {
              const officesResponse = await getOffices({
                countryCode: 'BGR',
                cityID: addressCity?.id,
                servingReceptions: true,
                showCargoReceptions: true,
                showLC: true,
              })
              officeList = officesResponse.data
            }
          }

          const office = officeList.find((office) => office.code === address.officeCode)

          let availableShippingPrices: AvailableShippingMethodFragment[] = []
          if (selectedMethod) {
            const addressCity = cityResponse.data.find((city) => city.postCode === address.postCode)
            const availableShippingPricesResponse = await getShippingPrices({
              address: address.street || '',
              city: address.city || '',
              cityId: String(addressCity?.id || ''),
              officeCode: address.officeCode,
              postCode: address.postCode,
              type: address.method,
              storeCode: address.storeCode,
            })
            if (availableShippingPricesResponse.status) {
              // filter eventually duplicated shipping methods Bug: PRAKTIS-816
              availableShippingPrices = availableShippingPricesResponse.data.filter(
                (method, index, self) => index === self.findIndex((m) => m.method.code === method.method.code)
              )
            }
          }
          const selectedAvailableShippingMethod = availableShippingPrices.find(
            (method) => method.method.code === selectedMethod
          )
          setFormData(
            'delivery',
            payloadToForm.delivery({
              address,
              city: addressCity,
              office,
              shippingMethod: selectedAvailableShippingMethod,
            })
          )

          setFormStatus('delivery', false)
          setFormStatus('payment', false)
        }

        if (paymentMethod) {
          setFormData('payment', payloadToForm.payment({ paymentMethod, note, promoCode: couponCode }))
        }
      }
    })()
  }, [
    ready,
    getCities,
    hasMounted,
    address,
    customer,
    getOffices,
    selectedMethod,
    getShippingPrices,
    paymentMethod,
    note,
    couponCode,
    items,
    trackedInitiateCheckout,
  ])

  const scrollToForm = (formType: FormType, timeout?: number) => {
    if (formType) {
      if (timeout) {
        setTimeout(() => {
          scrollToElement(formType, -20)
        }, timeout)
      }
      scrollToElement(formType, -20)
    }
  }

  const onPressPrev = () => {
    if (currentStep === 'delivery') {
      setCurrentStep('personal')
      scrollToForm('personal')
    }
    if (currentStep === 'payment') {
      setCurrentStep('delivery')
      scrollToForm('delivery')
    }
  }

  const handleOnClickForm = (formType: FormType) => {
    switch (formType) {
      case 'personal':
        scrollToForm('personal', 300)
        setFormAvailable('delivery', false)
        setFormAvailable('payment', false)
        break
      case 'delivery':
        scrollToForm('delivery', 300)
        setFormAvailable('payment', false)
        break
      case 'payment':
        scrollToForm('payment', 300)
        break
    }
    setCurrentStep(formType)
  }

  if (ready && !items?.length && !isRedirecting) {
    return (
      <Container className="my-10 max-w-screen-2xl">
        <EmptyCart />
      </Container>
    )
  }

  return (
    <Container className="my-10 flex flex-col lg:flex-row gap-5 max-w-screen-3xl">
      <div className="flex flex-col gap-4 w-full lg:w-8/12">
        {/* Personal Data */}
        <FormWrapper
          type="personal"
          title="Лични данни"
          formState={formsState}
          currentStep={currentStep}
          onClickForm={handleOnClickForm}
        >
          <PersonalData
            data={personalFormData}
            isExpanded={currentStep === 'personal'}
            formStatus={personalFormStatus}
            onSubmitAction={onPersonalFormSubmit}
          />
        </FormWrapper>
        {/* Delivery Data */}
        <FormWrapper
          type="delivery"
          title="Информация за доставка"
          formState={formsState}
          currentStep={currentStep}
          onClickForm={handleOnClickForm}
        >
          <DeliveryData
            data={deliveryFormData}
            isExpanded={currentStep === 'delivery'}
            formStatus={personalFormStatus}
            onSubmitAction={onDeliveryFormSubmit}
            onFetchShippingPricesAction={fetchShippingPrices}
          />
        </FormWrapper>
        {/* Payment Data */}
        <FormWrapper
          type="payment"
          title="Плащане"
          formState={formsState}
          currentStep={currentStep}
          onClickForm={handleOnClickForm}
        >
          <PaymentData
            data={paymentFormData}
            isExpanded={currentStep === 'payment'}
            formStatus={paymentFormStatus}
            onSubmitAction={onPaymentSubmit}
          />
        </FormWrapper>
      </div>
      <div className="w-full lg:w-4/12">
        <MiniCheckout
          onPressComplete={() => {
            if (personalFormData && deliveryFormData && paymentFormData) {
              onCreateOrder({ personalFormData, deliveryFormData, paymentFormData })
            }
          }}
          onPressPrev={onPressPrev}
          hasPreviousStep={currentStep !== 'personal'}
          isActive={currentStep === 'finale'}
          createOrderLoading={createOrderLoading}
        />
      </div>
    </Container>
  )
}

export default CheckoutPage
