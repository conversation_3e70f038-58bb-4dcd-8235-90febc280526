import { PersonalSchema } from '@features/checkout/components/forms/personal/personal.schema'
import {
  AvailableShippingMethod,
  AvailableShippingMethodFragment,
  CartFullFragment,
  ClientInput,
  InvoiceType,
  NewOrderInput,
  ShippingAddress,
  ShippingInput,
  ShippingMethod,
  ShippingMethodType,
} from '@lib/_generated/graphql_sdk'
import { DeliverySchema } from '@features/checkout/components/forms/delivery/delivery.schema'
import { EcontCity, EcontOffice } from '@features/checkout/service-providers/econt/types'
import { useCallback } from 'react'
import { PaymentSchema } from '@features/checkout/components/forms/payment/payment.schema'

export const formToPayload = {
  personal: (data: PersonalSchema): ClientInput => {
    const {
      email,
      firstName,
      invoice,
      invoiceAddress,
      invoiceCity,
      invoiceEIK,
      invoiceMRP,
      invoiceName,
      invoiceVatNumber,
      invoiceType,
      lastName,
      password,
      phone,
      registerMe,
    } = data

    return {
      email,
      firstName,
      ...(invoice
        ? {
            invoice: {
              address: invoiceAddress || '',
              city: invoiceCity || '',
              ...(invoiceType === 'Company'
                ? {
                    company: {
                      eik: invoiceEIK || '',
                      mol: invoiceMRP || '',
                      name: invoiceName || '',
                      vat: invoiceVatNumber || '',
                    },
                  }
                : {}),
              ...(invoiceType === 'Personal'
                ? {
                    individual: {
                      egn: invoiceEIK || '',
                      name: invoiceName || '',
                      vat: invoiceVatNumber || '',
                    },
                  }
                : {}),
              type: invoiceType === 'Company' ? InvoiceType.Company : InvoiceType.Personal,
            },
          }
        : {}),
      lastName,
      password: registerMe ? password || '' : '',
      phone,
      registerOnOrder: registerMe,
    }
  },
  delivery: ({
    data,
    cityList,
    officesList,
  }: {
    data: Partial<DeliverySchema>
    cityList: EcontCity[]
    officesList: EcontOffice[]
  }): ShippingInput => {
    const { deliveryType, deliveryAddress, deliveryPostalCode, deliveryStore, deliveryCity, deliveryOffice } = data

    const office = officesList.find((office) => office.code === deliveryOffice)
    const city = cityList.find((city) => city.id === deliveryCity)

    return {
      address:
        deliveryType === 'ECONT_TO_ADDRESS'
          ? deliveryAddress || ''
          : deliveryType === 'TO_STORE'
            ? ''
            : office?.address?.fullAddress || '',
      city: deliveryType === 'TO_STORE' ? '' : cityList ? city?.name || '' : String(deliveryCity),
      cityId: deliveryType === 'TO_STORE' ? '' : cityList ? city?.id?.toString() : '',
      officeCode:
        deliveryType === 'TO_STORE'
          ? ''
          : deliveryType === 'ECONT_TO_OFFICE'
            ? officesList
              ? office?.code?.toString()
              : deliveryOffice
            : '',
      postCode: deliveryType === 'TO_STORE' ? '' : deliveryPostalCode || '',
      type: deliveryType || ShippingMethodType.None,
      storeCode: deliveryStore,
    }
  },
  payment: (data: PaymentSchema): Pick<NewOrderInput, 'note' | 'paymentMethodCode' | 'promoCode'> => ({
    note: data.comment || '',
    paymentMethodCode: data.paymentMethod || '',
    promoCode: data.promoCode || '',
  }),
}

export const payloadToForm = {
  personal: (customer: any): PersonalSchema => ({
    firstName: customer.firstName,
    lastName: customer.lastName,
    phone: customer.phone,
    email: customer.email,
    registerMe: false,
    password: '',
    passwordRepeat: '',
    invoice: !!customer.invoice?.type,
    invoiceType: customer.invoice?.type === InvoiceType.Company ? 'Company' : 'Personal',
    invoiceName: customer.invoice?.[customer.invoice?.type === InvoiceType.Company ? 'company' : 'individual']?.name,
    invoiceMRP: customer.invoice?.company?.mol,
    invoiceCity: customer.invoice?.city,
    invoiceEIK: customer.invoice?.company?.eik || customer.invoice?.individual?.egn,
    invoiceVatRegistered:
      !!customer.invoice?.[customer.invoice?.type === InvoiceType.Company ? 'company' : 'individual']?.vat,
    invoiceVatNumber:
      customer.invoice?.[customer.invoice?.type === InvoiceType.Company ? 'company' : 'individual']?.vat,
    invoiceAddress: customer.invoice?.address,
  }),
  delivery: ({
    address,
    city,
    office,
    shippingMethod,
  }: {
    address: ShippingAddress
    city?: EcontCity
    office?: EcontOffice
    shippingMethod?: AvailableShippingMethodFragment
  }): DeliverySchema => {
    console.log('XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', address)
    return {
      deliveryType: address.method || null,
      deliveryStore: address.storeCode || null,
      deliveryCity: city ? city.id : address?.cityId ? Number(address.cityId) : null,
      deliveryPostalCode: address.postCode || null,
      deliveryOffice: office ? office.code : address?.officeCode ? address.officeCode : null,
      deliveryAddress: address.method === 'ECONT_TO_ADDRESS' ? address.street || '' : office?.address.fullAddress || '',
      deliveryMethod: shippingMethod?.method.code || null,
    }
  },
  payment: ({
    note,
    paymentMethod,
    promoCode,
  }: {
    note?: string
    paymentMethod?: string
    promoCode?: string
  }): PaymentSchema => ({
    paymentMethod: paymentMethod || null,
    comment: note || '',
    agreement: false,
    adult: false,
    subscribe: false,
    promoCode: promoCode || '',
  }),
}
