import { RefObject } from 'react'
import { PersonalSchema } from '@features/checkout/components/forms/personal/personal.schema'
import { DeliverySchema } from '@features/checkout/components/forms/delivery/delivery.schema'
import { PaymentSchema } from '@features/checkout/components/forms/payment/payment.schema'

export type FormType = 'personal' | 'delivery' | 'payment' | 'finale'

export interface FormProps<T> {
  data?: T | null
  isExpanded: boolean
  formStatus: boolean | 'loading'
  onSubmitAction: (data: T) => void
}

export interface FormRefProps<T> {
  validate: () => Promise<{
    isValid: boolean
    errors: {
      message: string
      ref: RefObject<any>
      type: string
    }[]
    data: T
  }>
}

interface FormStateItem<T> {
  isAvailable: boolean
  status: boolean | 'loading'
  data: T | null
}

export interface FormState {
  personal: FormStateItem<PersonalSchema>
  delivery: FormStateItem<DeliverySchema>
  payment: FormStateItem<PaymentSchema>
  finale: FormStateItem<{}>
}

export interface CheckoutFormData {
  personalFormData: PersonalSchema
  deliveryFormData: DeliverySchema
  paymentFormData: PaymentSchema
}

//
// export interface FormState<T extends FormType = FormType> {
//   isActive: boolean
//   status: boolean | 'loading'
//   data: T extends 'personal'
//     ? PersonalSchema
//     : T extends 'delivery'
//       ? DeliverySchema
//       : T extends 'payment'
//         ? PaymentSchema
//         : null
// }
