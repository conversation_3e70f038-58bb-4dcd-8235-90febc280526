import { useState } from 'react'

import Text from '@atoms/Text'
import { Select, SelectContent, SelectItem, SelectTrigger } from '@components/theme/ui/select'
import { InfoBar } from '@features/cart/components/InfoBar'
import Store from '@icons/minimalistic/static/store.inline.svg'

const stores = [
  { id: 1, name: 'Стара Загора 1' },
  { id: 2, name: 'PRAKTIS - София 1' },
  { id: 3, name: 'PRAKTIS - Видин' },
  { id: 4, name: 'PRAKTIS - Стара Загора' },
  { id: 5, name: 'PRAKTIS - Велико Търново' },
  { id: 6, name: 'PRAKTIS - Хасково' },
  { id: 7, name: 'PRAKTIS - Русе' },
  { id: 8, name: 'PRAKTIS - Пловдив' },
  { id: 9, name: '<PERSON>е<PERSON><PERSON><PERSON><PERSON> <PERSON>ърговски Център - Стар<PERSON> Загора' },
]

export const PickUpInfo = () => {
  const [selected, setSelected] = useState<number>()

  return (
    <InfoBar
      icon={<Store className="h-10 w-fit" />}
      label={<Text className="font-bold text-xs">Вземи от магазин:</Text>}
      text={
        <Select
          onValueChange={(value) => {
            setSelected(Number(value))
          }}
          value={String(selected)}
        >
          <div className="flex items-center gap-2">
            <Text>Заявен в магазин:</Text>{' '}
            <SelectTrigger className="w-fit underline underline-offset-4 cursor-pointer hover:bg-white border-none shadow-none">
              <div className="font-bold text-xs">
                {stores.find((store) => store.id === selected)?.name || 'Избери магазин'}
              </div>
            </SelectTrigger>
            <SelectContent className="bg-white">
              {stores.map((store) => (
                <SelectItem key={store.id} value={String(store.id)}>
                  {store.name}
                </SelectItem>
              ))}
            </SelectContent>
            <Text>Брой продукти от този магазин:</Text> <Text className="font-bold">1</Text>
          </div>
        </Select>
      }
    />
  )
}
