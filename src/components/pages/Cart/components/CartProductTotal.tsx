import { CircleX, LucideLoaderCircle } from 'lucide-react'
import { useState } from 'react'

import Text from '@atoms/Text'
import { Price } from '@components/molecules/Price'
import { Button } from '@components/theme/ui/button'
import { Tooltip, TooltipContent, TooltipTrigger } from '@components/theme/ui/tooltip'
import { useCartStore } from '@features/cart/cart-state'
import { CartItemFragment } from '@lib/_generated/graphql_sdk'
import { ProductPrice } from '@components/molecules/ProductPrice'

interface CartProductTotalProps {
  cartItem: CartItemFragment
}

export const CartProductTotal = ({ cartItem }: CartProductTotalProps) => {
  const productSku = cartItem.sku

  const removeItem = useCartStore((state) => state.removeItem)
  const [loading, setLoading] = useState(false)
  return (
    <div className="flex items-center gap-2">
      <ProductPrice
        data={{
          price: {
            value: cartItem.rowTotal.value,
            currency: cartItem.price.currency,
          },
          ...(cartItem.discountAmount.value || cartItem.discountPercent
            ? {
                special: {
                  value: cartItem.rowTotal.value - (cartItem.discountAmount.value || 0),
                  currency: cartItem.price.currency || '',
                },
              }
            : {}),
        }}
        variant="cartItem"
      />
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="tertiary"
            size="icon"
            className="w-6 h-6 p-0 hidden xl:inline-block"
            aria-label="Remove item"
            onClick={async () => {
              setLoading(true)
              removeItem(productSku).then(() => {
                setLoading(false)
              })
            }}
          >
            {loading && <LucideLoaderCircle className="animate-spin" />}
            {!loading && <CircleX className="text-gray-500" />}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <Text>Премахване</Text>
        </TooltipContent>
      </Tooltip>
    </div>
  )
}
