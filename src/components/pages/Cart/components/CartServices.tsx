import { Swiper, SwiperSlide } from 'swiper/react'

import AppLink from '@atoms/AppLink'
import { cn } from '@components/lib/utils'
import { ServiceTile } from '@components/molecules/ServiceTile'
import { Button } from '@components/theme/ui/button'

export const CartServices = () => {
  return (
    <div className={cn('bg-background rounded-2xl p-6 lg:p-10', 'grid grid-cols-24 gap-y-9 xl:gap-y-0')}>
      <div className="flex flex-col gap-4 col-span-24 xl:col-span-8">
        {/* <div className="font-bold text-xs">Информационен placeholder</div>
        <div className="text-xs">
          Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е
          индустриален стандарт от около 1500 година
        </div> */}
        <div className="flex">
          <AppLink href="/">
            <Button variant="tertiary">Продължете с пазаруването</Button>
          </AppLink>
        </div>
      </div>

      <div className="flex flex-col gap-3 col-span-24 xl:col-span-16 xl:col-start-12">
        <div className="text-xs font-bold">Добавете услуга</div>
        <div className="flex gap-4">
          <Swiper
            centeredSlides
            slidesPerView={1.2}
            spaceBetween={10}
            breakpoints={{
              320: {
                centeredSlides: true,
                slidesPerView: 1.2,
                spaceBetween: 10,
              },
              640: {
                centeredSlides: false,
                slidesPerView: 3,
                spaceBetween: 10,
              },
            }}
          >
            <SwiperSlide>
              <ServiceTile type="machine" title="Машини под наем" />
            </SwiperSlide>
            <SwiperSlide>
              <ServiceTile type="truck" title="Транспорт до адрес" />
            </SwiperSlide>
            <SwiperSlide>
              <ServiceTile type="tools" title="Монтажни услуги" />
            </SwiperSlide>
          </Swiper>
        </div>
      </div>
    </div>
  )
}
