import React from 'react'

import AppLink from '@atoms/AppLink'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { ClassName } from '@lib/types/ClassName'

interface CartProductNameProps {
  title: string
  sku: string
  url: string
  className?: ClassName
}

export const CartProductName: React.FC<CartProductNameProps> = ({ title, sku, url, className }) => {
  return (
    <div className={cn('grid grid-cols-24 flex-1', className)}>
      <div className="col-span-24 flex flex-col xl:flex-row justify-between">
        <div className="flex flex-col gap-3 sm:gap-2 md:gap-0">
          <AppLink href={`/${url}`}>
            <Text className="font-bold text-xs">{title}</Text>
          </AppLink>
          <div className="flex gap-1">
            <Text className="text-paragraph-muted text-xs">Прод. код:</Text>
            <span className="text-xs">{sku}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
