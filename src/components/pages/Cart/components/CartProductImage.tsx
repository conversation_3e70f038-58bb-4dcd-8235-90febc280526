import Img from '@atoms/Img'
import { cn } from '@components/lib/utils'
import { GalleryImage, Image, Product } from '@lib/_generated/graphql_sdk'
import { ClassName } from '@lib/types/ClassName'

interface CartProductImageProps {
  image?: Image
  className?: ClassName
}

export const CartProductImage = ({ image, className }: CartProductImageProps) => {
  return (
    <div className={cn('flex items-center justify-center w-[80px] aspect-square relative', className)}>
      <Img
        src={image?.src || ''}
        mobileSrc={image?.mobileSrc || undefined}
        alt={image?.alt || image?.title || ''}
        fill
        className="object-contain"
      />
    </div>
  )
}
