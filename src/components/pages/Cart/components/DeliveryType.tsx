import { Truck } from 'lucide-react'

import Text from '@atoms/Text'
import { Button } from '@components/theme/ui/button'

export const DeliveryType = () => (
  <div className="flex gap-2">
    <div>
      <Button size="icon" variant="inverse" aria-label="Truck delivery">
        <Truck size={38} />
      </Button>
    </div>
    <div className="flex flex-col text-xs">
      <Text>Доставка:</Text>
      <Text className="font-bold">20 лв. до “Зона 1” София</Text>
    </div>
  </div>
)
