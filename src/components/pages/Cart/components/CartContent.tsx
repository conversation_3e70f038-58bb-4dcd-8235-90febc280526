'use client'
import React from 'react'

import Text from '@atoms/Text'
// import { ProductSlider } from '@components/molecules/Widget/components/ProductSlider'
// import ProductsSlider from '@components/molecules/Widget/widgets/ProductsSlider'
import { CardProductsList } from '@components/pages/Cart/components/CardProductsList'
import { CartRow } from '@components/pages/Cart/components/CartRow'
// import { CartServices } from '@components/pages/Cart/components/CartServices'
import { CartSummary } from '@components/pages/Cart/components/CartSummary'
import { CartTableHeader } from '@components/pages/Cart/components/CartTableHeader'
import { DeliveryInfo } from '@components/pages/Cart/components/DeliveryInfo'
import { EmptyCart } from '@components/pages/Cart/components/EmptyCart'
import { Card } from '@components/theme/ui/card'
import { useCartStore } from '@features/cart/cart-state'
import { CartFullFragment } from '@lib/_generated/graphql_sdk'
// import { Product_StaticContent } from '@lib/data/product'

interface CartContentProps {
  cart: CartFullFragment | null
}

export const CartContent = ({ cart }: CartContentProps) => {
  // const Product_StaticContent_related = Product_StaticContent.related
  const cartStore = useCartStore()
  const items = cartStore.ready ? cartStore.items : cart?.items || []

  if (!cart || cart.items.length === 0) {
    return <EmptyCart />
  }

  const subTotal = cartStore.totals.find((total) => total.code === 'SUB_TOTAL')

  const freeDeliveryLimit = (cartStore.shipping?.freeShippingAfter || 0) - (subTotal?.amount.value || 0)
  return (
    <div>
      <Card className="my-5 rounded-2xl">
        <CartTableHeader />
        <CardProductsList>
          {/*<DeliveryInfo>*/}
          {/*<div className="flex gap-x-1 flex-wrap">*/}
          {/*  {freeDeliveryLimit > 0 ? (*/}
          {/*    <>*/}
          {/*      <Text>Остават</Text>{' '}*/}
          {/*      <Text className="text-primary font-bold">*/}
          {/*        {freeDeliveryLimit.toFixed(2)} {cartStore.currency}*/}
          {/*      </Text>*/}
          {/*      <Text>до</Text> <Text className="font-bold">безплатна доставка</Text>*/}
          {/*    </>*/}
          {/*  ) : (*/}
          {/*    <Text className="font-bold">Безплатна доставка</Text>*/}
          {/*  )}*/}
          {/*</div>*/}
          {/*</DeliveryInfo>*/}
          <div className="flex flex-col">
            {items.map((cartItem) => (
              <CartRow key={cartItem.id} cartItem={cartItem} />
            ))}
          </div>
        </CardProductsList>

        {/*<CartProductsPickUp items={items}  />*/}
        <CartSummary />
        {/*<CartServices />*/}
      </Card>
      {/*<div className="w-full overflow-hidden">*/}
      {/*  <ProductsSlider widget={Product_StaticContent_related as ProductsSliderWidget} />*/}
      {/*  <div className="font-bold py-10 px-6 text-xl">Често купувани заедно</div>*/}
      {/*  <ProductSlider products={Product_StaticContent_related.tabs[0].products} />*/}
      {/*</div>*/}
      {/*<div className="w-full overflow-hidden my-5">*/}
      {/*  <ProductsSlider widget={Product_StaticContent_related as ProductsSliderWidget} />*/}
      {/*</div>*/}
    </div>
  )
}
