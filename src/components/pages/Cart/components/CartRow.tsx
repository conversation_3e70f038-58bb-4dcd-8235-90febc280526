'use client'

import { cn } from '@components/lib/utils'
import { CounterManaged } from '@components/molecules/CounterManaged'
import { Price } from '@components/molecules/Price'
import { ProductBadge } from '@components/molecules/ProductBadge'
import { CartProductImage } from '@components/pages/Cart/components/CartProductImage'
import { CartProductName } from '@components/pages/Cart/components/CartProductName'
import { CartProductTotal } from '@components/pages/Cart/components/CartProductTotal'
import { getProductMainImage } from '@features/product/helpers'
import { CartItemFragment, SimpleProductViewFragment } from '@lib/_generated/graphql_sdk'
import { ProductPrice } from '@components/molecules/ProductPrice'

interface CartRowProps {
  cartItem: CartItemFragment
  PickItUp?: boolean
}

export const CartRow = ({ cartItem, PickItUp }: CartRowProps) => {
  const product = cartItem.product as SimpleProductViewFragment

  const productMainImage = getProductMainImage(product.image, product.gallery)
  const hasBadges = cartItem.labels.usePallet || cartItem.labels.freeShipping
  const itemPrice = product.measures.secondaryQty * product.price.price.value

  const maxQtyBaseMeasure = product.stock.minQty > 0 ? product.stock.qty - product.stock.minQty : product.stock.qty
  const maxQty = Math.floor(
    product.measures.secondaryMeasureUsed ? maxQtyBaseMeasure / product.measures.secondaryQty : maxQtyBaseMeasure
  )

  return (
    <div
      className={cn('flex xl:-mx-4 pt-5 xl:pt-0 px-0 xl:px-0', {
        'border-b-gray-300 hover:bg-gray-200 bg-secondary': PickItUp,
        'border-b-gray-200 hover:bg-gray-100 bg-white': !PickItUp,
      })}
    >
      <div className="w-4 hidden xl:block" />
      <div className={cn('w-full grid border-b pb-3', 'grid-cols-24 xl:py-8 gap-y-4 xl:gap-y-0')}>
        {/* Product */}
        <div className="col-span-24 grid grid-cols-12 xl:grid-cols-24 xl:col-span-7 2xl:col-span-8">
          <CartProductImage image={productMainImage} className="col-span-4 sm:col-span-2 md:col-span-2 xl:col-span-4" />
          <CartProductName
            className="col-span-8 sm:col-span-10 md:col-span-10 xl:col-span-17"
            title={product.name}
            sku={product.sku}
            url={product.urlKey}
          />
        </div>

        {/* Delivery */}
        <div className={cn('col-span-24 xl:col-span-7 flex justify-end', { 'hidden xl:block': !hasBadges })}>
          {/*<DeliveryTypeChanger className="order-2 xl:order-1 xl:max-w-[150px]" />*/}
          <div className="flex gap-3 order-1 xl:order-2 items-center">
            {cartItem.labels.usePallet && <ProductBadge type="palletizing" />}
            {cartItem.labels.freeShipping && <ProductBadge type="free-transport" />}
            {/*<div className="flex flex-col">*/}
            {/*  <Text className="text-2xs">Доставка:</Text>*/}
            {/*  <Text className="text-2xs font-bold">*/}
            {/*    20 лв. до “Зона 1” София*/}
            {/*  </Text>*/}
            {/*</div>*/}
          </div>
        </div>

        {/* Single Price */}
        <div className={cn('hidden xl:flex xl:flex-col col-span-3 items-center justify-center')}>
          <ProductPrice
            data={{
              price: cartItem.price,
              ...(cartItem.discountAmount.value || cartItem.discountPercent
                ? {
                    special: {
                      value: itemPrice - (cartItem.discountAmount.value / (cartItem.baseQty || 1) || 0),
                      currency: cartItem.price.currency || '',
                    },
                  }
                : {}),
            }}
            variant="cartItem"
          />
        </div>

        {/* Quantity */}
        <div className="col-span-7 xl:col-span-3 flex justify-start xl:justify-center items-center">
          <CounterManaged value={cartItem.baseQty} sku={product.sku} max={maxQty > 500 ? 500 : maxQty} min={1} />
        </div>

        {/* Total */}
        <div className={cn('col-span-17 col-start-10 xl:col-span-4 2xl:col-span-3 flex items-center justify-end')}>
          <CartProductTotal cartItem={cartItem} />
        </div>
      </div>
      {/*{unchangeableDeliveryType && <div className="w-4" />}*/}
      <div className="w-4 hidden xl:block" />
    </div>
  )
}
