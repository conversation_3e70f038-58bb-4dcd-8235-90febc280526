import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { ProductBadge } from '@components/molecules/ProductBadge'
import { DeliveryTypeChanger } from '@components/pages/Cart/components/DeliveryTypeChanger'
import { ClassName } from '@lib/types/ClassName'

interface CartProductDeliveryPropsProps {
  unchangeableDeliveryType?: boolean
  className?: ClassName
}

export const CartProductDelivery: React.FC<CartProductDeliveryPropsProps> = ({
  unchangeableDeliveryType,
  className,
}) => {
  return (
    <div className={cn('flex flex-col justify-between', className)}>
      {!unchangeableDeliveryType && (
        <div className="flex gap-3">
          <DeliveryTypeChanger className="hidden xl:flex" />
          <ProductBadge type="palletizing" />
          <div className="flex items-center gap-2">
            <ProductBadge type="free-transport" />
            <div className="flex flex-col">
              <Text className="text-xs">Доставка:</Text>
              <Text className="text-xs font-bold">20 лв. до “Зона 1” София</Text>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
