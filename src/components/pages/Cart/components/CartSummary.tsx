'use client'

import { CircleMinus, CirclePlus, LucideLoaderCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

import { NA } from '@atoms/_debug/NA'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { ProductStaticSlider } from '@components/molecules/ProductStaticSlider'
import { Button } from '@components/theme/ui/button'
import { Input } from '@components/theme/ui/input'
import { useCartStore } from '@features/cart/cart-state'
import { useCart } from '@features/cart/useCart'
import { ProductPrice } from '@components/molecules/ProductPrice'

export const CartSummary = () => {
  const cartStore = useCartStore()
  // const testingProducts = getRandomProductsList(10)
  const [loading, setLoading] = useState(false)
  const [couponCode, setCouponCode] = useState('')
  const [loadingCouponCode, setLoadingCouponCode] = React.useState(false)
  const router = useRouter()

  const delayedRedirect = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      router.push('/checkout')
    }, 1000)
  }

  const { cartTotals } = useCart()

  return (
    <div className={cn('grid grid-cols-24 pb-5')}>
      <div className="col-span-9 lg:col-span-13 2xl:col-span-16 hidden md:block bg-transparent px-5">
        {/*<div className="py-5 px-5">*/}
        {/*  <Text className="font-bold text-xs">Нещо изпуснахте?</Text>*/}
        {/*</div>*/}
        {/*<ProductStaticSlider products={testingProducts} />*/}
      </div>
      <div
        className={cn(
          'py-1 border-b bg-gray-100',
          'col-span-24',
          'md:col-span-15 md:col-start-10',
          'lg:col-span-11',
          '2xl:col-span-8',
          'rounded-b-2xl'
        )}
      >
        {/* Total sum */}
        <div className="grid grid-cols-3 xl:grid-cols-8 py-4 border-b pr-4 xl:pr-11">
          <div className="col-span-2 xl:col-span-5 px-6 flex items-center">
            <Text className="text-sm">Обща сума</Text>
          </div>
          <div className="col-span-1 xl:col-span-3 flex justify-end">
            {cartStore.ready && cartTotals.SUB_TOTAL && (
              <ProductPrice data={{ price: cartTotals.SUB_TOTAL?.amount }} variant="cartItem" />
            )}
          </div>
        </div>
        {/* Discount code */}
        {cartStore.ready && (
          <div className="grid grid-cols-3 xl:grid-cols-8 border-b bg-[#F9F9F9] xl:pr-11">
            <div className="col-span-2 sm:col-span-1 xl:col-span-3 pl-6 flex items-center py-4">
              <Text className="text-sm">Промо код:</Text>
            </div>
            <div className="col-span-1 sm:col-span-1 xl:col-span-2 flex items-center justify-end">
              {!!cartStore.couponCode.length && (
                <div className="rounded-full bg-green-300/20 flex items-center justify-center text-sm text-green-500 font-bold cursor-default">
                  <div className="px-4 py-3">{cartStore.couponCode}</div>
                  <Button
                    variant="ghost"
                    onClick={() => {
                      setLoadingCouponCode(true)
                      cartStore.removeCouponCode().finally(() => setLoadingCouponCode(false))
                    }}
                    size="icon"
                    className="hover:bg-primary/20 hover:text-primary text-primary p-1.5 w-11 h-11 aspect-square"
                    aria-label="Increase count"
                  >
                    {loadingCouponCode ? (
                      <LucideLoaderCircle size={18} className="animate-spin" />
                    ) : (
                      <CircleMinus size={18} />
                    )}
                  </Button>
                </div>
              )}
              {!cartStore.couponCode.length && (
                <div className="bg-white rounded-full flex shadow-sm border border-input">
                  <Input
                    className="rounded-full bg-white shadow-none border-none focus-visible:ring-0"
                    disabled={loadingCouponCode}
                    onChange={(e) => {
                      setCouponCode(e.target.value)
                    }}
                  />
                  <Button
                    variant="ghost"
                    onClick={() => {
                      setLoadingCouponCode(true)
                      cartStore.applyCouponCode(couponCode).finally(() => setLoadingCouponCode(false))
                    }}
                    size="icon"
                    className="hover:bg-gray-200 hover:text-primary text-primary p-1.5 w-11"
                    aria-label="Increase count"
                  >
                    {loadingCouponCode ? (
                      <LucideLoaderCircle size={18} className="animate-spin" />
                    ) : (
                      <CirclePlus size={18} />
                    )}
                  </Button>
                </div>
              )}
            </div>
            {cartStore.ready && cartTotals.DISCOUNT_TOTAL && cartTotals.DISCOUNT_TOTAL.amount && (
              <div className="col-span-8 sm:col-span-1 xl:col-span-3 flex justify-end py-4 pr-4 xl:pr-0">
                <ProductPrice data={{ price: cartTotals.DISCOUNT_TOTAL.amount }} variant="cartItem" />
              </div>
            )}
          </div>
        )}
        {/* Total */}
        <div className="grid grid-cols-3 xl:grid-cols-8 py-4 border-b pr-4 xl:pr-11">
          <div className="col-span-2 xl:col-span-3 px-6 flex items-center">
            <Text className="text-sm text-primary font-bold">Крайна сума:</Text>
          </div>
          <div className="xl:col-span-5 flex justify-end items-center">
            {cartStore.ready && cartTotals.GRANT_TOTAL?.amount && (
              <ProductPrice data={{ price: cartTotals.GRANT_TOTAL?.amount }} variant="cartTotal" />
            )}
          </div>
        </div>
        {/* Finish order button */}
        <div className="px-6 py-4 flex items-center flex-col">
          <Button className="w-full" onClick={delayedRedirect}>
            {loading && <LucideLoaderCircle className="animate-spin" />}
            Завършете поръчката
          </Button>
        </div>
      </div>
    </div>
  )
}
