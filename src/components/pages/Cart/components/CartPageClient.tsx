'use client'

import { CartFullFragment } from '@lib/_generated/graphql_sdk'
import { useFacebookPixel } from '@lib/fbp/useFacebookPixel'
import { useEffect } from 'react'

interface CartPageClientProps {
  cart: CartFullFragment | null
}

export const CartPageClient = ({ cart }: CartPageClientProps) => {
  const { trackAddToCart } = useFacebookPixel()

  const { items } = cart || { items: [] }

  useEffect(() => {
    const productPrice = items.reduce((total, item) => total + (item.price?.value || 0) * item.baseQty, 0)
    const productCurrency = items[0]?.price?.currency || 'BGN'

    trackAddToCart({
      contents: items.map((item) => ({ id: item.product.id, quantity: item.baseQty })),
      content_type: 'product',
      value: productPrice,
      currency: productCurrency,
    })
  }, [items, trackAddToCart])

  return null
}
