import { PopoverClose } from '@radix-ui/react-popover'
import { Luc<PERSON><PERSON>he<PERSON>, PenLine } from 'lucide-react'
import React from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { ButtonIcon } from '@components/molecules/ButtonIcon'
import { ProductBadge } from '@components/molecules/ProductBadge'
import { Button } from '@components/theme/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@components/theme/ui/popover'
import Truck from '@icons/minimalistic/static/truck.inline.svg'

interface DeliveryOptionProps {
  onClick: () => void
  active: boolean
  children: React.ReactNode
}

const DeliveryOption = ({ onClick, active, children }: DeliveryOptionProps) => {
  return (
    <div
      className={cn('flex justify-start gap-3 items-center hover:bg-gray-100 py-3 cursor-pointer px-2', {
        'bg-gray-100 font-bold': active,
      })}
      onClick={onClick}
    >
      <div className="w-5">{active && <LucideCheck size={17} />}</div>
      <div className="flex flex-1 items-center justify-between">{children}</div>
    </div>
  )
}

export const DeliveryTypeChanger = ({ className }: { className?: string }) => {
  const [deliveryType, setDeliveryType] = React.useState(1)
  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className={cn('flex gap-2 items-center', className)}>
          <div className="flex items-center">
            <ButtonIcon
              size="icon"
              variant="outline"
              aria-label="Edit delivery type"
              iconClassName="rounded-full p-2.5"
              icon={<PenLine size={24} />}
              label="Избери друг тип доставка"
              iconPosition="start"
              direction="horizontal"
              labelClassName="text-xs block underline tracking-normal text-left"
            />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-80 bg-white">
        <div className="flex flex-col">
          <div className="flex items-center justify-between">
            <ProductBadge type="free-transport" width={60} height={60} />
            <Text className="font-bold text-xs">Смени начин на доставка</Text>
          </div>
          <div className="pt-5">
            <PopoverClose asChild>
              <DeliveryOption onClick={() => setDeliveryType(1)} active={deliveryType === 1}>
                <Text className="text-xs">Бърза доставка</Text>
                <span className="text-xs">20.00 лв</span>
              </DeliveryOption>
            </PopoverClose>
            <PopoverClose asChild>
              <DeliveryOption onClick={() => setDeliveryType(2)} active={deliveryType === 2}>
                <Text className="text-xs">Вземи от магазин</Text>
                <span className="text-xs">0.00 лв</span>
              </DeliveryOption>
            </PopoverClose>
            <PopoverClose asChild>
              <DeliveryOption onClick={() => setDeliveryType(3)} active={deliveryType === 3}>
                <Text className="text-xs">Експресна доставка</Text>
                <span className="text-xs">60.00 лв</span>
              </DeliveryOption>
            </PopoverClose>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
