import React, { PropsWithChildren } from 'react'

import Text from '@atoms/Text'
import { InfoBar } from '@features/cart/components/InfoBar'
import Truck from '@icons/minimalistic/static/truck.inline.svg'

export const DeliveryInfo: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <InfoBar
      icon={<Truck className="h-10 w-fit" />}
      label={<Text className="font-bold text-xs">Продукт с доставка:</Text>}
      text={children}
    />
  )
}

/*
      {...(hasFreeShipping ? {
        text: (
          <>
            <Text>Остават</Text>{' '}
            <Text className="text-primary font-bold">{freeShippingAfter?.value} {freeShippingAfter?.currency}</Text>
            <Text>до</Text>{' '}
            <Text className="font-bold">безплатна доставка</Text>
          </>)
      } : {})}
 */
