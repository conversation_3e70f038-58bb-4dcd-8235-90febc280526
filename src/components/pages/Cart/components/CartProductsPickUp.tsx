import { CardProductsList } from '@components/pages/Cart/components/CardProductsList'
import { CartRow } from '@components/pages/Cart/components/CartRow'
import { PickUpInfo } from '@components/pages/Cart/components/PickUpInfo'
import { CartItemFragment, StoreCartItem } from '@lib/_generated/graphql_sdk'

interface CartProductsDeliveryProps {
  items: CartItemFragment[]
}

export const CartProductsPickUp = ({ items }: CartProductsDeliveryProps) => {
  return (
    <CardProductsList>
      <PickUpInfo />

      <div className="flex flex-col">
        {items.map((cartItem) => (
          <CartRow key={cartItem.id} cartItem={cartItem} PickItUp />
        ))}
      </div>
    </CardProductsList>
  )
}
