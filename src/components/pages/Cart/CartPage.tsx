import React from 'react'

import Container from '@atoms/Container'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import { CartContent } from '@components/pages/Cart/components/CartContent'
import { ServerCookie } from '@features/cart/ServerCookie'
import { CartFullFragment } from '@lib/_generated/graphql_sdk'
import GtagTrack from '../../molecules/GDPR/GtagTrack'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'
import { ServerGraphQLBackend } from '@lib/api/graphql_server'
import { CartPageClient } from '@components/pages/Cart/components/CartPageClient'

const CartPage = async () => {
  const token = await ServerCookie.getToken('cartToken')
  const isValid = await ServerCookie.isValid(token)

  let cart: CartFullFragment | null = null
  try {
    if (isValid && token) {
      const result = await ServerGraphQLBackend.GetCartById({ cart: token })
      cart = result.getCart
    }
  } catch (error) {
    console.log('Could not get cart by id', token)
    console.error(error)
  }

  return (
    <Container>
      <CartPageClient cart={cart} />
      {cart && (
        <GtagTrack
          action={{
            eventName: 'view_cart',
            properties: {
              currency: cart.items[0]?.price?.currency || 'BGN',
              value: cart.items.reduce((total, item) => total + (item.price?.value || 0) * item.baseQty, 0),
              items: cart.items.map((item, index) => ({
                item_id: item.product.id,
                item_name: item.product.name,
                price: item.price?.value,
                currency: item.price?.currency,
                quantity: item.baseQty,
                index,
              })),
            },
          }}
        />
      )}
      <Breadcrumb
        breadcrumbs={[
          { url: '/', label: 'Home' },
          { url: '#', label: 'Вашата количка' },
        ]}
      />
      <CartContent cart={cart} />
      
      <RelevaTrackPage pageType="cart" />
    </Container>
  )
}

export default CartPage
