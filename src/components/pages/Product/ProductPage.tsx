import React from 'react'
import { Product as ProductSchema } from 'schema-dts'

import ProductAvailability from '@/src/app/(store)/[...dynamic]/(product)/ProductAvailability/ProductAvailability'
import GtagTrack from '@components/molecules/GDPR/GtagTrack'
import MultipleProductSelection from '@/src/app/(store)/[...dynamic]/(product)/MultipleProductSelection/MultipleProductSelection'
import Container from '@atoms/Container'
import { HTML } from '@atoms/HTML'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import ExpandableBox from '@components/molecules/ExpandableBox'
import { MultipleBuyProductCard } from '@components/molecules/ProductBuyCards/MultipleBuyProductCard'
import { SimpleBuyProductCard } from '@components/molecules/ProductBuyCards/SimpleBuyProductCard'
import { ProductDetails } from '@components/molecules/ProductDetails'
import { ProductPageProps } from '@components/pages/Product/types'
import SimpleMenu from '@context/SimpleMenuContext'
import { ProductGallery } from '@features/product/components/ProductGallery'
import { SimpleProductViewFragment, AvailabilityStatus, Product } from '@lib/_generated/graphql_sdk'

import { SchemaOrg } from '../../atoms/SchemaOrg'
import { siteStatusHelper, SiteStatusHelper } from '@lib/utils/SiteStatusHelper'
import { cn } from '../../lib/utils'

import { CMSBlock, CMSMarkup } from '../../molecules/Widget/markup/instanced'
import { ProductActions } from '../../molecules/ProductBuyCards/components/ProductActions'
import { Widget } from '@components/molecules/Widget/Widget'
import { Body404 } from '@/src/app/(store)/not-found'
import { PixelTrackViewProduct } from '@lib/fbp/components/PixelTrackViewProduct'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'
import { sortStores } from '@lib/utils/sortStores'
import { notFound } from 'next/navigation'

export default async function ProductPage({ data, breadcrumbs: _breadcrumbs }: ProductPageProps) {
  const product = data.product as SimpleProductViewFragment
  // Check if product is valid
  const isProductValid = product.sku && product.price.price.value
  if (!isProductValid) {
    notFound()
  }

  const boughtTogether = data.boughtTogether
  const breadcrumbs = [
    ..._breadcrumbs,
    {
      label: product.name,
      url: `/${product.urlKey}`,
    },
  ]

  const status = siteStatusHelper.getStatus(product)

  // Otherwise render the normal product page
  return (
    <Container>
      {/* Track ViewContent event for product page */}
      <PixelTrackViewProduct data={product} category={breadcrumbs[breadcrumbs.length - 2]?.label || ''} />
      <GtagTrack
        action={{
          eventName: 'view_item',
          properties: {
            currency: product.price.price.currency,
            value: product.price.price.value,
            items: [
              {
                item_id: product.sku,
                item_name: product.name,
                item_category: breadcrumbs.map((item) => item.label).join(' > '),
                price: product.price.price.value,
                quantity: 1,
              },
            ],
          },
        }}
      />
      <meta property="product:price:amount" content={`${product.price.price.value}`} />
      <meta property="product:price:currency" content={`${product.price.price.currency}`} />
      <meta name="robots" content="INDEX,FOLLOW" />
      <SchemaOrg<ProductSchema>
        schema={{
          '@context': 'https://schema.org',
          '@type': 'Product',
          'sku': product.sku,
          'name': product.name,
          'image': product.image?.src || product.gallery?.at(0)?.image?.src || '',
          'brand': {
            '@type': 'Brand',
            'name': product.brand?.name,
            'image': product.brand?.image.src,
            'logo': product.brand?.image.src,
          },
          'category': breadcrumbs.map((item) => item.label).join(', '),
          'offers': {
            '@type': 'Offer',
            'sku': product.sku,
            'price': product.price.price.value,
            'priceCurrency': product.price.price.currency,
            'availability': product.skuAvailability.some((sku) => sku.available === AvailabilityStatus.Available)
              ? 'InStock'
              : 'OutOfStock',
          },
        }}
      />
      <Breadcrumb breadcrumbs={breadcrumbs} hasPagination={false} attached />
      <div className="flex flex-col lg:flex-row lg:justify-between gap-10 md:gap-4">
        <div className="flex flex-col z-50 lg:w-[36%] 3xl:max-w-xl 4xl:max-w-1xl 5xl:max-w-2xl 6xl:max-w-3xl">
          <ProductGallery product={product} />
        </div>
        <div className="flex flex-col lg:w-5/8 mb-24 3xl:w-full">
          <div className={cn('flex gap-3 flex-col lg:flex-row')}>
            <div
              className={cn(
                'flex flex-col',
                product.stock.zeronSiteStatus === SiteStatusHelper.ZERON_SITE_STATUS_4 ||
                  product.stock.zeronSiteStatus === SiteStatusHelper.ZERON_SITE_STATUS_5
                  ? 'w-full'
                  : 'lg:w-1/2 xl:w-7/12 2xl:w-full'
              )}
            >
              <ProductDetails {...(product as Product)} />
            </div>
            {product.stock.zeronSiteStatus !== SiteStatusHelper.ZERON_SITE_STATUS_4 &&
              product.stock.zeronSiteStatus !== SiteStatusHelper.ZERON_SITE_STATUS_5 && (
                <div className="lg:w-1/2 xl:w-5/12 xl:min-w-[420px] 2xl:min-w-[460px] flex flex-col">
                  {!product.measures.secondary && (
                    <SimpleBuyProductCard product={product}>
                      <ProductActions type="primary" />
                    </SimpleBuyProductCard>
                  )}
                  {product.measures.secondary && (
                    <MultipleBuyProductCard
                      product={{
                        ...product,
                      }}
                    >
                      <ProductActions type="primary" />
                    </MultipleBuyProductCard>
                  )}
                </div>
              )}
          </div>
          <div className="grid gap-4 mt-4">
            {boughtTogether && boughtTogether.length > 0 && (
              <SimpleMenu defaultIsOpen>
                <ExpandableBox label="Често купувани заедно">
                  <div className="p-1 sm:p-3 xl:p-7">
                    <MultipleProductSelection
                      mainProduct={product}
                      subProducts={boughtTogether as SimpleProductViewFragment[]}
                    />
                  </div>
                </ExpandableBox>
              </SimpleMenu>
            )}
            <div id="detailed-description-block">
              <SimpleMenu>
                <ExpandableBox label="Описание">
                  <div className="xl:p-12 xl:pt-0 p-6 pt-0 sm:px-8">
                    <div className='raw-content'>
                    {product.description && <CMSMarkup markup={product.description} />}
                    </div>
                    <CMSBlock id="automatic_text_below_description" />
                  </div>
                </ExpandableBox>
              </SimpleMenu>
            </div>
            {!(status === SiteStatusHelper.INDIVIDUAL_ORDER || status === SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP) &&
              product.stock.manageStock &&
              product.skuAvailability &&
              product.skuAvailability.length > 0 && (
                <div id="availability-description-block">
                  <SimpleMenu defaultIsOpen>
                    <ExpandableBox label="Наличност">
                      <div className="xl:p-12 xl:pt-0 p-2 sm:p-8 pt-0 text-xs sm:text-sm">
                        <ProductAvailability storeAvailability={sortStores(product.skuAvailability)} />
                      </div>
                    </ExpandableBox>
                  </SimpleMenu>
                </div>
              )}
            <div>
              <SimpleMenu>
                <ExpandableBox label="Доставка и връщане">
                  <div className="xl:p-12 xl:pt-0 p-6 pt-0 sm:px-8">
                    <CMSBlock id="product_delivery_info" />
                  </div>
                </ExpandableBox>
              </SimpleMenu>
            </div>
          </div>
        </div>
      </div>
      <div className="w-full overflow-hidden">
        <Widget.List widgets={data?.widgets ?? []} />
      </div>

      <RelevaTrackPage
        pageType="product"
        productId={product.sku}
        categories={breadcrumbs.map(b => b.label)}
      />
    </Container>
  )
}
