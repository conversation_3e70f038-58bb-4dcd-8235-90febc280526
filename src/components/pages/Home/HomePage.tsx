import React from 'react'

import { HomePageBody } from '@components/templates/HomePageBody'

import {notFound} from "next/navigation"
import {GraphQLBackend} from "@lib/api/graphql"

export default async function HomePage() {
  try {
    const {
      slider,
      widgets,
      brands,
    } = await GraphQLBackend.GetHomePageData().then(x => {
      return {
        slider: x.getHomepage?.heroSlider ?? {} as any,
        widgets: x.getHomepage?.widgets ?? [],
        brands: x.getBrands ?? [],
      }
    })

    return <HomePageBody
      slider={slider}
      widgets={widgets}
      brands={brands}
    />
  } catch (error) {
    console.error('Error fetching home page data:', error)
    notFound()
  }
}
