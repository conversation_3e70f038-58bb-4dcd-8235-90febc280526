import React from 'react'
import { CollectionPage } from 'schema-dts'

import CatalogEffect from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogEffect'
import GtagTrack from '@components/molecules/GDPR/GtagTrack'
import { getBrands } from '@/src/app/(store)/[...dynamic]/api'
import { getStaticContent } from '@/src/features/static/api'
import Container from '@atoms/Container'
import { HTML } from '@atoms/HTML'
import { SchemaOrg } from '@atoms/SchemaOrg'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import FeaturedBrandsWidget from '@components/molecules/Widget/widgets/FeaturedBrands/FeaturedBrandsWidget'
import { CatalogPageProps } from '@components/pages/Catalog/types'
import CategoriesList from '@features/catalog/CategoriesList'
import CategoryProductsList from '@features/catalog/CategoryProductsList'
import { CatalogTitle } from '@features/catalog/components/CatalogTitle'
import { CatalogLayout, ProductViewFragment, CatalogCategory } from '@lib/_generated/graphql_sdk'
import { PixelTrackViewCategory } from '@lib/fbp/components/PixelTrackViewCategory'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'

export async function CatalogPage({ data, breadcrumbs }: CatalogPageProps) {
  const isLandingPage = data.layout === CatalogLayout.LandingPage
  const category = data.category
  const { __typename: pageType } = category
  const staticContent = await getStaticContent()
  const brands = await getBrands({ featured: true })

  const getPageName = () => {
    if (pageType === 'Category') {
      return category.name
    }
    if (pageType === 'SearchCategory') {
      return category.name
    }
    if (pageType === 'SplashPage') {
      return category.title
    }
    return ''
  }

  const { products = [] } = data

  return (
    <>
      <Container>
        {!isLandingPage && (
          <SchemaOrg<CollectionPage>
            schema={{
              '@context': 'https://schema.org',
              '@type': 'CollectionPage',
              'name': getPageName(),
              'description':
                (pageType === 'Category'
                  ? category.description
                  : pageType === 'SplashPage' && category.__typename === 'SplashPage'
                    ? category.splashDescription
                    : null) || getPageName(),
              'url': `${staticContent.store.baseUrl}/${category.url}`,
              'mainEntity': products.map((product, i) => ({
                '@type': 'Product',
                'sku': product.sku,
                'name': product.name,
                'image': product.image?.src || '',
                'category': breadcrumbs.map((item) => item.label).join(', '),
                'offers': {
                  '@type': 'Offer',
                  'sku': product.sku,
                  'price': product.price?.price?.value || 0,
                  'priceCurrency': product.price?.price?.currency || 'BGN',
                  'availability': 'InStock',
                },
              })),
            }}
          />
        )}
        {data.state.filters.applied.length == 0 && <meta name="robots" content="INDEX,FOLLOW" />}
        <div className="my-5">
          <Breadcrumb
            breadcrumbs={breadcrumbs}
            banner={category.__typename === 'Category' ? category.banner : undefined}
            hasPagination={true}
            pagination={{
              pager: data.state.pager,
              sort: data.state.sort,
            }}
          />
        </div>

        {pageType === 'SplashPage' && category.__typename === 'SplashPage' && <CatalogTitle title={category.title} />}
        {pageType === 'Category' && <CatalogTitle title={category.name} />}
        {pageType === 'SearchCategory' && <CatalogTitle title={category.name} />}

        {isLandingPage ? (
          <>
            <GtagTrack
              action={{
                eventName: 'view_item_list',
                properties: {
                  item_list_id: category.__typename === 'Category' ? category.id : '',
                  item_list_name: getPageName(),
                  items: [],
                },
              }}
            />
            <CategoriesList widgets={category.__typename === 'Category' ? category.widgets : undefined} />
          </>
        ) : (
          <>
            <GtagTrack
              action={{
                eventName: 'view_item_list',
                properties: {
                  item_list_id: category.__typename === 'Category' ? category.id : '',
                  item_list_name: getPageName(),
                  ...(products[0]?.price?.price && {
                    currency: products[0].price.price.currency,
                  }),
                  items: products.map((product, index) => ({
                    item_id: product.sku,
                    item_name: product.name,
                    index,
                    item_category: getPageName(),
                    price: product.price?.price?.value,
                  })),
                },
              }}
            />
            <CategoryProductsList state={data.state} products={products as ProductViewFragment[]} />
          </>
        )}
      </Container>

      {category.__typename === 'Category' && category.description && (
        <div className="bg-white mt-10 mb-20 p-4 md:py-5 lg:py-10 flex flex-1">
          <div className="hidden lg:block lg:w-[280px] h-full" />
          <HTML className="flex-1 w-full overflow-hidden">{category.description}</HTML>
        </div>
      )}

      {category.__typename === 'SplashPage' && category.splashDescription && (
        <div className="bg-white mt-10 mb-20 p-4 md:py-5 lg:py-10 flex flex-1">
          <div className="hidden lg:block lg:w-[280px] h-full" />
          <HTML className="flex-1 w-full overflow-hidden">{category.splashDescription}</HTML>
        </div>
      )}

      <div>
        <FeaturedBrandsWidget brands={brands} />
      </div>

      <RelevaTrackPage
        pageType="category"
        productIds={products.map((p) => p.sku)}
        categories={breadcrumbs.map((b) => b.label)}
        filters={
          data.state.filters.applied.length > 0
            ? {
                operator: 'and',
                nested: data.state.filters.applied.map((filter) => ({
                  key: filter.attributeCode === 'price' ? 'price' : filter.label,
                  operator: 'eq',
                  value:
                    filter.attributeCode === 'price'
                      ? filter.value
                      : data.state.filters.available
                          .find((f) => f.requestVar === filter.requestVar)
                          ?.options.find((o) => o.value === filter.value)?.label,
                  action: 'include',
                })),
              }
            : undefined
        }
      />

      <CatalogEffect />
    </>
  )
}
