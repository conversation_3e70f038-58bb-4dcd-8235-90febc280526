'use client'

import React, { useState, useEffect, PropsWithChildren } from 'react'

export const App: React.FC<PropsWithChildren> = ({ children }) => {
  const [isSticky, setIsSticky] = useState(false)

  useEffect(() => {
    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const handleScroll = () => {
    console.log('document.documentElement.scrollTop:', document.documentElement.scrollTop)
    if (document.documentElement.scrollTop > 0) {
      setIsSticky(true)
    } else {
      setIsSticky(false)
    }
  }

  return <div className={isSticky ? 'sticky top-0' : ''}>{children}</div>
}

export default App
