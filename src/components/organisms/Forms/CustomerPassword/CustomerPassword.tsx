'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { LucideLoaderCircle } from 'lucide-react'
import React, { useCallback, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'

import { CustomerRegisteredGreeting } from '@/src/app/(store)/customer/account/(unauthenticated)/create/CustomerRegisteredGreeting'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { FormTextField } from '@components/molecules/FormControllers/FormTextField'
import { Button } from '@components/theme/ui/button'
import { Card, CardContent, CardTitle } from '@components/theme/ui/card'
import { useCustomerStore } from '@features/customer/customer.store'
import {
  customerPasswordSchema,
  CustomerPasswordSchema,
} from '@components/organisms/Forms/CustomerPassword/customerPassword.schema'
import { customerPasswordDefaults } from '@components/organisms/Forms/CustomerPassword/customerPassword.defaults'

export const CustomerPassword: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const updateCustomerPassword = useCustomerStore((state) => state.updateCustomerPassword)

  const form = useForm<CustomerPasswordSchema>({
    resolver: zodResolver(customerPasswordSchema),
    defaultValues: customerPasswordDefaults,
    disabled: loading,
  })

  const { watch, handleSubmit, setValue, getValues, reset } = form

  const onSubmit = useCallback(
    async (data: CustomerPasswordSchema) => {
      setLoading(true)
      const { currentPassword, newPassword } = data
      const response = await updateCustomerPassword(currentPassword, newPassword)
      setLoading(false)
      if (response) {
        reset()
      }
    },
    [reset, updateCustomerPassword]
  )

  const onError = useCallback((errors: any) => {
    console.log('Form validation failed', errors)
  }, [])

  return (
    <Card className="border-none shadow-none">
      <CardContent>
        <CardTitle className="pt-10 pb-4 flex flex-col items-center">
          <Text className={cn('leading-none pt-2 pb-6 text-2xl font-bold text-primary')}>Промяна на парола</Text>
        </CardTitle>
        <FormProvider {...form}>
          <form onSubmit={handleSubmit(onSubmit, onError)}>
            <div className="grid grid-cols-2 gap-4 xl:gap-y-7">
              <div className="col-span-2">
                <FormTextField type="password" name="currentPassword" label="Текуща Парола" className="lg:h-6" />
              </div>
              <div className="col-span-2">
                <FormTextField type="password" name="newPassword" label="Нова парола" className="lg:h-6" />
              </div>
              <div className="col-span-2">
                <FormTextField
                  type="password"
                  name="newPasswordConfirm"
                  label="Повторете новата парола"
                  className="lg:h-6"
                />
              </div>

              <div className="col-span-2 flex justify-center">
                <Button type="submit" disabled={loading}>
                  {loading && <LucideLoaderCircle className="mr-2 animate-spin" />}
                  Промяна на парола
                </Button>
              </div>
            </div>
          </form>
        </FormProvider>
      </CardContent>
    </Card>
  )
}
