import { z } from 'zod'

export const customerPasswordSchema = z
  .object({
    currentPassword: z.string().optional().default(''),
    newPassword: z.string().optional().default(''),
    newPasswordConfirm: z.string().optional().default(''),
  })
  .refine((data) => data.currentPassword && data.currentPassword.length > 0, {
    message: 'Въведете текущата си парола',
    path: ['currentPassword'],
  })
  .refine((data) => data.newPassword && data.newPassword.length > 0 && data.newPassword === data.newPasswordConfirm, {
    message: 'Паролите не съвпадат',
    path: ['newPasswordConfirm'],
  })

export type CustomerPasswordSchema = z.infer<typeof customerPasswordSchema>
