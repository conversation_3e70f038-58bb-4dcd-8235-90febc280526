import { z } from 'zod'

export const serviceRequestSchema = z.object({
  names: z
    .string({
      required_error: 'Името е задължително',
    })
    .min(5, { message: 'Името трябва да е поне 5 символа' }),

  email: z
    .string({
      required_error: 'Имейлът е задължителен',
    })
    .email({ message: 'Невалиден имейл адрес' }),

  phone: z
    .string({
      required_error: 'Телефонът е задължителен',
    })
    .min(4, { message: 'Телефонът трябва да е поне 4 цифри' }),

  type: z.enum(['Company', 'Personal'], {
    errorMap: () => ({ message: 'Изберете тип' }),
  }),

  message: z
    .string({
      required_error: 'Съобщението е задължително',
    })
    .min(1, { message: 'Моля въведете съобщение' }),
})

export type ServiceRequest = z.infer<typeof serviceRequestSchema>
