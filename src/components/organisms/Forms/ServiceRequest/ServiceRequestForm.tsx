'use client'
import { zod<PERSON><PERSON><PERSON><PERSON> } from '@hookform/resolvers/zod'
import { LucideLoaderCircle } from 'lucide-react'
import React, { useCallback, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'

import useRecaptcha from '@/src/hooks/useRecaptcha'
import { FormDropdown } from '@components/molecules/FormControllers/FormDropdown'
import { FormTextArea } from '@components/molecules/FormControllers/FormTextArea'
import { FormTextField } from '@components/molecules/FormControllers/FormTextField'
import { serviceRequestDefaults } from '@components/organisms/Forms/ServiceRequest/service-request.defaults'
import { ServiceRequest, serviceRequestSchema } from '@components/organisms/Forms/ServiceRequest/service.request'
import { Button } from '@components/theme/ui/button'
import { staticSelectors } from '@features/static/selectors'
import { StaticContentProps } from '@features/static/types'
import { GraphQLBackend } from '@lib/api/graphql'
import { showErrorToast, showSuccessToast } from '@lib/utils/toaster'
import { InquiryType } from '@/src/lib/_generated/graphql_sdk'

export const ServiceRequestForm: React.FC<StaticContentProps & { storeName: string }> = ({
  staticContent,
  storeName,
}) => {
  const [loading, setLoading] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)

  const { googleRecaptchaKey: siteKey } = staticSelectors.selectApiKeys(staticContent)
  const { executeRecaptcha } = useRecaptcha(siteKey)

  const form = useForm<ServiceRequest>({
    resolver: zodResolver(serviceRequestSchema),
    defaultValues: serviceRequestDefaults,
    disabled: loading,
  })

  const onSubmit = useCallback(
    async (data: ServiceRequest) => {
      try {
        setLoading(true)
        setSubmitError(null)

        const recaptchaToken = await executeRecaptcha('send_inquiry')

        const response = await GraphQLBackend.SendInquiry(
          {
            input: {
              name: data.names,
              store: storeName,
              type: data.type as InquiryType,
              email: data.email,
              message: data.message,
              phone: data.phone,
            },
          },
          { 'x-captcha-token': recaptchaToken }
        ).catch(console.log)
        if (!response) {
          throw new Error('Възникна грешка при изпращането.')
        }

        // Handle success
        setSubmitSuccess(true)
        showSuccessToast({
          title: 'Успешно изпратено',
          description: 'Вашето запитване е изпратено успешно.',
        })
        form.reset() // Reset form after successful submission
      } catch (error) {
        // Handle error
        showErrorToast({
          title: 'Грешка',
          description: error instanceof Error ? error.message : 'Възникна грешка при изпращането.',
        })
        setSubmitError(error instanceof Error ? error.message : 'Възникна грешка при изпращането.')
      } finally {
        setLoading(false)
      }
    },
    [executeRecaptcha, form, storeName]
  )

  const onError = useCallback(() => {}, [])

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit, onError)}>
        <div className="grid grid-cols-2 gap-4 xl:gap-y-7">
          <div className="col-span-2">
            <FormTextField name="names" label="Имена" />
          </div>
          <div className="col-span-2 xl:col-span-1">
            <FormTextField name="email" label="Имейл" />
          </div>
          <div className="col-span-2 xl:col-span-1">
            <FormTextField name="phone" label="Телефон" />
          </div>
          <div className="col-span-2">
            <FormDropdown
              name="type"
              label="Запитване за"
              options={[
                { value: 'Personal', label: 'Физическо лице' },
                { value: 'Company', label: 'Юридическо лице' },
              ]}
            />
          </div>
          <div className="col-span-2">
            <FormTextArea name="message" label="Коментар към поръчката" />
          </div>
          <div className="col-span-2">
            <Button type="submit" className="text-xs">
              {loading && <LucideLoaderCircle className="animate-spin" />}Изпратете запитване
            </Button>
          </div>
        </div>
      </form>
    </FormProvider>
  )
}
