'use client'

import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { LucideLoaderCircle, LucidePlus } from 'lucide-react'
import React, { useCallback, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { FormTextField } from '@components/molecules/FormControllers/FormTextField'
import { Button } from '@components/theme/ui/button'
import { Card, CardContent, CardTitle } from '@components/theme/ui/card'
import { showErrorToast } from '@lib/utils/toaster'
import { useCustomerStore } from '@features/customer/customer.store'
import { customerDataSchema, CustomerDataSchema } from '@components/organisms/Forms/CustomerData/customerData.schema'
import { customerDataDefaults } from '@components/organisms/Forms/CustomerData/customerData.defaults'
import { mapAddressToItem, mapCustomerDataFormToPayload } from '@components/organisms/Forms/CustomerData/mapper'
import { FormDropdown } from '@components/molecules/FormControllers/FormDropdown'
import { FormCheckbox } from '@components/molecules/FormControllers/FormCheckbox'
import { CustomerAddress } from '@lib/_generated/graphql_sdk'
import { ButtonIcon } from '@components/molecules/ButtonIcon'
import { useRouter } from 'next/navigation'

interface CustomerDataProps {
  data?: CustomerDataSchema
  addresses: CustomerAddress[]
}

export const CustomerData: React.FC<CustomerDataProps> = ({ data, addresses }) => {
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const updateCustomerData = useCustomerStore((state) => state.updateCustomerData)

  const form = useForm<CustomerDataSchema>({
    resolver: zodResolver(customerDataSchema),
    defaultValues: data || customerDataDefaults,
    disabled: loading,
  })

  const { handleSubmit } = form

  const onSubmit = useCallback(
    async (data: CustomerDataSchema) => {
      setLoading(true)
      try {
        await updateCustomerData(mapCustomerDataFormToPayload(data))
      } catch (error) {
        showErrorToast({
          title: 'Грешка',
          description: 'Промените не са запазили. Моля, опитайте отново.',
        })
      } finally {
        setLoading(false)
      }
    },
    [updateCustomerData]
  )

  const onError = useCallback((errors: any) => {
    console.log('Form validation failed', errors)
  }, [])

  return (
    <Card className={cn('shadow-none border-none')}>
      <CardContent>
        <CardTitle className="pt-10 pb-4 flex flex-col items-center">
          <Text className={cn('leading-none pt-2 pb-6 text-xl sm:text-2xl font-bold text-primary')}>
            Обновяване на профил
          </Text>
        </CardTitle>
        <FormProvider {...form}>
          <form onSubmit={handleSubmit(onSubmit, onError)}>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 xl:gap-y-7">
              <div className="sm:col-span-1">
                <FormTextField name="firstName" label="Име" />
              </div>
              <div className="sm:col-span-1">
                <FormTextField name="lastName" label="Фамилия" />
              </div>
              <div className="sm:col-span-2">
                <FormTextField name="email" label="Имейл" />
              </div>
              <div className="sm:col-span-1">
                <FormDropdown
                  options={addresses.map(mapAddressToItem)}
                  name="defaultShippingAddressID"
                  label="Адрес за доставка"
                  disabled={!addresses.length}
                  placeholder="Нямате добавени адреси"
                />
                <ButtonIcon
                  disabled={loading}
                  onClick={() => {
                    router.push('/customer/account/edit?t=address')
                  }}
                  type="button"
                  className="gap-4"
                  icon={<LucidePlus size={14} />}
                  iconClassName="p-0 w-6 h-6"
                  label="Добавяне на нов адрес"
                  direction="horizontal"
                  labelClassName="w-auto tracking-normal text-black"
                />
              </div>
              <div className="sm:col-span-1">
                <FormDropdown
                  options={addresses.map(mapAddressToItem)}
                  name="defaultBillingAddressID"
                  label="Адрес за фактуриране"
                  disabled={!addresses.length}
                  placeholder="Нямате добавени адреси"
                />
              </div>
              <div className="sm:col-span-2">
                <FormCheckbox name="newsletterSubscribe" label="Искам да получавам имейли" />
              </div>
              <div className="sm:col-span-2 flex justify-center">
                <Button type="submit" disabled={loading}>
                  {loading && <LucideLoaderCircle className="mr-2 animate-spin" />}
                  Запази
                </Button>
              </div>
            </div>
          </form>
        </FormProvider>
      </CardContent>
    </Card>
  )
}
