import { CustomerAddress, CustomerDataFullFragment, CustomerUpdateInput } from '@lib/_generated/graphql_sdk'
import { CustomerDataSchema } from '@components/organisms/Forms/CustomerData/customerData.schema'

export const mapCustomerDataFormToPayload = (data: CustomerDataSchema): CustomerUpdateInput => ({
  defaultBillingAddressID: data.defaultBillingAddressID,
  defaultShippingAddressID: data.defaultShippingAddressID,
  email: data.email,
  firstName: data.firstName,
  lastName: data.lastName,
  newsletterSubscribe: data.newsletterSubscribe,
})

export const mapCustomerDataPayloadToForm = (data: CustomerDataFullFragment): CustomerDataSchema => ({
  firstName: data.firstName,
  lastName: data.lastName,
  email: data.email,
  newsletterSubscribe: data.isSubscribed,
  defaultBillingAddressID: data.defaultBillingAddressID ? String(data.defaultBillingAddressID) : null,
  defaultShippingAddressID: data.defaultShippingAddressID ? String(data.defaultShippingAddressID) : null,
})

export const mapAddressToItem = (item: CustomerAddress) => ({
  value: String(item.id),
  label: `[${item.postCode}] ${item.city}, ${item.street} (${item.firstName} ${item.lastName})`,
})
