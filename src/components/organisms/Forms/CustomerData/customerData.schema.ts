import { z } from 'zod'

export const customerDataSchema = z.object({
  firstName: z.string().min(1, 'Името е задължително'),
  lastName: z.string().min(1, 'Фамилията е задължителна'),
  email: z.string().min(1, { message: 'Email е задължителен' }).email({ message: 'Невалиден email' }),
  newsletterSubscribe: z.boolean().default(false),
  defaultBillingAddressID: z.string().nullable().default(null),
  defaultShippingAddressID: z.string().nullable().default(null),
})

export type CustomerDataSchema = z.infer<typeof customerDataSchema>
