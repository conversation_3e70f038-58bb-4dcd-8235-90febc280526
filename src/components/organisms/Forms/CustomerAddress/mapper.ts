import { CustomerAddressSchema } from '@components/organisms/Forms/CustomerAddress/customerAddress.schema'
import { CustomerAddressFullFragment, CustomerAddressInput } from '@lib/_generated/graphql_sdk'

export const mapAddressPayloadToForm = ({
  data,
  defaultBilling,
  defaultShipping,
}: {
  data: CustomerAddressFullFragment
  defaultBilling?: boolean
  defaultShipping?: boolean
}): CustomerAddressSchema => {
  return {
    id: String(data.id),
    firstName: data?.firstName || '',
    lastName: data?.lastName || '',
    companyName: data?.companyName || '',
    phone: data?.phone || '',
    city: data?.city || '',
    cityID: data?.cityID ? Number(data.cityID) : null,
    postCode: data?.postCode || '',
    street: data?.street || '',
    country: 'BG',
    isDefaultBilling: !!defaultBilling,
    isDefaultShipping: !!defaultShipping,
  }
}

export const mapAddressFormToPayload = ({
  data,
  defaultBilling,
  defaultShipping,
}: {
  data: CustomerAddressFullFragment
  defaultBilling?: boolean
  defaultShipping?: boolean
}): CustomerAddressInput => {
  return {
    firstName: data?.firstName || '',
    lastName: data?.lastName || '',
    phone: data?.phone || '',
    companyName: data?.companyName || '',
    city: data?.city || '',
    cityID: data?.cityID ? String(data.cityID) : '',
    postCode: data?.postCode || '',
    street: data?.street || '',
    isDefaultBilling: !!defaultBilling,
    isDefaultShipping: !!defaultShipping,
  }
}
