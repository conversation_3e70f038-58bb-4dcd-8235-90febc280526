'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { LucideCheck, LucideCheckCircle, LucideCheckSquare, LucideLoaderCircle } from 'lucide-react'
import React, { useCallback, useEffect, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'

import { CustomerRegisteredGreeting } from '@/src/app/(store)/customer/account/(unauthenticated)/create/CustomerRegisteredGreeting'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { FormTextField } from '@components/molecules/FormControllers/FormTextField'
import { Button } from '@components/theme/ui/button'
import { Card, CardContent, CardTitle } from '@components/theme/ui/card'
import {
  CustomerAddressSchema,
  customerAddressSchema,
} from '@components/organisms/Forms/CustomerAddress/customerAddress.schema'
import { FormCheckbox } from '@components/molecules/FormControllers/FormCheckbox'
import { useCustomerStore } from '@features/customer/customer.store'
import { customerAddressDefaults } from '@components/organisms/Forms/CustomerAddress/customerAddress.defaults'
import AppLink from '@atoms/AppLink'
import { FormComboBox } from '@components/molecules/FormControllers/FormComboBox'
import { EcontCity } from '@features/checkout/service-providers/econt/types'
import { useEcontStore } from '@features/checkout/checkout.state'
import { CustomerAddressInput, ShippingInput, ShippingMethodType } from '@lib/_generated/graphql_sdk'
import { useRouter } from 'next/navigation'

interface CustomerAddressProps {
  mode: 'create' | 'update'
  data?: CustomerAddressSchema
}

export const CustomerAddress: React.FC<CustomerAddressProps> = ({ mode, data }) => {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const createCustomerAddress = useCustomerStore((state) => state.createCustomerAddress)
  const updateCustomerAddress = useCustomerStore((state) => state.updateCustomerAddress)

  const cities = useEcontStore((state) => state.cities)
  const { list: cityList } = cities
  const getCities = useEcontStore((state) => state.getCities)

  useEffect(() => {
    if (!cityList.length) {
      getCities({ countryCode: 'BGR' })
    }
  }, [cityList.length, getCities])

  const signUp = useCustomerStore((state) => state.signUp)

  const form = useForm<CustomerAddressSchema>({
    resolver: zodResolver(customerAddressSchema),
    defaultValues: data || customerAddressDefaults(),
    disabled: loading,
  })

  const { handleSubmit, watch } = form

  const city_id = watch('cityID')
  useEffect(() => {
    if (city_id) {
      const city = cityList.find((item) => item.id === city_id)
      if (city) {
        form.setValue('postCode', city.postCode)
        form.setValue('country', city.country.name)
        form.setValue('city', city.name)
      }
    }
  }, [cityList, city_id, form])

  const onSubmit = useCallback(
    async (data: CustomerAddressSchema) => {
      setLoading(true)
      try {
        const address: CustomerAddressInput = {
          city: cityList.find((item) => item.id === data.cityID)?.name || '',
          cityID: String(data.cityID),
          companyName: data.companyName,
          firstName: data.firstName,
          isDefaultBilling: data.isDefaultBilling,
          isDefaultShipping: data.isDefaultShipping,
          lastName: data.lastName,
          phone: data.phone,
          postCode: data.postCode,
          street: data.street,
        }

        if (mode === 'create') {
          await createCustomerAddress(address)
          router.push('/customer/account/edit?t=address')
        } else if (mode === 'update' && data.id) {
          const { id } = data
          await updateCustomerAddress(address, id)
        }
      } catch (error) {
        console.log('Error', error)
      } finally {
        setLoading(false)
      }
    },
    [cityList, createCustomerAddress, mode, router, updateCustomerAddress]
  )

  const onError = useCallback((errors: any) => {
    console.log('Form validation failed', errors)
  }, [])

  return (
    <Card className="border-none shadow-none">
      <CardContent>
        <CardTitle className="pt-10 pb-4 flex flex-col items-center">
          <Text className={cn('leading-none pt-2 pb-6 text-2xl font-bold text-primary')}>
            {mode === 'create' ? 'Добавяне на адрес' : 'Обновяване на адрес'}
          </Text>
        </CardTitle>
        <FormProvider {...form}>
          <form
            onSubmit={(e) => {
              console.log('Raw form submit event')
              handleSubmit(onSubmit, onError)(e)
            }}
          >
            <div className="flex flex-col xl:flex-row gap-4 xl:gap-y-7">
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 xl:gap-y-7 h-fit flex-1">
                <div className="xl:col-span-2 text-center">
                  <Text className={cn('leading-none pt-2 pb-6 text-lg font-bold text-primary')}>Данни за контакти</Text>
                </div>
                <div className="col-span-1">
                  <FormTextField name="firstName" label="Име" required />
                </div>
                <div className="col-span-1">
                  <FormTextField name="lastName" label="Фамилия" required />
                </div>
                <div className="xl:col-span-2">
                  <FormTextField name="companyName" label="Фирма" />
                </div>
                <div className="col-span-1">
                  <FormTextField name="phone" label="Телефон" required />
                </div>
              </div>
              <div className="w-px bg-gray-200 mx-5" />
              <div className="grid grid-c]ols-1 xl:grid-cols-2 gap-4 xl:gap-y-7 h-fit flex-1">
                <div className="xl:col-span-2 text-center">
                  <Text className={cn('leading-none pt-2 pb-6 text-lg font-bold text-primary')}>Адрес</Text>
                </div>
                <div className="xl:col-span-2">
                  <FormTextField name="street" label="Адрес" required />
                </div>
                <div className="col-span-1">
                  <FormComboBox<EcontCity>
                    name="cityID"
                    label="Град/Село"
                    data={cityList}
                    minLetters={2}
                    labelProp="name"
                    valueProp="id"
                    placeholder="Изберете град"
                    renderLabel={(item) => `[${item.postCode}] ${item.name}`}
                    filterFn={(item, keyword) => {
                      return `${item.postCode} ${item.name} ${item.nameEn}`
                        .toLowerCase()
                        .includes(keyword.toLowerCase())
                    }}
                    loading={cities.loading}
                    loadingLabel="Зареждане на градове..."
                  />
                </div>
                <div className="col-span-1">
                  <FormTextField name="postCode" label="Пощенски код" required />
                </div>
                <div className="xl:col-span-2">
                  <FormTextField name="country" label="Държава" required />
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-5 mt-5">
              <div className="flex gap-3 items-center">
                {!data?.isDefaultBilling ? (
                  <FormCheckbox name="isDefaultBilling" label="Адрес за фактуриране по подразбиране" />
                ) : (
                  <>
                    <LucideCheck className="text-primary" />
                    <Text className="text-primary font-bold">Адрес за фактуриране</Text>
                  </>
                )}

                {!data?.isDefaultShipping ? (
                  <FormCheckbox name="isDefaultShipping" label="Адрес за доставка по подразбиране" />
                ) : (
                  <>
                    <LucideCheck className="text-primary" />
                    <Text className="text-primary font-bold">Адрес за доставка</Text>
                  </>
                )}
              </div>
              <div className="flex justify-center">
                <Button type="submit" disabled={loading}>
                  {loading && <LucideLoaderCircle className="mr-2 animate-spin" />}
                  {mode === 'create' ? 'Добави' : 'Запази'}
                </Button>
              </div>
            </div>
          </form>
        </FormProvider>
      </CardContent>
    </Card>
  )
}
