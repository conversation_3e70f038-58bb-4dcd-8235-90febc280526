import { z } from 'zod'

export const customerAddressSchema = z
  .object({
    id: z.string().nullable().default(null),
    firstName: z.string().min(1, 'Името е задължително'),
    lastName: z.string().min(1, 'Фамилията е задължителна'),
    phone: z.string().min(1, { message: 'Телефон е задължителен' }),
    companyName: z.string().optional().default(''),
    city: z.string().min(1, 'Градът е задължителен'),
    cityID: z.number().nullable(),
    postCode: z.string().min(1, 'Пощенският код е задължителен'),
    street: z.string().min(1, 'Адресът е задължителен'),
    country: z.string().min(1, 'Държавата е задължителен'),
    isDefaultBilling: z.boolean().default(false),
    isDefaultShipping: z.boolean().default(false),
  })
  .refine((data) => data.cityID !== null, {
    message: 'Моля, изберете град',
    path: ['city_id'],
  })

export type CustomerAddressSchema = z.infer<typeof customerAddressSchema>
