import { z } from 'zod'

export const customerSignupSchema = z
  .object({
    name: z.string().min(1, 'Името е задължително'),
    family: z.string().min(1, 'Фамилията е задължителна'),
    email: z.string().min(1, { message: 'Email е задължителен' }).email({ message: 'Невалиден email' }),
    password: z.string().min(1, 'Паролата е задължителна'),
    passwordConfirm: z.string().min(1, 'Потвърди паролата'),
    phone: z.string().min(1, 'Телефонът е задължителен'),
    isCompany: z.boolean().default(false),
    agreement: z.boolean().refine((val) => val === true, {
      message: 'Трябва да се съгласите с условията',
    }),
    subscribe: z.boolean().default(false),
    // Company fields
    company: z.string().optional().default(''),
    mol: z.string().optional().default(''),
    eik: z.string().optional().default(''),
    vatRegistered: z.boolean().optional().default(false),
    vat: z.string().optional().default(''),
    address: z.string().optional().default(''),
    city: z.string().optional().default(''),
  })
  .refine((data) => !data.isCompany || (data.company && data.company.length > 0), {
    message: 'Името на фирмата е задължително',
    path: ['company'],
  })
  .refine((data) => !data.isCompany || (data.mol && data.mol.length > 0), {
    message: 'МОЛ е задължително',
    path: ['mol'],
  })
  .refine((data) => !data.isCompany || (data.eik && data.eik.length > 0), {
    message: 'ЕИК/Булстат е задължително',
    path: ['eik'],
  })
  .refine((data) => !data.isCompany || (data.address && data.address.length > 0), {
    message: 'Адресът за фактуриране е задължителен',
    path: ['address'],
  })
  .refine((data) => !data.isCompany || (data.city && data.city.length > 0), {
    message: 'Градът за фактуриране е задължителен',
    path: ['city'],
  })
  .refine((data) => !data.vatRegistered || (data.vat && data.vat.length > 0), {
    message: 'Номерът по ДДС е задължителен, когато сте регистриран по ЗДДС',
    path: ['vat'],
  })
  .refine((data) => data.password === data.passwordConfirm, {
    message: 'Паролите не съвпадат',
    path: ['passwordConfirm'],
  })

export type CustomerSignupSchema = z.infer<typeof customerSignupSchema>
