'use client'
import { zodResolver } from '@hookform/resolvers/zod'
import { LucideLoaderCircle } from 'lucide-react'
import React, { useCallback, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'

import { CustomerRegisteredGreeting } from '@/src/app/(store)/customer/account/(unauthenticated)/create/CustomerRegisteredGreeting'
import useRecaptcha from '@/src/hooks/useRecaptcha'
import AppLink from '@atoms/AppLink'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { FormCheckbox } from '@components/molecules/FormControllers/FormCheckbox'
import { FormTextField } from '@components/molecules/FormControllers/FormTextField'
import { customerSignupDefaults } from '@components/organisms/Forms/CustomerSignup/customerSignup.defaults'
import {
  customerSignupSchema,
  CustomerSignupSchema,
} from '@components/organisms/Forms/CustomerSignup/customerSignup.schema'
import { Button } from '@components/theme/ui/button'
import { Card, CardContent, CardTitle } from '@components/theme/ui/card'
import { useCustomerStore } from '@features/customer/customer.store'
import { staticSelectors } from '@features/static/selectors'
import { StaticContentProps } from '@features/static/types'
import { CustomerRegistrationData, InvoiceType } from '@lib/_generated/graphql_sdk'
import { showErrorToast } from '@lib/utils/toaster'
import Link from 'next/link'
import { gtagTrack } from '@/src/components/molecules/GDPR/GtagTrack'
import { trackCompleteRegistration } from '@lib/fbp/faceBookPixelHelper'

export const CustomerSignup: React.FC<StaticContentProps> = ({ staticContent }) => {
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)

  const signUp = useCustomerStore((state) => state.signUp)

  const { googleRecaptchaKey: siteKey } = staticSelectors.selectApiKeys(staticContent)
  const { executeRecaptcha } = useRecaptcha(siteKey)

  const form = useForm<CustomerSignupSchema>({
    resolver: zodResolver(customerSignupSchema),
    defaultValues: customerSignupDefaults,
    disabled: loading,
  })

  const { watch, handleSubmit, setValue, getValues } = form
  const isCompany = watch('isCompany') // Watch the isCompany field value
  const eikValue = watch('eik')
  const vatRegistered = watch('vatRegistered')

  const vatRegisteredRef = React.useRef(vatRegistered)

  React.useEffect(() => {
    if (vatRegistered && !vatRegisteredRef.current && eikValue) {
      const currentVat = getValues('vat')
      if (!currentVat) {
        setValue('vat', `BG${eikValue}`)
      }
    }
    vatRegisteredRef.current = vatRegistered
  }, [vatRegistered, eikValue, setValue, getValues])

  const onSubmit = useCallback(
    async (data: CustomerSignupSchema) => {
      setLoading(true)
      try {
        const recaptchaToken = await executeRecaptcha('customer_signup')
        const signUpData: CustomerRegistrationData = {
          email: data.email,
          firstname: data.name,
          lastname: data.family,
          password: data.password,
          phone: data.phone,
          invoice: data.isCompany
            ? {
                address: data.address,
                city: data.city,
                company: {
                  eik: data.isCompany ? data.eik : '',
                  mol: data.isCompany ? data.mol : '',
                  name: data.isCompany ? data.company : '',
                  vat: data.isCompany ? data.vat : '',
                },
                individual: {
                  egn: '',
                  name: '',
                  vat: '',
                },
                type: data.isCompany ? InvoiceType.Company : InvoiceType.Personal,
              }
            : undefined,
          newsletterSubscribe: data.subscribe,
        }
        const signUpResponse = await signUp(signUpData, recaptchaToken)
        if (signUpResponse) {
          setSuccess(true)
          trackCompleteRegistration({
            status: true,
          })
          gtagTrack({
            eventName: 'sign_up',
            properties: {
              method: 'Form',
            },
          })
        }
      } catch (error) {
        showErrorToast({
          title: 'Грешка',
          description: 'Възникна грешка при регистрацията. Моля, опитайте отново.',
        })
      } finally {
        setLoading(false)
      }
    },
    [executeRecaptcha, signUp]
  )

  const onError = useCallback((errors: any) => {
    console.log('Form validation failed', errors)
  }, [])

  return (
    <Card
      className={cn('rounded-3xl w-full lg:w-[600px] flex items-center justify-center', { 'aspect-square': success })}
    >
      <CardContent>
        {!success ? (
          <>
            <CardTitle className="pt-10 pb-4 flex flex-col items-center">
              <Text className={cn('leading-none pt-2 pb-6 text-2xl font-bold text-primary')}>Регистрация</Text>
            </CardTitle>
            <FormProvider {...form}>
              <form onSubmit={handleSubmit(onSubmit, onError)}>
                <div className="grid grid-cols-2 gap-4 xl:gap-y-7">
                  <div className="col-span-2 md:col-span-1">
                    <FormTextField name="name" label="Име" />
                  </div>
                  <div className="col-span-2 md:col-span-1">
                    <FormTextField name="family" label="Фамилия" />
                  </div>
                  <div className="col-span-2 md:col-span-1">
                    <FormTextField name="email" label="Имейл" />
                  </div>
                  <div className="col-span-2 md:col-span-1">
                    <FormTextField name="phone" label="Телефон" />
                  </div>
                  <div className="col-span-2 md:col-span-1">
                    <FormTextField type="password" name="password" label="Парола" className="lg:h-6" />
                  </div>
                  <div className="col-span-2 md:col-span-1">
                    <FormTextField type="password" name="passwordConfirm" label="Потвърди парола" />
                  </div>
                  <div className="col-span-2">
                    <FormCheckbox name="isCompany" label="Покажи данни за фирма" />
                  </div>
                  {isCompany && (
                    <>
                      <div className="col-span-2">
                        <FormTextField name="company" label="Фирма" />
                      </div>
                      <div className="col-span-2">
                        <FormTextField name="mol" label="Мол" />
                      </div>
                      <div className="col-span-2">
                        <FormTextField name="eik" label="ЕИК/Булстат" />
                      </div>
                      <div className="col-span-2">
                        <FormTextField name="city" label="Град / Село" />
                      </div>
                      <div className="col-span-2">
                        <FormTextField name="address" label="Адрес" />
                      </div>
                      <div className="col-span-2">
                        <FormCheckbox name="vatRegistered" label="Регистриран по ЗДДС" />
                      </div>
                      {watch('vatRegistered') && (
                        <div className="col-span-2">
                          <FormTextField name="vat" label="ДДС номер" />
                        </div>
                      )}
                    </>
                  )}
                  <div className="col-span-2">
                    <FormCheckbox name="subscribe" label="Искам да получавам имейли" />
                  </div>
                  <div className="col-span-2">
                    <FormCheckbox
                      name="agreement"
                      label={
                        <span>
                          Запознах се и приемам{' '}
                          <AppLink href="/obshti-uslovia" className="text-primary decoration-primary underline">
                            общите условия
                          </AppLink>
                        </span>
                      }
                    />
                  </div>
                  <div className="col-span-2 flex justify-center">
                    <Button type="submit" disabled={loading}>
                      {loading && <LucideLoaderCircle className="mr-2 animate-spin" />}
                      Регистрация
                    </Button>
                    <Link href="/customer/account/login">
                      <Button type="button" variant="link" disabled={loading}>
                        Вход
                      </Button>
                    </Link>
                  </div>
                </div>
              </form>
            </FormProvider>
          </>
        ) : (
          <CustomerRegisteredGreeting />
        )}
      </CardContent>
    </Card>
  )
}
