import { UseFormReturn } from 'react-hook-form'

import { CustomerSignupSchema } from '@components/organisms/Forms/CustomerSignup/customerSignup.schema'
import { Button } from '@components/theme/ui/button'

function makeid(length: number, n = false) {
  let result = ''
  const characters = n ? '0123456789' : 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz '
  const charactersLength = characters.length
  let counter = 0
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
    counter += 1
  }
  return result
}

export const Prefiller = ({ form }: { form: UseFormReturn<CustomerSignupSchema, any, undefined> }) => {
  const prefill = () => {
    const p = makeid(Math.floor(Math.random() * 10) + 6)
    form.reset({
      name: makeid(Math.floor(Math.random() * 10) + 5),
      family: makeid(Math.floor(Math.random() * 10) + 5),
      email: `${makeid(Math.floor(Math.random() * 10) + 5)}@gmail.com`,
      password: p,
      passwordConfirm: p,
      phone: makeid(Math.floor(Math.random() * 8) + 8, true),
      agreement: false,
    })
  }
  return (
    <Button variant="link" onClick={prefill}>
      Prefill
    </Button>
  )
}
