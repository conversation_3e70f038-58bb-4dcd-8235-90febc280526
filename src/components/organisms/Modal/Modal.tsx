import * as Dialog from '@radix-ui/react-dialog'
import React, { PropsWithChildren } from 'react'

import { cn } from '@components/lib/utils'
import { ClassName } from '@lib/types/ClassName'

import { Button } from '../../theme/ui/button'

interface ModalControlledProps {
  open: boolean
  onClose: () => void
  onOpenChange?: () => void
}

interface ModalProps {
  title: React.ReactNode
  description?: React.ReactNode
  closeText?: string
  controlled?: ModalControlledProps
  className?: ClassName
  backdrop?: boolean
}
export const Modal: React.FC<PropsWithChildren<ModalProps>> = ({
  children,
  title,
  description,
  closeText,
  controlled,
  className,
  backdrop,
}) => {
  const selfControl = !!controlled
  return (
    <Dialog.Root
      open={selfControl ? controlled?.open : undefined}
      onOpenChange={selfControl ? controlled?.onOpenChange : undefined}
      modal={true}
    >
      {!selfControl && (
        <Dialog.Trigger asChild>
          <Button>Open</Button>
        </Dialog.Trigger>
      )}
      <Dialog.Portal>
        <Dialog.Overlay
          className="fixed inset-0 bg-black bg-opacity-70 z-90"
          onClick={selfControl && backdrop ? controlled?.onClose : undefined}
        />
        <div className="fixed inset-0 z-100 flex justify-center w-full items-center p-1 sm:p-10">
          <Dialog.Content className={cn('bg-white rounded-2xl shadow-2xl w-full', className)}>
            <Dialog.Title className="p-4 font-semibold text-lg border-b">{title}</Dialog.Title>
            <Dialog.Description className="p-4 pb-0 font-semibold text-sm leading-tight">
              {description}
            </Dialog.Description>
            <div className={cn('p-1 sm:p-4 max-w-screen-xl')}>{children}</div>
            <div className="p-4 border-t flex justify-end">
              {closeText && (
                <Dialog.Close asChild>
                  <Button onClick={selfControl ? controlled?.onClose : undefined}>{closeText}</Button>
                </Dialog.Close>
              )}
            </div>
          </Dialog.Content>
        </div>
      </Dialog.Portal>
    </Dialog.Root>
  )
}
