import * as Dialog from '@radix-ui/react-dialog'
import React from 'react'

import CloseIcon from '@atoms/Icons/CloseIcon'

interface Props {
  title?: string
  children: React.ReactNode
  trigger: React.ReactNode
  image?: string
  profileImage?: boolean
}

const Store_Modal: React.FC<Props> = ({ title, children, trigger, image, profileImage = false }) => {
  return (
    <Dialog.Root>
      <Dialog.Trigger asChild>{trigger}</Dialog.Trigger>
      <Dialog.Portal>
        <Dialog.Overlay />
        <Dialog.Content>
          <div className="fixed inset-0 bg-black bg-opacity-70 z-100 flex justify-center items-center p-4">
            <div onClick={(e) => e.stopPropagation()} className="w-full flex justify-center items-center">
              <div className={`bg-white rounded-3xl shadow-lg max-w-[900px] w-full`}>
                <div className="flex">
                  {image && (
                    <div
                      className={
                        profileImage
                          ? `desktop:min-w-[250px] desktop:h-[250px] rounded-full flex bg-top bg-cover desktop:m-10`
                          : `w-[50%] rounded-3xl bg-cover bg-center`
                      }
                      style={{
                        backgroundImage: `url(${image})`,
                      }}
                    ></div>
                  )}
                  <div className="flex col w-full p-12 min-h-screen	overflow-auto">
                    <div className="flex justify-between items-center">
                      <h2 className="text-3xl font-semibold mt-4">{title && title}</h2>

                      <Dialog.Close className="bg-gray-100 p-4 rounded-full cursor-pointer hover:bg-gray-200">
                        <CloseIcon stroke="#EE7F00" />
                      </Dialog.Close>
                    </div>
                    <div className="mt-4">{children}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}

export default Store_Modal
