'use client'

import { But<PERSON> } from '@components/theme/ui/button'
import { LucideLoaderCircle } from 'lucide-react'
import Text from '@atoms/Text'
import React, { useState } from 'react'
import { useAsyncRoutePush } from '@/src/hooks/useAsyncRoutePush'

export const GoBackButton = () => {
  const [loading, setLoading] = useState(false)
  const push = useAsyncRoutePush()
  return (
    <Button
      variant="inverse"
      className="text-white"
      onClick={() => {
        setLoading(true)
        push('/cart').finally(() => {
          setLoading(false)
        })
      }}
    >
      {loading && <LucideLoaderCircle className="animate-spin" />}
      <Text className="tracking-tight sm:tracking-wides text-xs sm:text-base">Обратно към сайта</Text>
    </Button>
  )
}
