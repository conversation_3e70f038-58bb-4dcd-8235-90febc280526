import React from 'react'

import Container from '@atoms/Container'
import { Header<PERSON><PERSON> } from '@components/organisms/Header/components/HeaderLogo'
import CustomerAreaServer from '@components/organisms/Header/components/CustomerAreaServer'
import { GoBackButton } from '@components/organisms/CheckoutHeader/components/GoBackButton'

export const CheckoutHeader = () => {
  return (
    <header className="sticky top-0 isolate z-60">
      <CustomerAreaServer />
      <div className="bg-background-dark text-white">
        <Container className="flex justify-between items-center min-h-[80px]">
          <HeaderLogo />
          <GoBackButton />
        </Container>
      </div>
    </header>
  )
}
