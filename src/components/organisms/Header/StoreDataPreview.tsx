import React from 'react'

import { staticSelectors } from '@/src/features/static/selectors'
import { StaticContentProps } from '@/src/features/static/types'
import PhoneIcon from '@atoms/Icons/PhoneIcon'
import Text from '@atoms/Text'
import { Tooltip, TooltipContent, TooltipTrigger } from '@components/theme/ui/tooltip'
import { addClasses } from '@lib/style/style'
import { ThemeStyles } from '@lib/style/theme'
import { CMSBlock } from '../../molecules/Widget/markup/instanced'
import { trackContact } from '@lib/fbp/faceBookPixelHelper'

const StoreDataPreview: React.FC<ThemeStyles & StaticContentProps> = ({ staticContent, className }) => {
  const contacts = staticSelectors.selectStoreContacts(staticContent)
  return (
    <Tooltip>
      <TooltipTrigger>
        <div className={addClasses('flex gap-2 items-center', className)}>
          <PhoneIcon size={26} />
          <div className="flex flex-col">
            <Text className="text-paragraph-muted text-sm">Поръчай по телефона:</Text>
            <a href={`tel:${contacts?.general?.phone}`}>
              <Text className="text-primary-foreground font-bold text-xl">
                {contacts?.general?.phone || 'Не е посочен'}
              </Text>
            </a>
          </div>
        </div>
      </TooltipTrigger>
      <TooltipContent side="top" sideOffset={5} className="z-60 bg-white text-black shadow-lg py-3 px-4">
        <CMSBlock id="store_workhours" />
      </TooltipContent>
    </Tooltip>
  )
}

export default StoreDataPreview
