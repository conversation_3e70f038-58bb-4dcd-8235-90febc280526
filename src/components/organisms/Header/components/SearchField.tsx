'use client'

import { LucideCircleAlert, LucideCircleDashed, LucideMoveRight, LucideSearch } from 'lucide-react'
import { useRouter } from 'next/navigation'
import React, { useState, useRef, useEffect, useCallback, RefObject } from 'react'
import { useOnClickOutside, useDebounceValue } from 'usehooks-ts'

import { gtagTrack } from '@components/molecules/GDPR/GtagTrack'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Card } from '@components/theme/ui/card'
import { Input } from '@components/theme/ui/input'
import { Skeleton } from '@components/theme/ui/skeleton'
import {
  Product,
  ProductViewFragment,
  SearchPageResultsFragment,
  SearchResults,
  SimpleProduct,
} from '@lib/_generated/graphql_sdk'
import { GraphQLBackend } from '@lib/api/graphql'
import { trackSearch } from '@lib/fbp/faceBookPixelHelper'

export function SkeletonElement() {
  return (
    <div className="flex items-center gap-3">
      <Skeleton className="h-[50px] w-[50px] rounded-xl bg-gray-200" />
      <div className="flex flex-col gap-3 flex-1">
        <Skeleton className="h-4 w-full bg-gray-200" />
        <Skeleton className="h-4 w-8/12 bg-gray-200" />
      </div>
    </div>
  )
}

const ResultItem = ({ active, product, onClick }: { active: boolean; product: Product; onClick: () => void }) => {
  const ref = useRef<HTMLLIElement>(null)

  useEffect(() => {
    if (active && ref.current) {
      const listItem = ref.current
      const parentList = listItem.parentElement

      if (parentList) {
        const parentRect = parentList.getBoundingClientRect()
        const elementRect = listItem.getBoundingClientRect()

        const isAbove = elementRect.top < parentRect.top
        const isBelow = elementRect.bottom > parentRect.bottom

        if (isAbove) {
          parentList.scrollTop = parentList.scrollTop + (elementRect.top - parentRect.top) - 10
        } else if (isBelow) {
          parentList.scrollTop = parentList.scrollTop + (elementRect.bottom - parentRect.bottom) + 10
        }
      }
    }
  }, [active])

  return (
    <li
      ref={ref}
      id={`result-item-${product.sku}`}
      role="option"
      aria-selected={active}
      className={cn(
        'flex mx-2 gap-4 p-3 rounded-lg transition-all cursor-pointer',
        active ? 'bg-primary/20 border-l-4 border-primary' : 'bg-white hover:bg-gray-100'
      )}
      onClick={onClick}
    >
      <div className="flex-shrink-0 w-[80px] h-[80px] relative">
        <Img
          fill
          mobileSrc={product.image.mobileSrc || product.image.src}
          src={product.image.src}
          alt="product title"
          className="rounded-xl border border-gray-200 object-cover"
        />
      </div>
      <div className="flex flex-col flex-1 justify-between py-1">
        <div className="font-medium text-gray-900 line-clamp-2 text-sm">{product.name}</div>
        <div className="text-xs flex items-center gap-1">
          <div>Прод код:</div>
          <div className="bg-primary text-white px-1.5 py-1 font-bold">{product.sku}</div>
        </div>
      </div>
    </li>
  )
}

export const SearchField = () => {
  const [query, setQuery] = useState('')
  const [isFocused, setIsFocused] = useState(false)
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<SearchPageResultsFragment>({
    popularTerms: [],
    totalItems: 0,
    categories: [],
    products: [],
  })
  const [activeIndex, setActiveIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const [debouncedQuery] = useDebounceValue(query, 400)

  useEffect(() => {
    if (debouncedQuery.trim().length >= 3) {
      setActiveIndex(-1)
      setLoading(true)
      GraphQLBackend.SearchField({ text: debouncedQuery })
        .then((response) => {
          setResult({
            popularTerms: response.search.popularTerms,
            totalItems: response.search.totalItems,
            categories: response.search.categories,
            products: response.search.products,
          })
        })
        .catch(() =>
          setResult({
            popularTerms: [],
            totalItems: 0,
            categories: [],
            products: [],
          })
        )
        .finally(() => {
          setLoading(false)
        })
    } else {
      setResult({
        popularTerms: [],
        totalItems: 0,
        categories: [],
        products: [],
      })
    }
  }, [debouncedQuery])

  const handleInputFocus = useCallback(() => {
    setIsFocused(true)
  }, [])

  useEffect(() => {
    const inputElement = inputRef.current
    inputElement?.addEventListener('focus', handleInputFocus)

    return () => {
      inputElement?.removeEventListener('focus', handleInputFocus)
    }
  }, [handleInputFocus])

  useEffect(() => {
    if (!isFocused) {
      inputRef.current?.blur()
    }
  }, [isFocused])

  const handleClickOutside = useCallback(() => {
    setIsFocused(false)
    setActiveIndex(-1)
  }, [])

  useOnClickOutside(containerRef as RefObject<HTMLDivElement>, handleClickOutside)

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault()
      setActiveIndex((prev) => Math.min(prev + 1, result.products.length - 1))
    } else if (e.key === 'ArrowUp') {
      e.preventDefault()
      setActiveIndex((prev) => Math.max(prev - 1, 0))
    } else if (e.key === 'Enter') {
      if (activeIndex >= 0 && activeIndex < result.products.length) {
        const selectedProduct = result.products[activeIndex]
        // Track Search event when user selects a product with Enter key
        trackSearch({
          content_ids: [String(selectedProduct.id)],
          content_type: 'product',
          currency: selectedProduct.price.price.currency,
          search_string: query,
          value: selectedProduct.price.special?.value || selectedProduct.price.price.value,
        })
        gtagTrack({
          eventName: 'search',
          properties: {
            search_term: query,
          },
        })
        router.push(`/${selectedProduct.urlKey}`)
        setQuery('')
        setIsFocused(false)
        setActiveIndex(-1)
      } else if (query.trim().length >= 3) {
        // Track Search event when user presses Enter with a valid search query
        trackSearch({
          search_string: query,
          content_type: 'product',
        })
        gtagTrack({
          eventName: 'search',
          properties: {
            search_term: query,
          },
        })
        setQuery('')
        router.push(`/catalogsearch/result?q=${query}`)
        setIsFocused(false)
      }
    } else if (e.key === 'Escape') {
      setIsFocused(false)
      setActiveIndex(-1)
    }
  }

  const router = useRouter()
  const onProductClick = (urlKey: Product['urlKey'], product: ProductViewFragment) => {
    // Track Search event when user clicks on a product from search results
    trackSearch({
      content_ids: [String(product.id)],
      content_type: 'product',
      currency: product.price.price.currency,
      search_string: query,
      value: product.price.special?.value || product.price.price.value,
    })

    gtagTrack({
      eventName: 'search',
      properties: {
        search_term: query,
      },
    })

    setQuery('')
    router.push(`/${urlKey}`)
    setIsFocused(false)
    setActiveIndex(-1)
  }

  const hasQuery = query.trim().length > 0
  const hasMinChars = query.trim().length >= 3
  const hasResults = !loading && result.products.length > 0 && hasQuery
  const isOpened = loading || (isFocused && hasQuery && (loading || hasMinChars))

  return (
    <div className="relative" ref={containerRef}>
      <div
        className={cn(
          'bg-white rounded-full',
          isFocused && 'opacity-100 rounded-full ring-2 ring-primary ring-offset-2 transition-all duration-100'
        )}
      >
        <div
          className={cn(
            'relative z-70 flex items-center px-3 bg-input ring-2 ring-transparent transition rounded-full'
          )}
        >
          <Input
            ref={inputRef}
            aria-label="Search products"
            aria-expanded={isOpened}
            aria-controls="search-results"
            role="combobox"
            aria-autocomplete="list"
            aria-activedescendant={activeIndex >= 0 ? `result-item-${activeIndex}` : undefined}
            className={cn(
              'h-[35px] w-[400px] flex-1 border-none focus:ring-0 focus:outline-none focus-visible:ring-transparent text-black relative shadow-none'
            )}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
          />
          {!loading ? (
            <LucideSearch className={cn('transition-colors duration-300', isFocused ? 'text-primary' : 'text-black')} />
          ) : (
            <LucideCircleDashed className="text-primary animate-spin [animation-duration:0.5s]" />
          )}
        </div>
        <Card
          className={cn(
            'absolute z-80 w-full overflow-hidden transition-all duration-100 mt-[10px] border-none rounded-2xl'
          )}
          style={{
            maxHeight: isOpened ? '580px' : '0px',
            opacity: isOpened ? 1 : 0,
          }}
        >
          {loading ? (
            <div className="px-3 py-5 text-gray-500 flex flex-col gap-7">
              {[...Array(3)].map((_, index) => (
                <SkeletonElement key={index} />
              ))}
            </div>
          ) : hasResults ? (
            <div className="flex flex-col h-full max-h-[480px]">
              <div className="text-sm font-bold ml-6 mt-2 mb-1 bg-white z-10 shrink-0">Продукти</div>
              <ul className="flex flex-col flex-1 gap-3 overflow-y-auto py-2 min-h-0">
                {result.products.map((product, index) => (
                  <ResultItem
                    key={index}
                    active={activeIndex === index}
                    product={product as Product}
                    onClick={() => onProductClick(product.urlKey, product)}
                  />
                ))}
              </ul>
              <div className="shrink-0 py-2 border-t flex flex-col gap-2">
                <AppLink
                  href={`/catalogsearch/result?q=${query}`}
                  onClick={() => {
                    setIsFocused(false) // @TODO - close search better
                  }}
                  className="pl-4 py-1.5 text-xs text-primary underline"
                >
                  Покажи всички <span className="font-bold">{result.totalItems}</span> резултати
                </AppLink>
                <div className="rounded-xl bg-gray-300 flex flex-col gap-3 mx-3 mb-1 p-3">
                  <div className="font-bold text-sm">Популярни търсения</div>
                  <div className="flex flex-col gap-1">
                    {result.popularTerms.slice(0, 5).map((term, i) => (
                      <div className="text-xs underline flex items-center gap-2" key={i}>
                        <LucideMoveRight size={16} />
                        <AppLink href={'#'} onClick={() => setQuery(term)}>
                          {term}
                        </AppLink>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex justify-center h-[200px] items-center gap-3 p-7">
              <LucideCircleAlert />
              <div className="text-gray-500 rounded-2xl">
                <Text>{query.trim().length < 3 ? 'Въведете поне 3 символа за търсене' : 'Няма открити резултати'}</Text>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  )
}
