import React from 'react'

import AppLink from '@atoms/AppLink'
import BrochureIcon from '@atoms/Icons/BrochureIcon'
import { cn } from '@components/lib/utils'
import { HeaderButtonNew } from '@components/organisms/Header/components/HeaderButtonNew'
import { PropsWithClassName } from '@lib/types/ClassName'
import { StaticContentProps } from '@features/static/types'

export const ExtraLinks: React.FC<PropsWithClassName & StaticContentProps> = ({ className, staticContent }) => {
  return (
    <div className={cn('gap-3 lg:mr-3', className)}>
      <AppLink
        href={staticContent?.brochureLink || '/produkti-broshura'}
        className="w-full p-4 md:p-0 md:m-0 close-menu"
      >
        <HeaderButtonNew
          label="Нашата брошура"
          variant="secondary"
          icon={<BrochureIcon size={20} />}
          className="pointer-events-none shadow-none border-none rounded-t-2xl md:rounded-t-none md:ml-0 w-full md:w-auto"
          textClassName="inline-block md:hidden xl:inline-block"
        />
      </AppLink>
    </div>
  )
}
