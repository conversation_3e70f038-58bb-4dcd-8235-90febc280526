import React from 'react'

import { CustomerAreaClient } from '@components/organisms/Header/components/CustomerAreaClient'
import { ClassName } from '@lib/types/ClassName'
import { ServerGraphQLBackend } from '@lib/api/graphql_server'
export const dynamic = 'force-dynamic'

interface Props {
  showCustomerNav?: boolean
  className?: ClassName
}

const CustomerAreaServer: React.FC<Props> = async ({ className, showCustomerNav }) => {
  let customerData = null
  let wishlistSkus: string[] = []
  try {
    const response = await ServerGraphQLBackend.CustomerData()
    if (response.customerData) {
      customerData = response.customerData
    }
    const wishlistResponse = await ServerGraphQLBackend.GetWishlistSkus()
    if (wishlistResponse.customerWishlist) {
      wishlistSkus = wishlistResponse.customerWishlist.skus
    }
  } catch (e) {}

  return (
    <CustomerAreaClient
      customerData={customerData}
      className={className}
      wishlistSkus={wishlistSkus}
      showCustomerNav={showCustomerNav}
    />
  )
}

export default CustomerAreaServer
