'use client'
import { LucideShoppingCart, LucideX } from 'lucide-react'
import React, { RefObject, useMemo, useRef } from 'react'
import { useOnClickOutside } from 'usehooks-ts'

import { useCartStore } from '@/src/features/cart/cart-state'
import { CartPreview } from '@/src/features/cart/components/CartPreview'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { HeaderButtonNew } from '@components/organisms/Header/components/HeaderButtonNew'
import { Button } from '@components/theme/ui/button'

export const HeaderCart = ({ initialCount }: { initialCount: number }) => {
  const [isPreviewOpen, setIsPreviewOpen] = React.useState(false)
  const cartRef = useRef<HTMLDivElement>(null)
  const { items, ready } = useCartStore()
  // Detect touch device on component mount
  const isTouchDevice = useMemo(() => {
    if (typeof window !== 'undefined') {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0
    }
    return false
  }, [])

  useOnClickOutside(cartRef as RefObject<HTMLElement>, () => {
    setIsPreviewOpen(false)
  })

  // Debug the current state of events
  React.useEffect(() => {}, [isTouchDevice])

  // Handle mouse events only if not a touch device
  const handleMouseEnter = () => {
    if (!isTouchDevice) {
      setIsPreviewOpen(true)
    }
  }

  const handleMouseLeave = () => {
    if (!isTouchDevice) {
      setIsPreviewOpen(false)
    }
  }

  // Handle click for both touch and mouse devices
  const handleClick = () => {
    setIsPreviewOpen((prevState) => !prevState)
  }

  return (
    <div ref={cartRef} className="group" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      <Button
        className={cn(
          'md:hidden absolute left-0 right-0 bottom-0 top-0 z-10 h-[50px] rounded-none text-sm normal-case tracking-wide hover:bg-primary',
          { hidden: !isPreviewOpen }
        )}
        onClick={() => setIsPreviewOpen(false)}
      >
        <LucideX size={18} /> <Text>Затвори</Text>
      </Button>
      <HeaderButtonNew
        label="Вашата Количка"
        variant="default"
        textClassName="hidden xl:inline-block"
        icon={<LucideShoppingCart />}
        className={cn(
          { 'rounded-none': isPreviewOpen },
          'relative cursor-pointer flex items-center',
          { 'bg-primary': isPreviewOpen },
          'hover:bg-primary'
        )}
        onClick={handleClick}
      >
        <span className="rounded-full bg-white/70 text-primary w-[24px] h-[24px] p-3 flex items-center justify-center">
          {ready ? items.length : initialCount}
        </span>
      </HeaderButtonNew>
      <CartPreview open={isPreviewOpen} className="rounded-tr-none" onRedirect={() => setIsPreviewOpen(false)} />
    </div>
  )
}
