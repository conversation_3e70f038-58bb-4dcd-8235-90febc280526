import { VariantProps } from 'class-variance-authority'
import { PropsWithChildren, ReactNode } from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Button, buttonVariants } from '@components/theme/ui/button'
import { ClassName } from '@lib/types/ClassName'

export interface HeaderButtonProps extends VariantProps<typeof buttonVariants> {
  onClick?: () => void
  icon?: ReactNode
  label?: string
  className?: ClassName
  textClassName?: ClassName
  onMouseEnter?: () => void
  onMouseLeave?: () => void
}

export const HeaderButtonNew = ({
  onClick,
  icon,
  label,
  variant,
  className,
  textClassName,
  onMouseEnter,
  onMouseLeave,
  children,
}: PropsWithChildren<HeaderButtonProps>) => {
  return (
    <Button
      size="icon"
      className={cn(
        'h-[50px] pl-5 pr-6 rounded-t-none rounded-b-2xl text-sm shadow-none tracking-normal font-medium normal-case flex',
        className
      )}
      onClick={onClick}
      variant={variant}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {icon}
      <Text className={cn(textClassName)}>{label}</Text>
      {children}
    </Button>
  )
}
