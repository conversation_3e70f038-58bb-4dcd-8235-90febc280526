'use client'

import React, { useEffect, useState } from 'react'
import Cookies from 'universal-cookie'

import AppLink from '@atoms/AppLink'
import CloseIcon from '@atoms/Icons/CloseIcon'
import HeartIcon from '@atoms/Icons/HeartIcon'
import ProfileIcon from '@atoms/Icons/ProfileIcon'
import Text from '@atoms/Text'
import { addClasses } from '@lib/style/style'
import { ClassName } from '@lib/types/ClassName'
import { useCustomerStore } from '@features/customer/customer.store'
import { AuthHandler } from '@features/customer/AuthHandler'
import { CustomerDataFullFragment } from '@lib/_generated/graphql_sdk'
import { deleteToken } from '@features/customer/customer.actions'
import { AuthCookieName } from '@features/cart/ClientCookie'

const cookies = new Cookies()

interface CustomerAreaClientProps {
  showCustomerNav?: boolean
  customerData: CustomerDataFullFragment | null
  wishlistSkus: string[]
  className: ClassName
}

export const CustomerAreaClient = ({
  customerData,
  wishlistSkus,
  className,
  showCustomerNav,
}: CustomerAreaClientProps) => {
  const { authenticated, initCustomer } = useCustomerStore()
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    if (isMounted) return
    setIsMounted(true)
    if (customerData) {
      initCustomer({ data: customerData, wishlistSkus })
    } else {
      if (cookies.get(AuthCookieName)) {
        deleteToken()
      }
    }
  }, [customerData, initCustomer, isMounted, wishlistSkus])

  const isLoggedFlag = isMounted ? authenticated : !!customerData

  return (
    <>
      <AuthHandler />
      {showCustomerNav && (
        <div className={addClasses('flex items-center gap-2', className)}>
          {isLoggedFlag ? (
            <>
              <AppLink href="/wishlist">
                <div className="flex flex-col items-center group">
                  <div
                    className={addClasses(
                      'bg-black group-hover:bg-gray-600 rounded-full',
                      'transition-all duration-300 ease-in-out',
                      'w-[34px] h-[34px] flex items-center justify-center'
                    )}
                  >
                    <HeartIcon />
                  </div>
                  <span
                    className={addClasses(
                      'hidden sm:flex',
                      'text-paragraph-muted text-sm group-hover:text-white',
                      'transition-all duration-300 ease-in-out'
                    )}
                  >
                    <Text>Любими</Text>
                  </span>
                </div>
              </AppLink>
              <AppLink href={'/customer/account'}>
                <div className="flex flex-col items-center group h-full">
                  <div
                    className={addClasses(
                      'bg-black group-hover:bg-gray-600 rounded-full',
                      'transition-all duration-300 ease-in-out',
                      'w-[34px] h-[34px] flex items-center justify-center'
                    )}
                  >
                    <ProfileIcon fill="#ee7f00" />
                  </div>
                  <span
                    className={addClasses(
                      'hidden sm:flex',
                      'text-paragraph-muted text-sm group-hover:text-white',
                      'transition-all duration-300 ease-in-out'
                    )}
                  >
                    <Text>Профил</Text>
                  </span>
                </div>
              </AppLink>
              <AppLink href="/customer/account/logout">
                <div className="flex flex-col items-center group">
                  <div
                    className={addClasses(
                      'bg-black group-hover:bg-gray-600 rounded-full',
                      'transition-all duration-300 ease-in-out',
                      'w-[34px] h-[34px] flex items-center justify-center'
                    )}
                  >
                    <CloseIcon />
                  </div>
                  <span
                    className={addClasses(
                      'hidden sm:flex',
                      'text-paragraph-muted text-sm group-hover:text-white',
                      'transition-all duration-300 ease-in-out'
                    )}
                  >
                    <Text>Изход</Text>
                  </span>
                </div>
              </AppLink>
            </>
          ) : (
            <AppLink href={'/customer/account/login'}>
              <div className="flex flex-col items-center group">
                <div
                  className={addClasses(
                    'bg-black group-hover:bg-gray-600 rounded-full',
                    'transition-all duration-300 ease-in-out',
                    'w-[34px] h-[34px] flex items-center justify-center'
                  )}
                >
                  <ProfileIcon />
                </div>
                <span className={addClasses('hidden sm:flex', 'text-paragraph-muted text-sm group-hover:text-white')}>
                  <Text>вход</Text>
                </span>
              </div>
            </AppLink>
          )}
        </div>
      )}
    </>
  )
}
