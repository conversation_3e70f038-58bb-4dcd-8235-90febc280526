'use client'
import React from 'react'

import CloseIcon from '@atoms/Icons/CloseIcon'
import DotsIcon from '@atoms/Icons/DotsIcon'
import HeaderButton from '@components/organisms/Header/HeaderButton'
import { useSimpleMenuContext } from '@context/SimpleMenuContext'

const HeaderMenuButton: React.FC = () => {
  const { isOpen, toggle } = useSimpleMenuContext()

  return (
    <div className="h-[50px]">
      {isOpen ? (
        <div className="absolute z-10 left-0 w-full">
          <HeaderButton label="Затвори" className="bg-primary desktop:hidden rounded-b-none" onClick={toggle}>
            <CloseIcon className="mr-4" />
          </HeaderButton>
        </div>
      ) : (
        <HeaderButton label="Меню" className="bg-background text-black desktop:hidden min-w-[116px]" onClick={toggle}>
          <DotsIcon className="mr-4" />
        </HeaderButton>
      )}
    </div>
  )
}

export default HeaderMenuButton
