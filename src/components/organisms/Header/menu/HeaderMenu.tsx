'use client'

import { LucideX } from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'

import AppLink from '@atoms/AppLink'
import DotsIcon from '@atoms/Icons/DotsIcon'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { ButtonIcon } from '@components/molecules/ButtonIcon'
import { Button } from '@components/theme/ui/button'

const HeaderMenu = () => {
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if ((isOpen && menuRef.current && !menuRef.current.contains(target)) || target.classList.contains('close-menu')) {
        setIsOpen(false)
      }
    }
    document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [isOpen, setIsOpen])

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      if (isOpen) document.body.style.overflow = ''
    }
  }, [isOpen])

  return (
    <div className="flex flex-col flex-1 group" ref={menuRef}>
      <Button
        className={cn(
          'md:hidden absolute left-0 right-0 bottom-0 top-0 z-10 h-[50px] rounded-none text-sm normal-case tracking-wide hover:bg-primary',
          { hidden: !isOpen }
        )}
        onClick={() => setIsOpen(false)}
      >
        <LucideX size={18} /> <Text>Затвори</Text>
      </Button>
      <div className={cn('flex lg:hidden h-[50px]', { hidden: isOpen })}>
        <ButtonIcon
          className="flex flex-1 gap-0"
          variant="tertiary"
          icon={<DotsIcon />}
          iconClassName="shadow-none"
          onClick={() => setIsOpen(true)}
          label={<Text className="font-bold mr-2">Меню</Text>}
          direction="horizontal"
          labelClassName="text-sm leading-none tracking-normal w-full"
        />
      </div>
      <div
        className={cn(
          isOpen ? 'flex' : 'hidden',
          'flex-1',
          'flex-col items-start mt-[50px] py-2 lg:px-2 absolute shadow-lg left-0 right-0 bg-white',
          'lg:flex lg:flex-row lg:items-center lg:mt-0 lg:py-0 lg:static lg:shadow-none lg:gap-0 lg:justify-evenly lg:justify-start'
        )}
      >
        {links.map((link, index) => (
          <AppLink
            key={link.href}
            href={link.href || '#'}
            className={cn(
              'w-full md:w-auto px-6 md:px-0 lg:px-2 flex justify-start md:justify-center py-3.5 border-b md:border-none',
              'hover:bg-gray-50 md:hover:bg-white last:border-none',
              'close-menu'
            )}
          >
            <Text className="text-sm text-nowrap close-menu">{link.text}</Text>
          </AppLink>
        ))}
      </div>
    </div>
  )
}

export default HeaderMenu

type LinkProps = {
  text: string
  href: string
  style?: string
}

const links: LinkProps[] = [
  {
    text: 'Промоции',
    href: '/spetsialni-predlozheniya',
  },
  {
    text: 'Купи изгодно',
    href: '/kupi-izgodno',
  },
  {
    text: 'Кариери',
    href: '/karieri',
  },
  {
    text: 'Услуги',
    href: '/uslugi',
  },
  {
    text: 'Магазини',
    href: '/shops',
  },
  {
    text: 'Партньорство',
    href: '/predlojenie-za-satrudnichestvo',
  },
  {
    style: 'border-none',
    text: 'Контакти',
    href: '/contacts',
  },
]
