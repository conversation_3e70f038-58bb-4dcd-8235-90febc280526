import React from 'react'

import LocationIcon from '@atoms/Icons/LocationIcon'
import Text from '@atoms/Text'
import { StoreContacts } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'
import { getStyleClassesFromProps, ThemeStyles } from '@lib/style/theme'

const StoreLocationPreview = ({ className }: { className: string }) => {
  return (
    <div className={addClasses('gap-2 items-center hidden xl:flex', className)}>
      <LocationIcon size={26} />
      <div className="flex flex-col">
        <Text className="text-paragraph-muted text-xs">Моят магазин:</Text>
        <Text className="text-primary-foreground font-bold text-sm">Велико Търново</Text>
      </div>
    </div>
  )
}

export default StoreLocationPreview
