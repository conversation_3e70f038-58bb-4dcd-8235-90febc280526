import React from 'react'

import AppLink from '@atoms/AppLink'
import Text from '@atoms/Text'
import { addClasses } from '@lib/style/style'

interface Props {
  className?: string
  children?: React.ReactNode
  label?: string
  mobileLabel?: string
  href: string
}

const HeaderButton_Link: React.FC<Props> = ({ className, children, label, mobileLabel, href }) => {
  return (
    <AppLink
      href={href}
      className={addClasses(
        'flex items-center content-center place-content-center',
        'normal-case cursor-pointer relative pl-8',
        'rounded-b-xl rounded-t-none',
        'min-h-[50px]',
        `${className}`
      )}
    >
      <div className="absolute left-6">{children}</div>

      {mobileLabel && <Text className="normal-case font-medium desktop:hidden">{mobileLabel}</Text>}

      <Text className={addClasses('normal-case font-medium', mobileLabel && 'phone:hidden tablet:hidden')}>
        {label}
      </Text>
    </AppLink>
  )
}

export default HeaderButton_Link
