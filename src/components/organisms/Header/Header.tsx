import React from 'react'

import Container from '@atoms/Container'
import { cn } from '@components/lib/utils'
import CategoriesMenu from '@components/organisms/Header/categories/CategoriesMenu'
import CustomerAreaServer from '@components/organisms/Header/components/CustomerAreaServer'
import { ExtraLinks } from '@components/organisms/Header/components/ExtraLinks'
import { HeaderCart } from '@components/organisms/Header/components/HeaderCart'
import { HeaderLogo } from '@components/organisms/Header/components/HeaderLogo'
import { SearchField } from '@components/organisms/Header/components/SearchField'
import StoreDataPreview from '@components/organisms/Header/StoreDataPreview'
import StoreLocationPreview from '@components/organisms/Header/StoreLocationPreview'
import { ClientCookie } from '@features/cart/ClientCookie'
import { parseToken } from '@features/cart/cookie.helpers'
import { ServerCookie } from '@features/cart/ServerCookie'
import { CookieCartClaims } from '@features/cart/types'
import Static from 'src/features/static'

import HeaderMenu from './menu/HeaderMenu'
import { CMSBlock } from '../../molecules/Widget/markup/instanced'

const Header = async () => {
  const token = await ServerCookie.getToken('cartToken')
  const count = token && ClientCookie.isValid(token) ? (parseToken<CookieCartClaims>(token)?.products_count ?? 0) : 0

  return (
    <header className="sticky top-0 isolate z-60">
      <div className="bg-background-dark text-white">
        <Container className="grid grid-cols-24 gap-x-3 items-center min-h-[80px]">
          <div
            className={cn(
              'col-span-24 flex justify-between items-center py-3', // xs
              'md:col-span-5 md:py-0', // md
              'lg:col-span-4', // lg
              '3xl:col-span-3' // 3xl
            )}
          >
            <HeaderLogo />
            <CustomerAreaServer className="md:hidden" showCustomerNav />
          </div>
          <div
            className={cn(
              'col-span-24 pb-3', // xs
              'md:col-span-11 md:pb-0', // md
              'lg:col-span-9 lg:col-start-6', // lg
              'xl:col-span-8 xl:col-start-5', // xl
              '3xl:col-span-9 3xl:col-start-4' // 3xl
            )}
          >
            <SearchField />
          </div>
          <div
            className={cn(
              'col-span-3 hidden',
              'md:col-span-8 md:flex md:items-center md:justify-end',
              'lg:col-span-10',
              'xl:col-span-12 xl:justify-end',
              '2xl:col-span-12'
            )}
          >
            <div className={cn('hidden lg:flex flex-1 justify-center lg:justify-end lg:mr-4')}>
              <Static component={StoreDataPreview} className="items-center" />
            </div>
            <CustomerAreaServer showCustomerNav />
          </div>
        </Container>
      </div>
      <div className="min-h-[50px] flex relative shadow-sm rounded-b-2xl bg-white">
        <Static component={CategoriesMenu} />
        <div className="absolute" />
        <HeaderMenu />
        <Static component={ExtraLinks} className="hidden md:flex" />
        <HeaderCart initialCount={count} />
      </div>
    </header>
  )
}

export default Header
