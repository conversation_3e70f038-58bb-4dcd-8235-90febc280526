'use client'

import { LucideMenu, LucideX } from 'lucide-react'
import React, { useCallback, useEffect, useRef } from 'react'

import { staticSelectors } from '@/src/features/static/selectors'
import { StaticContentProps } from '@/src/features/static/types'
import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import { cn } from '@components/lib/utils'
import { AllCategories } from '@components/organisms/Header/categories/components/AllCategories'
import { debug } from '@components/organisms/Header/categories/debug'
import subCategoriesMenu from '@components/organisms/Header/categories/SubCategoriesMenu'
import { HeaderButtonNew } from '@components/organisms/Header/components/HeaderButtonNew'
import { Button } from '@components/theme/ui/button'

import { MainCategoryItem } from './components/MainCategoryItem'
import useBreakpoint from '@/src/hooks/useBreakpoint'

function CategoriesMenu({ staticContent }: StaticContentProps) {
  const categories = staticSelectors.selectMenuCategories(staticContent)
  const [isOpen, setIsOpen] = React.useState(false)
  const [hoverIndex, setHoverIndex] = React.useState(-1)
  const menuRef = useRef<HTMLDivElement>(null)

  const handleClose = useCallback(() => {
    setHoverIndex(-1)
    setIsOpen(false)
  }, [])

  const handleToggle = useCallback(() => {
    if (isOpen) {
      handleClose()
    } else {
      setIsOpen(true)
    }
  }, [handleClose, isOpen])

  useEffect(() => {
    const handleClickEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        handleClose()
      }
    }
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement

      if (debug) {
        console.log('target', target)
      }
      if (
        (isOpen && menuRef.current && !menuRef.current.contains(target)) ||
        target.classList.contains('main-category') ||
        target.classList.contains('sub-category') ||
        target.classList.contains('close-categories')
      ) {
        handleClose()
      }
    }
    document.addEventListener('keydown', handleClickEscape)
    document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
      document.removeEventListener('keydown', handleClickEscape)
    }
  }, [isOpen, handleClose])

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      if (isOpen) document.body.style.overflow = ''
    }
  }, [isOpen])

  const initialState = hoverIndex === -1

  return (
    <div>
      <HeaderButtonNew
        variant="default"
        icon={<LucideMenu size={24} />}
        label="Всички продукти"
        onClick={handleToggle}
        className="flex-1"
        textClassName="hidden md:inline-block"
      />

      {/* fixed xl:static */}
      <div
        className={cn({
          'fixed top-[125px] xl:top-[80px] left-0 right-0 bottom-0 bg-white/60 z-80 max-w-screen-6xl mx-auto': isOpen,
        })}
      >
        <div
          ref={menuRef}
          title="Categories Menu"
          className={cn(
            { 'border-2 border-red-600': debug },
            'hidden flex-col',
            { flex: isOpen },
            // 'absolute xl:static',
            'absolute w-full top-0 xl:h-auto z-30',
            // old 'max-h-[calc(100vh-120px)]',
            'overflow-auto fixed bottom-0 top-[107px] md:top-[80px] sm:top-[125px] max-h-screen max-w-screen-6xl', // xl:bottom-0
            'close-categories'
          )}
        >
          {/* Mobile Close Button */}
          <div className="flex xl:hidden flex-col xl:w-[240px]">
            <Button className="rounded-none h-[50px] normal-case tracking-normal text-white close-categories text-sm hover:bg-primary">
              <LucideX /> Затвори
            </Button>
          </div>
          <div
            className={cn(
              { 'border border-blue-700': debug },
              'bg-white relative xl:static flex flex-col w-full xl:w-[240px]'
            )}
          >
            <AllCategories />

            {/* Main Categories List */}
            {categories?.map((category, index) => {
              const isHovered = hoverIndex === index
              const defaultOpened = index === 0 && initialState
              return (
                <div
                  key={index}
                  tabIndex={index}
                  className={cn(
                    { 'border border-pink-500': debug },
                    'group shadow-md',
                    'focus-within:bg-background-dark focus-within:text-white bg-white text-black',
                    'xl:hover:bg-background-dark xl:hover:text-white bg-white text-black',
                    { 'xl:bg-background-dark xl:text-white': isHovered || defaultOpened }, // activate the first one
                    'border-b border-b-[bg-default]'
                  )}
                  onFocus={() => setHoverIndex(index)}
                  onMouseEnter={() => setHoverIndex(index)}
                >
                  <MainCategoryItem category={category} hovered={isHovered} defaultOpened={defaultOpened} />

                  {/* List Sub Categories*/}
                  <div
                    className={cn(
                      { 'border-2 border-yellow-500': debug },
                      'flex-col absolute left-[60px] top-0 z-30',
                      'right-0 min-h-full',
                      'hidden xl:group-hover:flex group-focus-within:flex',
                      'pl-5 shadow-lg',
                      'bg-background-dark',

                      { 'xl:flex': isHovered || defaultOpened },
                      'xl:left-[240px]',
                      'xl:min-h-0',
                      'xl:right-0',
                      'xl:pl-16 xl:pb-10',
                      'animate-[swipeRight_0.25s_normal]',
                      'xl:animate-none'
                    )}
                  >
                    <div className="font-medium pt-4 pb-6 xl:text-2xl xl:font-bold xl:py-7">{category.name}</div>
                    <div className="flex">
                      <div
                        className={cn(
                          'flex flex-col xl:w-[600px] xl:items-center',
                          'xl:grid xl:grid-cols-2 xl:gap-x-8 xl:h-fit'
                        )}
                      >
                        {category.children?.map((subCategory, index) => (
                          <AppLink
                            key={index}
                            className="text-white text-sm border-b border-b-[#474747] py-3.5 xl:hover:bg-[#474747] xl:px-5 sub-category h-full flex items-center"
                            href={subCategory.url}
                            textProps={{ className: 'sub-category' }}
                          >
                            {subCategory.name}
                          </AppLink>
                        ))}
                      </div>
                      <div className="hidden xl:flex flex-1 text-white">
                        {category.sideSection?.image && (
                          <div className="relative w-full h-[550px] mx-10">
                            <AppLink href={category.sideSection.url}>
                              <Img
                                fill
                                src={category.sideSection.image}
                                alt={category.name}
                                className="object-contain object-top close-categories"
                              />
                            </AppLink>
                          </div>
                        )}
                        {/* side content */}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
          <div className="absolute top-12 right-0 z-30 xl:hidden">
            <Button variant="ghost" className="text-white hover:bg-transparent" onClick={() => setHoverIndex(-1)}>
              <LucideX />
            </Button>
          </div>

          {/* Desktop Close Button */}
          <div className="hidden xl:flex flex-col xl:w-[240px]">
            <Button
              className="rounded-t-none rounded-b-xl normal-case tracking-normal text-white"
              onClick={handleClose}
            >
              <LucideX /> Затвори
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CategoriesMenu
