import Avto from '@icons/categories/avto-i-velo-svyat.inline.svg'
import Banya from '@icons/categories/banya.inline.svg'
import Boi from '@icons/categories/boi-lakove-i-mazilki.inline.svg'
import Dom from '@icons/categories/dom.inline.svg'
import Gradina from '@icons/categories/gradina.inline.svg'
import Mashini from '@icons/categories/mashini-i-instrumenti.inline.svg'
import Mebeli from '@icons/categories/mebeli.inline.svg'
import Osvetlenie from '@icons/categories/osvetlenie-i-el-materiali.inline.svg'
import Otoplenie from '@icons/categories/otoplenie-ohlazhdane-i-vik.inline.svg'
import Podovi from '@icons/categories/podovi-i-stenni-pokritiya.inline.svg'
import Stroitelstvo from '@icons/categories/stroitelstvo.inline.svg'
import { ClassName } from '@lib/types/ClassName'

export const CategoryIcon = ({ category, className }: { category: string; className?: ClassName }) => {
  switch (category.replace('/', '')) {
    case 'avto-i-velo-svyat':
      return <Avto className={className} />
    case 'banya':
      return <Banya className={className} />
    case 'boi-lakove-i-mazilki':
      return <Boi className={className} />
    case 'dom':
      return <Dom className={className} />
    case 'gradina':
      return <Gradina className={className} />
    case 'mashini-i-instrumenti':
      return <Mashini className={className} />
    case 'mebeli':
      return <Mebeli className={className} />
    case 'osvetlenie-i-el-materiali':
      return <Osvetlenie className={className} />
    case 'otoplenie-ohlazhdane-i-vik':
      return <Otoplenie className={className} />
    case 'podovi-i-stenni-pokritiya':
      return <Podovi className={className} />
    case 'stroitelstvo':
      return <Stroitelstvo className={className} />
    default:
      return null
  }
}
