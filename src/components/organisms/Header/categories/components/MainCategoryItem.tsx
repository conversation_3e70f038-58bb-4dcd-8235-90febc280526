import React from 'react'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import { cn } from '@components/lib/utils'
import { CategoryIcon } from '@components/organisms/Header/categories/components/CategoryIcon'
import { debug } from '@components/organisms/Header/categories/debug'
import { MenuItem } from '@lib/_generated/graphql_sdk'
import { ClassName } from '@lib/types/ClassName'

interface MainCategoryItemProps {
  category: MenuItem
  className?: ClassName
  hovered?: boolean
  defaultOpened?: boolean
}

export const MainCategoryItem: React.FC<MainCategoryItemProps> = ({ category, className, hovered, defaultOpened }) => {
  return (
    <div className={cn('flex gap-4 px-4 z-40')}>
      <div
        className={cn(
          {
            'border border-green-400': debug,
          },
          'flex items-center',
          { 'lg:invert': defaultOpened }
        )}
      >
        <div className={cn('relative h-7 w-7', { invert: hovered })}>
          <Img src={category.thumbnail} alt={category.name} fill className="object-cover" />
        </div>
      </div>
      <div className={cn({ 'border border-pink-500': debug }, 'w-full flex flex-col justify-center')}>
        <AppLink
          href={category.url}
          className={cn(
            { 'border border-cyan-500': debug },
            'flex flex-1 items-center py-4',
            'pointer-events-none xl:pointer-events-auto',
            'main-category'
          )}
          textProps={{ className: 'font-bold text-sm main-category main-category' }}
        >
          {category.name}
        </AppLink>
      </div>
    </div>
  )
}
