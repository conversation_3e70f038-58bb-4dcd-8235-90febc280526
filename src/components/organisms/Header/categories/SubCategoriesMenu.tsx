import React from 'react'

import AppLink from '@atoms/AppLink'
import Title from '@atoms/Title'
import { Maybe, MenuItem, MenuSideSection } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'

interface Props {
  categoryName: string
  categoryHref: string
  categories: Maybe<MenuItem[]> | undefined
  sideSection: Maybe<MenuSideSection> | undefined
  onClick?: () => void
}

const SubCategoriesMenu: React.FC<Props> = ({ categoryName, categoryHref, categories, sideSection, onClick }) => (
  <div className={addClasses('relative w-full h-100 pt-2 lg:pt-4', 'lg:py-10 lg:px-14')}>
    <AppLink href={categoryHref}>
      <Title className={addClasses('text-4xl font-bold md:mb-8 mb-2 text-white', 'text-base', 'px-6 pt-1 lg:mt-8')}>
        {categoryName}
      </Title>
    </AppLink>
    <div className={addClasses('flex text-white', 'md:flex-row flex-col')}>
      <div className={addClasses('grid grid-cols-1', 'md:grid-cols-2 gap-x-2')}>
        {categories &&
          categories.map(
            (category, index: number) =>
              category && (
                <AppLink
                  href={category.url}
                  key={index}
                  onClick={onClick}
                  className={addClasses(
                    'font-normal text-sm',
                    'border-b border-[#999]/[0.25]',
                    'lg:px-6 px-6 md:px-4 py-3 lg:mr-10',
                    'hover:bg-[#999]/[0.25]'
                  )}
                >
                  {category.name}
                </AppLink>
              )
          )}
      </div>
      <div className="flex basis-1/3 hidden">{sideSection && 'side section goes here'}</div>
    </div>
  </div>
)

export default SubCategoriesMenu
