'use client'
import React from 'react'

import CloseIcon from '@atoms/Icons/CloseIcon'
import MenuIcon from '@atoms/Icons/MenuIcon'
import HeaderButton from '@components/organisms/Header/HeaderButton'
import { useSimpleMenuContext } from '@context/SimpleMenuContext'
import { addClasses } from '@lib/style/style'

const CategoriesMenuButton: React.FC = () => {
  const { isOpen, toggle } = useSimpleMenuContext()

  return (
    <div className="h-[50px]">
      <HeaderButton
        label="Всички продукти"
        mobileLabel="Продукти"
        className={addClasses(
          'phone:w-full tablet:w-full',
          'phone:bg-primary phone:text-white',
          'tablet:bg-primary tablet:text-white',
          isOpen && 'bg-background text-black pl-0 phone:absolute z-40 phone:rounded-none tablet:rounded-none',
          !isOpen && 'bg-primary'
        )}
        onClick={toggle}
      >
        {isOpen ? <CloseIcon className={addClasses('mr-4')} /> : <MenuIcon className={addClasses('mr-4')} />}
      </HeaderButton>
    </div>
  )
}

export default CategoriesMenuButton
