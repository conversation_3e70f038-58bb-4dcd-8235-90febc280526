'use client'

import { LucideMenu } from 'lucide-react'
import React, { useCallback, useEffect, useRef, useState } from 'react'

import { staticSelectors } from '@/src/features/static/selectors'
import { StaticContentProps } from '@/src/features/static/types'
import AppLink from '@atoms/AppLink'
import CloseIcon from '@atoms/Icons/CloseIcon'
import Img from '@atoms/Img'
import Paper from '@atoms/Paper'
import Text from '@atoms/Text'
import { HeaderButtonNew } from '@components/organisms/Header/components/HeaderButtonNew'
import { Button } from '@components/theme/ui/button'
import { addClasses } from '@lib/style/style'

import SubCategoriesMenu from './SubCategoriesMenu'

function CategoriesMenu_Legacy({ staticContent }: StaticContentProps) {
  const [isOpen, setIsOpen] = useState(false)
  const categories = staticSelectors.selectMenuCategories(staticContent)
  const menuRef = useRef<HTMLDivElement>(null)

  const onClose = useCallback(() => setIsOpen(false), [])

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      if (isOpen) document.body.style.overflow = ''
    }
  }, [isOpen])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement

      if ((isOpen && menuRef.current && !menuRef.current.contains(target)) || target.closest('.main-category')) {
        onClose()
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [isOpen, onClose])

  return (
    <div className="flex flex-1 sm:flex-none relative categories-menu" ref={menuRef}>
      <HeaderButtonNew
        variant="default"
        icon={<LucideMenu size={24} />}
        label="Всички продукти"
        onClick={() => setIsOpen((prev) => !prev)}
        className="flex-1"
        textClassName="hidden md:inline-block"
      />

      {isOpen && (
        <div className="absolute w-screen max-w-screen-6xl min-h-[calc(100vh-80px)] overflow-y-auto overflow-x-hidden">
          <Paper className="absolute rounded-t-[0] rounded-b-[10px] p-0 z-10">
            <div
              className={addClasses('lg:shadow-[0_4px_4px_0px_rgba(0,0,0,0.15)]', 'relative w-screen lg:max-w-[320px]')}
            >
              {categories?.map(
                (category, index) =>
                  category && (
                    <div key={index}>
                      <div className="group peer" tabIndex={index}>
                        <div
                          className={addClasses(
                            'flex lg:items-center font-bold',
                            'flex-col md:flex-row',
                            'border-solid border-b border-gray-300',
                            'text-black',
                            'hover:bg-background-dark group-focus-within:bg-background-dark'
                          )}
                        >
                          <AppLink
                            href={category.url}
                            className={addClasses(
                              'group-hover:text-white group-focus-within:text-white',
                              'pointer-events-none lg:pointer-events-auto',
                              'py-3 px-4 w-full main-category'
                            )}
                          >
                            <span className="flex flex-row items-center text-sm">
                              <Img
                                alt={category.name}
                                className="flex mr-4 group-hover:invert group-focus-within:invert"
                                src={category.thumbnail}
                                width={25}
                                height={25}
                                itemProp={category.name}
                              />
                              {category.name}
                            </span>
                          </AppLink>
                          <div
                            className={addClasses(
                              'hidden lg:flex absolute',
                              'bg-background-dark md:group-hover:inline-flex group-focus-within:inline-flex',
                              'min-h-full top-0',
                              'w-[calc(100vw-50px)] lg:w-[calc(100vw-320px)]',
                              'left-[50px] lg:left-[320px]',
                              'group-hover:left-[50px] lg:group-hover:left-[320px]',
                              'z-10',
                              'animate-[swipeRight_0.25s_normal]',
                              'md:animate-none',
                              index === 0 ? '' : 'lg:hidden'
                            )}
                          >
                            <SubCategoriesMenu
                              categoryName={category.name}
                              categoryHref={category.url}
                              categories={category.children}
                              sideSection={category.sideSection}
                              onClick={onClose}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )
              )}
            </div>
            <div className={addClasses('text-white p-2 absolute right-0', 'top-[10px] z-90 lg:hidden')}>
              <CloseIcon className="mr-4" />
            </div>

            <div
              className={addClasses(
                'flex items-center font-bold rounded-b-lg',
                'column lg:py-4 px-4 bg-primary justify-between',
                'cursor-pointer text-white',
                'hover:bg-opacity-80 transition-all duration-300 ease-in-out',
                'hidden lg:flex'
              )}
              onClick={onClose}
            >
              <CloseIcon className="justify-self-center w-[24px]" />
              <Text>Затвори</Text>
              <div className="w-[24px]" />
            </div>
          </Paper>
          <div onClick={onClose} className="bg-white/75 h-screen w-screen fixed t-0"></div>
        </div>
      )}
    </div>
  )
}

export default CategoriesMenu_Legacy
