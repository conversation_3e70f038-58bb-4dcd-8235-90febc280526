import React from 'react'

import Text from '@atoms/Text'
import { addClasses } from '@lib/style/style'

interface Props {
  className?: string
  children?: React.ReactNode
  label?: string
  mobileLabel?: string
  itemsCount?: number
  onClick?: () => void
  href?: string
}

const HeaderButton: React.FC<Props> = ({ className, children, label, mobileLabel, itemsCount, onClick, href }) => {
  return (
    <div
      onClick={onClick}
      className={addClasses(
        'flex items-center justify-center',
        'normal-case cursor-pointer relative pl-8',
        'rounded-b-xl rounded-t-none',
        'min-h-[50px]',
        `${className}`
      )}
    >
      <div className="absolute left-6">{children}</div>

      {mobileLabel && <Text className="normal-case font-medium desktop:hidden">{mobileLabel}</Text>}

      <Text className={addClasses('normal-case font-medium', mobileLabel && 'phone:hidden tablet:hidden')}>
        {label}
      </Text>

      {itemsCount && (
        <div
          className={addClasses(
            'rounded-full w-8 h-8 flex bg-white',
            'text-black ml-4 justify-center leading-8 font-bold'
          )}
        >
          {itemsCount}
        </div>
      )}
    </div>
  )
}

export default HeaderButton
