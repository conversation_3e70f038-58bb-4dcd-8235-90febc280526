'use client'

import { PropsWithChildren, useState } from 'react'
import { useDebounceCallback, useEventListener } from 'usehooks-ts'

import { cn } from '@components/lib/utils'

interface StickyHeaderProps {
  headerHeight?: number
  swipeUpClass?: string
  swipeDownClass?: string
}

export const StickyHeader: React.FC<PropsWithChildren<StickyHeaderProps>> = ({
  children,
  headerHeight = 134,
  swipeUpClass = 'animate-swipeUp',
  swipeDownClass = 'animate-swipeDown',
}) => {
  const [lastScrollY, setLastScrollY] = useState(0)
  const [top, setTop] = useState(true)
  const [hideHeader, setHideHeader] = useState(false)

  const handleScroll = useDebounceCallback(() => {
    const currentScrollY = Math.max(0, window.scrollY)

    if (currentScrollY <= headerHeight) {
      setTop(true)
      setHideHeader(false)
    } else {
      setTop(false)
    }

    if (currentScrollY > lastScrollY) {
      // Scrolling down
      setHideHeader(true)
    } else if (currentScrollY < lastScrollY) {
      // Scrolling up
      setHideHeader(false)
    }

    setLastScrollY(currentScrollY)
  }, 50)
  useEventListener('scroll', handleScroll)

  return (
    <>
      {/* Fixed header */}
      <header
        className={cn(
          'z-50 fixed w-full transition-transform duration-300 ease-in-out',
          hideHeader && !top ? '-translate-y-full' : 'translate-y-0'
        )}
        style={{ height: `${headerHeight}px` }}
        role="banner"
      >
        {children}
      </header>

      {/* Spacer element to offset header's height */}
      <div style={{ height: `${headerHeight}px` }}></div>
    </>
  )
}
