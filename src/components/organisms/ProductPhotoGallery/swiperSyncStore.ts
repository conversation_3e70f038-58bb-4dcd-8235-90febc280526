// swiperSyncStore.ts
import { create } from 'zustand'

type SyncGroup = {
  selectedIndex: number
}

type SwiperSyncStore = {
  syncGroups: Record<string, SyncGroup>
  selectSlide: (syncId: string, index: number) => void
  selectNextSlide: (syncId: string, totalSlides: number) => void
  selectPrevSlide: (syncId: string) => void
  registerSyncGroup: (syncId: string) => void
}

export const useSwiperSyncStore = create<SwiperSyncStore>((set, get) => ({
  syncGroups: {},

  selectSlide: (syncId, index) =>
    set((state) => {
      // Create group if it doesn't exist
      const group = state.syncGroups[syncId] || { selectedIndex: 0 }

      return {
        syncGroups: {
          ...state.syncGroups,
          [syncId]: {
            ...group,
            selectedIndex: index,
          },
        },
      }
    }),

  selectNextSlide: (syncId, totalSlides) => {
    const state = get()
    const group = state.syncGroups[syncId]
    if (!group) return

    const nextIndex = Math.min(group.selectedIndex + 1, totalSlides - 1)
    state.selectSlide(syncId, nextIndex)
  },

  selectPrevSlide: (syncId) => {
    const state = get()
    const group = state.syncGroups[syncId]
    if (!group) return

    const prevIndex = Math.max(group.selectedIndex - 1, 0)
    state.selectSlide(syncId, prevIndex)
  },

  registerSyncGroup: (syncId) =>
    set((state) => {
      // Only create if it doesn't exist yet
      if (state.syncGroups[syncId]) return state

      return {
        syncGroups: {
          ...state.syncGroups,
          [syncId]: {
            selectedIndex: 0,
          },
        },
      }
    }),
}))
