'use client'
import React, { ReactNode, useState, useEffect, useRef, ReactElement, useCallback, useMemo, useContext } from 'react'
import ReactDOM from 'react-dom'
import { Swiper<PERSON>lide, Swiper as ReactSwiper, useSwiper } from 'swiper/react'
import { cn } from '../../lib/utils'
import { useSwiperSyncStore } from './swiperSyncStore'

interface SwiperProps {
  className?: string
  slideClassName?: string
  direction?: 'horizontal' | 'vertical'
  children: ReactNode
  slidesPerView?: string
  sync?: string // New prop for synchronization
}

// Enhanced context type with selection tracking
type SwiperContextType = {
  swiper: any
  direction: 'horizontal' | 'vertical'
  selectedIndex: number
  totalSlides: number
  selectNextSlide: () => void
  selectPrevSlide: () => void
  selectSlide: (index: number) => void
  isSelected: (index: number) => boolean
} | null

// Create the context with proper typing and initial value
const SwiperContext = React.createContext<SwiperContextType>(null)

// Custom hook to use the swiper context
export const useSwiperContext = () => {
  const context = useContext(SwiperContext)
  if (!context) {
    throw new Error('useSwiperContext must be used within a Swiper component')
  }
  return context
}

// Update the Portal component to accept HTMLDivElement ref specifically
function WrapperPortal({
  targetRef,
  children,
  contextValue,
}: {
  targetRef: React.RefObject<HTMLDivElement | null>
  children: React.ReactNode
  contextValue: SwiperContextType
}) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    return () => setMounted(false)
  }, [])

  return mounted && targetRef.current
    ? ReactDOM.createPortal(
        <SwiperContext.Provider value={contextValue}>
          <div className="w-full h-full">{children}</div>
        </SwiperContext.Provider>,
        targetRef.current
      )
    : null
}

export function Swiper({
  className,
  slideClassName,
  direction = 'horizontal',
  children,
  currentSlideClassName = '',
  slidesPerView = 'auto',
  sync,
}: SwiperProps & {
  currentSlideClassName?: string
  selectedSlideClassName?: string
}) {
  const wrapperRef = useRef<HTMLDivElement>(null)
  const childrenArray = React.Children.toArray(children)
  const swiperWrapperChildren = childrenArray.filter((c) => React.isValidElement(c) && c.type === SwiperOverlay)
  const slides = childrenArray.filter((c) => React.isValidElement(c) && c.type !== SwiperOverlay)
  const [swiperInstance, setSwiperInstance] = useState<any | null>(null)
  const [activeIndex, setActiveIndex] = useState(0)

  // Local state for non-synced swipers
  const [localSelectedIndex, setLocalSelectedIndex] = useState(0)

  const totalSlides = slides.length

  // Connect to the sync store if a sync ID is provided
  const syncStore = useSwiperSyncStore()

  // Register this swiper with the sync store when mounted
  useEffect(() => {
    if (sync) {
      syncStore.registerSyncGroup(sync)
    }
  }, [sync, syncStore])

  // Get the selected index from either the sync store or local state
  const selectedIndex = useMemo(() => {
    if (sync && syncStore.syncGroups[sync]) {
      return Math.min(syncStore.syncGroups[sync].selectedIndex, totalSlides - 1)
    }
    return localSelectedIndex
  }, [sync, syncStore.syncGroups, localSelectedIndex, totalSlides])

  // Update active index when slide changes
  useEffect(() => {
    if (swiperInstance) {
      const handleSlideChange = () => {
        const newIndex =
          swiperInstance.activeIndex < activeIndex && totalSlides - activeIndex < 3
            ? activeIndex - 1
            : //  : swiperInstance.activeIndex > activeIndex
              //   ? activeIndex + 1
              swiperInstance.activeIndex
        setActiveIndex(newIndex)

        // When the swiper changes by user interaction, update the selection
        if (sync) {
          syncStore.selectSlide(sync, newIndex)
        } else {
          setLocalSelectedIndex(newIndex)
        }
      }

      swiperInstance.on('slideChange', handleSlideChange)

      return () => {
        swiperInstance.off('slideChange', handleSlideChange)
      }
    }
  }, [activeIndex, totalSlides, swiperInstance, sync, syncStore])

  // When selected index changes from sync store, update the swiper position
  useEffect(() => {
    if (swiperInstance && selectedIndex !== activeIndex) {
      swiperInstance.slideTo(selectedIndex)
    }
  }, [selectedIndex, activeIndex, swiperInstance])

  // Function to select a specific slide
  const selectSlide = useCallback(
    (index: number) => {
      if (index < 0 || index >= totalSlides) return

      if (sync) {
        syncStore.selectSlide(sync, index)
      } else {
        setLocalSelectedIndex(index)
      }
    },
    [totalSlides, sync, syncStore]
  )

  // Function to select the next slide
  const selectNextSlide = useCallback(() => {
    if (sync) {
      syncStore.selectNextSlide(sync, totalSlides)
    } else {
      const nextIndex = Math.min(localSelectedIndex + 1, totalSlides - 1)
      setLocalSelectedIndex(nextIndex)
    }
  }, [localSelectedIndex, totalSlides, sync, syncStore])

  // Function to select the previous slide
  const selectPrevSlide = useCallback(() => {
    if (sync) {
      syncStore.selectPrevSlide(sync)
    } else {
      const prevIndex = Math.max(localSelectedIndex - 1, 0)
      setLocalSelectedIndex(prevIndex)
    }
  }, [localSelectedIndex, sync, syncStore])

  // Function to check if a slide is selected
  const isSelected = useCallback(
    (index: number) => {
      return index === selectedIndex
    },
    [selectedIndex]
  )

  // Create context value
  const contextValue = useMemo(
    () => ({
      swiper: swiperInstance,
      direction,
      selectedIndex,
      totalSlides,
      selectNextSlide,
      selectPrevSlide,
      selectSlide,
      isSelected,
    }),
    [swiperInstance, direction, selectedIndex, totalSlides, selectNextSlide, selectPrevSlide, selectSlide, isSelected]
  )
  const { defaultValue, breakpoints } = useMemo(() => {
    return parseResponsiveBreakpoints(slidesPerView)
  }, [slidesPerView])
  return (
    <div className="relative">
      <div ref={wrapperRef} className="absolute left-0 top-0 w-full h-full overflow-visible pointer-events-none z-10" />

      <ReactSwiper
        direction={direction}
        className={cn('relative w-full h-full py-8 overflow-visible flex', className)}
        breakpointsBase="window"
        spaceBetween={8}
        grabCursor={true}
        watchOverflow={true}
        slidesPerView={defaultValue}
        slidesPerGroup={1}
        autoHeight={false}
        breakpoints={breakpoints}
        onSwiper={setSwiperInstance}
      >
        {swiperWrapperChildren.length > 0 && (
          <WrapperPortal targetRef={wrapperRef} contextValue={contextValue}>
            {swiperWrapperChildren}
          </WrapperPortal>
        )}

        {slides.map(
          (child, index) =>
            swiperInstance &&
            swiperInstance.initialized && (
              <SwiperSlide
                key={index}
                className={cn(
                  'w-auto h-auto',
                  'overflow-visible flex flex-col w-auto',
                  direction === 'horizontal' ? 'py-2' : '',
                  slideClassName
                )}
                // style={{
                //   width: 'auto',
                //   height: 'auto',
                // }}
              >
                {React.cloneElement(child as ReactElement<any>, {
                  className: cn(
                    (child as ReactElement<any>).props.className,
                    selectedIndex === index ? currentSlideClassName : ''
                  ),
                  onClick: () => selectSlide(index),
                })}
              </SwiperSlide>
            )
        )}
      </ReactSwiper>
    </div>
  )
}

export const SwiperOverlay = ({ className, children }: { className?: string; children: ReactNode }) => {
  const processedChildren = React.Children.map(children, (child) =>
    React.isValidElement(child)
      ? React.cloneElement(child as ReactElement<any>, {
          className: cn('pointer-events-auto', (child.props as any).className || ''),
        })
      : child
  )

  return <div className={cn('w-full h-full overflow-visible relative', className)}>{processedChildren}</div>
}

export const SwiperPrevArrow = ({ className, children }: { className?: string; children?: ReactNode }) => {
  const context = React.useContext(SwiperContext)
  if (!context) return null

  const { selectPrevSlide, selectedIndex } = context

  return selectedIndex > 0 ? (
    <div onClick={selectPrevSlide} className={cn('pointer-events-auto z-10', className)}>
      {children}
    </div>
  ) : null
}

export const SwiperNextArrow = ({ className, children }: { className?: string; children?: ReactNode }) => {
  const context = React.useContext(SwiperContext)
  if (!context) return null

  const { selectNextSlide, selectedIndex, totalSlides } = context

  return selectedIndex < totalSlides - 1 ? (
    <div onClick={selectNextSlide} className={cn('pointer-events-auto z-10', className)}>
      {children}
    </div>
  ) : null
}
function parseResponsiveBreakpoints(slidesPerViewStr: string) {
  // Function to get breakpoint value safely
  function getBreakpointValue(key: string): number | undefined {
    switch (key) {
      case 'sm':
        return 640
      case 'md':
        return 768
      case 'lg':
        return 1024
      case 'xl':
        return 1280
      case '2xl':
        return 1536
      default:
        return undefined
    }
  }

  const breakpoints: Record<number, { slidesPerView: number | 'auto' }> = {}
  let defaultValue: number | 'auto' = 'auto'

  // Split by space to get individual breakpoint settings
  const parts = slidesPerViewStr.trim().split(/\s+/)
  parts.forEach((part) => {
    // Check if this part contains a breakpoint
    if (part.includes(':')) {
      const [breakpoint, value] = part.split(':')
      const breakpointValue = getBreakpointValue(breakpoint)

      if (breakpointValue !== undefined) {
        breakpoints[breakpointValue] = {
          slidesPerView: value === 'auto' ? 'auto' : parseFloat(value),
        }
      }
    } else {
      // This is the default value (no breakpoint prefix)
      defaultValue = part === 'auto' ? 'auto' : parseFloat(part)
    }
  })

  return { defaultValue, breakpoints }
}
