'use client'

import React, { useState, ReactElement } from 'react'

import './styles.css'
import Img from '@atoms/Img'
import { cn } from '@components/lib/utils'
import { GalleryImage, VideoFragment } from '@lib/_generated/graphql_sdk'
import { LucideX } from 'lucide-react'
import ReactD<PERSON> from 'react-dom'
import { Swiper, SwiperNextArrow, SwiperPrevArrow, SwiperOverlay } from './_Swiper'
import { Button } from '@components/theme/ui/button'
import OnTap from '../../atoms/OnTap'
import Image from 'next/image'
import dynamic from 'next/dynamic'

interface ProductPhotoGalleryProps {
  image: GalleryImage['image']
  images: GalleryImage[]
  videos?: VideoFragment[] | null
  galleryID?: string
  showThumbnails: boolean
  thumbnailSize?: number
  imageWidth?: number
  imageHeight?: number
  cardContainerClass?: string
  mainImageClass?: string
  mainImageContainerClass?: string
  thumbnailsContainerClass?: string
  thumbnailClass?: string
  prevButton?: ReactElement<{ onClick: () => void; className?: string }>
  nextButton?: ReactElement<{ onClick: () => void; className?: string }>
}

export const ProductPhotoGallery = ({
  image,
  images,
  videos,
  galleryID = 'photo-gallery',
  showThumbnails = true,
  thumbnailSize = 100,
  imageWidth = 800,
  imageHeight = 800,
  cardContainerClass,
  mainImageContainerClass = 'w-full mb-4',
  thumbnailsContainerClass = 'flex gap-2',
  mainImageClass = '',
  thumbnailClass = '',
  prevButton: PrevButton = <></>,
  nextButton: NextButton = <></>,
}: ProductPhotoGalleryProps) => {
  const [lightboxOpen, setLightboxOpen] = useState(false)

  const clickHandler = (e: any) => {
    if (images.length === 0) return
    e.preventDefault()
    setLightboxOpen(true)
  }

  return (
    <div className="relative w-full ">
      <div className="relative -top-2 ">
        <div className={cn('relative w-full mx-auto overflow-visible ')}>
          {/* Main Image Container */}
          <div className="relative w-full aspect-square ">
            <Swiper slidesPerView="1" sync="product_gallery" className="w-full " slideClassName="w-full">
              <SwiperOverlay className="md:hidden">
                <SwiperPrevArrow className="absolute -bottom-[45px] left-[calc(50%-65px)] transform -translate-y-1/2 ">
                  {PrevButton}
                </SwiperPrevArrow>
                <SwiperNextArrow className="absolute -bottom-[45px] right-[calc(50%-65px)] transform -translate-y-1/2 ">
                  {NextButton}
                </SwiperNextArrow>
              </SwiperOverlay>
              <SwiperOverlay className="hidden md:block">
                <SwiperPrevArrow className="absolute top-1/2 -left-3 transform -translate-y-1/2 ">
                  {PrevButton}
                </SwiperPrevArrow>
                <SwiperNextArrow className="absolute top-1/2 -right-3 transform -translate-y-1/2 ">
                  {NextButton}
                </SwiperNextArrow>
              </SwiperOverlay>
              {videos &&
                videos.length > 0 &&
                videos.map((video) => (
                  <div
                    key={video.id}
                    className="w-full aspect-square rounded-t-none rounded-b-3xl lg:rounded-t-3xl overflow-hidden flex items-center justify-center bg-white "
                  >
                    <iframe
                      src={`https://www.youtube.com/embed/${video.url}`}
                      className="w-full h-full"
                      allowFullScreen
                    />
                  </div>
                ))}
              {images.map((image, index) => (
                <OnTap key={`lightbox-thumb-${index}`} onTap={clickHandler}>
                  <div
                    className="w-full aspect-square rounded-t-none rounded-b-3xl lg:rounded-t-3xl overflow-hidden flex items-center justify-center bg-white "
                    onClick={clickHandler}
                  >
                    <Image
                      src={image.image.src}
                      alt={image.image.alt || ''}
                      width={500}
                      height={500}
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                </OnTap>
              ))}
            </Swiper>
          </div>
        </div>
        {showThumbnails && (
          <div className="hidden md:block">
            {/* Thumbnails */}
            <Swiper
              slidesPerView="3"
              sync="product_gallery"
              className=""
              currentSlideClassName="border-2 border-primary"
            >
              {videos &&
                videos.length > 0 &&
                videos.map((video) => (
                  <div
                    key={`lightbox-thumb-${video.id}`}
                    className={cn(
                      'flex-shrink-0 w-auto aspect-square  relative rounded-3xl border-2 transition-all duration-200 bg-white'
                    )}
                  >
                    <Img
                      src={`https://img.youtube.com/vi/${video.url}/maxresdefault.jpg`}
                      alt={video.description || video.title || ''}
                      fill
                      className="rounded-3xl object-cover"
                    />
                  </div>
                ))}
              {images.length > 1 &&
                images.map((image, index) => (
                  <div
                    key={`lightbox-thumb-${index}`}
                    className={cn(
                      'flex-shrink-0 w-auto aspect-square  relative rounded-3xl border-2 transition-all duration-200 bg-white'
                    )}
                  >
                    <Img src={image.image.src} alt={image.image.alt || ''} fill className="rounded-3xl object-cover" />
                  </div>
                ))}
            </Swiper>
          </div>
        )}
      </div>

      {/* Lightbox Component */}
      <Lightbox
        images={images}
        videos={videos || []}
        isOpen={lightboxOpen}
        onClose={() => setLightboxOpen(false)}
        prevButton={PrevButton}
        nextButton={NextButton}
      />
    </div>
  )
}

interface LightboxProps {
  images: GalleryImage[]
  videos: VideoFragment[]
  isOpen: boolean
  onClose: () => void
  prevButton?: ReactElement<{ onClick: () => void; className?: string }>
  nextButton?: ReactElement<{ onClick: () => void; className?: string }>
}

const Lightbox = dynamic(
  () =>
    Promise.resolve(({ images, videos, isOpen, onClose, prevButton: PrevButton = <></>, nextButton: NextButton = <>

        </> }: LightboxProps) => {
      return ReactDOM.createPortal(
        <div
          className={cn('fixed inset-0 z-[9999] flex items-center justify-center bg-black/80', !isOpen && 'hidden')}
          onClick={onClose}
        >
          <div
            className="relative w-full max-w-[400px] md:max-w-[60vw] lg:w-[50vw] xl:max-w-[40vw] mx-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Main Image Container */}
            <div className="w-full aspect-square mb-4">
              <Swiper slidesPerView="1" sync="product_gallery">
                <SwiperOverlay className="">
                  <SwiperPrevArrow className="lg:hidden absolute top-1/2 -left-3 transform -translate-y-1/2 ">
                    {PrevButton}
                  </SwiperPrevArrow>
                  <SwiperNextArrow className="lg:hidden absolute top-1/2 -right-3 transform -translate-y-1/2 ">
                    {NextButton}
                  </SwiperNextArrow>
                  <Button
                    variant="secondary"
                    className="absolute top-8 right-4 text-primary aspect-square p-0"
                    onClick={() => onClose()}
                  >
                    <LucideX />
                  </Button>
                </SwiperOverlay>
                {videos.map((video) => (
                  <div key={video.id} className={cn('w-full aspect-square rounded-3xl border-2 bg-white')}>
                    <iframe
                      src={`https://www.youtube.com/embed/${video.url}`}
                      className="w-full h-full rounded-3xl"
                      allowFullScreen
                    />
                  </div>
                ))}
                {images.map((image, index) => (
                  <div
                    key={`lightbox-thumb-${index}`}
                    className={cn('relative w-full aspect-square rounded-3xl bg-white')}
                  >
                    <Img
                      src={image.image.src}
                      alt={image.image.alt || ''}
                      fill
                      className="object-fill aspect-square rounded-3xl p-0.5"
                    />
                  </div>
                ))}
              </Swiper>
            </div>

            {/* Thumbnails */}
            <div className="relative w-full flex justify-center ">
              <div className=" relative w-full lg:w-[80%] pl-2">
                {images.length > 1 && (
                  <Swiper
                    slidesPerView="2.5 md:3"
                    sync="product_gallery"
                    className=""
                    currentSlideClassName="border-2 border-primary"
                  >
                    <SwiperOverlay className="hidden lg:block">
                      <SwiperPrevArrow className="absolute top-1/2 -left-14 transform -translate-y-1/2 scale-75">
                        {PrevButton}
                      </SwiperPrevArrow>
                      <SwiperNextArrow className="absolute top-1/2 -right-14 transform -translate-y-1/2 scale-75">
                        {NextButton}
                      </SwiperNextArrow>
                    </SwiperOverlay>
                    {videos.map((video) => (
                      <div
                        key={`lightbox-thumb-${video.id}`}
                        className={cn(
                          'flex-shrink-0 w-auto aspect-square  relative rounded-3xl border-2 transition-all duration-200 bg-white'
                        )}
                      >
                        <Img
                          src={`https://img.youtube.com/vi/${video.url}/maxresdefault.jpg`}
                          alt={video.description || ''}
                          fill
                          className="rounded-3xl object-cover"
                        />
                      </div>
                    ))}
                    {images.map((image, index) => (
                      <div
                        key={`lightbox-thumb-${index}`}
                        className={cn(
                          'flex-shrink-0 w-auto aspect-square  relative rounded-3xl border-2 transition-all duration-200 bg-white'
                        )}
                      >
                        <Img
                          src={image.image.src}
                          alt={image.image.alt || ''}
                          fill
                          className="rounded-3xl object-cover"
                        />
                      </div>
                    ))}
                  </Swiper>
                )}
              </div>
            </div>
          </div>
        </div>,
        document.body
      )
    }),
  { ssr: false }
)

export default ProductPhotoGallery
