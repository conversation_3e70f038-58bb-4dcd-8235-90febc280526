'use client'

import React, {
  useEffect,
  useRef,
  useState,
  cloneElement,
  ReactElement,
  JSXElementConstructor,
  isValidElement,
} from 'react'

import useBreakpoint from '@/src/hooks/useBreakpoint'

interface StickyWrapperProps {
  children: React.ReactNode
  offset?: number
  className?: string
  footerId?: string
  allowOnBreakpoints?: string[]
}

const StickyWrapper: React.FC<StickyWrapperProps> = ({
  children,
  offset = 10,
  className,
  footerId,
  allowOnBreakpoints = [],
}) => {
  const ref = useRef<HTMLDivElement | null>(null)
  const [isSticky, setIsSticky] = useState(false)
  const [clone, setClone] = useState<ReactElement<any, string | JSXElementConstructor<any>> | null>(null)
  const [maxTop, setMaxTop] = useState(offset)
  const [stickyStyles, setStickyStyles] = useState<{ left: number; width: number | string }>({ left: 0, width: 'auto' })
  const { breakpoint } = useBreakpoint()

  useEffect(() => {
    const handleScroll = () => {
      if (allowOnBreakpoints.length > 0 && breakpoint && !allowOnBreakpoints.includes(breakpoint)) return
      if (!ref.current) return
      const rect = ref.current.getBoundingClientRect()
      const footer = footerId ? document.getElementById(footerId) : null
      const footerRect = footer ? footer.getBoundingClientRect() : null
      const footerTop = footerRect ? footerRect.top : Infinity

      if (rect.top <= offset && !isSticky) {
        setIsSticky(true)
        if (isValidElement(children)) {
          setClone(cloneElement(children))
        }
        setStickyStyles({ left: rect.left, width: rect.width })
      } else if (rect.top > offset && isSticky) {
        setIsSticky(false)
        setClone(null)
      }

      setMaxTop(Math.min(offset, footerTop - rect.height))
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [offset, isSticky, children, footerId, breakpoint, allowOnBreakpoints])

  return (
    <>
      <div ref={ref} className={className} style={{ visibility: isSticky ? 'hidden' : 'visible' }}>
        {children}
      </div>
      {isSticky && clone && (
        <div
          style={{
            position: 'fixed',
            top: `${maxTop}px`,
            left: `${stickyStyles.left}px`,
            width: `${stickyStyles.width}px`,
            zIndex: 20,
          }}
          className={className}
        >
          {clone}
        </div>
      )}
    </>
  )
}

export default StickyWrapper
