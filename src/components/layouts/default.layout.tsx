import React from 'react'

import Footer from '@/src/app/(store)/_components/Footer/Footer'
import Header from '@components/organisms/Header/Header'
import Newsletter from '@components/organisms/Newsletter/Newsletter'
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'

// Enables Swiper mobile breakpoints
import 'swiper/swiper-bundle.css'
import { Providers } from '@components/providers/Providers'

export async function DefaultLayout({ children }: { children: React.ReactNode }) {
  return (
    <Providers>
      <div className="flex flex-col mx-auto">
        <Header />
        <main>
          {children}
          <Newsletter />
        </main>
        <Footer />
      </div>
    </Providers>
  )
}
