import React from 'react'

import Footer from '@/src/app/(store)/_components/Footer/Footer'
import Static from '@/src/features/static'
import { CheckoutHeader } from '@components/organisms/CheckoutHeader/CheckoutHeader'
import { Providers } from '@components/providers/Providers'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'

export async function CheckoutLayout({ children }: { children: React.ReactNode }) {
  return (
    <Providers>
      <div className="flex flex-col max-w-screen-6xl mx-auto h-screen">
        <CheckoutHeader />
        <main className="flex-1">{children}</main>
        <RelevaTrackPage />
        <Static component={Footer} />
      </div>
    </Providers>
  )
}
