export const scrollToElement = (elementId: string, offset = 0) => {
  const targetElement = document.getElementById(elementId)
  const header = document.querySelector('header')
  const headerOffset = header ? header.offsetHeight : 0
  const elementPosition = targetElement ? targetElement.getBoundingClientRect().top + offset : 0
  const offsetPosition = elementPosition + window.scrollY - headerOffset

  window.scrollTo({
    top: offsetPosition,
    behavior: 'smooth',
  })
}
