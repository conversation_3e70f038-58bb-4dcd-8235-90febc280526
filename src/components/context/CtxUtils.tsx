import React, { createContext, useContext } from 'react'

interface DefaultContextProvider<T> {
  value: T
  children: React.ReactNode
}

export function createRequiredContext<ContextValue>(err?: string) {
  const Context = createContext<ContextValue | null>(null)

  const useRequiredContext = () => {
    const ctx = useContext(Context)

    if (ctx === null) {
      throw new Error(err ?? 'Context must be used within a Provider')
    }

    return ctx
  }

  const Provider: React.FC<DefaultContextProvider<ContextValue>> = ({ children, value }) => {
    return <Context.Provider value={value}>{children}</Context.Provider>
  }

  return [Provider, useRequiredContext] as const
}

export function createOptionalContext<ContextValue>(initialValue: ContextValue | null = null) {
  const Context = createContext<ContextValue | null>(initialValue)

  const useOptionalContext = () => useContext(Context)

  const Provider: React.FC<DefaultContextProvider<ContextValue>> = ({ children, value }) => (
    <Context.Provider value={value}>{children}</Context.Provider>
  )

  return [Provider, useOptionalContext] as const
}
