'use client'
import React, { ReactNode } from 'react'

import { ToggleHook, useToggleHook } from '@/src/hooks/UiHooks'
import { createRequiredContext } from '@context/CtxUtils'

interface MenuContextValue extends ToggleHook {}
const [SimpleMenuContext, useSimpleMenuContext] = createRequiredContext<MenuContextValue>(
  'Simple menu context not initialized'
)

interface DropdownProps {
  children: ReactNode
  closeOnEscape?: boolean
  defaultIsOpen?: boolean
}

const SimpleMenu: React.FC<DropdownProps> = ({ children, closeOnEscape, defaultIsOpen }) => {
  const toggle = useToggleHook({
    closeOnEscape: !!closeOnEscape,
    defaultIsOpen,
  })

  return <SimpleMenuContext value={toggle}>{children}</SimpleMenuContext>
}

export default SimpleMenu
export { useSimpleMenuContext }
