import React from 'react'

import { cn } from '@components/lib/utils'
import { ClassName } from '@lib/types/ClassName'

type CalloutVariant = 'default' | 'info' | 'warning' | 'error' | 'success'

interface CalloutProps {
  children: React.ReactNode
  variant?: CalloutVariant
  title?: string
  icon?: React.ComponentType<React.SVGProps<SVGSVGElement>> | React.ReactNode
  className?: ClassName
}

interface VariantStyle {
  border: string
  background: string
  text: string
  icon: string
}

export const Callout: React.FC<CalloutProps> = ({ children, variant = 'default', title, icon, className }) => {
  // Define styles based on variant
  const variantStyles: Record<CalloutVariant, VariantStyle> = {
    default: {
      border: 'border-gray-200',
      background: 'bg-gray-50',
      text: 'text-gray-800',
      icon: 'text-gray-500',
    },
    info: {
      border: 'border-blue-200',
      background: 'bg-blue-50',
      text: 'text-blue-800',
      icon: 'text-blue-500',
    },
    warning: {
      border: 'border-yellow-200',
      background: 'bg-yellow-50',
      text: 'text-yellow-800',
      icon: 'text-yellow-500',
    },
    error: {
      border: 'border-red-200',
      background: 'bg-red-50',
      text: 'text-red-800',
      icon: 'text-red-500',
    },
    success: {
      border: 'border-green-200',
      background: 'bg-green-50',
      text: 'text-green-800',
      icon: 'text-green-500',
    },
  }

  // Default icons for each variant (you can override these with the icon prop)
  const defaultIcons: Record<CalloutVariant, React.ComponentType<React.SVGProps<SVGSVGElement>>> = {
    default: (props) => (
      <svg
        className="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    ),
    info: (props) => (
      <svg
        className="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    ),
    warning: (props) => (
      <svg
        className="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
        />
      </svg>
    ),
    error: (props) => (
      <svg
        className="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    ),
    success: (props) => (
      <svg
        className="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    ),
  }

  // Get styles for current variant
  const styles = variantStyles[variant] || variantStyles.default

  // Get icon for current variant
  const IconComponent = icon || defaultIcons[variant] || defaultIcons.default

  return (
    <div className={cn(`rounded-lg border ${styles.border} ${styles.background} p-4 mb-4`, className)}>
      <div className="flex items-start">
        <div className={`flex-shrink-0 mr-3 ${styles.icon}`}>
          {React.isValidElement(IconComponent) ? (
            IconComponent
          ) : typeof IconComponent === 'function' ? (
            <IconComponent />
          ) : (
            IconComponent
          )}
        </div>
        <div>
          {title && <h3 className={`text-sm font-medium ${styles.text} mb-1`}>{title}</h3>}
          <div className={`text-sm ${styles.text}`}>{children}</div>
        </div>
      </div>
    </div>
  )
}
