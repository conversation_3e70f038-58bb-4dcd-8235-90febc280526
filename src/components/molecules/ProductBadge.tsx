import Image, { ImageProps } from 'next/image'
import { cn } from '../lib/utils'

export type BadgeType =
  | 'free-transport'
  | 'online-promotion'
  | 'product-with-gift'
  | 'tile-quality-1'
  | 'tile-quality-2'
  | 'tile-quality-3'
  | 'online-price'
  | 'palletizing'
  | 'wholesale-price'
  | 'buy-cheap'
  | `warranty-${number}-months`

interface ProductBadgeProps extends Omit<ImageProps, 'src' | 'alt'> {
  type: BadgeType
  width?: number
  height?: number
  alt?: string
}

export const ProductBadge: React.FC<ProductBadgeProps> = ({ type, width = 76, height = 76, ...imageProps }) => {
  const warrantyMonths = type.match(/warranty-(\d+)-months/)?.at(1)
  const iconSrc = warrantyMonths ? '/icons/badges/warranty-X-months.svg' : `/icons/badges/${type}.svg`

  return (
    <div className="relative inline-block" style={{ width, height }}>
      <Image {...imageProps} src={iconSrc} width={width} height={height} alt={imageProps.alt || type} />
      {warrantyMonths && (
        <span
          className={cn(
            'absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-primary text-xl',
            Number(warrantyMonths) >= 100 && 'text-lg'
          )}
        >
          {warrantyMonths}
        </span>
      )}
    </div>
  )
}
