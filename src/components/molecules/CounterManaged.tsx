import React, { useCallback, useState } from 'react'
import { useDebounceCallback } from 'usehooks-ts'

import { Counter, CounterInputType, CounterProps } from '@components/molecules/Counter'
import { useCartStore } from '@features/cart/cart-state'
import { Product } from '@lib/_generated/graphql_sdk'
import { showErrorToast } from '@lib/utils/toaster'

interface CounterManagedProps extends Omit<CounterProps, 'onChangeAction'> {
  sku: Product['sku']
}

export const CounterManaged: React.FC<CounterManagedProps> = ({ sku, max, value, ...props }) => {
  const cartStore = useCartStore()
  const [loading, setLoading] = useState<CounterInputType | null>(null)

  const handleChange = useDebounceCallback(async (value: number, type: CounterInputType) => {
    setLoading(type)
    if (value > 0) {
      await cartStore.updateItem(sku, value)
    } else {
      await cartStore.removeItem(sku)
    }
    setLoading(null)
  }, 300)

  return (
    <Counter
      {...props}
      value={value}
      max={max}
      onChangeAction={handleChange}
      decLoading={loading === 'minus' || loading === 'input'}
      incLoading={loading === 'plus' || loading === 'input'}
    />
  )
}
