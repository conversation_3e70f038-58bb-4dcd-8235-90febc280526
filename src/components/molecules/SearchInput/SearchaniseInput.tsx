'use client'
import Script from 'next/script'
import React, { useEffect } from 'react'

import SearchIcon from '@atoms/Icons/SearchIcon'

interface Props {
  className?: string
}

declare global {
  interface Window {
    Searchanise: any
  }
}

const SearchaniseInput: React.FC<Props> = (props) => {
  useEffect(() => {
    window.Searchanise = {}
    window.Searchanise.host = 'http://www.searchanise.com'
    window.Searchanise.api_key = process.env.NEXT_PUBLIC_SEARCHANISE_API_KEY
    window.Searchanise.SearchInput = '#search'

    window.Searchanise.AutoCmpParams = {}
    window.Searchanise.AutoCmpParams.union = {}
    window.Searchanise.AutoCmpParams.union.price = {}
    window.Searchanise.AutoCmpParams.union.price.min = 'se_price_0'

    window.Searchanise.AutoCmpParams.restrictBy = {}
    window.Searchanise.AutoCmpParams.restrictBy.status = '1'
    window.Searchanise.AutoCmpParams.restrictBy.visibility = '3|4'
    window.Searchanise.AutoCmpParams.restrictBy.is_in_stock = '1'
    window.Searchanise.options = {}
    window.Searchanise.AdditionalSearchInputs = '#name,#description,#sku'

    window.Searchanise.options.ResultsDiv = '#snize_results'
    window.Searchanise.options.ResultsFormPath = process.env.NEXT_PUBLIC_SEARCHANISE_RESULT_URL
    window.Searchanise.options.ResultsFallbackUrl = process.env.NEXT_PUBLIC_SEARCHANISE_RESULT_URL + '/"?q='
    window.Searchanise.ResultsParams = {}
    window.Searchanise.ResultsParams.facetBy = {}
    window.Searchanise.ResultsParams.facetBy.price = {}
    window.Searchanise.ResultsParams.facetBy.price.type = 'slider'
    window.Searchanise.ResultsParams.union = {}
    window.Searchanise.ResultsParams.union.price = {}
    window.Searchanise.ResultsParams.union.price.min = 'se_price_0'

    window.Searchanise.ResultsParams.restrictBy = {}
    window.Searchanise.ResultsParams.restrictBy.status = '1'
    window.Searchanise.ResultsParams.restrictBy.visibility = '3|4'
    window.Searchanise.ResultsParams.restrictBy.is_in_stock = '1'
    window.Searchanise.options.PriceFormat = {
      decimals_separator: ',',
      thousands_separator: ' ',
      symbol: ' лв.',

      decimals: '2',
      rate: '1',
      after: true,
    }
    //]]>
  }, [])
  return (
    <>
      <Script src="https://www.searchanise.com/widgets/v1.0/init.js" defer={true} async={true} strategy="lazyOnload" />
      <div className="relative text-paragraph overflow-visible" style={{ zIndex: 700 }}>
        <input
          name={'search'}
          id={'search'}
          className="rounded-md px-8 w-full h-[34px]"
          placeholder={'Търсене по ключова дума или марка'}
        />
        <SearchIcon className="absolute right-4 top-[5px]" />
      </div>
    </>
  )
}

export default SearchaniseInput
