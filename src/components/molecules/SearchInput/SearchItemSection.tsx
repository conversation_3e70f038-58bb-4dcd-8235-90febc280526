import React from 'react'

import Text from '@atoms/Text'
import { addClasses } from '@lib/style/style'

type SectionProps = {
  title?: string
  isGrid?: boolean
  children: React.ReactNode | React.ReactNode[]
}
const SearchItemSection: React.FC<SectionProps> = ({ title, children, isGrid }) => {
  return (
    <div className="flex flex-col mb-4">
      {title && (
        <div className="bg-primary text-white px-6 py-2 rounded-t-md">
          <Text size={'medium'} className="uppercase font-bold">
            {title}
          </Text>
        </div>
      )}
      <div className={addClasses('flex flex-col items-start justify-start', !isGrid && 'p-4')}>{children}</div>
    </div>
  )
}

export default SearchItemSection
