'use client'

import { Check, ChevronsUpDown, LucideLoaderCircle } from 'lucide-react'
import * as React from 'react'
import { ForwardedRef, forwardRef, useEffect, useMemo, useState } from 'react'

import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@components/theme/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@components/theme/ui/popover'
import { ClassName } from '@lib/types/ClassName'

// Define a more precise constraint for the generic type
export interface ComboBoxProps<T> {
  name: string
  data: T[]
  minLetters?: number
  // Ensure these are strings or numbers only
  labelProp?: string
  valueProp?: string
  placeholder?: string
  renderLabel?: (item: T) => React.ReactNode
  filterFn?: (item: T, keyword: string) => boolean
  loading?: boolean
  disabled?: boolean
  loadingLabel?: string
  showFullList?: boolean
  className?: ClassName
  // Form props
  onBlur?: () => void
  // Make onChange accept the correct type based on the valueProp
  onChange?: (value: any) => void
  value?: T
}

function ComboBoxBase<T extends Record<string, any>>(
  {
    name,
    data,
    minLetters = 3,
    valueProp = 'value',
    labelProp = 'label',
    placeholder,
    renderLabel,
    filterFn,
    loading,
    disabled,
    loadingLabel,
    showFullList,
    className,
    // Form props
    onBlur,
    onChange,
    value,
    ...restProps
  }: ComboBoxProps<T>,
  ref: ForwardedRef<HTMLInputElement>
) {
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState('')
  const [selectedElement, setSelectedElement] = useState<T | undefined>(undefined)

  useEffect(() => {
    // if (value) {
    const initialSelectedElement = data.find((item) => item[valueProp] === value)
    setSelectedElement(initialSelectedElement)
    // }
  }, [value, data, valueProp])

  const shouldApplyFilter = search.length >= minLetters
  const filteredBySearch = shouldApplyFilter
    ? data.filter((item) => {
        const labelValue = item[labelProp]
        const labelString = labelValue != null ? String(labelValue) : ''
        return filterFn?.(item, search) || labelString.toLowerCase().includes(search.toLowerCase())
      })
    : data

  const filteredData = shouldApplyFilter ? filteredBySearch : data

  return (
    <Popover
      open={open}
      onOpenChange={(state) => {
        if (!state) {
          console.log(name, 'onOpenChange > onBlur')
          if (value !== selectedElement?.[valueProp]) {
            console.log(name, 'onOpenChange > onBlur > Call')
            onBlur?.()
          }
        }
        setOpen(state)
      }}
    >
      <PopoverTrigger asChild disabled={!!loading || !!disabled}>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn('w-full justify-between text-xs', className)}
        >
          {loading && !disabled ? (
            <>
              {loadingLabel || 'Зареждане...'}
              <LucideLoaderCircle className="animate-spin" />
            </>
          ) : (
            <>
              {selectedElement
                ? (renderLabel && renderLabel(selectedElement)) || String(selectedElement[labelProp])
                : placeholder || 'Изберете...'}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 min-w-[var(--radix-popover-trigger-width)] bg-white p-0">
        <Command filter={() => 1}>
          <CommandInput value={search} onValueChange={setSearch} placeholder="Search..." ref={ref} />
          <CommandList>
            {!showFullList && search.length < minLetters ? (
              <CommandEmpty>Въведете поне {minLetters} символа...</CommandEmpty>
            ) : filteredData.length === 0 ? (
              <CommandEmpty>Няма намерени резултати.</CommandEmpty>
            ) : (
              <CommandGroup heading="Results">
                {filteredData.map((item) => {
                  // Safely convert to string for the key and value props
                  const itemValue = item[valueProp]
                  const stringValue = String(itemValue)

                  return (
                    <CommandItem
                      key={stringValue}
                      value={stringValue}
                      onSelect={(currentValue) => {
                        let typedValue: number = parseInt(currentValue, 10)
                        if (typedValue !== selectedElement?.[valueProp]) {
                          const newSelectedElement = filteredData.find(
                            (item) => String(item[valueProp]) === String(typedValue)
                          )
                          if (newSelectedElement) {
                            setOpen(false)

                            const returnValue = valueProp ? newSelectedElement[valueProp] : newSelectedElement
                            onChange?.(returnValue)
                            onBlur?.()
                          }
                        }
                      }}
                    >
                      <Check
                        className={cn(
                          'mr-2 h-4 w-4',
                          selectedElement?.[valueProp] === item[valueProp] ? 'opacity-100' : 'opacity-0'
                        )}
                      />
                      {(renderLabel && renderLabel(item)) || String(item[labelProp])}
                    </CommandItem>
                  )
                })}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

type ComboBoxType = <T extends Record<string, any>>(
  props: ComboBoxProps<T> & { ref?: React.ForwardedRef<HTMLInputElement> }
) => React.ReactElement

export const ComboBox = forwardRef(ComboBoxBase) as ComboBoxType

ComboBoxBase.displayName = 'ComboBox'
