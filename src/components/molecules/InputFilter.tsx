'use client'
import React, { useEffect, useRef, useState } from 'react'

import Paper from '@atoms/Paper'

interface Props {
  options: string[]
  onSelect: (option: string) => void
}

const InputFilter: React.FC<Props> = ({ options, onSelect }) => {
  const [filteredOptions, setFilteredOptions] = useState<string[]>(options)
  const inputRef = useRef<HTMLInputElement>(null)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value

    const filtered = options.filter((option) => option.toLowerCase().includes(value.toLowerCase()))
    setFilteredOptions(filtered)
  }

  const handleOptionClick = (option: string) => {
    onSelect(option)
  }

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  return (
    <div className="relative w-full rounded-md text-black h-[43px] leading-9 cursor-pointer">
      <Paper className="z-10 w-full p-0 pb-4">
        <div className="p-4">
          <input
            type="text"
            ref={inputRef}
            className="p-2 px-4 border border-gray-300 rounded-md w-full h-[40px] text-base outline-none"
            onChange={handleInputChange}
          />
        </div>

        <div>
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option, index) => (
              <div
                key={index}
                className="py-2 px-6 hover:bg-gray-200 cursor-pointer text-black font-normal text-base"
                onClick={() => handleOptionClick(option)}
              >
                {option}
              </div>
            ))
          ) : (
            <div className="px-6 text-gray-500 text-base">Няма намерени резултати</div>
          )}
        </div>
      </Paper>
    </div>
  )
}

export default InputFilter
