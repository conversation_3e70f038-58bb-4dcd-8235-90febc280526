import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import * as React from 'react'

import { cn } from '@components/lib/utils'
import { ClassName } from '@lib/types/ClassName'

const buttonVariants = cva(
  // !! separates the custom class names with the default ones
  'group inline-flex flex-col items-center justify-center gap-1 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: '',
        destructive: '',
        outline: '',
        secondary: '',
        tertiary: '',
        ghost: '',
        link: '',
        inverse: '',
      },
      size: {
        default: '',
        sm: '',
        lg: '',
        icon: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

const iconVariants = cva(
  // !! separates the custom class names with the default ones
  'inline-flex flex-col items-center justify-center rounded-full aspect-square transition-colors aspect-square [&_svg]:shrink-0',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground shadow group-hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground shadow-sm group-hover:bg-destructive/90',
        outline: 'border border-input bg-background shadow-sm group-hover:bg-gray-200',
        secondary: 'bg-secondary text-secondary-foreground shadow-sm group-hover:bg-secondary/80',
        tertiary: 'bg-tertiary text-tertiary-foreground shadow-sm',
        ghost: 'group-hover:bg-accent group-hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 group-hover:underline',
        inverse: 'bg-black text-primary',
      },
      size: {
        default: 'p-2.5 flex w-10 h-10',
        sm: 'p-1 flex w-5 h-5',
        lg: 'p-1.5 flex w-7 h-7',
        icon: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

const labelVariants = cva(
  // !! separates the custom class names with the default ones
  'text-sm text-sm tracking-[0.9px] leading-[138.8%]',
  {
    variants: {
      variant: {
        default: '',
        destructive: '',
        outline: '',
        secondary: '',
        tertiary: '',
        ghost: '',
        link: '',
        inverse: '',
      },
      size: {
        default: 'min-w-[70px] w-min leading-[11px]',
        sm: '',
        lg: '',
        icon: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

export interface ButtonIconProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  icon?: React.ReactNode
  iconClassName?: ClassName
  label?: React.ReactNode
  labelClassName?: ClassName
  direction?: 'vertical' | 'horizontal'
  iconPosition?: 'start' | 'end'
}

const ButtonIcon = React.forwardRef<HTMLButtonElement, ButtonIconProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      icon,
      label,
      iconClassName,
      labelClassName,
      direction = 'vertical',
      iconPosition = 'start',
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button'

    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size, className }),
          direction === 'horizontal' ? 'flex flex-row' : 'flex flex-col',
          iconPosition === 'start'
            ? direction === 'horizontal'
              ? 'flex-row'
              : 'flex-col'
            : direction === 'horizontal'
              ? 'flex-row-reverse'
              : 'flex-col-reverse'
        )}
        ref={ref}
        {...props}
      >
        {icon && <div className={cn(iconVariants({ variant, size, className: iconClassName }))}>{icon}</div>}
        {label && <div className={cn(labelVariants({ variant, size, className: labelClassName }))}>{label}</div>}
      </Comp>
    )
  }
)

ButtonIcon.displayName = 'ButtonIcon'

export { ButtonIcon, buttonVariants, iconVariants, labelVariants }
