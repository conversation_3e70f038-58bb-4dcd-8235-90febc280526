import React from 'react'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import Text from '@atoms/Text'
import { EnergyLabel } from '@lib/_generated/graphql_sdk'

interface Props {
  energyLabel?: EnergyLabel
}

const ProductCard_EnergyLabel: React.FC<Props> = ({ energyLabel }) => {
  if (!energyLabel?.image?.src) return null

  return (
    <div className="flex flex-row text-xs items-center justify-between">
      <div>
        <AppLink href={energyLabel?.labelUrl ?? '#'} target="_blank">
          <Img
            className="w-full mr-3"
            src={energyLabel?.image?.src ?? ''}
            width={42}
            height={20}
            itemProp={'infoImage'}
            alt="Product Image"
          />
        </AppLink>
      </div>
      <AppLink className="flex flex-col text-right pl-4" href={energyLabel?.infoUrl ?? '#'} target="_blank">
        <Text className="text-primary hover:underline cursor-pointer">Продуктов информационен лист</Text>
      </AppLink>
    </div>
  )
}

export default ProductCard_EnergyLabel
