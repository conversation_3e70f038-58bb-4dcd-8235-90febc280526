import React from 'react'

import { formatPrice } from '@/src/features/product/filters'
import DeliveryIcon from '@atoms/Icons/DeliveryIcon'
import PinIcon from '@atoms/Icons/PinIcon'
import Text from '@atoms/Text'
import { addClasses } from '@lib/style/style'

interface Props {
  shopName?: string
  deliveryPrice?: {
    value: number
    currency: string
  }
  deliveryAddress?: string
}

const ProductCard_DeliveryInfo: React.FC<Props> = ({ shopName, deliveryPrice, deliveryAddress }) => {
  return (
    <div className="flex-col group-hover:flex hidden">
      <div className="flex flex-col">
        {shopName && (
          <div
            className={addClasses('flex flex-row border-b border-t', 'border-gray-200 mt-4 text-xs', 'items-center')}
          >
            <PinIcon className="ml-2 my-3 mr-5" />
            <div className="flex flex-col">
              <Text className="font-bold">Моят магазин:</Text>
              <span>{shopName}</span>
            </div>
          </div>
        )}
        {deliveryAddress && (
          <div className={addClasses('flex flex-row border-b', 'border-gray-200 text-xs', 'items-center')}>
            <DeliveryIcon className="ml-1 my-3 mr-4" />
            <div className="flex flex-col">
              <Text className="font-bold">Транспорт:</Text>
              <div>
                <span className="font-bold text-primary">{deliveryPrice && formatPrice(deliveryPrice.value)}</span>
                {deliveryAddress}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ProductCard_DeliveryInfo
