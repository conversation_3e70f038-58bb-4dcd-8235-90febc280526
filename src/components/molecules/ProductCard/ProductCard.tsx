'use client'

import { Maximize2 } from 'lucide-react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import React, { useEffect, useState } from 'react'

import AppLink from '@atoms/AppLink'
import { cn } from '@components/lib/utils'
import BuyCheapBadge from '@components/molecules/ProductCard/components/BuyCheapBadge'
import { ProductCharacteristics } from '@components/molecules/ProductCard/components/ProductCharacteristics'
import { QuickButton } from '@components/molecules/ProductCard/components/QuickButton'
import { Product, ProductViewFragment } from '@lib/_generated/graphql_sdk'
import useBreakpoint from '@/src/hooks/useBreakpoint'
import { ProductPrice } from '@components/molecules/ProductPrice'
import { EnergyType, ProductEnergyTag } from '@components/molecules/ProductEnergyTag'
import { ProductCardBuy } from '@components/molecules/ProductCard/components/ProductCardBuy'
import { FavButton } from '@components/molecules/ProductCard/components/FavButton'
import { useCustomerStore } from '@features/customer/customer.store'
import { ClassName } from '@lib/types/ClassName'
import { siteStatusHelper } from '@/src/lib/utils/SiteStatusHelper'

interface Props {
  product: ProductViewFragment
  inWishList?: boolean
  className?: ClassName
}

const ProductCard: React.FC<Props> = ({ product, inWishList, className }) => {
  const router = useRouter()
  const { maxBreakpoint } = useBreakpoint()
  const infoUrl = product.__typename === 'SimpleProduct' && product.energyLabel?.infoUrl
  const energyLabel = product.__typename === 'SimpleProduct' && (product.energyLabel?.image.title as EnergyType)

  const [isMounted, setIsMounted] = useState(false)
  useEffect(() => {
    setIsMounted(true)
  }, [])

  const wishListStore = useCustomerStore((state) => state.wishlistSkus)
  const wishListed = isMounted ? wishListStore.includes(product.sku) : inWishList

  const debug = false
  return (
    <div
      className={cn(
        debug && 'border border-green-600',
        'flex flex-col relative rounded-2xl bg-white shadow-xl md:hover:shadow-none group transition-all duration-300',
        'min-h-[320px] md:min-h-[390px] hover:h-auto hover:min-h-[320px] md:hover:min-h-[390px]',
        'w-full',
        'max-w-[218px] 3xl:max-w-[250px]',
        className
      )}
      onClick={() => {
        if (maxBreakpoint('lg')) {
          router.push(`/${product.urlKey}` || '#')
        }
      }}
    >
      {product.labels.buyCheap && (
        <div className="absolute z-20">
          <BuyCheapBadge />
        </div>
      )}
      <div className="relative aspect-square w-full max-w-[218px] 3xl:max-w-[250px] max-h-[218px] mx-auto">
        <div className="hidden lg:block absolute inset-0 bg-[rgba(0,0,0,0)] group-hover:bg-[rgba(0,0,0,0.7)] transition-colors duration-300 z-10 rounded-2xl">
          <div
            className={cn(
              'flex flex-1 h-full items-center justify-evenly group-hover:opacity-100 opacity-0 px-0 3xl:px-2'
            )}
          >
            <FavButton product={product as Product} wishListed={wishListed} />
            <QuickButton
              icon={<Maximize2 color="#FFFFFF" />}
              label="Виж детайли"
              onClick={() => {
                router.push(`/${product.urlKey}` || '#')
              }}
            />
          </div>
        </div>
        <Image
          alt={product.image?.alt ?? product.name ?? 'Product image'}
          className="rounded-2xl aspect-square object-contain object-center transition-transform"
          src={product.image?.src ?? ''}
          fill
        />
        {product.__typename === 'SimpleProduct' && (
          <div className="absolute z-20 group-hover:z-40 -bottom-2 lg:-bottom-4 left-3 lg:left-4 right-0">
            <ProductCharacteristics product={product} />
          </div>
        )}
      </div>
      <div className={cn(debug && 'border border-blue-700', 'hidden lg:group-hover:block relative w-full flex-1')}>
        <div
          className={cn(
            'lg:group-hover:flex hidden absolute bg-white shadow-2xl rounded-b-2xl w-full min-h-full z-30 bottom-0 top-0 group-hover:top-auto group-hover:bottom-auto',
            'flex-col flex-1 justify-between px-4 lg:px-4 pt-4 lg:pt-6 pb-4'
          )}
        >
          <div className={cn(debug && 'border border-red-600', 'flex-1')}>
            <AppLink
              href={product.urlKey || '#'}
              title={product.name || 'Unnamed Product'}
              className="cursor-pointer hover:underline line-clamp-none text-sm"
            >
              {product.name}
            </AppLink>
          </div>
          {infoUrl || energyLabel ? (
            <div className="mt-4">
              <div className="flex flex-col items-start mb-3">
                <div>{energyLabel && <ProductEnergyTag type={energyLabel} />}</div>
                <ProductPrice data={product.price} showLabels={false} variant="productCard" />
              </div>
              <div className="flex flex-row justify-center">
                {infoUrl && (
                  <AppLink className="text-xs text-primary hover:underline" href={infoUrl} target="_blank">
                    Продуктов информационен лист
                  </AppLink>
                )}
                {siteStatusHelper.isSalable(product) && (
                  <div className="flex flex-col justify-center">
                    <ProductCardBuy product={product} />
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className={cn(debug && 'border border-yellow-500', 'flex flex-col justify-between items-end')}>
              <ProductPrice data={product.price} showLabels={false} variant="productCard" />
              {product.__typename === 'SimpleProduct' && siteStatusHelper.isSalable(product) && (
                <ProductCardBuy product={product} />
              )}
            </div>
          )}
        </div>
      </div>
      <div className="flex lg:group-hover:hidden flex-1 flex-col relative">
        <div className={cn('flex flex-col flex-1 justify-between px-4 lg:px-4 pt-4 lg:pt-6 pb-4')}>
          <AppLink
            href={product.urlKey || '#'}
            title={product.name || 'Unnamed Product'}
            className="cursor-pointer hover:underline line-clamp-3 text-sm"
          >
            {product.name}
          </AppLink>

          <ProductPrice data={product.price} showLabels={false} variant="productCard" />
        </div>
        {/*<div*/}
        {/*  className={cn(*/}
        {/*    'group-hover:flex hidden',*/}
        {/*    'flex-col flex-1 justify-between px-4 pt-4 lg:pt-6 pb-4',*/}
        {/*    'shadow-xl rounded-b-2xl transform origin-top',*/}
        {/*    'min-h-full absolute z-10 top-auto w-full bg-white'*/}
        {/*  )}*/}
        {/*>*/}
        {/*  <span*/}
        {/*    title={product.name || 'Unnamed Product'}*/}
        {/*    className="cursor-pointer hover:underline line-clamp-none text-sm"*/}
        {/*    onClick={() => {*/}
        {/*      router.push(`/${product.urlKey}` || '#')*/}
        {/*    }}*/}
        {/*  >*/}
        {/*    {product.name}*/}
        {/*  </span>*/}
        {/*  <div className="h-20" />*/}
        {/*  <div className="flex items-end justify-between">*/}
        {/*    <ProductPrice data={product.price} showLabels={false} variant="productCard" />*/}
        {/*    {!infoUrl && <ProductCardBuy productSku={product.sku} />}*/}
        {/*  </div>*/}
        {/*  {infoUrl && (*/}
        {/*    <div className="flex justify-between">*/}
        {/*      <div>*/}
        {/*        <AppLink className="text-xs text-primary hover:underline" href={infoUrl}>*/}
        {/*          Продуктов информационен лист*/}
        {/*        </AppLink>*/}
        {/*      </div>*/}
        {/*      <div>*/}
        {/*        <ProductCardBuy productSku={product.sku} />*/}
        {/*      </div>*/}
        {/*    </div>*/}
        {/*  )}*/}
        {/*</div>*/}
      </div>
    </div>
  )
}

export default ProductCard
