'use client'
import React from 'react'

import Text from '@atoms/Text'
import { addClasses } from '@lib/style/style'

interface Props {
  text: string
  children: React.ReactNode
  onClick: () => void
  dark?: boolean
}

const IconWithText: React.FC<Props> = ({ text, children, onClick, dark = false }) => {
  return (
    <div
      onClick={onClick}
      className={addClasses(
        'group/button',
        'flex flex-col w-[48px] mx-1 desktop:mx-2 cursor-pointer',
        'items-center text-xs text-center text-white'
      )}
    >
      <div
        className={addClasses(
          'flex w-full h-[48px] border rounded-full',
          'items-center justify-center mb-2',
          `${dark ? 'bg-background' : 'bg-transparent'}`,
          `${
            dark ? 'group-hover/button:bg-gray-200 click-animation' : 'group-hover/button:bg-white/20 click-animation'
          }`
        )}
      >
        {children}
      </div>
      <span
        className={addClasses(
          'text-stone-400',
          'transition-all duration-300 ease-in-out',
          `${dark ? '' : 'group-hover/button:text-white'}`
        )}
      >
        <Text className={addClasses(`${dark ? 'text-black' : ''}`)}>{text}</Text>
      </span>
    </div>
  )
}

export default IconWithText
