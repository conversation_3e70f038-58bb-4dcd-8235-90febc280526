import React from 'react'

import { formatPrice } from '@/src/features/product/filters'
import { ProductPrice } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'

interface Props {
  price: ProductPrice
}

const ProductCard_PriceBox: React.FC<Props> = ({ price }) => {
  const normal = price.price
  const specialPrice = price.special

  return (
    <div className="flex flex-col">
      <div className={addClasses('text-lg pr-1', specialPrice ? 'line-through' : 'font-bold')}>
        {formatPrice(normal)}
      </div>
      {specialPrice && (
        <div className="flex items-center text-primary">
          <div className="text-xl font-bold pr-1">{formatPrice(specialPrice)}</div>
        </div>
      )}
    </div>
  )
}

export default ProductCard_PriceBox
