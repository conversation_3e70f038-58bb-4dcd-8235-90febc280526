import React from 'react'

import { ProductCharacteristic } from '@components/molecules/ProductCard/components/ProductCharacteristic'
import { EnergyType, ProductEnergyTag } from '@components/molecules/ProductEnergyTag'
import { calcDiscountPercent } from '@features/product/filters'
import { ProductViewFragment, SimpleProduct } from '@lib/_generated/graphql_sdk'

interface ProductCharacteristicsProps {
  product: ProductViewFragment
}

export const ProductCharacteristics: React.FC<ProductCharacteristicsProps> = ({ product }) => {
  const energyLabel = (product.__typename === 'SimpleProduct' && product.energyLabel?.image.title) || ''
  const hasEnergyLabel = energyLabel.length > 0

  return (
    <div className="flex flex-col gap-0.5">
      {hasEnergyLabel && <ProductEnergyTag className="group-hover:hidden" type={energyLabel as EnergyType} />}
      {product.price.special && (
        <ProductCharacteristic
          label={`Отстъпка ${calcDiscountPercent(product.price.price.value, product.price.special.value).toFixed(0)}%`}
          primary
        />
      )}
      {product.labels.fromBrochure && <ProductCharacteristic label="Продукт от брошура" />}
      {product.labels.freeDelivery && <ProductCharacteristic label="Безплатна доставка" />}
    </div>
  )
}
