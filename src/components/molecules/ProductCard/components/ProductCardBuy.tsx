'use client'

import { LucideLoaderCircle } from 'lucide-react'
import React, { useCallback, useEffect, useState } from 'react'

import CustomButton from '@atoms/Button/CustomButton'
import { cn } from '@components/lib/utils'
import { useCartStore } from '@features/cart/cart-state'
import { ProductViewFragment } from '@lib/_generated/graphql_sdk'

export const ProductCardBuy = ({ product }: { product: ProductViewFragment }) => {
  const cartStore = useCartStore()
  const [loading, setLoading] = useState(false)
  const { sku } = product
  const clickHandler = useCallback(
    async (e: React.MouseEvent) => {
      e.stopPropagation()
      e.preventDefault()
      setLoading(true)
      cartStore.addItem(sku, 1).finally(() => {
        setLoading(false)
      })
    },
    [cartStore, sku]
  )

  return (
    <CustomButton
      className={cn(
        'hidden lg:inline-block duration-0 opacity-0 group-hover:opacity-100',
        'rounded-[8px] py-[10px] px-[14px]'
      )}
      onClick={clickHandler}
    >
      {!loading ? 'Купи' : <LucideLoaderCircle className="animate-spin" />}
    </CustomButton>
  )
}
