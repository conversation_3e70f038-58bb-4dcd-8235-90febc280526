import React from 'react'

import { getProductLabels, PraktisProduct } from '@/src/features/product/filters'
import { addClasses } from '@lib/style/style'

interface Props {
  product: PraktisProduct
}

const ProductLabels: React.FC<Props> = ({ product }) => {
  const productLabels = getProductLabels(product)
  if (productLabels.length === 0) return null

  return (
    <div className="absolute left-0 -bottom-4">
      {productLabels.map((label, index: number) => (
        <div
          key={index}
          className={addClasses(label.color, 'inline-flex text-white', 'uppercase text-xs p-1 px-2 mx-4 mb-0.5')}
        >
          {label.text}
        </div>
      ))}
    </div>
  )
}

export default ProductLabels
