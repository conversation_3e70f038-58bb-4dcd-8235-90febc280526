import { QuickButton } from '@components/molecules/ProductCard/components/QuickButton'
import { Heart, LucideHeartOff, LucideLoaderCircle } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useCustomerStore } from '@features/customer/customer.store'
import { Product } from '@/src/lib/_generated/graphql_sdk'

interface FavButtonProps {
  product: Product
  wishListed?: boolean
}

export const FavButton: React.FC<FavButtonProps> = ({ product, wishListed }) => {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const authenticated = useCustomerStore((state) => state.authenticated)
  const addToWishlist = useCustomerStore((state) => state.addToWishlist)
  const removeFromWishlist = useCustomerStore((state) => state.removeFromWishlist)

  return (
    <QuickButton
      icon={
        loading ? (
          <LucideLoaderCircle className="animate-spin" color="#FFFFFF" />
        ) : wishListed ? (
          <LucideHeartOff color="#FFFFFF" />
        ) : (
          <Heart color="#FFFFFF" />
        )
      }
      label={wishListed ? 'Премахни от любими' : 'Добави в любими'}
      onClick={(e) => {
        e.stopPropagation()
        e.preventDefault()
        if (authenticated) {
          setLoading(true)
          if (wishListed) {
            removeFromWishlist(product.sku).then(() => {
              setLoading(false)
            })
          } else {
            addToWishlist(product.sku, product.price.price.value, product.price.price.currency, {
              fbContentId: product.id,
            }).then(() => {
              setLoading(false)
            })
          }
        } else {
          router.push('/customer/account/login')
        }
      }}
    />
  )
}
