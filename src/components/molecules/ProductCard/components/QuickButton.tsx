import React, { ReactElement } from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'

interface CardButtonProps {
  icon: ReactElement
  label: string
  onClick: (e: React.MouseEvent) => void
}

export const QuickButton: React.FC<CardButtonProps> = ({ icon, label, onClick }) => {
  return (
    <div className={cn('flex max-w-[100px] flex-1 flex-col justify-center items-center gap-2.5')}>
      <button
        // button with icon and border as circle
        className={cn(
          'w-[50px] h-[50px]',
          'flex justify-center items-center',
          'border border-white rounded-full',
          'p-2 transition-all duration-100',
          'hover:bg-white hover:bg-opacity-30'
        )}
        onClick={onClick}
      >
        {icon}
      </button>
      <Text className="text-white opacity-80 text-center text-sm">{label}</Text>
    </div>
  )
}
