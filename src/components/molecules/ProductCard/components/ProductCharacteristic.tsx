import React from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'

interface ProductCharacteristicProps {
  primary?: boolean
  label: string
}
export const ProductCharacteristic: React.FC<ProductCharacteristicProps> = ({ primary, label }) => (
  <Text className={cn(primary ? 'bg-primary' : 'bg-black/80', 'py-1 text-white text-xs pl-2.5 leading-3 font-medium')}>
    {label}
  </Text>
)
