import React from 'react'

import TimerIcon from '@atoms/Icons/TimerIcon'
import { addClasses } from '@lib/style/style'
import Text from '@atoms/Text'

interface Props {
  className?: string
  children?: React.ReactNode
  dark?: boolean
}

const BuyCheapBadge: React.FC<Props> = (props) => {
  return (
    <div
      className={addClasses(
        'h-[25px] lg:h-[35px] rounded-full items-center lg:text-sm pl-1 pr-3',
        'inline-flex',
        'z-20 whitespace-nowrap',
        props.dark ? 'bg-black' : 'bg-primary'
      )}
    >
      <TimerIcon className="w-[15px] h-[15px] lg:h-auto lg:w-auto mr-1 lg:mr-3" />
      <Text className="text-white font-bold text-sm">Купи изгодно</Text>
    </div>
  )
}

export default BuyCheapBadge
