import React from 'react'

import { ThemeColors } from '@/tailwind.config'
import CustomButton from '@atoms/Button/CustomButton'
import CloseIcon from '@atoms/Icons/CloseIcon'
import InfoIcon from '@atoms/Icons/InfoIcon'
import { addClasses } from '@lib/style/style'

type ErrorType = 'info' | 'error' | 'success' | 'warning' | 'none'

const TypeColors: Record<
  ErrorType,
  {
    bgColor: ThemeColors
    textColor: ThemeColors
    buttonHoverColor: ThemeColors
  }
> = {
  info: {
    bgColor: 'bg-blue-200',
    textColor: 'text-blue-800',
    buttonHoverColor: 'hover:bg-blue-300',
  },
  error: {
    bgColor: 'bg-red-200',
    textColor: 'text-red-800',
    buttonHoverColor: 'hover:bg-red-300',
  },
  success: {
    bgColor: 'bg-green-200',
    textColor: 'text-green-800',
    buttonHoverColor: 'hover:bg-green-300',
  },
  warning: {
    bgColor: 'bg-yellow-200',
    textColor: 'text-yellow-800',
    buttonHoverColor: 'hover:bg-yellow-300',
  },
  none: {
    bgColor: 'bg-gray-200',
    textColor: 'text-gray-800',
    buttonHoverColor: 'hover:bg-gray-300',
  },
}

export interface NotificationProps {
  type?: ErrorType
  bgColor?: ThemeColors
  textColor?: ThemeColors
  buttonHoverColor?: ThemeColors
  closeAction?: () => void
  children: React.ReactNode
}

const NotificationMessage: React.FC<NotificationProps> = ({
  type,
  bgColor,
  textColor,
  buttonHoverColor,
  closeAction,
  children,
}) => {
  const typeColors = TypeColors[type ?? 'none']
  bgColor = bgColor ?? typeColors.bgColor ?? 'bg-gray-200'
  textColor = textColor ?? typeColors.textColor ?? 'text-gray-800'
  buttonHoverColor = buttonHoverColor ?? typeColors.buttonHoverColor ?? 'hover:bg-gray-300'

  return (
    <div
      className={addClasses(
        'rounded-md transition-opacity duration-500',
        !children ? 'opacity-0' : 'opacity-100 mb-4',
        bgColor,
        textColor
      )}
    >
      {children && (
        <div className="p-4 flex items-center">
          <InfoIcon />
          <div className="ml-3 text-sm font-medium">{children}</div>
          {closeAction && (
            <CustomButton
              color={'none'}
              unstyled={true}
              className={addClasses('ml-auto -mx-1.5 -my-1.5 w-8 h-8', buttonHoverColor)}
              onClick={closeAction}
            >
              <CloseIcon />
            </CustomButton>
          )}
        </div>
      )}
    </div>
  )
}

export default NotificationMessage
