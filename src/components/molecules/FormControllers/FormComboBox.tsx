'use client'

import React from 'react'
import { Controller, useFormContext, useFormState } from 'react-hook-form'

import { cn } from '@components/lib/utils'
import { ComboBox, ComboBoxProps } from '@components/molecules/ComboBox'
import { FormError } from '@components/molecules/FormControllers/components/FormError'
import { FormField } from '@components/molecules/FormControllers/components/FormField'
import { FormItem } from '@components/molecules/FormControllers/components/FormItem'
import { FormLabel } from '@components/molecules/FormControllers/components/FormLabel'
import { FormItemProps } from '@components/molecules/FormControllers/components/types'
import { Label } from '@components/theme/ui/label'

interface FormComboBoxProps<T extends { [key: string]: any }>
  extends Omit<ComboBoxProps<T>, 'rowKey' | 'onBlur' | 'value'>,
    FormItemProps {}

export function FormComboBox<T extends { [key: string]: any }>({
  name,
  label,
  ...comboBoxProps
}: FormComboBoxProps<T>) {
  const { control } = useFormContext()
  const { errors } = useFormState({ name })

  const errorMessage = errors[name]?.message as string | undefined

  return (
    <FormItem>
      <FormLabel htmlFor={name}>{label}:</FormLabel>
      <FormField>
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <ComboBox
              {...comboBoxProps}
              {...field}
              name={name}
              className={cn(
                'rounded-full w-full border h:8 sm:h-9 md:h-10 lg:h-12 text-xs',
                'py-0',
                errorMessage ? 'border-red-500 bg-red-100' : 'border-[#D9D9D9]'
              )}
            />
          )}
        />
        <FormError>{errorMessage}</FormError>
      </FormField>
    </FormItem>
  )
}
