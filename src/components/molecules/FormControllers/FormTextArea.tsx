import React from 'react'
import { Controller, useFormContext, useFormState } from 'react-hook-form'

import { cn } from '@components/lib/utils'
import { FormError } from '@components/molecules/FormControllers/components/FormError'
import { FormField } from '@components/molecules/FormControllers/components/FormField'
import { FormItem } from '@components/molecules/FormControllers/components/FormItem'
import { FormLabel } from '@components/molecules/FormControllers/components/FormLabel'
import { FormItemProps } from '@components/molecules/FormControllers/components/types'
import { Textarea } from '@components/theme/ui/textarea'

export const FormTextArea: React.FC<FormItemProps> = ({ name, label }) => {
  const { control } = useFormContext()
  const { errors } = useFormState({ name })

  const errorMessage = errors[name]?.message as string | undefined

  return (
    <FormItem>
      <FormLabel>{label}</FormLabel>
      <FormField>
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <Textarea
              {...field}
              id={name}
              rows={5}
              className={cn(
                'rounded-2xl w-full border h-12',
                errorMessage ? 'border-red-500 bg-red-100' : 'border-[#D9D9D9]'
              )}
            />
          )}
        />
        <FormError>{errorMessage}</FormError>
      </FormField>
    </FormItem>
  )
}
