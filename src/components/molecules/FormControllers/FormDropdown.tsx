import React from 'react'

import { Controller, useFormContext, useFormState } from 'react-hook-form'
import { cn } from '@components/lib/utils'
import { FormError } from '@components/molecules/FormControllers/components/FormError'
import { FormField } from '@components/molecules/FormControllers/components/FormField'
import { FormItem } from '@components/molecules/FormControllers/components/FormItem'
import { FormLabel } from '@components/molecules/FormControllers/components/FormLabel'
import { errorClassName } from '@components/molecules/FormControllers/components/styles'
import { FormItemProps } from '@components/molecules/FormControllers/components/types'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@components/theme/ui/select'
import { SelectProps } from '@radix-ui/react-select'

interface FormDropdownProps extends FormItemProps, Omit<SelectProps, 'name'> {
  options: { value: string; label: string }[]
  placeholder?: string
}

export const FormDropdown: React.FC<FormDropdownProps> = ({ name, label, options, disabled, placeholder }) => {
  const { control } = useFormContext()
  const { errors } = useFormState({ name })
  const errorMessage = errors[name]?.message as string | undefined // ✅ Ensure it's a string

  return (
    <FormItem>
      <FormLabel htmlFor={name}>{label}</FormLabel>
      <FormField>
        <Controller
          name={name}
          control={control}
          render={({ field: { onBlur, disabled: fieldDisabled, onChange, value, name, ref } }) => {
            return (
              <Select
                name={name}
                disabled={disabled || fieldDisabled}
                onValueChange={(value) => {
                  onChange(value)
                  onBlur()
                }}
                value={value || undefined}
                defaultValue={value || undefined}
              >
                <SelectTrigger
                  ref={ref}
                  className={cn(
                    'rounded-full w-full border h:8 sm:h-9 md:h-10 lg:h-12 text-xs lg:text-base',
                    'font-bold text-sm px-5',
                    errorMessage ? errorClassName : 'border-[#D9D9D9]'
                  )}
                >
                  <SelectValue placeholder={placeholder || 'Изберете опция'} />
                </SelectTrigger>
                <SelectContent className="bg-white">
                  {options?.map((option, index) => (
                    <SelectItem key={index} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )
          }}
        />
        <FormError>{errorMessage}</FormError>
      </FormField>
    </FormItem>
  )
}
