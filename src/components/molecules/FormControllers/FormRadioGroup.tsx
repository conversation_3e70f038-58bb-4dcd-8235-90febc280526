'use client'

import React, { ReactNode } from 'react'
import { Controller, useFormContext, useFormState } from 'react-hook-form'

import { cn } from '@components/lib/utils'
import { FormError } from '@components/molecules/FormControllers/components/FormError'
import { FormField } from '@components/molecules/FormControllers/components/FormField'
import { FormItem } from '@components/molecules/FormControllers/components/FormItem'
import { FormLabel } from '@components/molecules/FormControllers/components/FormLabel'
import { errorClassName } from '@components/molecules/FormControllers/components/styles'
import { Label } from '@components/theme/ui/label'
import { RadioGroup, RadioGroupItem } from '@components/theme/ui/radio-group'
import { ClassName } from '@lib/types/ClassName'

interface FormRadioGroupValueProps {
  value: string
  label: string
  description?: ReactNode
}

interface FormRadioGroupProps {
  name: string
  label?: ReactNode
  options: FormRadioGroupValueProps[]
  width?: 'full' | 'half'
  validateOnChange?: boolean
  className?: ClassName
  containerClassName?: ClassName
  optionClassName?: ClassName
  optionSelectedClassName?: ClassName
  labelClassName?: ClassName
  radioItemClassName?: ClassName
  radioItemSelectedClassName?: ClassName
  indicatorClassName?: ClassName
  descriptionSelectedClassName?: ClassName
}

export const FormRadioGroup: React.FC<FormRadioGroupProps> = ({
  name,
  options,
  label,
  className,
  containerClassName,
  optionClassName,
  optionSelectedClassName,
  radioItemClassName,
  radioItemSelectedClassName,
  indicatorClassName,
  labelClassName,
  descriptionSelectedClassName,
  width = 'full',
  validateOnChange,
}) => {
  const { control, trigger } = useFormContext()
  const { errors } = useFormState({ name })

  const errorMessage = errors[name]?.message as string | undefined

  return (
    <FormItem>
      <FormLabel>{label}</FormLabel>
      <FormField>
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <RadioGroup
              {...field}
              className={cn(className)}
              onValueChange={(value) => {
                field.onChange(value)
                if (!!validateOnChange) {
                  trigger(name)
                }
              }}
              defaultValue={field.value}
            >
              {options.map((option) => (
                <Label
                  htmlFor={`${name}-${option.value}`}
                  className={cn(
                    'flex gap-2 items-center py-1.5 cursor-pointer px-2 w-full',
                    optionClassName,
                    field.value === option.value && optionSelectedClassName
                  )}
                  key={option.value}
                >
                  <RadioGroupItem
                    disabled={field.disabled}
                    id={`${name}-${option.value}`}
                    value={option.value}
                    indicatorClassName={indicatorClassName}
                    className={cn(
                      radioItemClassName,
                      field.value === option.value && radioItemSelectedClassName,
                      errorMessage ? errorClassName : null
                    )}
                  />
                  <div className={cn('flex flex-col flex-1', labelClassName)}>
                    <span className={cn(errorMessage && 'text-red-500')}>{option.label}</span>
                    {option.description && (
                      <div
                        className={cn(
                          'text-sm text-gray-500',
                          field.value === option.value && descriptionSelectedClassName
                        )}
                      >
                        {option.description}
                      </div>
                    )}
                  </div>
                </Label>
              ))}
            </RadioGroup>
          )}
        />
        <FormError>{errorMessage}</FormError>
      </FormField>
    </FormItem>
  )
}
