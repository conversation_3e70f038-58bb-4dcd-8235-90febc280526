import { LucideLoaderCircle } from 'lucide-react'
import React, { PropsWithChildren } from 'react'
import { useFormContext, useFormState } from 'react-hook-form'

import { Button, ButtonProps } from '@components/theme/ui/button'

interface FormSubmitProps extends ButtonProps {
  loading: boolean
}

export const FormSubmit: React.FC<PropsWithChildren<FormSubmitProps>> = ({ children, loading, ...props }) => {
  const { control } = useFormContext()
  const { disabled } = useFormState({ control })

  return (
    <Button {...props} type="submit" disabled={disabled}>
      {loading && <LucideLoaderCircle className="animate-spin text-white" />}
      {children}
    </Button>
  )
}
