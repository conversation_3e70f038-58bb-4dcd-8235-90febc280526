'use client'

import React from 'react'
import { Controller, useFormContext, useFormState } from 'react-hook-form'

import { cn } from '@components/lib/utils'
import { FormError } from '@components/molecules/FormControllers/components/FormError'
import { FormField } from '@components/molecules/FormControllers/components/FormField'
import { FormItem } from '@components/molecules/FormControllers/components/FormItem'
import { FormLabel } from '@components/molecules/FormControllers/components/FormLabel'
import { errorClassName } from '@components/molecules/FormControllers/components/styles'
import { FormItemProps } from '@components/molecules/FormControllers/components/types'
import { Input } from '@components/theme/ui/input'

export const FormTextField: React.FC<FormItemProps & { type?: HTMLInputElement['type'] }> = ({
  name,
  label,
  required,
  type = 'text',
}) => {
  const { control } = useFormContext()
  const { errors } = useFormState({ name })

  const errorMessage = errors[name]?.message as string | undefined

  return (
    <FormItem>
      <FormLabel htmlFor={name}>
        {label}
        {required && '*'}
      </FormLabel>
      <FormField>
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              value={field.value ?? ''}
              id={name}
              type={type}
              className={cn(
                'rounded-full w-full border h:8 sm:h-9 md:h-10 lg:h-12 text-xs lg:text-base pl-5',
                errorMessage ? errorClassName : 'border-[#D9D9D9]'
              )}
            />
          )}
        />
        {errorMessage && <FormError>{errorMessage}</FormError>}
      </FormField>
    </FormItem>
  )
}
