import React from 'react'
import { useF<PERSON><PERSON><PERSON>xt, Controller, useFormState } from 'react-hook-form'

import { cn } from '@components/lib/utils'
import { FormError } from '@components/molecules/FormControllers/components/FormError'
import { FormField } from '@components/molecules/FormControllers/components/FormField'
import { FormItem } from '@components/molecules/FormControllers/components/FormItem'
import { errorClassName } from '@components/molecules/FormControllers/components/styles'
import { FormItemProps } from '@components/molecules/FormControllers/components/types'
import { Checkbox } from '@components/theme/ui/checkbox'
import { Label } from '@components/theme/ui/label'

interface FormCheckboxProps
  extends FormItemProps,
    Omit<React.InputHTMLAttributes<HTMLInputElement>, 'name' | 'className'> {
  divider?: boolean
  fakeLabelSpace?: boolean
}

export const FormCheckbox: React.FC<FormCheckboxProps> = ({ name, label, divider, fakeLabelSpace, ...rest }) => {
  const { control } = useFormContext()
  const { errors } = useFormState({ name })

  const errorMessage = errors[name]?.message as string | undefined

  return (
    <FormItem>
      {fakeLabelSpace && <div className="h-[14px]"></div>}
      <FormField type="horizontal">
        <Controller
          name={name}
          control={control}
          render={({ field }) => {
            return (
              <div className="flex items-center gap-3">
                <Checkbox
                  id={name}
                  className={cn('p-3 rounded-md', errorMessage ? errorClassName : 'border-[#D9D9D9]')}
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  disabled={field.disabled || rest.disabled}
                  ref={field.ref}
                  name={field.name}
                />
                <Label htmlFor={name}>{label}</Label>
                {divider && <div className="flex-1 border-b border-gray-300" />}
              </div>
            )
          }}
        />
      </FormField>
      {errorMessage && <FormError>{errorMessage}</FormError>}
    </FormItem>
  )
}
