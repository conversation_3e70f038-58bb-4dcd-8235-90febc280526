'use client'

import React from 'react'

import FullDescriptionButton from '@/src/app/(store)/[...dynamic]/(product)/FullDescriptionButton'
import { HTML } from '@atoms/HTML'
import { CMSMarkup } from './Widget/markup/instanced'

export const ProductShortDescription: React.FC<{ children: string }> = ({ children }) => {
  return (
    <div className="flex flex-col gap-4 items-start raw-content">
      <CMSMarkup markup={children} />
      <FullDescriptionButton />
    </div>
  )
}
