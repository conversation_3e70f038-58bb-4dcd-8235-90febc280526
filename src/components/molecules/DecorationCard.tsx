import React, { ReactNode } from 'react'

import Img from '@atoms/Img'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Card, CardContent, CardFooter, CardHeader } from '@components/theme/ui/card'
import { DialogContent } from '@components/theme/ui/dialog'
import { ClassName } from '@lib/types/ClassName'

export interface DecorationCardProps {
  title: ReactNode
  description: ReactNode
  action: ReactNode
  image: string
  alt?: string
  className?: ClassName
}

export const DecorationCard: React.FC<DecorationCardProps> = ({
  image,
  alt,
  title,
  description,
  action,
  className,
}) => {
  return (
    <Card className={cn('relative h-full rounded-3xl shadow-none border-none p-4 xl:p-7', className)}>
      <Img fill className="rounded-2xl object-cover" src={image} alt={alt || ''} />
      <Card className={cn('relative flex flex-col p-0 xl:p-4 h-full w-full md:w-3/5 xl:w-1/2')}>
        {title && (
          <CardHeader>
            {typeof title === 'string' ? <Text className="text-2xl font-bold">{title}</Text> : title}
          </CardHeader>
        )}
        <CardContent className="flex-1">{description}</CardContent>
        {action && <CardFooter>{action}</CardFooter>}
      </Card>
    </Card>
  )
}
