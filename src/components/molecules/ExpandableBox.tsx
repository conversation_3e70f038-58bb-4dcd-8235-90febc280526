'use client'
import React from 'react'

import ArrowCircle from '@/src/components/atoms/Icons/ArrowCircle'
import { addClasses } from '@/src/lib/style/style'
import { useSimpleMenuContext } from '@components/context/SimpleMenuContext'
import { cn } from '@components/lib/utils'
import { ButtonIcon } from '@components/molecules/ButtonIcon'
import { Card } from '@components/theme/ui/card'
import TriangleDown from '@images/icons/triangle-down-fill.inline.svg'

interface Props {
  label: string
  children: React.ReactNode
}

const ExpandableBox: React.FC<Props> = ({ label, children }) => {
  const { isOpen, toggle } = useSimpleMenuContext()

  return (
    <Card className="w-full p-0">
      <div
        onClick={toggle}
        className={addClasses(
          'flex font-bold text-xl',
          'py-3 pr-4 pl-6',
          'sm:px-8',
          'md:py-6',
          'lg:py-7',
          'xl:px-12 xl:py-8',
          'cursor-pointer justify-between items-center'
        )}
      >
        {label}
        <ButtonIcon
          variant="tertiary"
          iconClassName={cn('p-0', isOpen ? 'rotate-180' : '')}
          icon={<TriangleDown className="w-6 h-6 2xl:w-8 2xl:h-8" />}
        />
      </div>

      {isOpen && children}
    </Card>
  )
}

export default ExpandableBox
