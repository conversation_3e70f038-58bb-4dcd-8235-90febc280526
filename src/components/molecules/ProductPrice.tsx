import { cn } from '@components/lib/utils'
import { Price, ProductPrice as ProductPriceType } from '@lib/_generated/graphql_sdk'
import React from 'react'

export interface ProductPriceProps extends React.HTMLAttributes<HTMLDivElement> {
  data: ProductPriceType
  variant?:
    | 'default'
    | 'productCard'
    | 'productPrimary'
    | 'productSecondary'
    | 'productTotal'
    | 'productSaved'
    | 'productUnit'
    | 'productFbtCard'
    | 'productFbtTotal'
    | 'cartPreview'
    | 'cartPreviewItem'
    | 'cartItem'
    | 'cartTotal'
    | 'checkoutTotal'
    | 'checkoutGrandTotal'
  colorScheme?: 'default' | 'primary' | 'secondary' | 'accent' | 'warning' | 'success'
  regularPriceLabel?: string
  specialPriceLabel?: string
  showLabels?: boolean
  currencyPosition?: 'before' | 'after'
  formatValue?: (value: number) => string
  regularPriceClassName?: string
  specialPriceClassName?: string
  labelsClassName?: string
  classes?: Record<string, string>
}

export const ProductPrice = React.forwardRef<HTMLDivElement, ProductPriceProps>(
  (
    {
      className,
      variant = 'default',
      colorScheme = 'default',
      data,
      regularPriceLabel = 'Regular price',
      specialPriceLabel = 'Special price',
      showLabels = false,
      currencyPosition = 'after',
      formatValue,
      regularPriceClassName,
      specialPriceClassName,
      labelsClassName,
      classes,
      ...props
    },
    ref
  ) => {
    const hasSpecial = !!data.special

    const getContainerClass = () => {
      const baseClass = 'flex cursor-default'

      let specialClass = ''
      let variantClass = ''
      switch (variant) {
        case 'default':
          variantClass = 'gap-1'
          break
        case 'productCard':
          variantClass = 'flex-row items-baseline justify-between flex-col-reverse gap-0 w-full'
          break
        case 'productPrimary':
          variantClass = 'justify-between flex-1 rounded-md'
          specialClass = !hasSpecial ? 'justify-end' : 'justify-between'
          break
        case 'productSecondary':
          variantClass = 'justify-between flex-1 xl:gap-2 lg:gap-3'
          break
        case 'productTotal':
          variantClass = 'flex-row'
          break
        case 'productSaved':
          variantClass = 'flex-row flex-1 justify-between'
          break
        case 'productUnit':
          variantClass = 'flex-row flex-1'
          break
        case 'productFbtCard':
          variantClass = 'gap-2 items-baseline'
          specialClass = !hasSpecial ? 'flex-row justify-start' : 'flex-col-reverse justify-between'
          break
        case 'cartPreview':
          variantClass = 'bg-white py-2 px-3 rounded-xl'
          break
        case 'cartItem':
          variantClass = 'flex-col-reverse items-end'
          break
        default:
          variantClass = 'flex-col gap-1'
      }

      return cn(baseClass, variantClass, specialClass)
    }

    const getRegularColClass = () => {
      let colClass = ''
      switch (variant) {
        case 'productCard':
          colClass = 'justify-between'
          break
        case 'productPrimary':
          colClass = 'flex-col items-end'
          break
        case 'productSaved':
          colClass = 'flex-col-reverse flex-1 items-end gap-0'
          break
        case 'productUnit':
          colClass = 'flex-col-reverse flex-1 items-end gap-0'
          break
        case 'productSecondary':
          colClass = 'flex-col-reverse items-center'
          break
      }
      return colClass
    }

    const getRegularPriceClass = () => {
      const baseClass = 'font-medium'

      let variantClass = ''
      switch (variant) {
        case 'productCard':
          variantClass = `${hasSpecial ? 'font-medium' : 'font-bold'} ${hasSpecial ? 'text-base' : 'text-lg'}`
          break
        case 'productPrimary':
          variantClass = `font-bold ${hasSpecial ? 'text-black' : 'text-white'} ${hasSpecial ? 'text-2xl' : 'text-3xl'}`
          break
        case 'productSecondary':
          variantClass = `font-bold ${hasSpecial ? 'text-black' : 'text-white'} ${hasSpecial ? 'text-xl' : 'text-2xl'}`
          break
        case 'productTotal':
          variantClass = 'text-base text-white font-bold'
          break
        case 'productUnit':
          variantClass = 'text-sm text-white font-normal'
          break
        case 'productSaved':
          variantClass = 'text-sm text-white font-medium'
          break
        case 'productFbtTotal':
          variantClass = 'text-3xl text-white font-bold'
          break
        case 'productFbtCard':
          variantClass = 'text-base text-black font-bold'
          break
        case 'cartPreview':
          variantClass = 'text-sm font-bold text-primary'
          break
        case 'cartPreviewItem':
          variantClass = 'text-sm font-normal text-gray-500'
          break
        case 'cartItem':
          variantClass = `${hasSpecial ? 'text-lg' : 'text-xl'} font-bold text-black`
          break
        case 'cartTotal':
          variantClass = 'text-xl font-bold text-primary'
          break
        case 'checkoutTotal':
          variantClass = 'text-sm font-bold text-black'
          break
        case 'checkoutGrandTotal':
          variantClass = 'text-sm font-bold text-primary'
          break
      }

      const specialClass = hasSpecial ? 'line-through' : ''

      return cn(baseClass, variantClass, specialClass)
    }

    const getRegularCurrencyClass = () => {
      let variantClass = ''
      switch (variant) {
        case 'productPrimary':
          variantClass = `flex-col-reverse ${hasSpecial ? 'text-black' : 'text-white'} ${hasSpecial ? 'text-sm' : 'text-2xl'} font-normal`
          break
        case 'productSecondary':
          variantClass = `flex-col-reverse ${hasSpecial ? 'text-black' : 'text-white'} ${hasSpecial ? 'text-sm' : 'text-base'}`
          break
        case 'productTotal':
          variantClass = 'text-sm text-white font-medium'
          break
        case 'productSaved':
          variantClass = 'text-sm text-white font-medium'
          break
        case 'productUnit':
          variantClass = 'text-sm text-white font-normal'
          break
        case 'productFbtTotal':
          variantClass = 'text-2xl text-white'
          break
        case 'productFbtCard':
          variantClass = 'text-sm text-black'
          break
        case 'cartPreview':
          variantClass = 'text-sm font-bold text-primary'
          break
        case 'cartPreviewItem':
          variantClass = 'text-sm font-normal text-gray-500'
          break
        case 'cartItem':
          variantClass = `${hasSpecial ? 'text-sm' : 'text-base'} font-normal`
          break
        case 'cartTotal':
          variantClass = 'text-base text-primary'
          break
        case 'checkoutTotal':
          variantClass = 'text-sm font-normal text-black'
          break
        case 'checkoutGrandTotal':
          variantClass = 'text-sm font-normal text-primary'
          break
      }

      const specialClass = hasSpecial ? 'line-through' : ''

      return cn(variantClass, specialClass)
    }

    const getRegularLabelClass = () => {
      const baseClass = 'text-sm font-medium text-black'

      let variantClass = ''
      switch (variant) {
        case 'default':
          variantClass = 'hidden'
          break
        case 'productCard':
          break
        case 'productPrimary':
          variantClass = 'text-xs text-white/70'
          variantClass = 'text-white text-xs font-normal'
          break
        case 'productSaved':
          variantClass = 'text-xs text-white/90 font-normal'
          break
        case 'productUnit':
          variantClass = 'text-xs text-white/90 font-normal'
          break
        case 'productSecondary':
          variantClass = 'text-xs text-white font-normal'
          break
        case 'productFbtTotal':
          variantClass = 'text-sm text-white flex items-center'
          break
      }

      let hiddenClass = ''
      if (variant === 'default') {
        hiddenClass = 'hidden'
      }

      return cn(baseClass, hiddenClass, variantClass)
    }

    const getSpecialColClass = () => {
      let colClass = ''
      switch (variant) {
        case 'productPrimary':
          colClass = 'flex-col'
          break
        case 'productSecondary':
          colClass = 'flex-col-reverse items-center'
          break
      }
      return colClass
    }

    const getSpecialPriceClass = () => {
      const baseClass = 'font-bold'

      let variantClass = ''
      switch (variant) {
        case 'productCard':
          variantClass = 'text-primary text-lg'
          break
        case 'productPrimary':
          variantClass = 'text-white text-2xl'
          break
        case 'productSecondary':
          variantClass = 'text-white text-2xl'
          break
        case 'productFbtCard':
          variantClass = 'text-lg text-primary'
          break
        case 'cartItem':
          variantClass = 'text-xl text-primary'
          break
      }

      return cn(baseClass, variantClass)
    }

    const getSpecialCurrencyClass = () => {
      let variantClass = ''
      switch (variant) {
        case 'productCard':
          variantClass = 'text-primary sm'
          break
        case 'productPrimary':
          variantClass = 'text-white'
          break
        case 'productSecondary':
          variantClass = 'text-white'
          break
        case 'productFbtCard':
          variantClass = 'text-primary text-base'
          break
        case 'cartItem':
          variantClass = 'text-lg text-primary'
          break
      }
      return variantClass
    }

    const getSpecialLabelClass = () => {
      const baseClass = 'text-xs font-medium'

      let variantClass = ''
      switch (variant) {
        case 'default':
          variantClass = 'hidden'
          break
        case 'productCard':
          break
        case 'productPrimary':
          variantClass = 'text-sm text-white/70'
        case 'productSecondary':
          variantClass = 'text-xs text-white font-normal'
      }

      let hiddenClass = ''
      if (variant === 'default') {
        hiddenClass = 'hidden'
      }

      return cn(baseClass, hiddenClass, variantClass)
    }

    const formatPrice = (price: Price, eur = false) => {
      return formatValue ? formatValue(price.value) : (eur ? price.value / 1.95583 : price.value).toFixed(2)
    }

    const formatCurrency = (price: Price) => {
      return price.currency.replace('BGN', 'лв').replace('EUR', 'евро')
    }

    const priceInfoPriceContainerClass = cn('flex flex-col gap-2  xl:flex-row', {
      'flex-col': variant === 'productPrimary',
      'justify-between w-full': variant === 'productCard' || variant === 'productFbtCard',
      'lg:flex-row': !hasSpecial,
    })

    return (
      <div
        data-name="price-info"
        data-variant={variant}
        ref={ref}
        className={cn(getContainerClass(), className)}
        {...props}
      >
        {/* Special price section */}
        {hasSpecial && (
          <div
            data-name="price-info-special"
            className={cn('flex flex-row justify-between w-full', getSpecialColClass())}
          >
            {showLabels && <span className={cn(getSpecialLabelClass(), labelsClassName)}>{specialPriceLabel}</span>}
            <div
              data-name="price-info-special-price-container"
              className={cn(priceInfoPriceContainerClass, classes?.['price-info-special-price-container'])}
            >
              <div data-name="price-info-special-price" className="flex gap-1 items-baseline">
                <span
                  data-name="price-info-special-price-value"
                  className={cn(getSpecialPriceClass(), specialPriceClassName)}
                >
                  {formatPrice(data.special!)}
                </span>
                <span
                  data-name="price-info-special-price-currency"
                  className={cn(getSpecialCurrencyClass(), specialPriceClassName)}
                >
                  {formatCurrency(data.special!)}
                </span>
              </div>
              <div data-name="price-info-special-price-eur" className="flex gap-1 items-baseline">
                <span
                  data-name="price-info-special-price-eur-value"
                  className={cn(getSpecialPriceClass(), specialPriceClassName)}
                >
                  {formatPrice(data.special!, true)}
                </span>
                <span
                  data-name="price-info-special-price-eur-currency"
                  className={cn(getSpecialCurrencyClass(), specialPriceClassName)}
                >
                  €
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Regular price section */}
        <div data-name="price-info-regular" className={cn('flex flex-row w-full gap-2', getRegularColClass())}>
          {showLabels && (
            <span
              data-name="price-info-regular-label"
              className={cn('flex-1', getRegularLabelClass(), labelsClassName)}
            >
              {regularPriceLabel}
            </span>
          )}
          <div
            data-name="price-info-regular-price-container"
            className={cn(priceInfoPriceContainerClass, classes?.['price-info-regular-price-container'])}
          >
            <div data-name="price-info-regular-price" className="flex gap-1 items-baseline">
              <span
                data-name="price-info-regular-price-value"
                className={cn(getRegularPriceClass(), regularPriceClassName)}
              >
                {formatPrice(data.price)}
              </span>
              <span
                data-name="price-info-regular-price-currency"
                className={cn(getRegularCurrencyClass(), regularPriceClassName)}
              >
                лв
                {/*{formatCurrency(data.price)}*/}
              </span>
            </div>
            <div data-name="price-info-regular-price-eur" className="flex gap-1 items-baseline">
              <span
                data-name="price-info-regular-price-eur-value"
                className={cn(getRegularPriceClass(), regularPriceClassName)}
              >
                {formatPrice(data.price, true)}
              </span>
              <span
                data-name="price-info-regular-price-eur-currency"
                className={cn(getRegularCurrencyClass(), regularPriceClassName)}
              >
                €
              </span>
            </div>
          </div>
        </div>
      </div>
    )
  }
)

ProductPrice.displayName = 'ProductPrice'
