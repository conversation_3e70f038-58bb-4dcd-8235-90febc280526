import { LucideLoaderCircle } from 'lucide-react'
import { useCallback, useState } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { useCartStore } from '@features/cart/cart-state'
import { calcDiscountPercent } from '@features/product/filters'
import { Product } from '@lib/_generated/graphql_sdk'

interface ProductsSliderProps {
  products: Product[]
}

const ProductCartHorizontal = ({ product, bordered }: { product: Product; bordered: boolean }) => {
  const firstAvailableImage = product.gallery[2].image
  const [loading, setLoading] = useState(false)
  const cartStore = useCartStore()
  const clickHandler = useCallback(async () => {
    setLoading(true)
    await cartStore.addItem(product.sku, 1)
    setLoading(false)
  }, [cartStore, product.sku])

  return (
    <div
      className={cn('flex mt-7 col-span-8', {
        'border-l border-r border-gray-300 px-3': bordered,
      })}
    >
      <div className="relative">
        <div className="absolute z-10 bg-primary text-white text-xs px-3  py-0.5">
          Отстъпка {calcDiscountPercent(product.price.price.value, product.price.special?.value || 0)}%
        </div>
        <AppLink href={product.urlKey} className="relative w-[150px] aspect-square block">
          <Img
            className="object-fill"
            fill
            src={firstAvailableImage.src}
            alt={firstAvailableImage.alt || firstAvailableImage.title || ''}
          />
        </AppLink>
      </div>
      <div className="flex flex-col flex-1 justify-between">
        <div className="w-[150px]">
          <AppLink href={product.urlKey} className="hover:underline">
            <Text className="text-xs leading-4 text-black line-clamp-4">{product.name}</Text>
          </AppLink>
        </div>
        <div className="flex justify-between items-end gap-1">
          <div className="flex flex-col gap-1">
            {product.price.special && (
              <div className="flex gap-1 items-end line-through">
                <span className="text-sm font-bold">{product.price.price.value.toFixed(2)}</span>
                <span className="text-xs">{product.price.price.currency}</span>
              </div>
            )}

            <div
              className={cn('flex gap-1 items-end', {
                'text-primary': product.price.special,
              })}
            >
              <span className="font-bold">{(product.price.special || product.price.price).value.toFixed(2)}</span>
              <span className="text-sm">{product.price.price.currency}</span>
            </div>
          </div>
          {loading && <LucideLoaderCircle className="animate-spin" />}
          {!loading && (
            <Button size="sm" className="text-xs rounded-lg tracking-normal font-bold" onClick={clickHandler}>
              <Text>Добави</Text>
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export const ProductStaticSlider = ({ products }: ProductsSliderProps) => {
  return (
    <div>
      <Swiper
        breakpoints={{
          768: {
            slidesPerView: 1.1,
            spaceBetween: 10,
          },
          1024: {
            slidesPerView: 1.5,
            spaceBetween: 10,
          },
          1280: {
            slidesPerView: 2,
            spaceBetween: 10,
          },
          1536: {
            slidesPerView: 3,
            spaceBetween: 10,
          },
        }}
      >
        {products.slice(0, 3).map((product, i) => (
          <SwiperSlide key={product.id}>
            <ProductCartHorizontal
              key={product.id}
              bordered={i > 0 && i < 2}
              product={{
                ...product,
                price: {
                  ...product.price,
                  special: {
                    value: product.price.price.value * 0.9,
                    currency: product.price.price.currency,
                  },
                },
              }}
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  )
}
