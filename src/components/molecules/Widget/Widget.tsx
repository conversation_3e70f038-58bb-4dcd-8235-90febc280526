import React, { PropsWithChildren } from 'react'

import AppLink from '@atoms/AppLink'
import { HomePageContainer } from '@atoms/HomePageContaner'
import Text from '@atoms/Text'
import Title from '@atoms/Title'
import { cn } from '@components/lib/utils'
import News from '@components/molecules/Widget/widgets/News'
import ProductsSlider from '@components/molecules/Widget/widgets/ProductsSlider'
import ServiceWidget from '@components/molecules/Widget/widgets/ServiceWidget'
import Tiles from '@components/molecules/Widget/widgets/TilesWidget/Tiles'
import { Button } from '@components/theme/ui/button'
import {
  AppCategoryLinkWidgetFragment,
  AppHtmlWidgetFragment,
  AppProductsSliderWidgetFragment,
  CarouselWidgetFragmentFragment,
  NewsWidgetFragmentFragment,
  ServiceWidgetFragmentFragment,
  TilesWidgetFragmentFragment,
} from '@lib/_generated/graphql_sdk'

import { ListProps, WrapperProps } from './types'
import { Carousel } from '@components/molecules/Widget/Carousel'

export type AppWidgets =
  | CarouselWidgetFragmentFragment
  | AppProductsSliderWidgetFragment
  | TilesWidgetFragmentFragment
  | AppCategoryLinkWidgetFragment
  | ServiceWidgetFragmentFragment
  | NewsWidgetFragmentFragment
  | AppHtmlWidgetFragment

export function Widget({
  children,
  title,
  subTitle,
  link,
  transparent = true,
  hasPadding = true,
}: PropsWithChildren<WrapperProps>) {
  return (
    <div className={cn(!transparent && 'bg-white pt-5', hasPadding && 'px-4')}>
      <div data-widget-nme="home-widget-wrapper">
        {(title || subTitle) && (
          <div
            className={cn(
              'flex flex-col md:flex-row justify-between md:items-center',
              'mt-5 mb-10 mx-8',
              'xl:mt-14 xl:mb-20 xl:mx-52 xl:gap-2'
            )}
          >
            <div className={cn('flex flex-col lg:flex-row lg:gap-2')}>
              {title && (
                <Title size="h2" className="text-lg font-bold inline">
                  {title}
                </Title>
              )}
              {subTitle && <Text className="text-lg font-bold text-gray-400"> {subTitle}</Text>}
            </div>
            {link && (
              <div className="sm:hidden lg:flex flex items-center">
                <AppLink href={link.href} title={link.title}>
                  <Button variant={transparent ? 'tertiary' : 'outline'} className="text-black rounded-full" size="lg">
                    {link.text}
                  </Button>
                </AppLink>
              </div>
            )}
          </div>
        )}
        {children}
      </div>
    </div>
  )
}

function List({ widgets }: ListProps) {
  return (
    <div data-name="widgets-list" className="flex flex-col">
      {widgets.map((widget: AppWidgets, i) => (
        <HomePageContainer key={i}>
          {widget.__typename === 'ProductsSliderWidget' && <ProductsSlider widget={widget} />}
          {widget.__typename === 'CarouselWidget' && <Carousel widget={widget} />}
          {widget.__typename === 'TilesWidget' && <Tiles widget={widget} />}
          {widget.__typename === 'NewsWidget' && <News widget={widget} />}
          {widget.__typename === 'ServiceWidget' && <ServiceWidget widget={widget} />}
        </HomePageContainer>
      ))}
    </div>
  )
}

// Widget.Wrapper = Wrapper;
Widget.List = List

Widget.Carousel = null // not ready
Widget.CategoryLink = null // not ready
Widget.Html = null // not ready
Widget.News = News
Widget.Service = ServiceWidget
