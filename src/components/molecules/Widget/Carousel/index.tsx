'use client'
import { <PERSON><PERSON>ef<PERSON>, ArrowRight } from 'lucide-react'
import React, { useCallback, useRef } from 'react'
import { Navigation, Autoplay } from 'swiper/modules'
import { Swiper, SwiperRef, SwiperSlide } from 'swiper/react'

import Slide from '@components/molecules/Widget/Carousel/Slide'
import CircularProgressButton from '@components/molecules/Widget/components/Slider/CircularProgressButton'
import { CarouselWidget as CarouselWidgetModel } from '@lib/_generated/graphql_sdk'
import { SlideStatic } from '@components/molecules/Widget/Carousel/SlideStatic'

interface CarouselWidgetProps {
  widget: CarouselWidgetModel
}

export const Carousel: React.FC<CarouselWidgetProps> = ({ widget }) => {
  // Fix for Swiper widget: Resolves disappearing slides issue when there are fewer than 9 slides, with centered and looped settings enabled.
  const slides = widget.slides.length < 9 ? [...widget.slides, ...widget.slides, ...widget.slides] : widget.slides
  const [currentSlide, setCurrentSlide] = React.useState(0)
  const delay = 5000
  const prevRef = useRef(null)
  const nextRef = useRef(null)
  const sliderRef = useRef<SwiperRef>(null)

  const handlePrev = useCallback(() => {
    if (!sliderRef.current) return
    sliderRef.current.swiper.slidePrev()
  }, [])

  const handleNext = useCallback(() => {
    if (!sliderRef.current) return
    sliderRef.current.swiper.slideNext()
  }, [])

  return (
    <Swiper
      ref={sliderRef}
      autoplay={{
        delay,
        disableOnInteraction: false,
      }}
      loop
      centeredSlides
      spaceBetween={20}
      // slidesPerView={1.05}
      slidesPerView="auto"
      modules={[Navigation, Autoplay]}
      // breakpoints={{
      //   640: { slidesPerView: 1.2 },
      //   768: { slidesPerView: 1.2 },
      //   1024: { slidesPerView: 1.4 },
      //   1280: { slidesPerView: 1.4 },
      //   1536: { slidesPerView: 1.9 },
      //   1920: { slidesPerView: 2.05 },
      //   2560: { slidesPerView: 3 },
      //   3440: { slidesPerView: 3.1 }, // 10 - 3840x2160 4k,
      //   3840: { slidesPerView: 3.9 },
      // }}
      onSlideChange={(swiper) => {
        setCurrentSlide(swiper.realIndex)
      }}
    >
      {slides.map((slide, i) => (
        <SwiperSlide key={i} className="w-fit !h-auto hero-slide">
          <SlideStatic href={slide.link?.href || '#'} title={slide.title} image={slide.image} />
        </SwiperSlide>
      ))}
      <div className="relative py-3 -mt-8 left-auto right-auto z-20 w-full flex justify-center gap-6">
        <CircularProgressButton
          duration={delay}
          width={60}
          height={60}
          onClick={handlePrev}
          currentSlide={currentSlide}
          autoplay={false}
          ref={prevRef}
        >
          <ArrowLeft className="text-primary" />
        </CircularProgressButton>
        <CircularProgressButton
          duration={delay}
          width={60}
          height={60}
          onClick={handleNext}
          currentSlide={currentSlide}
          autoplay={true}
          ref={nextRef}
        >
          <ArrowRight className="text-primary" />
        </CircularProgressButton>
      </div>
    </Swiper>
  )
}
