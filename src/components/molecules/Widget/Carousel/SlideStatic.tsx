import React, { PropsWithChildren } from 'react'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import { Image as ImgProps } from '@lib/_generated/graphql_sdk'

interface Props {
  href: string
  title: string
  image: ImgProps
}

export const SlideStatic: React.FC<Props> = ({ href, title, image }) => {
  return (
    <div className="w-[371px] h-[478px] xl:h-[480px] xl:w-[930px] relative">
      <AppLink href={href} title={title} className="cursor-pointer">
        <div className="text-black ">
          <Img fill className="rounded-3xl" src={image.src} mobileSrc={image.mobileSrc} alt={title} />
        </div>
      </AppLink>
    </div>
  )
}
