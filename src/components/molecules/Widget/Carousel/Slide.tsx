'use client'
import React, { PropsWithChildren } from 'react'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import RawContent from '@atoms/RawContent'
import BuyCheapBadge from '@components/molecules/ProductCard/components/BuyCheapBadge'
import { MagentoLabels, Maybe, Price } from '@lib/_generated/graphql_sdk'
import { cn } from '@/src/components/lib/utils'

interface Props {
  href: string
  title: string
  image: string
  description: string
  price: Maybe<Price> | undefined
  brandLogo?: string
  otherBadges?: Maybe<string[]>
  buttonLabel?: string
  priceLabels?: Maybe<MagentoLabels>
}

const SlideTitle: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <h1
      className={cn('lg:text-5xl text-3xl font-bold', 'bg-clip-text text-transparent leading-[36px] lg:leading-[60px]')}
      style={{
        backgroundImage: 'linear-gradient(to right, #EEC800 0, #EE7F00 100%)',
      }}
    >
      {children}
    </h1>
  )
}
const SlideDescription: React.FC<{ description: string }> = ({ description }) => {
  return (
    <div className="text-white mt-2 text-sm lg:text-base leading-5 lg:leading-6 lg:pr-36">
      <RawContent content={description} />
    </div>
  )
}
const SlidePrice: React.FC<{ priceLabels?: Maybe<MagentoLabels>; price: Price }> = ({ priceLabels, price }) => {
  return (
    <div className="relative flex flex-col bg-primary w-fit rounded-xl px-5 pt-2.5 pb-1">
      <div className="absolute -top-[30%] lg:-top-[50%]">{priceLabels?.buyCheap && <BuyCheapBadge dark />}</div>
      <div className="flex gap-2 items-baseline">
        <span className="font-bold text-3xl lg:text-4xl text-white">{price.value}</span>{' '}
        <span className="text-2xl lg:text-3xl text-white">{price.currency}</span>
      </div>
    </div>
  )
}

const Slide: React.FC<Props> = ({
  href,
  title,
  image,
  description,
  price,
  brandLogo,
  otherBadges,
  buttonLabel,
  priceLabels,
}) => {
  return (
    <AppLink href={href} title={title} className="cursor-pointer hover:underline block">
      <div
        className={cn(
          'bg-background-dark flex flex-col',
          'h-[480px] w-full bg-cover bg-center rounded-3xl relative',
          'lg:overflow-hidden lg:bg-transparent'
        )}
      >
        <div
          className={cn(
            'top-0 left-0 w-full z-10 relative flex-none',
            'lg:absolute lg:w-8/12 4xl:w-7/12 lg:h-full lg:overflow-hidden'
          )}
        >
          <div className={cn('transform lg:-skew-x-26 origin-top-left', 'lg:bg-background-dark lg:h-full')}>
            <div
              className={cn(
                'transform py-6 px-7 lg:px-16 lg:py-10 h-full flex flex-col justify-between relative',
                'lg:skew-x-26 lg:pr-3 lg:ml-24 lg:-mr-28'
              )}
            >
              <SlideTitle>{title}</SlideTitle>
              {description && <SlideDescription description={description} />}
              <div className="hidden lg:block">{price && <SlidePrice priceLabels={priceLabels} price={price} />}</div>
            </div>
          </div>
        </div>
        <div className={cn('w-full relative rounded-3xl lg:h-[480px] flex-1 flex-grow')}>
          <div className="relative h-full z-10">
            <div className="flex">
              {price && (
                <div className="flex lg:hidden relative pl-3">
                  <SlidePrice priceLabels={priceLabels} price={price} />
                </div>
              )}
              {brandLogo && (
                <Img
                  className="absolute right-3 top-3 bg-white rounded-xl"
                  src={brandLogo}
                  width={70}
                  height={40}
                  alt="brand logo"
                />
              )}
            </div>
            {otherBadges && (
              <div className="absolute bottom-7 right-3 row">
                {otherBadges.map((badge, index) => (
                  <Img key={index} src={badge} width={100} height={50} alt="product badge" />
                ))}
              </div>
            )}
          </div>
          <Img
            fill
            className="lg:object-contain bg-[#2d3132] lg:object-right rounded-3xl"
            src={image || ''}
            alt={title}
          />
        </div>
      </div>
    </AppLink>
  )
}

export default Slide
