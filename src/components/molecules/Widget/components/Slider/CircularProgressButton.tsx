import React, { useEffect, useState, forwardRef } from 'react'

import CustomButton from '@atoms/Button/CustomButton'
import { cn } from '@components/lib/utils'

interface CircularProgressButtonProps {
  children: React.ReactNode
  duration: number // Duration of the progress in ms
  width: number
  height: number
  onClick: () => void
  currentSlide: number
  autoplay: boolean
  className?: string
  style?: React.CSSProperties
}

const CircularProgressButton = forwardRef<HTMLButtonElement, CircularProgressButtonProps>(
  (
    {
      children,
      duration,
      width,
      height,
      onClick,
      currentSlide,
      autoplay = false,
      className = '',
      style = {},
    }: CircularProgressButtonProps,
    ref
  ) => {
    const strokeWidth = 2
    const radius = (width - strokeWidth) / 2
    const circumference = radius * 2 * Math.PI
    const [currentProgress, setCurrentProgress] = useState(0)

    useEffect(() => {
      if (!autoplay) {
        setCurrentProgress(0)
        return
      }

      let animationFrame: number
      const start = performance.now()

      const updateProgress = (time: number) => {
        const elapsed = time - start
        const newProgress = Math.min((elapsed / duration) * 100, 100)
        setCurrentProgress(newProgress)

        if (newProgress < 100) {
          animationFrame = requestAnimationFrame(updateProgress)
        }
      }

      animationFrame = requestAnimationFrame(updateProgress)

      return () => cancelAnimationFrame(animationFrame)
    }, [duration, currentSlide, autoplay])

    const strokeDashoffset = circumference - (currentProgress / 100) * circumference

    return (
      <CustomButton
        className={cn('relative rounded-full select-none shadow-lg', className)}
        variant="white"
        onClick={onClick}
        ref={ref}
        style={{ ...style, width, height }}
      >
        {autoplay && (
          <svg
            className="absolute inset-0 w-full h-full"
            width={width}
            height={height}
            viewBox={`0 0 ${width} ${height}`}
          >
            <circle
              stroke="#ee7f00"
              fill="transparent"
              strokeWidth={strokeWidth}
              strokeDasharray={`${circumference} ${circumference}`}
              strokeDashoffset={strokeDashoffset}
              r={radius}
              cx={width / 2}
              cy={height / 2}
            />
          </svg>
        )}
        {children}
      </CustomButton>
    )
  }
)

CircularProgressButton.displayName = 'CircularProgressButton'

export default CircularProgressButton
