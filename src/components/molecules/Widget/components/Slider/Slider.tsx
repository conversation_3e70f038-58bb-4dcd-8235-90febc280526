'use client'
import React, { useEffect, useState, useCallback } from 'react'

import tailwindConfig from '@/tailwind.config'
import CustomButton from '@atoms/Button/CustomButton'
import Container from '@atoms/Container'
import Img from '@atoms/Img'
import { addClasses } from '@lib/style/style'

import CircularProgressButton from './CircularProgressButton'

interface Props {
  children: React.ReactNode
  heroMode?: boolean
  newsMode?: boolean
  slidesPerView?: {
    phone: number
    desktop: number
  }
  slidesToScroll: number
  autoplay?: boolean
  autoplaySpeed?: number
  linearProgress?: boolean
  navBtnsRightTop?: boolean
  navBtnsCenter?: boolean
  navBtnsCenterSmall?: boolean
  onSlideChange?: (currentSlide: number) => void
}

function Slider({
  children,
  slidesPerView = {
    phone: 1,
    desktop: 1,
  },
  slidesToScroll,
  heroMode = false,
  newsMode = false,
  autoplay = false,
  autoplaySpeed = 3000,
  linearProgress = false,
  navBtnsRightTop = false,
  navBtnsCenter = false,
  navBtnsCenterSmall = false,
  onSlideChange,
}: Props) {
  const childrenArray = React.Children.toArray(children)
  const totalSlides = childrenArray.length
  const [currentSlide, setCurrentSlide] = useState(0)
  const [slidesToShow, setSlidesToShow] = useState(slidesPerView.desktop)
  const [touchStart, setTouchStart] = useState(0)
  const [touchEnd, setTouchEnd] = useState(0)
  const [manualChange, setManualChange] = useState(false)
  const breakpoints = tailwindConfig?.theme?.screens as any

  const breakpointToNum = (inputString: string) => {
    if (typeof inputString === 'string') {
      return parseInt(inputString.replace('px', ''), 10)
    }
    return NaN
  }

  let slides = []
  if (heroMode && totalSlides > 1) {
    slides = [childrenArray[totalSlides - 1], ...childrenArray, childrenArray[0]]
  } else {
    slides = [...childrenArray]
  }

  const updateSlidesToShow = useCallback(() => {
    const windowWidth = window.innerWidth

    if (windowWidth >= breakpointToNum(breakpoints.desktop)) {
      setSlidesToShow(slidesPerView.desktop)
    } else {
      setSlidesToShow(slidesPerView.phone)
    }
  }, [breakpoints, slidesPerView])

  const changeSlide = useCallback(
    (newIndex: number) => {
      setCurrentSlide(newIndex)
      setManualChange(true)
      if (onSlideChange) {
        onSlideChange(newIndex)
      }
    },
    [onSlideChange]
  )

  const handleTouchStart = (e: any) => {
    setTouchStart(e.targetTouches[0].clientX)
  }

  const handleTouchMove = (e: any) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (touchStart - touchEnd > 75) {
      nextSlide()
    } else if (touchStart - touchEnd < -75) {
      prevSlide()
    }
  }

  const nextSlide = useCallback(() => {
    let nextIndex = (currentSlide + slidesToScroll) % totalSlides
    let visibleSlidesEndIndex = nextIndex + slidesToShow - (newsMode ? slidesPerView.desktop : 1)
    if (visibleSlidesEndIndex >= totalSlides) {
      nextIndex = 0
    }
    changeSlide(nextIndex)
  }, [changeSlide, currentSlide, slidesToScroll, slidesToShow, totalSlides, newsMode, slidesPerView.desktop])

  const prevSlide = useCallback(() => {
    let newIndex = currentSlide - slidesToScroll
    if (newIndex < 0) {
      newIndex = totalSlides - slidesToShow + (newsMode ? 1 : newIndex)
      newIndex = Math.max(0, newIndex)
    }

    changeSlide(newIndex)
  }, [currentSlide, slidesToScroll, changeSlide, totalSlides, slidesToShow, newsMode])

  useEffect(() => {
    updateSlidesToShow()
    window.addEventListener('resize', updateSlidesToShow)

    return () => {
      window.removeEventListener('resize', updateSlidesToShow)
    }
  }, [updateSlidesToShow])

  useEffect(() => {
    let interval: any = null

    if (autoplay && !manualChange) {
      interval = setInterval(() => {
        const nextIndex = (currentSlide + slidesToScroll) % totalSlides
        setCurrentSlide(nextIndex)
        if (onSlideChange) {
          onSlideChange(nextIndex)
        }
      }, autoplaySpeed)
    }

    if (manualChange) {
      clearInterval(interval)
      setManualChange(false)

      interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + slidesToScroll) % totalSlides)
      }, autoplaySpeed)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [autoplay, autoplaySpeed, currentSlide, manualChange, onSlideChange, slidesToScroll, totalSlides])

  const calcTransformNewsMode = () => {
    let offset = 0
    for (let i = 0; i < currentSlide; i++) {
      const windowWidth = window.innerWidth

      if (windowWidth <= breakpointToNum(breakpoints.desktop)) {
        offset += 90
      } else {
        offset += 30
      }
    }
    return `translateX(-${offset}%)`
  }

  const calcTransform = useCallback(() => {
    let slideIndex = currentSlide
    if (heroMode) {
      slideIndex += 1
    }

    return `translateX(-${slideIndex * (100 / slidesToShow)}%)`
  }, [currentSlide, heroMode, slidesToShow])

  const slideStyle = { width: `calc(100% / ${slidesToShow})` }
  const slideStyleNewsMode = (index: number) => {
    return currentSlide === index ? 'desktop:w-[70%] w-[90%]' : 'desktop:w-[30%] w-[90%]'
  }

  const calculateProgress = (totalCount: number, currentItemCount: number) => {
    if (totalCount === 0) {
      return 0
    }
    const progress = ((currentItemCount + 1) / totalCount) * 100
    return Number(progress.toFixed(2))
  }

  return (
    <div
      className={`relative flex flex-col desktop:w-full overflow-hidden`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <div
        className={addClasses(
          'flex transition-transform ease-in-out duration-300 relative',
          `${heroMode && 'desktop:mx-[25%]'}`,
          `${!heroMode && !navBtnsCenterSmall && 'relative z-10 pb-20'}`,
          `${navBtnsRightTop ? 'order-3' : 'order-1'}`,
          `${navBtnsCenter && 'pb-0 desktop:b-20'}`,
          `${newsMode && 'desktop:mx-[12.5%]'}`
        )}
        style={{
          transform: newsMode ? calcTransformNewsMode() : calcTransform(),
        }}
      >
        {slides.map((slide, index) => (
          <div
            key={index}
            className={addClasses(
              'flex-shrink-0',
              `phone:w-10/12 desktop:w-2/12 tablet:w-4/12`,
              `${newsMode && slideStyleNewsMode(index)}`
            )}
            style={!newsMode ? slideStyle : {}}
          >
            {slide}
          </div>
        ))}
      </div>

      {totalSlides > 1 && slidesToShow < totalSlides && (
        <div className={addClasses('grid order-2 z-1', `${heroMode ? 'mt-[-32px]' : 'mt-0'}`)}>
          <Container
            className={addClasses(
              `w-full flex`,
              `${navBtnsRightTop ? 'pb-16 justify-end absolute right-0 left-0 top-[-136px]' : 'justify-center'}`,

              // `${navBtnsCenterSmall && navBtnsCenter && 'max-w-full'}`,
              `${
                navBtnsCenter &&
                !navBtnsCenterSmall &&
                'absolute top-[17.5%] z-50 left-0 right-0 justify-between !px-0 max-w-[calc(75%+60px)]'
              }`,
              `${!heroMode && 'hidden desktop:flex'}`
            )}
          >
            <CustomButton
              className={addClasses(
                'w-[60px] h-[60px] !rounded-[30px] select-none',

                `${
                  navBtnsCenter && '!absolute top-1/2 z-50 left-0 right-0 justify-between !px-0 max-w-[calc(75%+60px)]'
                }`,
                `${navBtnsCenterSmall ? 'absolute top-1/2 transform -translate-y-1/2' : 'mx-3'}`,
                `${navBtnsCenterSmall && currentSlide === 0 && 'hidden'}`
              )}
              variant="white"
              onClick={prevSlide}
            >
              <Img
                src={'/images/icons/slider-arrow-left.svg'}
                alt="Prev"
                width={24}
                height={24}
                itemProp={'slider arrow left'}
              />
            </CustomButton>
            <div
              className={addClasses(
                `${
                  navBtnsCenterSmall && 'absolute top-1/2 transform -translate-y-1/2 right-0'
                } ${newsMode && 'absolute mx-3 right-0'}`
              )}
            >
              <CircularProgressButton
                duration={autoplaySpeed}
                onClick={nextSlide}
                currentSlide={currentSlide}
                autoplay={autoplay}
                width={60}
                height={60}
              >
                <Img
                  src={'/images/icons/slider-arrow-right.svg'}
                  alt="Next"
                  width={24}
                  height={24}
                  itemProp={'slider arrow right'}
                />
              </CircularProgressButton>
            </div>
          </Container>
        </div>
      )}
      {linearProgress && (
        <div className="w-full bg-gray-200 order-3">
          <div
            className="bg-primary p-1 transition-all duration-500 ease-in-out"
            style={{
              width: `${calculateProgress(totalSlides, currentSlide)}%`,
            }}
          />
        </div>
      )}
    </div>
  )
}

export default Slider
