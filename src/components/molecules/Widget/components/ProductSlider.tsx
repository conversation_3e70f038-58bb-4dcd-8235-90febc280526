'use client'

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react'
import React, { useCallback, useRef } from 'react'
import { FreeMode } from 'swiper/modules'
import { Navigation } from 'swiper/modules'
import { Swiper, SwiperRef, SwiperSlide } from 'swiper/react'

import ProductCard from '@components/molecules/ProductCard/ProductCard'
import { ProductViewFragment } from '@lib/_generated/graphql_sdk'

import '../styles.css'
import CircularProgressButton from './Slider/CircularProgressButton'

interface ProductSliderProps {
  products: ProductViewFragment[]
}

export const ProductSlider = ({ products }: ProductSliderProps) => {
  const [currentSlide, setCurrentSlide] = React.useState(0)
  const sliderRef = useRef<SwiperRef>(null)

  const handlePrev = useCallback(() => {
    if (!sliderRef.current) return
    sliderRef.current.swiper.slidePrev()
  }, [])

  const handleNext = useCallback(() => {
    if (!sliderRef.current) return
    sliderRef.current.swiper.slideNext()
  }, [])

  return (
    <Swiper
      ref={sliderRef}
      modules={[FreeMode, Navigation]}
      onSlideChange={(swiper) => {
        setCurrentSlide(swiper.realIndex)
      }}
      loop
      spaceBetween={20}
      slidesPerView={1.7}
      freeMode={{
        enabled: true,
        sticky: true,
      }}
      centeredSlides={false}
      breakpoints={{
        640: { slidesPerView: 2.7 },
        768: { slidesPerView: 3.2 },
        1024: { slidesPerView: 5.2 },
        1280: { slidesPerView: 6.2 },
        1536: { slidesPerView: 7.2 },
        1920: { slidesPerView: 8.2 },
        2560: { slidesPerView: 10.2 },
        3440: { slidesPerView: 12.2 }, // 10 - 3840x2160 4k,
        3840: { slidesPerView: 14.2 },
      }}
      className="product-slider"
    >
      {products.map((product, index) => (
        <SwiperSlide key={index}>
          <ProductCard product={product} className="flex" />
        </SwiperSlide>
      ))}
      <div className="hidden md:flex relative py-4 left-auto right-auto z-20 w-full justify-center gap-6">
        <CircularProgressButton
          duration={0}
          width={50}
          height={50}
          onClick={handlePrev}
          currentSlide={currentSlide}
          autoplay={false}
        >
          <ArrowLeft className="text-primary" />
        </CircularProgressButton>
        <CircularProgressButton
          duration={0}
          width={50}
          height={50}
          onClick={handleNext}
          currentSlide={currentSlide}
          autoplay={false}
        >
          <ArrowRight className="text-primary" />
        </CircularProgressButton>
      </div>
    </Swiper>
  )
}
