'use client'
import React, { useState, ReactElement } from 'react'

import useBreakpoint from '@/src/hooks/useBreakpoint'
import ArrowCircle from '@atoms/Icons/ArrowCircle'
import { useSimpleMenuContext } from '@context/SimpleMenuContext'
import { addClasses } from '@lib/style/style'

import Tab from './Tab'

type TabProps = {
  title: string
  active: boolean
  onClick: () => void
}

interface Props {
  children: ReactElement<TabProps>[]
}

const Tabs: React.FC<Props> = ({ children }) => {
  const [activeTab, setActiveTab] = useState(0)
  const { isOpen, toggle } = useSimpleMenuContext()
  const { breakpoint, minBreakpoint } = useBreakpoint()

  const handleTabClick = (index: number) => () => {
    setActiveTab(index)
    // Close the menu on mobile and tablet
    if (minBreakpoint('lg')) {
      toggle()
    }
  }

  const tabs: React.ReactElement<TabProps>[] = React.Children.map(
    children,
    (child, index) =>
      index === activeTab && (
        <div className="flex justify-between items-center">
          <Tab key={index} title={child?.props.title} active={index === activeTab} onClick={handleTabClick(index)} />
          <ArrowCircle big className={isOpen ? 'rotate-180' : ''} />
        </div>
      )
  )

  return (
    <div>
      {tabs.length > 1 && (
        <div
          className={addClasses(
            'mb-8 w-full border-b-[3px] border-white px-6',
            isOpen && 'bg-white xl:bg-transparent rounded-[8px]'
          )}
        >
          <div
            className={addClasses(
              'xl:hidden flex-col w-full flex xl:justify-center',
              'border-b-[3px] border-primary mb-[-3px]'
            )}
            onClick={toggle}
          >
            {tabs}
          </div>

          <div className={addClasses('xl:flex hidden flex-col xl:flex-row justify-center', isOpen && '!flex')}>
            {React.Children.map(children, (child, index) => (
              <Tab
                key={index}
                title={`${child?.props.title}`}
                active={index === activeTab}
                onClick={handleTabClick(index)}
              />
            ))}
          </div>
        </div>
      )}
      <div className="relative px-4">
        {React.Children.map(children, (child, index) => (
          <div
            key={index}
            className={addClasses(
              `inset-0 transition-opacity duration-300 ease-linear`,
              `${index === activeTab ? 'opacity-100' : 'opacity-0 h-[0] overflow-hidden'}`
            )}
            style={{ visibility: index === activeTab ? 'visible' : 'hidden' }}
          >
            {child}
          </div>
        ))}
      </div>
    </div>
  )
}

export default Tabs
