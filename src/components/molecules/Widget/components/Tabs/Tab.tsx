import React from 'react'

import { addClasses } from '@lib/style/style'

interface Props {
  title: string
  active: boolean
  onClick: any
}

const Tab: React.FC<Props> = ({ title, active, onClick }) => {
  return (
    <div
      className={addClasses(
        'cursor-pointer mb-[-3px] text-lg opacity-50 py-6 xl:py-6 xl:px-10 ',
        'hover:opacity-75',
        active && 'xl:border-b-[3px] border-primary !opacity-100 font-bold'
      )}
      onClick={onClick}
    >
      {title}
    </div>
  )
}

export default Tab
