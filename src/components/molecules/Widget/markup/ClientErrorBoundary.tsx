'use client'

import React, { useState, useEffect } from 'react'

interface ClientErrorBoundaryProps {
  children: React.ReactNode
  fallbackHtml: string
}

export function ClientErrorBoundary({ children, fallbackHtml }: ClientErrorBoundaryProps) {
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    const errorHandler = (event: ErrorEvent) => {
      event.preventDefault()
      setHasError(true)
      console.error('Caught error in ClientErrorBoundary:', event.error)
    }

    window.addEventListener('error', errorHandler)
    return () => window.removeEventListener('error', errorHandler)
  }, [])

  if (hasError) {
    return <div dangerouslySetInnerHTML={{ __html: fallbackHtml }} />
  }

  return <React.Fragment>{children}</React.Fragment>
}
