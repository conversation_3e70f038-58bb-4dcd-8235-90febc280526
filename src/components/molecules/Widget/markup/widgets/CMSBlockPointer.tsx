import CMSMarkup, { mkCMSMarkup } from '../CMSMarkup'

const mkCMSBlockPointer = (getBlock: (id: string) => Promise<string | undefined>, dependencies: any) => {
  const CMSBlock = ({ id }: { id: string }) => {
    const CMSMarkup = mkCMSMarkup({ CMSBlock, ...dependencies })
    return getBlock(id).then((block) => {
      //   console.log('CMS BLOCK POINTER GOT', id, block)
      return block && <CMSMarkup markup={block} />
    })
  }
  return CMSBlock
}
export default mkCMSBlockPointer
