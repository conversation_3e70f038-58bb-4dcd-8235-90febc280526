import { cn } from '@/src/components/lib/utils'
import Image from 'next/image'
import Link from 'next/link'
import React, { ReactNode } from 'react'
const x = 'h-[300px] overflow-x-auto border-box m-10'
export const PromoBanner = ({ children, className }: { children: ReactNode; className?: string }) => {
  // Extract children elements
  const childArray = React.Children.toArray(children)

  // Find heading and paragraph
  const titleElement = childArray.find(
    (child) => React.isValidElement(child) && ['h1', 'h2', 'h3'].includes(child.type as string)
  ) as React.ReactElement | undefined

  const paragraphElement = childArray.find((child) => React.isValidElement(child) && child.type === 'p') as
    | React.ReactElement
    | undefined

  // Find image
  const imgElement = childArray.find((child) => React.isValidElement(child) && child.type === 'img') as
    | React.ReactElement<React.ImgHTMLAttributes<HTMLImageElement>>
    | undefined

  // Find icon container
  const iconContainer = childArray.find(
    (child) => React.isValidElement(child) && ((child as React.ReactElement).props as any).slot === 'icon'
  ) as React.ReactElement | undefined

  // Extract the icon from the container
  const iconElement = iconContainer
    ? (React.Children.only((iconContainer.props as any).children) as React.ReactElement)
    : null

  // Find link/button
  const linkElement = childArray.find((child) => React.isValidElement(child) && 'href' in (child.props as any)) as
    | React.ReactElement
    | undefined

  // Extract image props - only src and alt
  const imgSrc = imgElement?.props.src || ''
  const imgAlt = imgElement?.props.alt || (titleElement?.props as any).children?.toString() || 'Seasonal offering'
  const imgClass = imgElement?.props.className || ''
  // Clone the icon element with the className
  const iconWithClass = iconElement
    ? React.cloneElement(iconElement, {
        className: `w-16 rounded-xl bg-white p-2  ${imgClass || ''}`,
      } as React.HTMLAttributes<HTMLElement>)
    : null

  return (
    <div className={`row-span-1 min-w-[90%] max-h-full ${className || ''}`}>
      <div className={`card w-full   h-full p-10 text-white relative overflow-hidden rounded-3xl bg-black/10`}>
        {imgSrc && (
          <Image
            alt={imgAlt}
            src={imgSrc}
            fill
            className={cn('absolute object-cover object-right-bottom', imgElement?.props.className)}
            sizes="100vw"
          />
        )}

        {/* Add flex-grow-0 to prevent excessive growth */}
        <div className="min-w-[200px] w-[60%] h-full flex justify-between items-start flex-col relative z-10">
          {/* Add flex-shrink-0 to main content to prevent shrinking and overflow-hidden to contain text */}
          <div className="w-full flex-shrink-0">
            {iconElement && <div className="rounded-2xl inline-block relative">{iconWithClass}</div>}

            <div className={`text-4xl font-bold mt-8 mb-4`}>{titleElement}</div>

            {paragraphElement && (
              <div
                className={`mb-8 text-lg 2xl:text-xl max-h-full line-clamp-2 2xl:line-clamp-2 4xl:line-clamp-4 overflow-hidden `}
              >
                {paragraphElement}
              </div>
            )}
          </div>

          {/* Ensure the link doesn't get pushed out by adding flex-shrink-0 */}
          {linkElement && (
            <Link
              href={(linkElement.props as any).href}
              className="inline-flex items-center justify-center gap-2 whitespace-nowrap transition-colors 
                         focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring 
                         disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none 
                         [&_svg]:size-4 [&_svg]:shrink-0 uppercase text-sm tracking-[1.65px] 
                         leading-[138.8%] bg-slate-200 text-tertiary-foreground shadow-sm h-10 
                         px-8 rounded-full font-medium flex-shrink-0"
            >
              {(linkElement.props as any).children}
            </Link>
          )}
        </div>
      </div>
    </div>
  )
}
