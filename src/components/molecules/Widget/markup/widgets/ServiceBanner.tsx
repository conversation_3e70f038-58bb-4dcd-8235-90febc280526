import { LucidePlusCircle } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import React, { ReactNode } from 'react'

export const ServiceBanner = ({ children, className }: { children: ReactNode; className?: string }) => {
  // Extract children elements
  const childArray = React.Children.toArray(children)

  // Find title (text node)
  const title = childArray.filter(
    (child) => React.isValidElement(child) && ['h1', 'h2', 'h3'].includes(child.type as string)
  )
  const info = childArray.filter((child) => React.isValidElement(child) && ['p'].includes(child.type as string)) as
    | ReactNode
    | undefined
  // Find image
  const imgElement = childArray.find((child) => React.isValidElement(child) && child.type === 'img') as
    | React.ReactElement<React.ImgHTMLAttributes<HTMLImageElement>>
    | undefined

  // Find icon container
  const iconContainer = childArray.find(
    (child) =>
      React.isValidElement(child) &&
      child.type === 'div' &&
      ((child.props as any).slot === 'icon' || (child.props as any)['data-slot'] === 'icon')
  ) as React.ReactElement | undefined

  // Extract the icon from the container
  const iconElement = iconContainer
    ? (React.Children.only((iconContainer.props as any).children) as React.ReactElement)
    : null

  // Find link
  const linkElement = childArray.find((child) => React.isValidElement(child) && 'href' in (child.props as any)) as
    | React.ReactElement
    | undefined
  // Extract image props
  const imgProps = (imgElement?.props as React.ImgHTMLAttributes<HTMLImageElement>) || {}

  // Clone the icon element with the className
  const iconWithClass = iconElement
    ? React.cloneElement(iconElement, {
        className: `w-16 h-16 ${(iconElement.props as any).className || ''}`,
      } as React.HTMLAttributes<HTMLElement>)
    : null

  return (
    <Link
      href={(linkElement?.props as any).href || ''}
      className={`relative aspect-[4/6] w-full min-h-fit ${className || ''}`}
    >
      <Image
        src={imgProps.src || ''}
        alt={imgProps.alt || ''}
        width={300}
        height={400}
        className="object-cover rounded-3xl absolute h-full w-full"
      />
      <div className="absolute w-full h-full flex flex-col justify-end p-6 group hover:cursor-pointer min-h-fit">
        <div className="bg-white rounded-2xl w-full h-1/2 p-4 2xl:p-8  flex flex-col justify-between min-h-fit">
          <div className="flex flex-col 2xl:gap-6 h-2/3 min-h-fit">
            <div className="text-2xl  2xl:text-3xl 3xl:text-5xl 4xl:text-6xl font-bold col-span-2">{title}</div>
            <div className="block  text-lg 2xl:text-xl 3xl:text-2xl  min-h-32">{info}</div>
          </div>
          <div className="flex justify-between">
            {iconWithClass}
            <div className="flex items-center">
              <span className="hidden lg:block text-nowrap group-hover:underline">
                {(linkElement?.props as any).children}
              </span>
              <LucidePlusCircle className="rounded-full fill-primary stroke-white h-12 w-12" />
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}
