import { GraphQLBackend } from '@/src/lib/api/graphql'
import mkCMSBlockPointer from './widgets/CMSBlockPointer'
import Box3DIcon from '@icons/box3d.inline.svg'
import { HeadphonesIcon } from 'lucide-react'
import ChangeIcon from '@/src/components/atoms/Icons/ChangeIcon'
import { ProductPerk } from '../../ProductBuyCards/components/ProductActions'
import Link from 'next/link'
import FooterColumn from '@/src/app/(store)/_components/Footer/FooterColumn'
import { HoverIcons } from '@/src/components/atoms/Icons/HoverIcons'
import mkCMSMarkup from './CMSMarkup'
import { ServiceBanner } from './widgets/ServiceBanner'
import { PromoBanner } from './widgets/PromoBanner'
import { PaymentImageBox } from '@/src/app/(store)/_components/Footer/PaymentMethods'
import { HTML } from '@/src/components/atoms/HTML'
import SimpleMenu from '@/src/components/context/SimpleMenuContext'
import ExpandableBox from '../../ExpandableBox'

const dependencies = {
  Box3DIcon,
  HeadphonesIcon,
  ChangeIcon,
  ProductPerk,
  Link,
  FooterColumn,
  ...HoverIcons,
  ServiceBanner,
  PromoBanner,
  PaymentImageBox,
  /* TODO: MagentoWidget Placeholder implementation to prevent errors. Matches arbitrary Magento Widgets.
   Implementing product slider widget on arbitrary CMS Blocks/Pages/etc requires the ability 
   to query products by category id instead of (or in addition to) category slug. 
   To be discussed.
  */
  MagentoWidget: (props: any) => <></>,
}

export const CMSBlock = mkCMSBlockPointer(
  (blockId) =>
    GraphQLBackend.getStaticBlock({ id: blockId })
      .then((b) => b.getStaticBlock.content)
      .catch((err) => ''),
  dependencies
)
export const CMSMarkup = mkCMSMarkup({ CMSBlock, ...dependencies })
