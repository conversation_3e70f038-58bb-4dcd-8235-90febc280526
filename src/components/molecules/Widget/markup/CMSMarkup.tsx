import React from 'react'
import * as babel from '@babel/standalone'
import { ClientErrorBoundary } from './ClientErrorBoundary'
import RawContent from '@/src/components/atoms/RawContent'

interface CMSMarkupProps {
  markup: string
}

/**
 * Renders a string of CMS markup (JSX/XML) as React components with SSR support
 * Falls back to sanitized HTML if React rendering fails
 */
export const mkCMSMarkup = (dependencies: Record<string, any>) => {
  const CMSMarkup = ({ markup }: CMSMarkupProps) => {
    // Skip rendering if markup is empty
    if (!markup || markup.trim() === '') {
      return null
    }

    try {
      // console.time(`CMS BLOCK ${markup.slice(0, 50)}}`)
      // Preprocess the markup to convert HTML syntax to React syntax
      const processedMarkup = preprocessMarkup(markup)
      // if (markup.includes('universal')) console.log('ORIGINAL MARKUP', markup)
      // console.log('PROCESSED MARKUP', processedMarkup)
      // Wrap the markup in a fragment
      const wrappedMarkup = `<React.Fragment>${processedMarkup}</React.Fragment>`

      // Transform the markup to JavaScript
      // const transformedCode = babel.transform(`const MarkupComponent = () => (${wrappedMarkup});`, {
      //   presets: ['react'],
      //   filename: 'cms-markup.jsx',
      // }).code
      let transformedCode
      try {
        // Transform the markup to JavaScript
        transformedCode = babel.transform(`const MarkupComponent = () => (${wrappedMarkup});`, {
          presets: ['react'],
          filename: 'cms-markup.jsx',
        }).code
      } catch (babelError) {
        console.error('Babel transform error:\n\n\n', markup, '\n\n\n---------', babelError)
        return <RawContent content={markup} />
      }

      // Create argument names and values arrays for the Function constructor
      const dependencyNames = Object.keys(dependencies)
      const dependencyValues = Object.values(dependencies)

      // Create a function that returns the component
      const fn = new Function('React', ...dependencyNames, `${transformedCode}; return MarkupComponent;`)

      // Execute the function with the injected dependencies
      const MarkupComponent = fn(React, ...dependencyValues)
      // console.timeEnd(`CMS BLOCK ${markup.slice(0, 50)}}`)

      // Render the React component
      return (
        <ClientErrorBoundary fallbackHtml={markup}>
          <MarkupComponent />
        </ClientErrorBoundary>
      )
    } catch (reactError) {
      console.error('Error rendering as React component:', reactError)

      try {
        const html = markup

        return <RawContent content={html} />
        // || <div dangerouslySetInnerHTML={{ __html: html }} />
      } catch (htmlError) {
        console.error('Error rendering as HTML:', htmlError)

        // Ultimate fallback
        return <div className="error-failed-rendering-markup">Failed to render content</div>
      }
    }
  }
  return CMSMarkup
}
export default mkCMSMarkup

/**
 * Preprocesses HTML-style markup to make it compatible with React
 */
function preprocessMarkup(markup: string) {
  // Replace class with className
  let processed = markup
    .replace(/\sclass=/g, ' className=')
    // replace {{media url}} template with actual url (hardcoded for now)
    .replace(/"{{media url="([^"]+)"}}"/g, `"${process.env.NEXT_PUBLIC_IMAGE_DOMAIN}/media/$1"`)
    // convert magento {{widget ... }} to react component <MagentoWidget .../>
    .replace(/\{\{widget\s+(.*?)\}\}/g, '<MagentoWidget $1/>')
    // replace a tag with next Link
    .replace(/<a/g, '<Link')
    .replace(/<\/a>/g, '</Link>')
    // remove html comments
    .replace(/<!--[\s\S]*?-->/g, '')
    // Transform style attributes
    .replace(/\sstyle="([^"]*)"/g, (match, styleContent) => {
      if (!styleContent.trim()) return ' style={{}}'

      const styleObj = styleContent
        .split(';')
        .filter((style: string) => style.trim() !== '')
        .map((style: string) => {
          const [property, value] = style.split(':').map((s) => s.trim())
          // Convert kebab-case to camelCase
          const camelCaseProp = property.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
          return `"${camelCaseProp}": "${value}"`
        })
        .join(', ')

      return ` style={{${styleObj}}}`
    })

  // Stack to track custom components
  const stack = []
  let result = ''
  let i = 0

  while (i < processed.length) {
    // Handle self-closing div with data-type
    if (
      i + 5 < processed.length &&
      processed.slice(i, i + 5) === '<div ' &&
      processed.slice(i).includes('data-type="') &&
      processed.slice(i, processed.indexOf('>', i) + 1).includes('/>')
    ) {
      const tagEnd = processed.indexOf('/>', i) + 2
      const tag = processed.slice(i, tagEnd)
      const dataTypeMatch = /data-type="([^"]+)"/.exec(tag)

      if (dataTypeMatch) {
        const componentName = dataTypeMatch[1]
        const attrs = tag
          .slice(5, tag.length - 2)
          .replace(/\s*data-type="[^"]+"\s*/, ' ')
          .trim()

        result += `<${componentName}${attrs ? ' ' + attrs : ''}/>`
        i = tagEnd
        continue
      }
    }

    // Handle opening div with data-type
    if (
      i + 5 < processed.length &&
      processed.slice(i, i + 5) === '<div ' &&
      processed.slice(i).includes('data-type="')
    ) {
      const tagEnd = processed.indexOf('>', i) + 1
      if (tagEnd > i && processed[tagEnd - 2] !== '/') {
        const tag = processed.slice(i, tagEnd)
        const dataTypeMatch = /data-type="([^"]+)"/.exec(tag)

        if (dataTypeMatch) {
          const componentName = dataTypeMatch[1]
          const attrs = tag
            .slice(5, tag.length - 1)
            .replace(/\s*data-type="[^"]+"\s*/, ' ')
            .trim()

          result += `<${componentName}${attrs ? ' ' + attrs : ''}>`
          stack.push(componentName)
          i = tagEnd
          continue
        }
      }
    }

    // Handle opening regular div
    if (i + 4 < processed.length && processed.slice(i, i + 4) === '<div') {
      const nextChar = processed[i + 4]
      if (nextChar === ' ' || nextChar === '>') {
        const tagEnd = processed.indexOf('>', i) + 1
        if (tagEnd > i && !processed.slice(i, tagEnd).includes('data-type="') && processed[tagEnd - 2] !== '/') {
          result += processed.slice(i, tagEnd)
          stack.push('div')
          i = tagEnd
          continue
        }
      }
    }

    // Handle closing div
    if (i + 6 <= processed.length && processed.slice(i, i + 6) === '</div>') {
      if (stack.length > 0) {
        const lastTag = stack.pop()
        result += lastTag !== 'div' ? `</${lastTag}>` : '</div>'
      } else {
        result += '</div>'
      }
      i += 6
      continue
    }

    // Regular character
    result += processed[i]
    i++
  }

  return result
}
