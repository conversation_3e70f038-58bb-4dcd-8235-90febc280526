import React from 'react'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import Paper from '@atoms/Paper'
import { Brand } from '@lib/_generated/graphql_sdk'

interface Props {
  brand: Brand
}

function BrandPreview({ brand }: Props) {
  return (
    <Paper className="flex justify-center items-center py-4 w-full">
      <AppLink href={brand.url.href}>
        <Img className="w-full rounded-3xl" src={brand.image.src} alt={brand.image.alt || ''} width={125} height={80} />
      </AppLink>
    </Paper>
  )
}

export default BrandPreview
