import React from 'react'

import { Widget } from '@components/molecules/Widget/Widget'
import BrandsPreview from '@components/molecules/Widget/widgets/FeaturedBrands/BrandsPreview'
import { Brand } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'
import { cn } from '@components/lib/utils'

interface Props {
  brands: Brand[]
}

const FeaturedBrandsWidget: React.FC<Props> = ({ brands }) => {
  const first16Brands = brands.slice(0, 24)
  return (
    <Widget
      title="Марките."
      subTitle="Предлагаме голяма гама висококачествени продукти."
      link={{ text: 'Всички марки', href: '/marki' }}
    >
      <div className="flex flex-col justify-center items-center">
        <div
          className={cn(
            'grid mb-20 sm:mb-6',
            'gap-4 lg:gap-6 justify-items-center',
            'grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-8 xl:grid-cols-9 2xl:grid-cols-10 3xl:grid-cols-12'
          )}
        >
          {first16Brands.map((brand, i) => (
            <BrandsPreview brand={brand} key={i} />
          ))}
        </div>
      </div>
    </Widget>
  )
}

export default FeaturedBrandsWidget
