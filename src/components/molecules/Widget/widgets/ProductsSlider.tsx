import React from 'react'

import Tabs from '@components/molecules/Widget/components/Tabs/Tabs'
import { Widget } from '@components/molecules/Widget/Widget'
import SimpleMenu from '@context/SimpleMenuContext'
import {
  AppProductsSliderWidgetFragment,
  ProductsSliderWidget as ProductsSliderWidgetType,
  ProductViewFragment
} from '@lib/_generated/graphql_sdk'
import { splitSentences } from '@lib/utils/string'

import { ProductSlider } from '../components/ProductSlider'

interface Props {
  widget: AppProductsSliderWidgetFragment
}

const ProductsSlider: React.FC<Props> = ({ widget }) => {
  let firstSentence = widget.title || ''
  let secondSentence = widget.subtitle || ''

  if (widget.tabs.length > 1) {
    firstSentence = ''
    secondSentence = ''
  }

  return (
    <Widget title={firstSentence} subTitle={secondSentence} hasPadding={false}>
      <SimpleMenu>
        <Tabs>
          {widget.tabs.map((tab, index) => (
            <div title={tab.title} key={index}>
              <ProductSlider products={tab.products as ProductViewFragment[]} />
            </div>
          ))}
        </Tabs>
      </SimpleMenu>
    </Widget>
  )
}

export default ProductsSlider
