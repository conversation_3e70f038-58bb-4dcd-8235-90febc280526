import React from 'react'

import { GraphQLBackend } from '@/src/lib/api/graphql'
import {ServiceWidget as ServiceWidgetType, ServiceWidgetFragmentFragment} from '@lib/_generated/graphql_sdk'
import { splitSentences } from '@lib/utils/string'

import { Widget } from '../Widget'
import { ServiceWidgetClient } from '@components/molecules/Widget/widgets/ServiceWidgetClient'
import Static from '@features/static'

interface Props {
  widget: ServiceWidgetFragmentFragment
}

const ServiceWidget: React.FC<Props> = async ({ widget }) => {
  return (
    <Widget title={widget?.title} subTitle={widget.subtitle} link={widget.link}>
      <Static component={ServiceWidgetClient} widget={widget} stores={widget.availableStores} />
    </Widget>
  )
}

export default ServiceWidget
