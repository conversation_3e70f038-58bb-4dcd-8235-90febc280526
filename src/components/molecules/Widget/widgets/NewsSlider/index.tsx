'use client'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import React, { useRef } from 'react'
import { Autoplay, Navigation, Pagination } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/react'

import { cn } from '@components/lib/utils'
import CircularProgressButton from '@components/molecules/Widget/components/Slider/CircularProgressButton'
import { Article, BlogPost } from '@lib/_generated/graphql_sdk'

import './styles.css'

interface NewsSliderProps {
  articles: Article[]
}

export const NewsSlider = ({ articles }: NewsSliderProps) => {
  const [currentSlide, setCurrentSlide] = React.useState(0)
  const delay = 5000
  const prevRef = useRef(null)
  const nextRef = useRef(null)
  const [page, setPage] = React.useState({ current: 0, total: 0 })

  return (
    <div
      className={cn(
        'relative',
        'sm:h-[252px]', // +40 for custom pagination offset
        'md:h-[368px]', // +50 for custom pagination offset
        'lg:h-[484px]', // +60 for custom pagination offset
        'xl:h-[600px]' // +70 for custom pagination offset
      )}
    >
      <Swiper
        autoplay={{
          delay,
          disableOnInteraction: false,
        }}
        slidesPerView="auto"
        spaceBetween={10}
        centeredSlides={true}
        grabCursor={true}
        modules={[Pagination, Navigation, Autoplay]}
        pagination={{
          type: 'custom',
          renderCustom: function (swiper, current, total) {
            setPage({ current, total })
            return ''
          },
        }}
        onBeforeInit={(swiper: any) => {
          swiper.params.navigation.prevEl = prevRef.current
          swiper.params.navigation.nextEl = nextRef.current
          swiper.navigation.init()
          swiper.navigation.update()
        }}
        onSlideChange={(swiper: any) => {
          setCurrentSlide(swiper.activeIndex)
        }}
        className="widget-news-slider"
      >
        {articles.map((slide, i) => (
          <SwiperSlide key={i} className={cn('group', 'sm:h-[212px]', 'md:h-[318px]', 'lg:h-[424px]', 'xl:h-[530px]')}>
            <div>
              <div
                className={cn(
                  'transition-all ease-in-out delay-100 duration-300',
                  'w-[280px] h-[180px]',
                  'sm:w-[280px] sm:h-[180px]',
                  'md:w-[276px] md:h-[156px]',
                  'lg:w-[368px] lg:h-[208px]',
                  'xl:w-[460px] xl:h-[260px]',
                  'group-[.swiper-slide-active]:sm:w-[372px] group-[.swiper-slide-active]:sm:h-[212px]', // sm
                  'group-[.swiper-slide-active]:md:w-[558px] group-[.swiper-slide-active]:md:h-[318px]', // md
                  'group-[.swiper-slide-active]:lg:w-[744px] group-[.swiper-slide-active]:lg:h-[424px]', // lg
                  'group-[.swiper-slide-active]:xl:w-[930px] group-[.swiper-slide-active]:xl:h-[530px]' // xl
                )}
              >
                <Image src={slide.image.src} alt={slide.title} fill className={cn('rounded-2xl', 'object-cover')} />
              </div>

              <div
                className={cn(
                  'opacity-0',
                  'group-[.swiper-slide-active]:opacity-100',
                  'transition-all ease-in-out delay-300 duration-300',
                  'absolute z-20 top-0 bottom-0 left-0 right-0',
                  'bg-gradient-to-t group-[.swiper-slide-active]:from-[rgba(0,0,0,0.7)] group-[.swiper-slide-active]:to-transparent',
                  'text-white',
                  'rounded-2xl'
                )}
              >
                <Link href={slide.link.href}>
                  <div className={cn('flex flex-col justify-end items-start h-full py-5 xl:py-10')}>
                    <h3
                      className={cn(
                        'font-bold text-xs px-3',
                        '2xl:text-2xl 2xl:px-14',
                        'xl:text-2xl xl:px-14',
                        'lg:text-2xl lg:px-10',
                        'md:text-lg md:px-10',
                        'sm:text-base sm:px-5'
                      )}
                    >
                      {slide.title}
                    </h3>
                    <p
                      className={cn(
                        'w-full px-3 hidden md:block',
                        '2xl:w-5/6 2xl:text-lg 2xl:px-14 text-xs',
                        'xl:w-5/6 xl:text-lg xl:px-14 text-xs',
                        'lg:w-5/6 lg:text-base lg:px-10 text-xs',
                        'md:w-full md:text-base md:px-10 text-xs',
                        'sm:w-full sm:text-xs sm:px-5 text-xs',
                        'sm:line-clamp-2 md:line-clamp-3 lg:line-clamp-4 xl:line-clamp-5'
                      )}
                    >
                      {slide.description}
                    </p>
                  </div>
                </Link>
              </div>

              {/* Inactive Slide */}
              <div
                className={cn(
                  'absolute',
                  'group-[.swiper-slide-active]:hidden',
                  'flex-col justify-end items-start',
                  'w-full',
                  'text-black'
                )}
              >
                <h3
                  className={cn(
                    'font-bold text-xs py-1',
                    '2xl:text-2xl 2xl:py-6',
                    'xl:text-2xl xl:py-6',
                    'lg:text-2xl lg:py-6',
                    'md:text-1xl md:py-6',
                    'sm:text-sm sm:py-2'
                  )}
                >
                  {slide.title}
                </h3>
                <p
                  className={cn(
                    'text-sm hidden md:block',
                    '2xl:w-5/6 2xl:text-base',
                    'xl:w-5/6 xl:text-base',
                    'lg:w-full lg:text-sm',
                    'md:w-full md:text-sm',
                    'sm:w-full sm:text-xs',
                    'sm:line-clamp-2 md:line-clamp-3 lg:line-clamp-4 xl:line-clamp-5'
                  )}
                >
                  {slide.description}
                </p>
              </div>
            </div>
          </SwiperSlide>
        ))}

        <div className={cn('absolute top-48 left-0 right-0 z-30 flex justify-between px-56', 'hidden lg:flex')}>
          <CircularProgressButton
            className={currentSlide > 0 ? 'opacity-100' : 'opacity-0'}
            duration={delay + 500}
            width={50}
            height={50}
            onClick={() => {}}
            currentSlide={currentSlide}
            autoplay={false}
            ref={prevRef}
          >
            <ArrowLeft className="text-primary" />
          </CircularProgressButton>

          <CircularProgressButton
            duration={delay + 500}
            width={50}
            height={50}
            onClick={() => {}}
            currentSlide={currentSlide}
            autoplay={true}
            ref={nextRef}
          >
            <ArrowRight className="text-primary" />
          </CircularProgressButton>
        </div>
      </Swiper>
      <div
        className="swiper-pagination-progressbar swiper-pagination-horizontal absolute -mb-2 bottom-0 z-20"
        style={{ top: 'inherit' }}
      >
        <span
          className="swiper-pagination-progressbar-fill"
          style={{
            transform: `translate3d(0px, 0px, 0px) scaleX(${page.current / page.total}) scaleY(1)`,
            transitionDuration: '300ms',
          }}
        />
      </div>
    </div>
  )
}
