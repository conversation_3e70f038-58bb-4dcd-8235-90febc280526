'use client'

import { StoreServices } from '@/src/app/(store)/shop/[identifier]/_components/StoreServices'
import { addClasses } from '@lib/style/style'
import { InquireCard } from '@/src/app/(store)/shop/[identifier]/_components/InquireCard'
import StoreLocator from '@/src/app/(store)/_components/StoreLocator'
import React from 'react'
import {
  AppStorePreviewFragment,
  ServiceWidget as ServiceWidgetType,
  ServiceWidgetFragmentFragment
} from '@lib/_generated/graphql_sdk'
import { StaticContentProps } from '@features/static/types'
import { stat } from 'fs'

interface Props {
  widget: ServiceWidgetFragmentFragment
  stores: AppStorePreviewFragment[]
}

export const ServiceWidgetClient: React.FC<Props & StaticContentProps> = ({ widget, stores, staticContent }) => {
  const [selectedStoreName, setSelectedStoreName] = React.useState<string>()
  return (
    <>
      <div className="xl:col-span-2 pb-4 xl:pb-6">
        <StoreServices services={widget.services} />
      </div>

      <div className={addClasses('flex flex-col lg:grid lg:grid-cols-2 lg:gap-6 gap-4')}>
        {selectedStoreName && (
          <InquireCard staticContent={staticContent} storeName={selectedStoreName} title={widget.form.title} />
        )}
        {stores.length > 0 && <StoreLocator stores={stores} onChange={setSelectedStoreName} />}
      </div>
    </>
  )
}
