'use client'
import React, { useState } from 'react'
import { AppCategoryTileFragment } from '@lib/_generated/graphql_sdk'
import Paper from '@atoms/Paper'
import { cn } from '@components/lib/utils'
import Img from '@atoms/Img'
import Title from '@atoms/Title'
import AppLink from '@atoms/AppLink'
import { Button } from '@components/theme/ui/button'
import { LucideMinus, LucidePlus } from 'lucide-react'
import Text from '@atoms/Text'

interface Props {
  tile: AppCategoryTileFragment
}

const CategoryTileContainer: React.FC<Props> = ({ tile }) => {
  const [categoriesVisible, setCategoriesVisible] = useState(false)
  const { image, title, categories } = tile

  return (
    <Paper
      className={cn(
        'h-full aspect-square sm:aspect-auto w-full xl:aspect-square p-0 rounded-3xl',
        'bg-white bg-cover overflow-hidden relative'
      )}
    >
      <Img fill className="object-cover" src={image?.src || ''} alt={title} />
      {!categoriesVisible && <div className="bg-black-to-transparent-gradient w-full h-full flex absolute" />}
      <div
        className={cn(
          'relative z-10 p-6 flex justify-between flex-col h-full',
          'transition-all duration-300 ease-in-out',
          `${categoriesVisible ? 'bg-white' : ''}`
        )}
      >
        <div className="h-full flex flex-col">
          <Text
            color={categoriesVisible ? 'black' : 'white'}
            className={cn('text-xl xl:text-2xl 3xl:text-4xl xl:w-1/2 font-bold mb-4 mt-0 xl:leading-10')}
          >
            {title}
          </Text>
          <div className="overflow-auto xl:pl-2 xl:pt-2">
            <div className={cn('flex flex-col overflow-auto w-[80%] transition-all duration-300 ease-in-out')}>
              {categoriesVisible &&
                categories?.map((category, i) => (
                  <AppLink
                    key={i}
                    href={category.href}
                    className={cn(
                      'border-solid border-b border-gray-200 w-full flex px-4 py-3',
                      'hover:bg-[#e8c94c] xl:text-base text-sm',
                      'animate-[swipeDown_0.3s_ease-in-out_forwards]'
                    )}
                  >
                    {category.text}
                  </AppLink>
                ))}
            </div>
          </div>
        </div>
        <div className={cn('absolute bottom-5', `${categoriesVisible ? 'right-5' : 'left-5'}`)}>
          <Button
            size="icon"
            variant="outline"
            onClick={() => setCategoriesVisible(!categoriesVisible)}
            className="w-8 h-8 text-primary border-none"
          >
            {categoriesVisible ? <LucideMinus /> : <LucidePlus />}
          </Button>
        </div>
      </div>
    </Paper>
  )
}

export default CategoryTileContainer
