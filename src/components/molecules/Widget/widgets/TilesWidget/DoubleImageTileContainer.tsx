import React from 'react'
import { AppDoubleImageTileFragment, DoubleImageTilePosition } from '@lib/_generated/graphql_sdk'
import Paper from '@atoms/Paper'
import { cn } from '@components/lib/utils'
import Img from '@atoms/Img'
import Title from '@atoms/Title'
import AppLink from '@atoms/AppLink'
import Text from '@atoms/Text'

interface Props {
  tile: AppDoubleImageTileFragment
}

const DoubleImageTileContainer: React.FC<Props> = ({ tile }) => {
  const { imageOne, imageTwo, imageOnePosition, imageTwoPosition, contentOne } = tile
  const contentTwo = null as unknown as AppDoubleImageTileFragment['contentOne']

  return (
    <div className={cn('h-full w-full rounded-3xl overflow-hidden relative', 'flex flex-col xl:flex-col gap-4')}>
      {/* First Image Section */}
      <div
        className={cn(
          'relative w-full h-full rounded-2xl overflow-hidden',
          imageOnePosition === DoubleImageTilePosition.Left ? 'xl:order-1' : 'xl:order-2',
          'bg-gradient-to-t from-black/70 to-transparent'
        )}
      >
        <Img src={imageOne?.src || ''} alt={imageOne?.alt || ''} fill className="object-cover" />
        {contentOne && (
          <div className="absolute inset-0 flex flex-col justify-end p-6 bg-gradient-to-t from-black/70 to-transparent">
            <Title size="h3" color="white" className="text-xl xl:text-2xl font-bold mb-2">
              {contentOne.title}
            </Title>
            {contentOne.text && <Text className="text-white mb-4">{contentOne.text}</Text>}
            {contentOne.link && (
              <AppLink href={contentOne.link.href} className="inline-flex items-center text-white hover:underline">
                {contentOne.link.text}
              </AppLink>
            )}
          </div>
        )}
      </div>

      {/* Second Image Section */}
      <div
        className={cn(
          'relative w-full h-full rounded-2xl overflow-hidden',
          imageTwoPosition === DoubleImageTilePosition.Left ? 'xl:order-1' : 'xl:order-2',
          'bg-gradient-to-t from-black/70 to-transparent'
        )}
      >
        <Img src={imageTwo?.src || ''} alt={imageTwo?.alt || ''} fill className="object-cover" />
        {contentTwo && (
          <div className="absolute inset-0 flex flex-col justify-end p-6 bg-gradient-to-t from-black/70 to-transparent">
            <Title size="h3" color="white" className="text-xl xl:text-2xl font-bold mb-2">
              {contentTwo.title}
            </Title>
            {contentTwo.text && <Text className="text-white mb-4">{contentTwo.text}</Text>}
            {contentTwo.link && (
              <AppLink href={contentTwo.link.href} className="inline-flex items-center text-white hover:underline">
                {contentTwo.link.text}
              </AppLink>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default DoubleImageTileContainer
