import React from 'react'
import { AppImageTileFragment, TileContentPosition } from '@lib/_generated/graphql_sdk'
import Paper from '@atoms/Paper'
import { cn } from '@components/lib/utils'
import Img from '@atoms/Img'
import { TileContent } from '@components/molecules/Widget/widgets/TilesWidget/TileContent'

interface Props {
  tile: AppImageTileFragment
}

const ImageTileContainer: React.FC<Props> = ({ tile }) => {
  const { image, content, position, bgColor } = tile

  const getPositionClasses = () => {
    switch (position) {
      case TileContentPosition.Left:
        return 'col-span-2 row-span-2 order-0 aspect-[9/16] xl:aspect-square min-w-[320px]'
      case TileContentPosition.Right:
        return 'col-span-2 row-span-2 order-1 aspect-[9/16] xl:aspect-square min-w-[320px]'
      case TileContentPosition.LeftTop:
        return 'col-span-2 row-span-1 order-0 aspect-[9/16] xl:aspect-auto min-w-[320px] xl:aspect-auto'
      case TileContentPosition.LeftBottom:
        return 'col-span-2 row-span-1 order-2 aspect-[9/16] xl:aspect-auto min-w-[320px] xl:aspect-auto'
      case TileContentPosition.RightTop:
        return 'col-span-2 row-span-1 order-1 aspect-[9/16] xl:aspect-auto min-w-[320px] xl:aspect-auto'
      case TileContentPosition.RightBottom:
        return 'col-span-2 row-span-1 order-2 aspect-[9/16] xl:aspect-auto min-w-[320px] xl:aspect-auto'
      default:
        return 'items-center justify-center text-center'
    }
  }

  const getContentPosition = () => {
    switch (position) {
      case TileContentPosition.Right:
        return ''
      case TileContentPosition.LeftTop:
        return ''
      case TileContentPosition.RightBottom:
        return 'col-start-2'
      case TileContentPosition.LeftBottom:
        return 'col-start-2'
      case TileContentPosition.RightTop:
        return ''
      default:
        return ''
    }
  }

  return (
    <Paper
      className={cn(
        // 'h-full w-full rounded-3xl overflow-hidden relative ',
        'h-full w-full rounded-3xl overflow-hidden relative p-0',
        getPositionClasses()
      )}
      style={{ backgroundColor: bgColor || 'transparent' }}
    >
      <Img src={image?.src || ''} alt={image?.alt || ''} fill className="object-cover" />
      <div className={cn('absolute inset-5')}>
        {content && (
          <div
            className={cn(
              position === TileContentPosition.Left || position === TileContentPosition.Right
                ? 'xl:grid xl:grid-cols-2 xl:grid-rows-2 xl:gap-x-5 xl:gap-y-16'
                : 'xl:grid xl:grid-cols-2 xl:grid-rows-1',
              'h-1/2 xl:h-full w-full'
            )}
          >
            <TileContent
              title={tile.content.title}
              description={tile.content.text}
              icon={tile.content.icon}
              link={tile.content.link}
              position={getContentPosition()}
            />
          </div>
        )}
      </div>
    </Paper>
  )
}

export default ImageTileContainer
