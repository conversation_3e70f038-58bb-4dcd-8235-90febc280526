import React from 'react'

import { cn } from '@components/lib/utils'
import {Tile} from '@lib/_generated/graphql_sdk'

import TileComplexCard from './TileComplexCard'
import TileSimpleCard from './TileSimpleCard'

interface Props {
  tiles: any[]
}

const TilesGrid: React.FC<Props> = ({ tiles }) => {
  return (
    <div className="xl:grid xl:grid-cols-4 xl:gap-6 gap-4 mt-3 flex overflow-auto">
      {tiles.map((tile, index) => (
        <div
          className={cn(
            'col-span-1 row-span-1',
            tile.dimensions ? `row-span-${tile.dimensions.height}` : '',
            'min-w-[90%]',
            tile.type
          )}
          style={{
            gridColumn: `span ${tile.dimensions?.width} / span ${tile.dimensions?.width}`,
            gridRow: `span ${tile.dimensions?.height} / span ${tile.dimensions?.height}`,
          }}
          key={index}
        >
          {tile.type === 'TileType.Simple' ? (
            <TileSimpleCard
              bgColor={tile.bgColor || ''}
              bgImage={tile.image}
              textColor="white"
              logo={tile.content?.brand}
              title={tile.content?.title || ''}
              link={tile.content?.link}
              description={tile.content?.text || ''}
            />
          ) : (
            <TileComplexCard
              bgColor={tile.bgColor || ''}
              bgImage={tile.image}
              leftIcon={tile.content?.leftIcon}
              title={tile.content?.title || ''}
              link={tile.content?.link}
              description={tile.content?.text || ''}
            />
          )}
        </div>
      ))}
    </div>
  )
}

export default TilesGrid
