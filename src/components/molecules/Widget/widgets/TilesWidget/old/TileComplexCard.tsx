import {LucidePlus} from 'lucide-react'
import React from 'react'
import {Maybe} from 'yup'

import AppLinkButton from '@atoms/AppLink'
import Img from '@atoms/Img'
import Paper from '@atoms/Paper'
import Text from '@atoms/Text'
import Title from '@atoms/Title'
import {cn} from '@components/lib/utils'
import {ButtonIcon} from '@components/molecules/ButtonIcon'
import {Image, Link} from '@lib/_generated/graphql_sdk'
import {addClasses} from '@lib/style/style'

interface Props {
  bgColor?: string
  bgImage?: Maybe<Image>
  leftIcon?: Maybe<Image>
  title: string
  description?: string
  link?: Link
}

const TileComplexCard: React.FC<Props> = ({ bgColor, bgImage, leftIcon, title, description, link }) => {
  return (
    <Paper
      className={addClasses('w-full h-full bg-white p-3 xl:p-5 bg-cover rounded-2xl', 'relative overflow-hidden')}
      style={{ backgroundColor: bgColor }}
    >
      <Img fill src={bgImage?.src || ''} className={cn('object-cover xl:object-fill')} alt={title} />
      <div className={addClasses('min-w-[200px] h-full flex justify-between', 'items-start flex-col relative z-10')}>
        <Paper
          className={addClasses('w-full max-w-[400px]', 'xl:pt-8 xl:pb-7 p-8 flex justify-between flex-col rounded-md')}
        >
          <div>
            <Title size="h1" className="text-2xl font-bold mb-6">
              {title}
            </Title>
            {description && <p className="mb-8 text-sm">{description}</p>}
          </div>
          <div className="row justify-between items-end">
            <div>
              {leftIcon && (
                <Img
                  width={73}
                  height={73}
                  src={leftIcon?.src || ''}
                  mobileSrc={leftIcon?.mobileSrc || ''}
                  alt={leftIcon?.alt || ''}
                />
              )}
            </div>
            <div className="flex">
              {link && (
                <AppLinkButton href={link.href} className="text-primary flex items-center">
                  <ButtonIcon
                    direction="horizontal"
                    iconPosition="end"
                    size="icon"
                    icon={<LucidePlus />}
                    iconClassName="p-1.5"
                    label={<Text>{link.text}</Text>}
                    className="gap-2"
                    labelClassName="hidden xl:flex text-black"
                  ></ButtonIcon>
                </AppLinkButton>
              )}
            </div>
          </div>
        </Paper>
      </div>
    </Paper>
  )
}

export default TileComplexCard
