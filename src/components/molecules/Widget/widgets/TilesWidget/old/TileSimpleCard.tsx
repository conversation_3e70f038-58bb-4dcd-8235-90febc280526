import React from 'react'
import { Maybe } from 'yup'

import { useTextColor } from '@/src/hooks/TextColorHook'
import Img from '@atoms/Img'
import Paper from '@atoms/Paper'
import Title from '@atoms/Title'
import LinkButton from '@components/molecules/LinkButton'
import { Button } from '@components/theme/ui/button'
import { Image, Link, Brand } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'

interface Props {
  bgColor?: string
  bgImage?: Maybe<Image>
  textColor?: 'white' | 'dark'
  logo?: Maybe<Brand>
  title: string
  description?: string
  link?: Link
}

const TileSimpleCard: React.FC<Props> = ({ bgColor, bgImage, textColor, logo, title, description, link }) => {
  const textColorClass = useTextColor(bgColor || '')

  return (
    <Paper
      className={addClasses(
        'w-full min-h-[455px] h-full bg-white p-10',
        `${textColor && `text-${textColor}`}`,
        'relative overflow-hidden rounded-3xl'
      )}
      style={{
        backgroundColor: bgColor,
      }}
    >
      {bgImage && <Img fill src={bgImage?.src || ''} alt={title} className="absolute" />}
      <div
        className={addClasses(
          'min-w-[200px] w-[60%] h-full flex justify-between items-start flex-col',
          'relative z-10'
        )}
      >
        <div>
          {logo && (
            <div className="bg-white rounded-lg inline-block relative">
              <Img
                width={70}
                height={30}
                src={logo.image?.src || ''}
                mobileSrc={logo.image?.mobileSrc || ''}
                alt={logo.image?.alt || ''}
                className="p-2"
              />
            </div>
          )}
          <Title size="h1" className={addClasses('text-xl font-bold mt-8 mb-4', textColorClass)}>
            {title}
          </Title>
          {description && <p className={addClasses('mb-8 text-sm', textColorClass)}>{description}</p>}
        </div>

        {link && (
          <Button variant="tertiary" className="rounded-full font-medium" size="lg">
            {link.text}
          </Button>
        )}
      </div>
    </Paper>
  )
}

export default TileSimpleCard
