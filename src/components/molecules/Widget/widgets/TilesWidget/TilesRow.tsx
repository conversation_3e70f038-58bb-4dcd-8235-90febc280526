import React, { PropsWithChildren } from 'react'
import { PropsWithClassName } from '@lib/types/ClassName'
import { cn } from '@components/lib/utils'

export const TilesRow: React.FC<PropsWithChildren<PropsWithClassName>> = ({ className, children }) => {
  const cols: Record<number, string> = {
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
  }
  return (
    <div
      className={cn('xl:grid xl:gap-6 gap-4 mt-3 flex overflow-auto', cols[React.Children.count(children)], className)}
    >
      {children}
    </div>
  )
}
