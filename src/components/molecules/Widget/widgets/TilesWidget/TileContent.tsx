import { <PERSON>, Card<PERSON>ontent, <PERSON>Footer, <PERSON>Title } from '@components/theme/ui/card'
import Text from '@atoms/Text'
import AppLink from '@atoms/AppLink'
import { AppImageTileFragment } from '@lib/_generated/graphql_sdk'
import React from 'react'
import { ButtonIcon } from '@components/molecules/ButtonIcon'
import { LucidePlus, LucidePlusCircle } from 'lucide-react'
import { cn } from '@components/lib/utils'

interface TileContentProps {
  title: AppImageTileFragment['content']['title']
  description: AppImageTileFragment['content']['text']
  icon: AppImageTileFragment['content']['icon']
  link: AppImageTileFragment['content']['link']
  position: string
}

export const TileContent: React.FC<TileContentProps> = ({ title, description, icon, link, position }) => {
  return (
    <Card className={cn('bg-white shadow-none border-none py-4 xl:py-11 flex flex-col h-full w-full', position)}>
      <CardTitle className="px-4 xl:px-11 break-words">
        <Text className="text-2xl tracking-wide">{title}</Text>
      </CardTitle>
      <CardContent className="px-4 xl:px-11 py-4 flex-1 overflow-auto">
        <Text className="text-sm">{description}</Text>
      </CardContent>
      <CardFooter className="px-4 xl:px-11 py-0 flex justify-between">
        <div />
        <AppLink href={link.href} className="text-sm">
          <ButtonIcon
            labelClassName="w-auto"
            icon={<LucidePlus />}
            label="Виж предложения"
            direction="horizontal"
            iconPosition="end"
          />
        </AppLink>
      </CardFooter>
    </Card>
  )
}
