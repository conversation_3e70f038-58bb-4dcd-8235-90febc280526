import React from 'react'
import { Widget } from '@components/molecules/Widget/Widget'
import { CategoryTile, DoubleImageTile, ImageTile, TilesWidgetFragmentFragment } from '@lib/_generated/graphql_sdk'
import CategoryTileContainer from '@components/molecules/Widget/widgets/TilesWidget/CategoryTileContainer'
import ImageTileContainer from '@components/molecules/Widget/widgets/TilesWidget/ImageTileContainer'
import DoubleImageTileContainer from '@components/molecules/Widget/widgets/TilesWidget/DoubleImageTileContainer'
import { cn } from '@components/lib/utils'
import { TilesRow } from '@components/molecules/Widget/widgets/TilesWidget/TilesRow'

interface Props {
  widget: TilesWidgetFragmentFragment
}

const Tiles: React.FC<Props> = ({ widget }) => {
  if (widget?.rows?.length < 1) return null

  const getTile = (tile: CategoryTile | ImageTile | DoubleImageTile, index: number) => {
    const colType = tile?.__typename
    switch (colType) {
      case 'CategoryTile':
        return <CategoryTileContainer key={index} tile={tile} />
      case 'ImageTile':
        return <ImageTileContainer key={index} tile={tile} />
      case 'DoubleImageTile':
        return <DoubleImageTileContainer key={index} tile={tile} />
      default:
        return null
    }
  }

  const [firstrow, ...restrows] = widget.rows
  return (
    <Widget title={widget.title} subTitle={widget.subtitle}>
      <TilesRow className={cn('flex flex-col')}>{firstrow.cols.map((tile, index) => getTile(tile, index))}</TilesRow>
      <div className="flex flex-col xl:flex-col xl:aspect-auto overflow-x-auto scrollbar-hide">
        <div className="flex flex-row xl:flex-col w-max xl:w-full gap-4">
          {restrows.map((row, index) => (
            <div key={index} className="flex-shrink-0 xl:flex-shrink xl:w-full">
              <TilesRow className="flex xl:grid grid-cols-4">
                {row?.cols.map((tile, index) => getTile(tile, index))}
              </TilesRow>
            </div>
          ))}
        </div>
      </div>
    </Widget>
  )
}

export default Tiles
