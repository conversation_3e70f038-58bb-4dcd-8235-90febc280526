import React from 'react'

import { GraphQLBackend } from '@/src/lib/api/graphql'
import {NewsWidget as NewsWidgetType, NewsWidgetFragmentFragment} from '@lib/_generated/graphql_sdk'
import { splitSentences } from '@lib/utils/string'
import { NewsSlider } from 'src/components/molecules/Widget/widgets/NewsSlider'

import { Widget } from '../Widget'

interface Props {
  widget: NewsWidgetFragmentFragment
}

const News: React.FC<Props> = async ({ widget }) => {
  return (
    <Widget title={widget.title}
            subTitle={widget.subtitle}
            link={widget.link}
            hasPadding={false} transparent={false}>
      <NewsSlider articles={widget.articles} />
    </Widget>
  )
}

export default News
