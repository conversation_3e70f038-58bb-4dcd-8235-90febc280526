import React from 'react'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import { staticSelectors } from '@features/static/selectors'
import { StaticContent } from '@lib/_generated/graphql_sdk'
import Static from '@features/static'

interface Props {
  className?: string
  width?: number
  height?: number
  href?: string
}

const LogoServer: React.FC<Props & { staticContent: StaticContent }> = ({ className, href, staticContent }) => {
  if (!staticContent.logo) return
  const { logo } = staticContent
  const { url, width, height, alt } = logo

  const img = (
    <Img
      className={className}
      src={`logo/${url}`}
      width={width || 180}
      height={height || 35}
      alt={alt || 'Praktis.bg store logo'}
    />
  )
  return href ? <AppLink href={href}>{img}</AppLink> : img
}

const Logo: React.FC<Props> = (props) => {
  return <Static component={LogoServer} {...props} />
}

export default Logo
