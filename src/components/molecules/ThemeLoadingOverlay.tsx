import React from 'react'

import { ThemeColors } from '@/tailwind.config'
import ThemeIconSpinner from '@atoms/Icons/ThemeIconSpinner'
import { addClasses } from '@lib/style/style'

interface Props {
  loading?: boolean
  color?: ThemeColors
  opacity?: number
  zIndex?: number
  loaderSize?: number
}

const ThemeLoadingOverlay: React.FC<Props> = ({ loading, opacity, zIndex, loaderSize, color }) => {
  if (!loading) {
    return <></>
  }

  const bgColor = 'bg-' + (color ?? 'primary')

  return (
    <div
      className={addClasses('full-cover', 'flex justify-center items-center')}
      style={{
        zIndex: zIndex ?? 50,
      }}
    >
      <div
        className={bgColor + ' full-cover'}
        style={{
          opacity: opacity ?? 0.1,
        }}
      />
      <ThemeIconSpinner size={loaderSize ?? 50} />
    </div>
  )
}

export default ThemeLoadingOverlay
