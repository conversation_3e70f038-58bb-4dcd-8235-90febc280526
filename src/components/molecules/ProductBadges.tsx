import React from 'react'

import { ProductBadge } from '@components/molecules/ProductBadge'
import { MagentoLabels } from '@lib/_generated/graphql_sdk'
import { Tooltip, TooltipContent, TooltipTrigger } from '@components/theme/ui/tooltip'

interface ProductBadgesProps {
  list: MagentoLabels
}

export const ProductBadges: React.FC<ProductBadgesProps> = ({ list }) => {
  return (
    <div className="flex justify-between">
      <div className="flex gap-3">
        {list.warrantyMonths && list.warrantyMonths > 0 ? (
          <Tooltip>
            <TooltipTrigger asChild>
              <ProductBadge type={`warranty-${list.warrantyMonths}-months`} width={60} height={60} />
            </TooltipTrigger>
            <TooltipContent>{`${list.warrantyMonths} месеца гаранция`}</TooltipContent>
          </Tooltip>
        ) : (
          <></>
        )}
        {list.freeDelivery && (
          <Tooltip>
            <TooltipTrigger asChild>
              <ProductBadge type="free-transport" width={60} height={60} />
            </TooltipTrigger>
            <TooltipContent>Безплатен транспорт</TooltipContent>
          </Tooltip>
        )}
      </div>
    </div>
  )
}
