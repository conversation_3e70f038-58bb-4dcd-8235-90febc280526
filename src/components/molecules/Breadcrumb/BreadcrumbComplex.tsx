import { LucideHouse } from 'lucide-react'
import React from 'react'

import Pagination from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/Pagination'
import { cn } from '@components/lib/utils'
import Banner from '@components/molecules/Breadcrumb/components/Banner'
import { Fragment } from '@components/molecules/Breadcrumb/components/Fragment'
import { BreadcrumbProps } from '@components/molecules/Breadcrumb/types'
import { Card } from '@components/theme/ui/card'
import { Swiper } from '../../organisms/ProductPhotoGallery/_Swiper'

export const BreadcrumbComplex: React.FC<BreadcrumbProps> = ({ breadcrumbs, banner, className, pagination }) => (
  <Card className={cn('w-full flex flex-col rounded-2xl my-5 shadow-sm  text-nowrap', className)}>
    {banner && <Banner banner={banner} />}
    <div className="flex flex-col lg:flex-row justify-between">
      <ul id="breadcrumb" className="flex items-center w-full overflow-x-auto ">
        {breadcrumbs?.map((item, i: number) => {
          if (!item || item.url === null) return null
          if (item.url === '/') {
            return <Fragment key={i} href="/" icon={<LucideHouse className="mx-4" />} />
          } else if (item.url === '#') {
            return <Fragment key={i} href="#" label={item.label} siblings={item.siblings} />
          }
          return <Fragment key={i} href={item.url ?? '#'} label={item.label} siblings={item.siblings} />
        })}
      </ul>
      {(pagination?.pager.totalPages || 0 > 0) && <Pagination pager={pagination?.pager} />}
    </div>
  </Card>
)
