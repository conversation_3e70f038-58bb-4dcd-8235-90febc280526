import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>bList } from 'schema-dts'

import { BreadcrumbComplex } from '@components/molecules/Breadcrumb/BreadcrumbComplex'
import BreadCrumbMini from '@components/molecules/Breadcrumb/BreadCrumbMini'
import { BreadcrumbProps } from '@components/molecules/Breadcrumb/types'

import { SchemaOrg } from '../../atoms/SchemaOrg'
import '././styles.css'
import { GraphQLBackend } from '@/src/lib/api/graphql'
import { getStaticContent } from '@/src/features/static/api'
const Breadcrumb: React.FC<BreadcrumbProps> = async (props) => {
  const sd = await getStaticContent()
  return (
    <>
      <SchemaOrg<BreadcrumbList>
        schema={{
          '@context': 'https://schema.org',
          '@type': 'BreadcrumbList',
          'itemListElement': props.breadcrumbs?.map((item, index) => ({
            '@type': 'ListItem',
            'position': index + 1,
            'name': item.label,
            'item': item.url.startsWith('/') ? sd.store.baseUrl + item.url.slice(1) : item.url,
          })),
        }}
      />
      <BreadcrumbComplex {...props} className="" />
      {/* <BreadCrumbMini {...props} className="lg:hidden" /> */}
    </>
  )
}

export default Breadcrumb
