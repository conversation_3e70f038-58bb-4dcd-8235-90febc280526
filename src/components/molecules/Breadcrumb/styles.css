:root {
    --breadcrumb-bg: #FFFFFF;
    --breadcrumb-text: #000000;
    --breadcrumb-hover: #ee7f00;
    --breadcrumb-hover-text: #FFFFFF;
}

#breadcrumb {
    list-style: none;
    display: flex;
}
#breadcrumb .icon {
    font-size: 14px;
}
#breadcrumb li {
    /*float: left;*/
}
#breadcrumb li.active .fragment-link,
#breadcrumb li .fragment-link:hover {
    color: var(--breadcrumb-hover-text);
}
#breadcrumb li .fragment-link {
    color: var(--breadcrumb-text);
    background: var(--breadcrumb-bg);
    text-decoration: none;
    position: relative;
    height: 50px;
    line-height: 50px;
    padding: 0 10px 0 10px;
    text-align: center;
    margin-right: 23px;
}
#breadcrumb li:nth-child(even) .fragment-link {
    background-color: #FFF; /* Active Crumb */
}
#breadcrumb li:nth-child(even) .fragment-link:before {
    border-color: #FFF; /* Active Crumb */
    border-left-color: transparent;
}
#breadcrumb li:nth-child(even) .fragment-link:after {
    border-left-color: #FFF; /* Active Crumb */
}
#breadcrumb li:first-child .fragment-link {
    padding-left: 15px;
    border-radius: 1rem 0 0 1rem;
}
#breadcrumb li:first-child .fragment-link:before {
    border: none;
}
#breadcrumb li:last-child .fragment-link {
    padding-right: 0px;
    /*border-radius: 0 1rem 1rem 0;*/
}
#breadcrumb li:last-child .fragment-link:after {
    /*border: none;*/
    background: #FFFFFF;
}
#breadcrumb li .fragment-link:before, #breadcrumb li .fragment-link:after {
    content: "";
    position: absolute;
    top: 0;
    border: 0 solid #FFF;
    border-width: 25px 15px;
    width: 0;
    height: 0;
}
#breadcrumb li .fragment-link:before {
    left: -22px;
    border-left-color: transparent;
}
#breadcrumb li .fragment-link:after {
    left: 100%;
    border-color: transparent;
    border-left-color: #FFF;
    background: #f3f3f3;
}
#breadcrumb li.active .fragment-link,
#breadcrumb li .fragment-link:hover {
    background-color: var(--breadcrumb-hover);
}
#breadcrumb li.active .fragment-link:before,
#breadcrumb li .fragment-link:hover:before {
    border-color: var(--breadcrumb-hover);
    border-left-color: transparent;
}
#breadcrumb li.active .fragment-link:after,
#breadcrumb li .fragment-link:hover:after {
    border-left-color: var(--breadcrumb-hover);
}
#breadcrumb li .fragment-link:active {
    background-color: var(--breadcrumb-hover);
}
#breadcrumb li .fragment-link:active:before {
    border-color: var(--breadcrumb-hover);
    border-left-color: transparent;
}
#breadcrumb li .fragment-link:active:after {
    border-left-color: var(--breadcrumb-hover);
}
