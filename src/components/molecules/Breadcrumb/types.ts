import { B<PERSON><PERSON>rumb, CategoryBanner, DynamicRouteDataFragment, Pager, SortDirection } from '@lib/_generated/graphql_sdk'
import { ClassName } from '@lib/types/ClassName'

export interface BreadcrumbProps {
  breadcrumbs?: Breadcrumb[]
  banner?: CategoryBanner | null
  hasPagination?: boolean
  className?: ClassName
  attached?: boolean
  pagination?: {
    sort: {
      value: string
      dir: SortDirection
    }
    pager: Pager
  }
}
