'use client'

import { LucideHouse } from 'lucide-react'
import React, { useEffect, useMemo, useRef } from 'react'

import AppLink from '@atoms/AppLink'
import { cn } from '@components/lib/utils'
import Banner from '@components/molecules/Breadcrumb/components/Banner'
import { BreadcrumbPopoverContent } from '@components/molecules/Breadcrumb/components/BreadcrumbPopoverContent'
import { BreadcrumbSeparator } from '@components/molecules/Breadcrumb/components/BreadcrumbSeparator'
import { BreadcrumbProps } from '@components/molecules/Breadcrumb/types'
import { ButtonArrow } from '@components/molecules/Buttons/ButtonArrow'
import { Popover, PopoverTrigger } from '@components/theme/ui/popover'
import Pagination from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/Pagination'

const BreadcrumbMini: React.FC<BreadcrumbProps> = ({ breadcrumbs, banner, attached, pagination, className }) => {
  const [isOpen, setIsOpen] = React.useState(false)
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // This effect runs after the component mounts and the DOM is ready
    if (scrollContainerRef.current) {
      // Scroll to the rightmost position
      const container = scrollContainerRef.current as HTMLDivElement
      container.scrollLeft = container.scrollWidth
    }
  }, []) // Empty dependency array means this runs once when component mounts

  const breadcrumbItems = useMemo(() => {
    return breadcrumbs?.filter((b) => b.url !== '/') || []
  }, [breadcrumbs])

  return (
    <div className={cn('flex flex-col bg-white rounded-3xl mt-4', className)}>
      {banner && <Banner banner={banner} />}
      <div
        className={cn(
          'bg-white px-2 h-[40px] max-h-[40px] flex items-center relative overflow-hidden', //border-b-gray-100 border-b
          'rounded-2xl',
          { 'rounded-b-none': attached },
          { 'rounded-t-none': banner }
        )}
      >
        <div className="pl-2 pr-1.5">
          <AppLink href="/">
            <LucideHouse className="shrink-0" size={18} />
          </AppLink>
        </div>
        <BreadcrumbSeparator />
        <div
          className="relative flex flex-1 ml-2 w-full max-w-full overflow-x-auto scrollbar-hide"
          ref={scrollContainerRef}
        >
          {/* Exclude the last element */}
          {breadcrumbItems.map((item, index) => (
            <React.Fragment key={index}>
              <Popover onOpenChange={(open) => console.log(open)}>
                <PopoverTrigger asChild>
                  <div
                    className={cn(
                      'whitespace-nowrap flex items-center justify-center text-gray-800 rounded-md border-red-500 text-sm',
                      'relative',
                      'justify-start'
                    )}
                    style={{ zIndex: 10 + index }}
                  >
                    <ButtonArrow label={item?.label} arrowDirection={isOpen ? 'up' : 'down'} />
                  </div>
                </PopoverTrigger>
                <BreadcrumbPopoverContent siblings={breadcrumbs} url={item.url} label={item.label} />
                {index < breadcrumbItems.length - 1 && (
                  <div className="w-[30px] bg-white block mx-1">
                    <BreadcrumbSeparator className="z-20" />
                  </div>
                )}
              </Popover>
            </React.Fragment>
          ))}
        </div>
      </div>
      {(pagination?.pager.totalPages || 0) > 0 && <Pagination pager={pagination?.pager} />}
    </div>
  )
}

export default BreadcrumbMini
