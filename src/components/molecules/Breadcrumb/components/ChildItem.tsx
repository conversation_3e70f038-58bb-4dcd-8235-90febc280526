import React from 'react'

import AppLink from '@atoms/AppLink'
import { cn } from '@components/lib/utils'
import { ClassName } from '@lib/types/ClassName'

interface ChildItemProps {
  href: string
  label?: string
  icon?: React.ReactNode
  className?: ClassName
}

export const ChildItem: React.FC<ChildItemProps> = ({ href, label, icon, className }) => {
  return (
    <div key="back" className="border-b border-gray-300 last:border-none flex hover:border-none hover:mb-[1px]">
      <AppLink
        href={href}
        className={cn(
          'text-sm',
          'flex items-center gap-2',
          'whitespace-normal p-3 rounded-lg flex w-full',
          'hover:bg-primary hover:text-white',
          className
        )}
      >
        {icon}
        {label}
      </AppLink>
    </div>
  )
}
