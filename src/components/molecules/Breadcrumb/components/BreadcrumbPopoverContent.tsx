import { LucideArrowBigLeft } from 'lucide-react'
import React from 'react'

import { ChildItem } from '@components/molecules/Breadcrumb/components/ChildItem'
import { Card } from '@components/theme/ui/card'
import { PopoverContent } from '@components/theme/ui/popover'
import { Breadcrumb } from '@lib/_generated/graphql_sdk'

interface BreadcrumbPopoverContentProps {
  siblings: Breadcrumb['children']
  url: string
  label: string
}

export const BreadcrumbPopoverContent: React.FC<BreadcrumbPopoverContentProps> = ({ siblings, url, label }) => {
  return (
    <>
      <PopoverContent className="w-80 p-0 border-none">
        {siblings && siblings.length > 0 && (
          <Card className="p-3 shadow-xl">
            {siblings?.map((childBreadcrumb, i) => (
              <ChildItem
                key={i}
                href={childBreadcrumb.url}
                label={childBreadcrumb.label}
                className={label === childBreadcrumb.label ? 'bg-primary text-white' : ''}
              />
            ))}
          </Card>
        )}
      </PopoverContent>
    </>
  )
}
