'use client'

import React, { RefObject, useEffect, useRef, useState } from 'react'
import { useOnClickOutside } from 'usehooks-ts'

import { Button } from '@/src/components/theme/ui/button'
import AppLink from '@atoms/AppLink'
import ArrowCircle from '@atoms/Icons/ArrowCircle'
import Paper from '@atoms/Paper'
import { Card } from '@components/theme/ui/card'
import { Popover, PopoverContent, PopoverTrigger } from '@components/theme/ui/popover'
import { Breadcrumb, Maybe } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'

interface Props {
  dropdownItems?: Maybe<Array<Breadcrumb>>
}

const Dropdown: React.FC<Props> = ({ dropdownItems }) => {
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement | null>(null)

  const toggle = (e: React.MouseEvent) => {
    e.stopPropagation() // Prevent event bubbling
    setIsOpen((prev) => !prev)
  }

  useOnClickOutside(menuRef as RefObject<HTMLDivElement>, close)

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline">Open popover</Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0">
        <Card>
          xaxax
          <AppLink href={'/'}>Home</AppLink>
        </Card>
      </PopoverContent>
    </Popover>
  )

  // return (
  //   <div className="w-[30px] h-[30px] flex items-center" ref={menuRef}>
  //     <div onClick={toggle}>
  //       <ArrowCircle
  //         className={addClasses(isOpen && 'rotate-180', 'cursor-pointer ml-3')}
  //       />
  //     </div>
  //
  //     {isOpen && (
  //       <Paper
  //         className={addClasses(
  //           'flex flex-col p-0 desktop:mt-8 top-[60px] desktop:top-[40px]',
  //           'desktop:max-w-[350px] desktop:min-w-[300px] tablet:max-w-[350px] tablet:min-w-[300px]',
  //           'phone:w-full left-0 absolute px-6 py-4'
  //         )}
  //       >
  //         <div className="max-h-[350px] overflow-auto">
  //           {dropdownItems?.map((childBreadcrumb, i) => (
  //             <div
  //               key={i}
  //               className='border-b border-gray-300 last:border-none flex hover:border-none hover:mb-[1px]'
  //             >
  //               <AppLink
  //                 href={childBreadcrumb.url}
  //                 className={addClasses(
  //                   'whitespace-normal p-3 rounded-lg flex w-full',
  //                   'hover:bg-primary hover:text-white'
  //                 )}
  //               >
  //                 {childBreadcrumb.label}
  //               </AppLink>
  //             </div>
  //           ))}
  //         </div>
  //       </Paper>
  //     )}
  //   </div>
  // )
}

export default Dropdown
