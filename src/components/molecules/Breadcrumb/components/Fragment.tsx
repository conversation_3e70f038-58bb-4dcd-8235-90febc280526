'use client'
import { usePathname } from 'next/navigation'
import React, { ReactNode, useMemo, useState } from 'react'

import AppLink from '@atoms/AppLink'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { BreadcrumbPopoverContent } from '@components/molecules/Breadcrumb/components/BreadcrumbPopoverContent'
import { ButtonArrow } from '@components/molecules/Buttons/ButtonArrow'
import { Popover, PopoverTrigger } from '@components/theme/ui/popover'
import { Breadcrumb } from '@lib/_generated/graphql_sdk'
import { PropsWithClassName } from '@lib/types/ClassName'

interface FragmentProps {
  href: string
  label?: string
  icon?: ReactNode
  siblings?: Breadcrumb['children']
}

export const Fragment: React.FC<PropsWithClassName<FragmentProps>> = ({ href, icon, label, siblings, className }) => {
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)
  const onOpenChange = (open: boolean) => setIsOpen(open)
  const link = href === '/' ? '/' : href.split('/').slice().join('/')
  //  useMemo(() => {
  //   if ((siblings && siblings.length === 0) || !siblings) {
  //     return href
  //   }
  //   if (href === '/') {
  //     return '/'
  //   } else {
  //     return '#'
  //   }
  // }, [siblings, href])
  // console.log(
  //   'pathname',
  //   pathname,
  //   href,
  //   siblings?.length === 0 && (href === '#' || pathname === href),
  //   siblings?.length === 0,
  //   siblings
  // )
  const hasSiblings = siblings && siblings?.length > 0 && false
  const lastCrumb = (siblings == null || siblings?.length) && (href === '#' || pathname === href)
  const Crumb = lastCrumb
    ? ({ children }: { children: ReactNode }) => (
        <div className="fragment-link flex gap-2 items-center min-h-[50px] pointer-events-none">{children}</div>
      )
    : AppLink
  return (
    <Popover onOpenChange={onOpenChange}>
      <li className={cn({ active: isOpen })}>
        <PopoverTrigger asChild>
          <Crumb href={link} dontAppendSlash className="fragment-link flex gap-2 items-center min-h-[50px]">
            {icon}
            {label && <Text className="text-sm">{label}</Text>}
            {!lastCrumb && link !== '/' && link !== pathname && <ButtonArrow arrowDirection={isOpen ? 'up' : 'down'} />}
          </Crumb>
        </PopoverTrigger>
      </li>
      {link !== '/' && hasSiblings && (
        <BreadcrumbPopoverContent siblings={siblings} label={label as string} url={link} />
      )}
    </Popover>
  )
}
