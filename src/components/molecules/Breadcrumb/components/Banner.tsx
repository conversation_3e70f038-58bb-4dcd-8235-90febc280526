import React from 'react'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import { CategoryBanner } from '@lib/_generated/graphql_sdk'

interface Props {
  banner?: CategoryBanner | null
}

const Banner: React.FC<Props> = ({ banner }) => {
  if (!banner || !banner.url) return null

  return (
    <div className="w-full relative">
      <AppLink href={banner?.url}>
        <Img
          fill
          className="!relative rounded-2xl"
          src={banner.image.src}
          mobileSrc={banner.image.mobileSrc ? banner.image.mobileSrc : ''}
          alt={banner.image.title ? banner.image.title : ''}
        />
      </AppLink>
    </div>
  )
}

export default Banner
