import React, { useCallback, useState } from 'react'

import { ButtonIcon } from '@components/molecules/ButtonIcon'
import TriangleLeft from '@icons/triangle-left.inline.svg'
import TriangleRight from '@icons/triangle-right.inline.svg'
import { useRouter } from 'next/navigation'
import { Pager } from '@lib/_generated/graphql_sdk'

interface PaginationMiniProps {
  pager?: Pager
}

export const PaginationMini: React.FC<PaginationMiniProps> = ({ pager }) => {
  const router = useRouter()

  const onChangePage = useCallback(
    (page: number) => {
      const url = new URL(window.location.href)
      const searchParams = url.searchParams

      searchParams.set('p', page.toString())

      router.push(`${url.pathname}?${searchParams.toString()}`, { scroll: false })
      console.log(Array.from(searchParams))
    },
    [router]
  )

  const { totalPages, page, pageSize = 24 } = pager || { totalPages: 1, page: 1 }

  const [limit, setLimit] = useState(pageSize)

  return (
    <div className="flex justify-between items-center py-3 px-4">
      <div className="flex items-center">
        <ButtonIcon
          icon={<TriangleLeft className="w-[5px] h-[5px] text-primary fill-accent" />}
          variant="outline"
          direction="horizontal"
          iconPosition="end"
          iconClassName="w-[35px] h-[35px]"
          labelClassName="tracking-normal text-sm w-fit"
          className="flex gap-3"
          onClick={() => onChangePage(page - 1 < 1 ? 1 : page - 1)}
        />
        <span className="px-4">
          <span className="text-lg font-medium">{page}</span> <span className="mx-1 text-sm text-gray-400">/</span>{' '}
          <span className="text-sm text-gray-400">{totalPages}</span>
        </span>
        <ButtonIcon
          icon={<TriangleRight className="w-[5px] h-[5px] text-primary fill-accent" />}
          variant="outline"
          direction="horizontal"
          iconPosition="end"
          iconClassName="w-[35px] h-[35px]"
          labelClassName="tracking-normal text-sm w-fit"
          className="flex gap-3"
          onClick={() => onChangePage(page + 1 > totalPages ? totalPages : page + 1)}
        />
      </div>
      <div>
        {/*<select id="limit" className="font-bold w-full xl:w-1/2" value={limit.toString()} onChange={handleLimitChange}>*/}
        {/*  <option value="24">24</option>*/}
        {/*  <option value="36">36</option>*/}
        {/*  <option value="48">48</option>*/}
        {/*</select>*/}
      </div>
    </div>
  )
}
