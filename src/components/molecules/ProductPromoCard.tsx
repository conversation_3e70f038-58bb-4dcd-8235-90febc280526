'use client'

import { LucideLoaderCircle } from 'lucide-react'
import React, { useCallback } from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Counter } from '@components/molecules/Counter'
import { Button } from '@components/theme/ui/button'
import { Card } from '@components/theme/ui/card'
import { useCartStore } from '@features/cart/cart-state'
import { AppProductPriceFragment, SimpleProductViewFragment, ProductMeasures } from '@lib/_generated/graphql_sdk'
import { ProductPrice } from '@components/molecules/ProductPrice'
import { siteStatusHelper, SiteStatusHelper } from '@lib/utils/SiteStatusHelper'
import { SingleCounter } from '@components/molecules/ProductBuyCards/components/SingleCounter'
import { BuyButton } from '@components/molecules/ProductBuyCards/components/BuyButton'
import productCard from '@components/molecules/ProductCard/ProductCard'
import { useProductPrice } from '@/src/features/product/product.selectors'
import { ProductStatus } from '@components/molecules/ProductBuyCards/components/ProductStatus'

interface ProductPromoCardProps {
  product: SimpleProductViewFragment
}

export const ProductPromoCard: React.FC<ProductPromoCardProps> = ({ product }) => {
  const [quantity, setQuantity] = React.useState(1)
  const [loading, setLoading] = React.useState(false)
  const cartStore = useCartStore()
  const productSku = product.sku
  const { price, measures } = product

  const { secondaryUnitPrice, totalAmount, savedAmount } = useProductPrice()
  const handleClick = useCallback(async () => {
    setLoading(true)
    await cartStore.addItem(productSku, quantity)
    setLoading(false)
  }, [cartStore, productSku, quantity])

  const status = siteStatusHelper.getStatus(product)

  return (
    <Card className="bg-primary px-0 py-5 my-3 rounded-2xl hidden lg:block">
      <div
        className={cn('flex flex-col 5xl:flex-row items-center gap-3 xl:px-5', {
          '2xl:flex-col 6xl:flex-row': !!price.special,
        })}
      >
        <ProductPrice
          data={measures.secondary ? { price: { value: secondaryUnitPrice, currency: measures.secondary } } : price}
          regularPriceLabel="Редовна цена"
          specialPriceLabel="Промо цена"
          showLabels={!!price.special}
          variant="productSecondary"
        />
        <div className="flex items-center gap-3">
          {status === SiteStatusHelper.AVAILABLE ||
          status === SiteStatusHelper.AVAILABLE_PICKUP ||
          status === SiteStatusHelper.INDIVIDUAL_ORDER ||
          status === SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP ? (
            <>
              <div className="flex items-center gap-1 flex-1">
                <Counter
                  onChangeAction={(value) => {
                    setQuantity(value)
                  }}
                  min={1}
                />
                <Text className="text-white text-xs min-w-fit">/ {measures?.secondary || measures?.base}</Text>
              </div>
              {(status === SiteStatusHelper.AVAILABLE || status === SiteStatusHelper.INDIVIDUAL_ORDER) && (
                <BuyButton loading={loading} onClick={handleClick} />
              )}
              {(status === SiteStatusHelper.AVAILABLE_PICKUP ||
                status === SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP) && (
                <BuyButton
                  loading={loading}
                  onClick={handleClick}
                  buttonText="Купи онлайн и вземи от магазин"
                  className="whitespace-normal tracking-tight"
                />
              )}
            </>
          ) : (
            <ProductStatus
              product={product}
              type="text"
              className="col-span-2 text-center text-white py-4"
              allowedStatuses={[
                SiteStatusHelper.NO_STATUS,
                SiteStatusHelper.NOT_ACTIVE,
                SiteStatusHelper.DEPLETED,
                // SiteStatusHelper.AVAILABLE,

                // SiteStatusHelper.AVAILABLE_PICKUP,
                SiteStatusHelper.AWAITING_DELIVERY,
                SiteStatusHelper.AWAITING_DELIVERY_PICKUP,

                SiteStatusHelper.INDIVIDUAL_ORDER,

                SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP,
                SiteStatusHelper.ONLY_IN_STORE,
                SiteStatusHelper.LIMITED_VISIBILITY,
              ]}
            />
          )}
        </div>
      </div>
    </Card>
  )
}
