import React, { ReactElement, ReactNode } from 'react'

import { useOnClickOutside } from '@/src/hooks/UiHooks'
import { useMenuContext } from '@components/molecules/Menu/Menu'
import { addClasses } from '@lib/style/style'

export interface MenuDropdownProps {
  className?: string
  width?: string
  children: ReactNode
}

const MenuDropdown = ({ children, width, className }: MenuDropdownProps): ReactElement<any> => {
  const { isOpen, close } = useMenuContext()
  const ref = React.useRef<any>(null)

  useOnClickOutside(ref, close)

  return (
    <div
      ref={ref}
      className={addClasses(
        isOpen ? 'block' : 'hidden',
        width ?? 'w-56',
        'origin-top-right absolute right-0 mt-2',
        'rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5',
        className
      )}
    >
      {children}
    </div>
  )
}
MenuDropdown.displayName = 'Menu.Dropdown'

export default MenuDropdown
