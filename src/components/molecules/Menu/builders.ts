import { ReactElement } from 'react'

export function getChildByDisplayName(
  children: ReactElement<any>[] | ReactElement<any>,
  displayName: string
): ReactElement<any> | undefined {
  const childrenArray = Array.isArray(children) ? children : [children]

  return childrenArray.find(
    (child) =>
      typeof child?.type === 'function' &&
      // @ts-ignore
      child?.type?.displayName === displayName
  )
}
