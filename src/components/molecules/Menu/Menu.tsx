'use client'
import React, { ReactElement } from 'react'

import { ToggleHook, useToggleHook } from '@/src/hooks/UiHooks'
import { ButtonProps } from '@atoms/Button/CustomButton'
import { getChildByDisplayName } from '@components/molecules/Menu/builders'
import MenuDropdown, { MenuDropdownProps } from '@components/molecules/Menu/Menu.Dropdown'
import MenuTarget from '@components/molecules/Menu/Menu.Target'
import { createRequiredContext } from '@context/CtxUtils'
import { addClasses } from '@lib/style/style'

interface MenuContextValue extends ToggleHook {}
const [MenuContext, useMenuContext] = createRequiredContext<MenuContextValue>('GridProvider')

interface DropdownProps {
  className?: string
  children: ReactElement<any> | ReactElement<any>[]
}
const Menu: React.FC<DropdownProps> & {
  Target: React.FC<ButtonProps>
  Dropdown: React.FC<MenuDropdownProps>
} = (props) => {
  const toggle = useToggleHook({
    closeOnEscape: true,
  })

  return (
    <MenuContext value={toggle}>
      <div className={addClasses('relative inline-block', props.className)}>
        {getChildByDisplayName(props.children, MenuTarget.displayName)}
        {getChildByDisplayName(props.children, MenuDropdown.displayName)}
      </div>
    </MenuContext>
  )
}

Menu.Target = MenuTarget
Menu.Dropdown = MenuDropdown

export default Menu
export { useMenuContext }
