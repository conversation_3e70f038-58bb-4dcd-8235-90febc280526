import React, { ReactElement } from 'react'

import CustomButton, { ButtonProps } from '@atoms/Button/CustomButton'
import { useMenuContext } from '@components/molecules/Menu/Menu'

const MenuTarget = ({ children, ..._buttonProps }: ButtonProps): ReactElement<any> => {
  const { toggle } = useMenuContext()

  return (
    <CustomButton
      style={{
        zIndex: 100,
      }}
      unstyled
      {..._buttonProps}
      onClick={toggle}
    >
      {children}
    </CustomButton>
  )
}
MenuTarget.displayName = 'Menu.Target'

export default MenuTarget
