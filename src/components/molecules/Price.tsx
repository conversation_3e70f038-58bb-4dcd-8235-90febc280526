import React from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'

interface PriceProps {
  value: string
  currency: string
  oldPrice?: boolean
  discounted?: boolean
}
export const Price: React.FC<PriceProps> = ({ value, currency, oldPrice, discounted }) => {
  return (
    <div
      className={cn('flex gap-1 place-items-baseline', { 'line-through': oldPrice }, { 'text-primary': discounted })}
    >
      <Text className={cn('font-bold text-xl leading-6', oldPrice ? 'text-lg' : 'text-xl')}>{value}</Text>
      <Text className={cn('leading-6', oldPrice ? 'text-sm' : 'text-base')}>{currency}.</Text>
    </div>
  )
}
