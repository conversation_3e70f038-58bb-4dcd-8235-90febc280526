import React from 'react'

import Img from '@atoms/Img'
import { cn } from '@components/lib/utils'
import { ClassName } from '@lib/types/ClassName'

// Define the available icon types
const AVAILABLE_ICONS = [
  'card',
  'cube3d',
  'drill',
  'key',
  'lamp',
  'machine',
  'paint',
  'painting',
  'paper',
  'saw',
  'scissors',
  'shower',
  'tiles',
  'tools',
  'truck',
  'wallpaper',
  'xxx',
] as const

// Define the TypeScript type from the available icons
export type ServiceType = (typeof AVAILABLE_ICONS)[number]

// Import icons dynamically with type safety
const importIcons = (type: ServiceType) => {
  try {
    return [
      require(`@icons/minimalistic/hoverable/default/${type}.inline.svg`).default,
      require(`@icons/minimalistic/hoverable/hover/${type}.inline.svg`).default,
    ]
  } catch {
    return [null, null] // Gracefully handle missing icons
  }
}

interface ServiceTileProps {
  type?: ServiceType // Enforces valid icon types
  icon?: string
  title?: string
  className?: ClassName
}

const mapServices: Record<string, ServiceType> = {
  'rent_machine': 'machine',
  'transport': 'truck',
  'machine_tuning': 'painting',
  'montaj': 'tools',
  'carpets_treatment': 'paper',
  '3dprojecting': 'cube3d',
  'ryazane': 'scissors',
  'keys': 'key',
}

function getFilename(url: string) {
  const fullFilename = url.split('/').pop()
  return fullFilename?.split('.')[0]
}

export const ServiceTile: React.FC<ServiceTileProps> = ({ type, icon, title, className }) => {
  const filename = icon ? getFilename(icon) : null
  const guessedType = filename ? mapServices[filename] : null

  const iconType = type || guessedType
  const [Normal, Hovered] = iconType ? importIcons(iconType) : [null, null]

  return (
    <div
      className={cn(
        'flex flex-col h-full items-center space-y-2 bg-white rounded-2xl py-2 px-5 self-start cursor-pointer group',
        className
      )}
    >
      <div className="relative h-12">
        {iconType && (
          <>
            {Normal && <Normal className="transition-opacity duration-300 opacity-100 group-hover:opacity-0" />}
            {Hovered && (
              <Hovered className="absolute top-0 left-0 transition-opacity duration-300 opacity-0 group-hover:opacity-100" />
            )}
          </>
        )}
        {!iconType && (
          <Img src={icon ?? ''} alt={title || ''} width={80} height={60} style={{ objectFit: 'contain' }} />
        )}
      </div>
      {title && (
        <div className="flex flex-1 items-center justify-center text-center text-sm text-gray-700 font-bold">
          {title}
        </div>
      )}
    </div>
  )
}
