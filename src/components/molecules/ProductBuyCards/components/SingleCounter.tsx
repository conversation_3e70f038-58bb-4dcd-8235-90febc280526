import Text from '@atoms/Text'
import { Counter } from '@components/molecules/Counter'

interface SingleCounterProps {
  label: string
  onChangeAction: (value: number) => void
}

export const SingleCounter: React.FC<SingleCounterProps> = ({ label, onChangeAction }) => {
  return (
    <div>
      <Counter onChangeAction={onChangeAction} min={1} />
      <Text className="text-white text-xs"> / {label}</Text>
    </div>
  )
}
