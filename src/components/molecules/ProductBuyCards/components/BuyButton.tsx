import { LucideLoaderCircle } from 'lucide-react'

import Text from '@atoms/Text'
import { Button } from '@components/theme/ui/button'
import { ClassName } from '@lib/types/ClassName'
import { cn } from '@components/lib/utils'

interface BuyButtonProps {
  onClick: () => void
  loading: boolean
  buttonText?: string
  className?: ClassName
}

export const BuyButton = ({ onClick, loading, buttonText, className }: BuyButtonProps) => {
  return (
    <Button
      variant="inverse"
      className={cn(
        'text-white text-sm px-2.5 sm:px-6 xl:px-2 2xl:px-5 tracking-widest font-medium',
        'whitespace-normal',
        'max-w-[190px] sm:max-w-none',
        className
      )}
      onClick={onClick}
    >
      {loading && <LucideLoaderCircle className="animate-spin" />}
      <Text>{buttonText || 'Купи продукта'}</Text>
    </Button>
  )
}
