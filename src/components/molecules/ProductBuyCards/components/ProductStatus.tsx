import { SimpleProductViewFragment } from '@lib/_generated/graphql_sdk'

interface ProductStatusProps {
  product: SimpleProductViewFragment
  type?: 'badge' | 'text'
  allowedStatuses?: number[]
  className?: ClassName
}

import { SiteStatusHelper, siteStatusHelper } from '@lib/utils/SiteStatusHelper'
import { LucideWarehouse } from 'lucide-react'
import { Button } from '@components/theme/ui/button'
import { cn } from '@components/lib/utils'
import { ClassName } from '@lib/types/ClassName'

const styles = {
  [SiteStatusHelper.NO_STATUS]:
    'text-gray-700 border-gray-400 bg-gray-300 hover:bg-gray-400 hover:text-gray-800 hover:border-gray-500',
  [SiteStatusHelper.NOT_ACTIVE]:
    'text-gray-700 border-red-400 bg-red-300 hover:bg-red-400 hover:text-gray-800 hover:border-red-500',
  [SiteStatusHelper.DEPLETED]:
    'text-white border-red-600 bg-red-500 hover:bg-red-600 hover:text-white hover:border-red-700',
  [SiteStatusHelper.AVAILABLE]:
    'text-white border-green-600 bg-green-500 hover:bg-green-600 hover:text-white hover:border-green-700',
  [SiteStatusHelper.AVAILABLE_PICKUP]:
    'text-white border-green-600 bg-green-500 hover:bg-green-600 hover:text-white hover:border-green-700',
  [SiteStatusHelper.AWAITING_DELIVERY]:
    'text-white border-amber-600 bg-amber-500 hover:bg-amber-600 hover:text-white hover:border-amber-700',
  [SiteStatusHelper.AWAITING_DELIVERY_PICKUP]:
    'text-white border-amber-600 bg-amber-500 hover:bg-amber-600 hover:text-white hover:border-amber-700',
  [SiteStatusHelper.INDIVIDUAL_ORDER]:
    'text-white border-cyan-600 bg-cyan-500 hover:bg-cyan-600 hover:text-white hover:border-cyan-700',
  [SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP]:
    'text-white border-cyan-600 bg-cyan-500 hover:bg-cyan-600 hover:text-white hover:border-cyan-700',
  [SiteStatusHelper.ONLY_IN_STORE]:
    'text-white border-purple-600 bg-purple-500 hover:bg-purple-600 hover:text-white hover:border-purple-700',
  [SiteStatusHelper.LIMITED_VISIBILITY]:
    'text-white border-gray-600 bg-gray-500 hover:bg-gray-600 hover:text-white hover:border-gray-700',
}

const baseButtonClass = 'bg-transparent text-xs w-full py-2 tracking-tight lg:tracking-widest'

const wIcon = <LucideWarehouse className="mr-1 h-4 w-4" />

export const ProductStatus: React.FC<ProductStatusProps> = ({
  product,
  type = 'badge',
  allowedStatuses,
  className,
}) => {
  const status = siteStatusHelper.getStatus(product)

  // If allowedStatuses is provided and the current status is not in the list, return null
  if (allowedStatuses && allowedStatuses.length > 0 && !allowedStatuses.includes(status)) {
    return null
  }

  // Define status text and icon for each status
  let statusText = ''
  let hasIcon = false

  switch (status) {
    case SiteStatusHelper.NO_STATUS:
      statusText = 'Няма статус'
      break
    case SiteStatusHelper.NOT_ACTIVE:
      statusText = 'Неактивен'
      break
    case SiteStatusHelper.DEPLETED:
      statusText = type === 'badge' ? 'Изчерпан' : 'Продуктът е изчерпан.'
      break
    case SiteStatusHelper.AVAILABLE:
      statusText = 'Наличен'
      break
    case SiteStatusHelper.AVAILABLE_PICKUP:
      statusText = type === 'badge' ? 'Наличен за вземане от магазин' : 'Купи онлайн. Вземи от магазин'
      hasIcon = true
      break
    case SiteStatusHelper.AWAITING_DELIVERY:
      statusText = type === 'badge' ? 'Изчерпана наличност' : 'Продъктът очаква доставка.'
      break
    case SiteStatusHelper.AWAITING_DELIVERY_PICKUP:
      statusText =
        type === 'badge'
          ? 'Изчерпана наличност'
          : 'Продуктът очаква доставка. Когато е наличен, може да бъде закупен онлайн и взет от магазин.'
      hasIcon = true
      break
    case SiteStatusHelper.INDIVIDUAL_ORDER:
      statusText = 'Продукт по индивидуална поръчка'
      break
    case SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP:
      statusText = 'Продукт по индивидуална поръчка'
      hasIcon = true
      break
    case SiteStatusHelper.ONLY_IN_STORE:
      statusText =
        type === 'badge' ? 'Наличен само в магазин' : 'Не се предлага онлайн. Може да се закупи само от магазин'
      hasIcon = true
      break
    case SiteStatusHelper.LIMITED_VISIBILITY:
      statusText = 'Ограничена видимост'
      break
    default:
      return null
  }

  // Return based on type prop
  if (type === 'badge') {
    return (
      <Button className={cn(baseButtonClass, styles[status], className)}>
        {hasIcon && wIcon}
        {statusText}
      </Button>
    )
  } else {
    // Return just the text for 'text' type
    if (className) {
      return <span className={cn(className)}>{statusText}</span>
    }
    return statusText
  }
}
