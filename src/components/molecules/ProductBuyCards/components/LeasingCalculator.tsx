import React, { useCallback } from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import PostBankLogo from '@images/postbank-logo.inline.svg'
import TBIBankLogo from '@images/tbi-bank-logo.inline.svg'
import { GraphQLBackend } from '@lib/api/graphql'
import { SimpleProductViewFragment, TbiCreditDataFragment } from '@lib/_generated/graphql_sdk'
import * as Dialog from '@/src/components/theme/ui/dialog'
import { TBIBankPayment } from '@/src/app/showcase/TBIPayment'
import { LucideCircleX, LucideLoaderCircle } from 'lucide-react'
import { Callout } from '@components/molecules/Callout'

interface LeasingCalculatorProps {
  type: 'primary' | 'secondary'
  product: SimpleProductViewFragment
  quantity?: number
}

// Inside your component or function

export const LeasingCalculator: React.FC<LeasingCalculatorProps> = ({ type, product, quantity = 1 }) => {
  const [creditData, setCreditData] = React.useState<TbiCreditDataFragment>()
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState(false)

  const { sku } = product
  const onClick = useCallback(async () => {
    if (!creditData) {
      setError(false)
      setLoading(true)
      GraphQLBackend.GetTBICredit({ sku: sku })
        .then((response) => {
          if (response.getCreditCalculatorTBIBank) {
            setCreditData(response.getCreditCalculatorTBIBank)
          }
        })
        .catch(() => {
          setError(true)
          setCreditData(undefined)
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }, [creditData, sku])

  return (
    <Dialog.Dialog>
      <Dialog.DialogTrigger asChild>
        <div className="flex gap-3 items-center">
          <TBIBankLogo width={80} height={20} />
          <Button
            onClick={onClick}
            variant="outline"
            className={cn(
              'bg-transparent text-xs w-full py-2 tracking-tighter lg:tracking-widest',
              type === 'primary'
                ? 'text-black border-black hover:bg-black hover:text-white hover:border-white'
                : 'text-white hover:bg-white hover:text-primary hover:border-primary'
            )}
          >
            <Text>TBI Лизингов калкулатор</Text>
          </Button>
        </div>
      </Dialog.DialogTrigger>
      <Dialog.DialogContent className="2xl:rounded-2xl 2xl:max-w-2xl bg-white">
        <Dialog.DialogHeader>
          <Dialog.DialogTitle className="font-bold">Вземи на изплащане</Dialog.DialogTitle>
          {/* className="sm:max-w-[425px]" */}
        </Dialog.DialogHeader>
        {/*<Dialog.DialogContent>*/}
        {/*  <Dialog.DialogHeader>*/}
        {/*    <Dialog.DialogTitle>*/}
        {/*      Вземи желания продукт сега, избери подходяща за теб схема на изплащане:*/}
        {/*    </Dialog.DialogTitle>*/}
        {/*    <Dialog.DialogDescription>*/}
        {/*      Make changes to your profile here. Click save when you&apos;re done.*/}
        {/*    </Dialog.DialogDescription>*/}
        {/*  </Dialog.DialogHeader>*/}
        {creditData && <TBIBankPayment product={product} quantity={quantity} creditData={creditData} />}
        {loading && (
          <Callout
            variant="warning"
            title="Моля, изчакайте..."
            icon={<LucideLoaderCircle className="animate-spin" />}
            className="mb-0"
          >
            <Text>Зареждане на информация...</Text>
          </Callout>
        )}
        {error && (
          <Callout variant="error" title="Грешка при зареждане на информация" icon={<LucideCircleX />} className="mb-0">
            <Text>В момента нямаме информация за този продукт. Моля, опитайте по-късно.</Text>
          </Callout>
        )}
        {/*</Dialog.DialogContent>*/}
      </Dialog.DialogContent>
    </Dialog.Dialog>
  )
}
