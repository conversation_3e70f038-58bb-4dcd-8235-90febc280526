import React from 'react'

import Text from '@atoms/Text'
import { AppProductPriceFragment } from '@lib/_generated/graphql_sdk'
import { ProductPrice } from '@components/molecules/ProductPrice'

interface PriceInfoProps {
  price: AppProductPriceFragment
}

export const PriceInfo: React.FC<PriceInfoProps> = ({ price }) => (
  <div className="flex flex-col-reverse items-end leading-3 justify-between">
    <Text className="text-xs text-white/90">Обща цена</Text>
    <ProductPrice data={price} variant="productTotal" />
  </div>
)
