import React from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { scrollToElement } from '@context/ScrollUtils'
import { SiteStatusHelper, siteStatusHelper } from '@lib/utils/SiteStatusHelper'
import { LucideWarehouse } from 'lucide-react'
import { SimpleProductViewFragment } from '@lib/_generated/graphql_sdk'

interface AvailabilityCheckButtonProps {
  type: 'primary' | 'secondary'
  product: SimpleProductViewFragment
}

export const AvailabilityCheckButton: React.FC<AvailabilityCheckButtonProps> = ({ type, product }) => {
  const isLimitedVisibility = siteStatusHelper.isLimitedAvailability(product)
  const status = siteStatusHelper.getStatus(product)

  if (status === SiteStatusHelper.INDIVIDUAL_ORDER) return null

  const btnClass = !isLimitedVisibility
    ? cn(
        type === 'primary'
          ? 'text-black border-primary hover:bg-primary hover:text-white hover:border-white'
          : 'text-white hover:bg-white hover:text-primary hover:border-primary'
      )
    : type === 'primary'
      ? 'text-black border-yellow-500 bg-yellow-400 hover:bg-yellow-500 hover:text-black hover:border-yellow-600'
      : 'text-white hover:bg-white hover:text-primary hover:border-primary'

  return (
    <Button
      onClick={(e) => {
        e.preventDefault()
        scrollToElement('availability-description-block')
      }}
      variant="outline"
      className={cn('bg-transparent text-xs w-full py-2 tracking-tight lg:tracking-widest', btnClass)}
    >
      <Text>{isLimitedVisibility ? 'Ограничена наличност' : 'Провери наличност в магазините'}</Text>
    </Button>
  )
}
