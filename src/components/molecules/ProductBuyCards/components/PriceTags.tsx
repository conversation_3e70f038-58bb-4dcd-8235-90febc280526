import React from 'react'

import { EnergyType, ProductEnergyTag } from '@components/molecules/ProductEnergyTag'
import { AppEnergyLabelFragment, AppProductPriceFragment } from '@lib/_generated/graphql_sdk'
import { ProductPrice } from '@components/molecules/ProductPrice'
import AppLink from '@atoms/AppLink'

interface PriceTagsProps {
  energyLabel?: AppEnergyLabelFragment | null
  data: AppProductPriceFragment
}

export const PriceTags: React.FC<PriceTagsProps> = ({ energyLabel, data }) => (
  <div>
    <div className="flex gap-2 pb-3">
      {energyLabel?.image && energyLabel.image.title && energyLabel.image.title.length > 0 && (
        <div className="flex">
          <div className="self-stretch flex items-center">
            <AppLink href={energyLabel.labelUrl ?? '#'} target="_blank">
              <ProductEnergyTag
                type={energyLabel.image.title as EnergyType}
                size="sm"
                alt="Продуктов енергиен етикет"
              />
            </AppLink>
          </div>
        </div>
      )}
      <ProductPrice
        data={data}
        showLabels={!!data.special}
        variant="productPrimary"
        specialPriceLabel="Промо цена"
        regularPriceLabel="Предишна цена"
      />
    </div>
  </div>
)
