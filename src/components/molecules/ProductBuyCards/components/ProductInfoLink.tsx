import React from 'react'

import AppLink from '@atoms/AppLink'
import Text from '@atoms/Text'

interface ProductInfoLinkProps {
  title: string
  url: string
}
export const ProductInfoLink: React.FC<ProductInfoLinkProps> = ({ title, url }) => {
  return (
    <div className="px-2">
      <AppLink
        href={url}
        className="text-white/80 text-[15px] md:text-white/70 md:text-xs underline block"
        target="_blank"
      >
        <Text>{title}</Text>
      </AppLink>
    </div>
  )
}
