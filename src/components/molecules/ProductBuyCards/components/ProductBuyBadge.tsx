import React from 'react'

import AppLink from '@atoms/AppLink'

interface ProductBuyBadgeProps {
  regularLabel?: string
  highlightLabel?: string
  url?: string
}

export const ProductBuyBadge: React.FC<ProductBuyBadgeProps> = ({ regularLabel, highlightLabel, url }) => (
  <div className="inline-flex flex-row items-center justify-center gap-2 bg-white rounded-full text-sm px-2.5 py-1.5 font-medium">
    {highlightLabel && <TextOrLink text={highlightLabel} url={url} className="text-primary text-nowrap" />}
    {regularLabel && <TextOrLink text={regularLabel} url={url} className="text-black text-nowrap" />}
  </div>
)

const TextOrLink = ({ text, url, className }: { text: string; url?: string; className: string }) =>
  url ? (
    <AppLink className={className} href={url}>
      {text}
    </AppLink>
  ) : (
    <div className={className}>{text}</div>
  )
