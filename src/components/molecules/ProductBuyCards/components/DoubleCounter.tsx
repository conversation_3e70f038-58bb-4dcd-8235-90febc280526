'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { useDebounceCallback } from 'usehooks-ts'

import Text from '@atoms/Text'
import { Counter } from '@components/molecules/Counter'
import ChainedCounters from '@icons/chained-counters.inline.svg'
import { ProductMeasures, ProductPrice } from '@lib/_generated/graphql_sdk'

interface DoubleCounterProps {
  measures: ProductMeasures
  onCountChangeAction: (baseCounter: number, secondaryCounter: number) => void
}

export const DoubleCounter: React.FC<DoubleCounterProps> = ({ measures, onCountChangeAction }) => {
  const [baseCounter, setBaseCounter] = useState(1)
  const [secondaryCounter, setSecondaryCounter] = useState(1)

  const updateBase = useCallback(
    (value: number) => {
      const positiveValue = Math.max(measures.secondaryQty, value)
      const base = positiveValue
      const secondary = Math.ceil(positiveValue / measures.secondaryQty)
      setBaseCounter(base)
      setSecondaryCounter(secondary)
      onCountChangeAction(base, secondary)
    },
    [measures.secondaryQty, onCountChangeAction]
  )

  const updateSecondary = useCallback(
    (value: number) => {
      const positiveValue = Math.max(1, value)
      console.log('updateSecondary', positiveValue)
      const base = Math.floor(positiveValue * measures.secondaryQty)
      const secondary = value
      setBaseCounter(base)
      setSecondaryCounter(secondary)
      onCountChangeAction(base, secondary)
    },
    [measures.secondaryQty, onCountChangeAction]
  )

  const debouncedBase = useDebounceCallback(updateBase, 10)
  const debouncedSecondary = useDebounceCallback(updateSecondary, 10)

  return (
    <div className="flex">
      <div className="flex justify-start pl-1 pt-5">
        <ChainedCounters height={42} width={20} className="text-white" />
      </div>
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-2 break-keep">
          <Counter value={secondaryCounter} onChangeAction={debouncedSecondary} min={1} />
          <Text className="text-black text-xs">/ {measures.secondary}</Text>
        </div>
        <div className="flex items-center gap-2 break-keep">
          <Counter value={Math.round(baseCounter)} onChangeAction={debouncedBase} min={1} />
          <Text className="text-black text-xs"> / {measures.base}</Text>
        </div>
      </div>
    </div>
  )
}
