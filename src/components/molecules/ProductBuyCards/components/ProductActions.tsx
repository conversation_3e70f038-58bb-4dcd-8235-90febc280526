import React from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { ButtonIcon } from '@components/molecules/ButtonIcon'
import { ButtonVertical } from '@components/molecules/ButtonVertical'
import Box3DIcon from '@icons/box3d.inline.svg'
import ChangeIcon from '@icons/change.inline.svg'
import HeadphonesIcon from '@icons/headphones.inline.svg'
import { CMSBlock } from '../../Widget/markup/instanced'
import Link from 'next/link'

interface ProductActionsProps {
  type: 'primary' | 'secondary'
}

interface ProductPerkProps {
  children: React.ReactNode
  type?: 'primary' | 'secondary'
}

const classnames = {
  primary: {
    button: 'bg-primary text-white hover:bg-white hover:text-primary',
    icon: 'text-white/90 group-hover:bg-transparent',
  },
  secondary: {
    button: 'bg-transparent text-white hover:bg-white/10',
    icon: 'text-white/90 group-hover:bg-transparent',
  },
}

export const ProductActions: React.FC<ProductActionsProps> = ({ type }) => {
  return (
    <div className="flex gap-2 border border-white/20 rounded-lg">
      <CMSBlock id="product_page_perks" />
      {/* <ProductPerk>
        <HeadphonesIcon />
        Консултация
      </ProductPerk>
      <ProductPerk href="/asd">
        <ChangeIcon />
        Право на връщане
      </ProductPerk>
      <ProductPerk>
        <Box3DIcon />
        Проверка на пратка
      </ProductPerk> */}
    </div>
  )
}
export const ProductPerk = ({
  href,
  target = '_self',
  children,
}: {
  href?: string
  target?: string
  children: React.ReactNode
}) => {
  // Extract icon and text from children
  const childrenArray = React.Children.toArray(children)
  const icon = childrenArray[0]
  const text = childrenArray.slice(1).join('')
  const Wrapper = ({ ...props }: any) => (href ? <Link {...props} /> : <div {...props} />)
  return (
    <Wrapper
      href={href}
      target={target}
      className="flex flex-col flex-1 items-center justify-start py-2 hover:bg-white/10"
    >
      <ButtonIcon
        className="gap-2"
        variant="outline"
        icon={icon}
        iconClassName="p-2.5 w-10 h-10 text-black"
        label={<Text className="leading-4">{text}</Text>}
        labelClassName="tracking-tight text-black"
      />
    </Wrapper>
  )
}
