'use client'

import React, { useEffect, useState } from 'react'

import Text from '@atoms/Text'
import { AvailabilityCheckButton } from '@components/molecules/ProductBuyCards/components/AvailabilityCheckButton'
import { BuyButton } from '@components/molecules/ProductBuyCards/components/BuyButton'
import { DoubleCounter } from '@components/molecules/ProductBuyCards/components/DoubleCounter'
import { PriceInfo } from '@components/molecules/ProductBuyCards/components/PriceInfo'
import { PriceTags } from '@components/molecules/ProductBuyCards/components/PriceTags'
import { ProductActions } from '@components/molecules/ProductBuyCards/components/ProductActions'
import { ProductBuyBadge } from '@components/molecules/ProductBuyCards/components/ProductBuyBadge'
import { ProductBuyDivider } from '@components/molecules/ProductBuyCards/components/ProductBuyDivider'
import { EnergyType } from '@components/molecules/ProductEnergyTag'
import { Card } from '@components/theme/ui/card'
import { useCartStore } from '@features/cart/cart-state'
import { calcDiscountPercent } from '@features/product/filters'
import { productToState } from '@features/product/helpers'
import { useProductPrice } from '@features/product/product.selectors'
import { useProductStore } from '@features/product/product.store'
import { SimpleProductViewFragment, SimpleProduct } from '@lib/_generated/graphql_sdk'
import { ProductPrice } from '@components/molecules/ProductPrice'
import { ProductStatus } from '@components/molecules/ProductBuyCards/components/ProductStatus'
import { siteStatusHelper, SiteStatusHelper } from '@lib/utils/SiteStatusHelper'
import { SingleCounter } from '@components/molecules/ProductBuyCards/components/SingleCounter'
import { LeasingCalculator } from '@components/molecules/ProductBuyCards/components/LeasingCalculator'

interface MultipleBuyProductCardProps {
  product: SimpleProductViewFragment
  children: React.ReactNode
}

export const MultipleBuyProductCard: React.FC<MultipleBuyProductCardProps> = ({ product, children }) => {
  const { price: regularPrice, special: promoPrice } = product.price
  const { initProduct, setSecondaryQty, setPrimaryQty, secondaryUnit, secondaryQty, ready, reset } = useProductStore()
  const { secondaryUnitPrice, totalAmount, savedAmount } = useProductPrice()
  const cartStore = useCartStore()

  const [loading, setLoading] = useState(false)

  const clickHandler = () => {
    setLoading(true)
    cartStore.addItem(product.sku, secondaryQty).finally(() => {
      setLoading(false)
    })
  }
  useEffect(reset, [reset])

  useEffect(() => {
    if (!ready) {
      initProduct(productToState(product))
    }
  }, [initProduct, product, ready])

  const status = siteStatusHelper.getStatus(product)

  const canBeBought = (status: number) => {
    return (
      status === SiteStatusHelper.AVAILABLE ||
      status === SiteStatusHelper.AVAILABLE_PICKUP ||
      status === SiteStatusHelper.INDIVIDUAL_ORDER ||
      status === SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP
    )
  }

  return (
    <Card className="flex flex-col w-full flex-1">
      <div className="bg-primary rounded-xl">
        <div className="flex gap-2 pt-2.5 pl-3">
          {promoPrice?.value && (
            <ProductBuyBadge
              highlightLabel={`-${calcDiscountPercent(regularPrice.value, promoPrice.value).toFixed(0)}%`}
              regularLabel="отстъпка"
            />
          )}
          {product.labels.buyCheap && <ProductBuyBadge highlightLabel="Купи изгодно" />}
          {product.labels.fromBrochure && <ProductBuyBadge regularLabel="Продукт от брошура" />}
        </div>
        <div className="flex flex-col gap-4 py-4">
          <div className="px-5 sm:px-4" id="product-buy-card">
            <PriceTags
              data={{
                price: {
                  value: product.price.price.value,
                  currency: product.price.price.currency,
                },
                ...(product.price.special
                  ? {
                      special: {
                        value: product.price.special.value,
                        currency: product.price.special.currency,
                      },
                    }
                  : {}),
              }}
              energyLabel={product?.energyLabel}
            />
            <ProductBuyDivider />
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-y-3 2xl:grid-cols-2 justify-between px-2 sm:px-4">
            {canBeBought(status) && (
              <DoubleCounter
                measures={product.measures}
                onCountChangeAction={(baseCounter, secondaryCounter) => {
                  setPrimaryQty(baseCounter)
                  setSecondaryQty(secondaryCounter)
                }}
              />
            )}
            <div className="flex flex-col gap-2 px-5 2xl:px-0">
              {canBeBought(status) && (
                <PriceInfo
                  price={{
                    price: {
                      value: totalAmount,
                      currency: product.price.price.currency,
                    },
                  }}
                />
              )}
              {promoPrice && savedAmount > 0 && (
                <ProductPrice
                  showLabels
                  regularPriceLabel="Вие спестявате"
                  data={{
                    price: {
                      value: savedAmount,
                      currency: product.price.price.currency,
                    },
                  }}
                  variant="productSaved"
                />
              )}
              <ProductPrice
                showLabels
                regularPriceLabel={`Цена за ${secondaryUnit}`}
                data={{
                  price: {
                    value: secondaryUnitPrice,
                    currency: product.price.price.currency,
                  },
                }}
                variant="productUnit"
              />
              {/*{process.env.NODE_ENV === 'development' && (*/}
              {/*  <>*/}
              {/*    <div className="flex justify-between text-white/90 text-xs">*/}
              {/*      <Text>Цена за {primaryUnit}:</Text>*/}
              {/*      <span>{primaryUnitPrice.toFixed(2)} {currency}</span>*/}
              {/*    </div>*/}
              {/*    <div className="flex justify-between text-white/90 text-xs">*/}
              {/*      <Text>{primaryUnit} в {secondaryUnit}:</Text>*/}
              {/*      <span>{multiplier} {primaryUnit}</span>*/}
              {/*    </div>*/}
              {/*  </>*/}
              {/*)}*/}

              {canBeBought(status) && (
                <div className="sm:col-span-2 lg:col-span-2 xl:col-span-1 3xl:col-span-3 4xl:col-span-4 flex justify-end">
                  {(status === SiteStatusHelper.AVAILABLE || status === SiteStatusHelper.INDIVIDUAL_ORDER) && (
                    <BuyButton onClick={clickHandler} loading={loading} />
                  )}
                  {(status === SiteStatusHelper.AVAILABLE_PICKUP ||
                    status === SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP) && (
                    <BuyButton
                      loading={loading}
                      onClick={clickHandler}
                      buttonText={!loading ? 'Купи онлайн и вземи от магазин' : 'Купи онлайн и вземи от...'}
                      className="whitespace-normal tracking-tight py-6 px-2"
                    />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-4 py-4 px-6 flex-1 justify-between">
        {canBeBought(status) && (
          <>
            {totalAmount > 100 && <LeasingCalculator quantity={secondaryQty} product={product} type="primary" />}
            {status !== SiteStatusHelper.INDIVIDUAL_ORDER && status !== SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP && (
              <AvailabilityCheckButton type="primary" product={product} />
            )}
          </>
        )}
        {/*<AvailabilityCheckButton type="primary" product={product} />*/}
        <ProductStatus product={product} />
        {/* <ProductActions type="primary" /> */}
        {children}
      </div>
    </Card>
  )
}
