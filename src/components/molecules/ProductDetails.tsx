'use client'
import React, { useEffect } from 'react'

import Text from '@atoms/Text'
import { ButtonIcon } from '@components/molecules/ButtonIcon'
import { ProductBrandImage } from '@components/molecules/ProductBrandImage'
import { Card, CardFooter, CardHeader } from '@components/theme/ui/card'
import HeartIcon from '@icons/heart.inline.svg'
import { Product } from '@lib/_generated/graphql_sdk'

import { ProductBadges } from './ProductBadges'
import { ProductShortDescription } from './ProductShortDescription'
import { useCustomerStore } from '@/src/features/customer/customer.store'
import { LucideHeart, LucideHeartOff } from 'lucide-react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'

export const ProductDetails: React.FC<Product> = ({
  name,
  sku,
  id,
  description,
  brand,
  labels,
  price,
  shortDescription,
}) => {
  const { addToWishlist, removeFromWishlist, wishlistSkus, authenticated } = useCustomerStore()
  const router = useRouter()
  const path = usePathname()

  return (
    <Card className="flex flex-col flex-1">
      <CardHeader className="pb-3">
        <div className="flex justify-between">
          <div className="flex flex-col gap-4 flex-1">
            <h1 className="text-base font-bold">{name}</h1>
            <div className="flex flex-row justify-between">
              <div className="flex lg:flex-col xl:flex-row gap-1.5 items-center">
                <Text className="text-sm">Прод. код:</Text>
                <span className="bg-primary py-0.5 px-1.5 rounded-sm text-white font-medium text-sm">{sku}</span>
              </div>
              {brand && (
                <div className="xl:hidden">
                  <ProductBrandImage brand={brand} />
                </div>
              )}
            </div>
          </div>
          {brand && (
            <div className="hidden xl:block">
              <ProductBrandImage brand={brand} />
            </div>
          )}
        </div>
      </CardHeader>
      <div className="flex-1 px-6 border-t border-gray-200 py-5">
        <ProductShortDescription>{shortDescription}</ProductShortDescription>
      </div>
      <CardFooter className="justify-between mt-3 pb-3">
        <div>
          <ProductBadges list={labels} />
        </div>
        <div className="flex">
          <ButtonIcon
            variant="outline"
            icon={wishlistSkus.includes(sku) ? <LucideHeartOff /> : <LucideHeart />}
            label={
              <Text className="leading-4">
                {wishlistSkus.includes(sku) ? 'Премахни от любими' : 'Добави в любими'}{' '}
              </Text>
            }
            labelClassName="text-black tracking-tight w-24"
            onClick={() =>
              !authenticated
                ? router.push(`/customer/account/login?onSuccessUrl=${path}`)
                : wishlistSkus.includes(sku)
                  ? removeFromWishlist(sku)
                  : addToWishlist(sku, price.price.value, price.price.currency, { fbContentId: id })
            }
          />
        </div>
      </CardFooter>
    </Card>
  )
}
