import React from 'react'

import { ButtonIcon, ButtonIconProps } from '@components/molecules/ButtonIcon'
import TriangleDown from '@icons/triangle-down.inline.svg'
import TriangleLeft from '@icons/triangle-left.inline.svg'
import TriangleRight from '@icons/triangle-right.inline.svg'
import TriangleUp from '@icons/triangle-up.inline.svg'

type ButtonArrowProps = ButtonIconProps & {
  arrowDirection?: 'up' | 'down' | 'left' | 'right'
}

const ButtonArrow = React.forwardRef<HTMLButtonElement, ButtonArrowProps>(({ arrowDirection, ...props }, ref) => {
  const triangleIcon = {
    up: <TriangleUp className="w-[5px] h-[5px] text-primary fill-accent" />,
    down: <TriangleDown className="w-[5px] h-[5px] text-primary fill-accent" />,
    left: <TriangleLeft className="w-[5px] h-[5px] text-primary fill-accent" />,
    right: <TriangleRight className="w-[5px] h-[5px] text-primary fill-accent" />,
  }
  return (
    <ButtonIcon
      {...props}
      icon={triangleIcon[arrowDirection || 'right']}
      variant="outline"
      direction="horizontal"
      iconPosition="end"
      iconClassName="w-[10px] h-[10px]"
      labelClassName="tracking-normal text-sm w-fit"
      className="flex gap-3"
    />
  )
})

ButtonArrow.displayName = 'ButtonArrow'

export { ButtonArrow }
