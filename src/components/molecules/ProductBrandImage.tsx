import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import { Button } from '@components/theme/ui/button'
import { Brand } from '@lib/_generated/graphql_sdk'

interface ProductBrandImageProps {
  brand: Brand
}

export const ProductBrandImage: React.FC<ProductBrandImageProps> = ({ brand }) => {
  return (
    <div>
      {brand && (
        <AppLink href={brand.url.href} title={brand.url.title}>
          {brand.image.src && (
            <div className="h-12 w-24 relative">
              <Img
                fill
                src={brand.image.src}
                alt={brand.image.alt || brand.name}
                title={brand.image.title || brand.name}
                className="object-contain"
              />
            </div>
          )}
          {!brand.image.src && (
            <Button variant="secondary" className="rounded-none font-bold">
              {brand.name}
            </Button>
          )}
        </AppLink>
      )}
    </div>
  )
}
