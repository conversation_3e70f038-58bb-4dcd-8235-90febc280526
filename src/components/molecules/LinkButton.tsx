import React from 'react'

import AppLink from '@atoms/AppLink'
import ButtonContent from '@atoms/Button/Button.Content'
import { ButtonProps, getButtonClassesFromProps } from '@atoms/Button/CustomButton'

export interface ButtonLinkProps extends ButtonProps {
  href: string
  onClick?: (e: any) => void
}

const LinkButton: React.FC<ButtonLinkProps> = (props) => {
  const { href, unstyled, children } = props

  let classes = ''
  if (!unstyled) {
    classes = getButtonClassesFromProps(props)
  }

  return (
    <AppLink href={href} className={classes} onClick={props.onClick}>
      <ButtonContent {...props}>{children}</ButtonContent>
    </AppLink>
  )
}

export default LinkButton
