'use client'

import { CircleMinus, CirclePlus, LucideLoaderCircle } from 'lucide-react'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useDebounceCallback } from 'usehooks-ts'

import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { Input } from '@components/theme/ui/input'
import { ClassName } from '@lib/types/ClassName'
import { showErrorToast } from '@lib/utils/toaster'

export type CounterInputType = 'plus' | 'minus' | 'input'

export interface CounterProps {
  value?: number
  min?: number
  max?: number
  onChangeAction: (value: number, type: CounterInputType) => void
  className?: ClassName
  decLoading?: boolean
  incLoading?: boolean
}

let t: ReturnType<typeof setTimeout>

export const Counter = ({ value = 0, onChangeAction, className, min, max, decLoading, incLoading }: CounterProps) => {
  const [count, setCount] = useState(() => {
    let initialValue = value
    if (min !== undefined && initialValue < min) initialValue = min
    if (max !== undefined && initialValue > max) initialValue = max
    return initialValue
  })
  const inputRef = useRef<HTMLInputElement>(null)

  const onChangeHandler = useCallback(
    (value: number, type: CounterInputType) => {
      let clampedValue = value

      // Check if value exceeds max and show error toast
      if (typeof max !== 'undefined' && value > max) {
        showErrorToast({
          title: 'Недостатъчна наличност.',
          description: `Моля, изберете количеството до ${max} броя.`,
        })
      }

      if (typeof min !== 'undefined') {
        clampedValue = Math.max(min, clampedValue)
      }
      if (typeof max !== 'undefined') {
        clampedValue = Math.min(max, clampedValue)
      }
      console.log(clampedValue, ' !== ', count)
      if (clampedValue !== count) {
        setCount(clampedValue)
        onChangeAction(clampedValue, count < clampedValue ? 'plus' : 'minus')
      }
    },
    [count, min, max, onChangeAction]
  )

  useEffect(() => {
    let newValue = value
    if (min !== undefined && newValue < min) newValue = min
    if (max !== undefined && newValue > max) newValue = max
    setCount(newValue)
  }, [min, max, value])

  useEffect(() => {
    const handleBlur = () => {
      let newValue = count
      if (min && newValue < min) {
        newValue = min
      }
      if (max && newValue > max) {
        showErrorToast({
          title: 'Грешка!',
          description: `Моля, изберете количеството до ${max} броя.`,
        })
        newValue = max
      }
      if (newValue !== count) {
        setCount(newValue)
        onChangeAction(newValue, count < newValue ? 'plus' : 'minus')
      }
    }
    const currentRef = inputRef.current
    currentRef?.addEventListener('blur', handleBlur as EventListener)
    return () => {
      currentRef?.removeEventListener('blur', handleBlur as EventListener)
    }
  }, [count, min, max, onChangeAction])

  return (
    <div
      className={cn(
        'inline-flex rounded-full border border-gray-300 justify-between items-center w-fit bg-white',
        className
      )}
    >
      <Button
        variant="ghost"
        onClick={() => onChangeHandler(count - 1, 'minus')}
        size="icon"
        className="hover:bg-gray-200 hover:text-black p-1.5"
        aria-label="Decrease count"
        disabled={decLoading || incLoading || (typeof min !== 'undefined' && count <= min)}
      >
        {decLoading ? <LucideLoaderCircle className="animate-spin" /> : <CircleMinus />}
      </Button>
      <Input
        ref={inputRef}
        className="font-bold text-sm px-1 text-center ring-0 outline-0 border-none focus-visible:ring-0 w-7"
        aria-live="polite"
        aria-atomic="true"
        value={count}
        onChange={(e) => {
          onChangeHandler(Number(e.target.value), 'input')
        }}
        disabled={decLoading || incLoading}
      />
      <Button
        variant="ghost"
        onClick={() => {
          if (typeof max !== 'undefined' && count >= max) {
            showErrorToast({
              title: 'Грешка!',
              description: `Моля, изберете количеството до ${max} броя.`,
            })
            return
          }
          onChangeHandler(count + 1, 'plus')
        }}
        size="icon"
        className="hover:bg-gray-200 hover:text-black p-1.5"
        aria-label="Increase count"
        disabled={decLoading || incLoading || (typeof max !== 'undefined' && count >= max)}
      >
        {incLoading ? <LucideLoaderCircle className="animate-spin" /> : <CirclePlus />}
      </Button>
    </div>
  )
}
