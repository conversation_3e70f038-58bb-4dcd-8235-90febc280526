import { ReactNode } from 'react'

import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { ClassName } from '@lib/types/ClassName'

interface ButtonVerticalProps {
  icon: ReactNode
  title: ReactNode
  btnClassName?: ClassName
  iconClassName?: ClassName
  titleClassName?: ClassName
}

export const ButtonVertical: React.FC<ButtonVerticalProps> = ({
  icon,
  title,
  btnClassName,
  iconClassName,
  titleClassName,
}) => {
  return (
    <Button
      variant="ghost"
      size="icon"
      className={cn('flex flex-col gap-1 hover:bg-transparent group w-[70px] whitespace-normal', btnClassName)}
    >
      <div
        className={cn(
          'justify-center flex items-center bg-gray-200 rounded-full group-hover:bg-primary aspect-square',
          iconClassName
        )}
      >
        {icon}
      </div>
      <div
        className={cn(
          'group-hover:text-primary break-words tracking-normal normal-case text-center w-full block',
          titleClassName
        )}
      >
        {title}
      </div>
    </Button>
  )
}
