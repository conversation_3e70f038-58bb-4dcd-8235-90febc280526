import Image, { ImageProps } from 'next/image'
import React from 'react'

export const energyType = ['a+++', 'a++', 'a+', 'a', 'b', 'c', 'd', 'e', 'f', 'g'] as const

export type EnergyType = (typeof energyType)[number]

interface ProductEnergyTagProps extends Omit<ImageProps, 'src' | 'alt'> {
  type: EnergyType
  size?: 'sm' | 'lg'
  alt?: string
}

export const ProductEnergyTag: React.FC<ProductEnergyTagProps> = ({ type, size, ...imageProps }) => {
  return (
    <Image
      {...imageProps}
      src={`/icons/energy-labels/${type
        .replace('+', '')
        .replace(/^(OLD_|NEW_)/, '')
        .toLowerCase()}.large.inline.svg`}
      width={size === 'lg' ? 80 : 44}
      height={size === 'lg' ? 80 : 44}
      alt={imageProps.alt || type}
    />
  )
}
