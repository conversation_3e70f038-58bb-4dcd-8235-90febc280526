'use client'
import { DialogTitle } from '@radix-ui/react-dialog'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import React, { FC, useEffect, useState } from 'react'

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@components/theme/ui/accordion'
import { Button } from '@components/theme/ui/button'
import { Switch } from '@components/theme/ui/switch'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@components/theme/ui/tabs'
import { CookieGroup, GdprCookieDetails, StaticContent } from '@lib/_generated/graphql_sdk'

import { Dialog, DialogContent } from '../../theme/ui/dialog'

import { useGDPRStore, isGroupNecessary, getStoredConsent, handleConsentChoice, setNecessaryCookies } from './store'

interface GDPRProps {
  staticContent: StaticContent
}

export const GDPR: React.FC<GDPRProps> = ({ staticContent }) => {
  const {
    tab,
    isOpen,
    allowedGroups,
    setConfig,
    showTab,
    allowGroup,
    disallowGroup,
    isGroupAllowed,
    saveConsentChoice,
    allowAllCookies,
    closeModal,
  } = useGDPRStore()

  const { gdpr } = staticContent
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    // Add Releva access token to the config
    const configWithReleva = gdpr
      ? {
          ...gdpr,
          relevaAccessToken: '2bc42955-262e-43ca-aac9-8f80e973c4cf', // TODO: must come from BE
        }
      : gdpr
    setConfig(configWithReleva)

    // Initialize allowed groups
    const storedConsent = getStoredConsent()
    if (storedConsent === undefined) {
      // If no stored consent, initialize all groups as allowed
      if (configWithReleva) {
        setNecessaryCookies(configWithReleva)
        allowGroup(Object.values(configWithReleva.modal.cookieGroups))
      }
    } else {
      handleConsentChoice(storedConsent, configWithReleva)
      closeModal()
    }
  }, [allowGroup, closeModal, gdpr, setConfig])

  if (!mounted || !isOpen || !gdpr) return null

  return (
    <Dialog open={isOpen}>
      <DialogContent className="max-w-[90vw] md:max-w-2xl" disableCloseButton>
        <VisuallyHidden>
          <DialogTitle>Бисквитки</DialogTitle>
        </VisuallyHidden>
        <Tabs value={tab} onValueChange={(value) => showTab(value as any)}>
          <TabsList className="w-full mb-6">
            <TabsTrigger value="consent" className="flex-1  py-4">
              Съгласие
            </TabsTrigger>
            <TabsTrigger value="details" className="flex-1 py-4">
              Детайли
            </TabsTrigger>
          </TabsList>

          <TabsContent value="consent" className="max-h-[50vh] p-2 overflow-y-auto">
            <h4 className="text-xl font-bold mb-4">{gdpr.modal.title}</h4>
            {gdpr.modal.content && <div className="mb-6" dangerouslySetInnerHTML={{ __html: gdpr.modal.content }} />}
          </TabsContent>

          <TabsContent value="details" className="max-h-[50vh] p-2 overflow-y-auto">
            {Object.values(gdpr.modal.cookieGroups).map((group) => (
              <CookieGroupComponent
                key={group.id}
                group={group}
                isAllowed={allowedGroups.includes(group.id)}
                isNecessary={isGroupNecessary(group)}
                onAllow={() => allowGroup(group)}
                onDisallow={() => disallowGroup(group)}
              />
            ))}
          </TabsContent>
        </Tabs>

        <div className="flex justify-center gap-4 mt-6 md:flex-row flex-col">
          {tab === 'details' ? (
            <Button variant="secondary" onClick={() => saveConsentChoice()} className="uppercase">
              Приемам Избраните
            </Button>
          ) : (
            <Button variant="secondary" onClick={() => showTab('details')} className="uppercase">
              Предпочитания
            </Button>
          )}
          <Button onClick={allowAllCookies} color="primary" className="uppercase">
            Приемам Всички
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

interface CookieGroupProps {
  group: CookieGroup
  isAllowed: boolean
  isNecessary: boolean
  onAllow: () => void
  onDisallow: () => void
}

const CookieGroupComponent: FC<CookieGroupProps> = ({ group, isAllowed, isNecessary, onAllow, onDisallow }) => {
  const handleSwitchChange = (checked: boolean) => {
    if (isNecessary) return
    if (checked) onAllow()
    else onDisallow()
  }

  return (
    <div className="mb-6 pb-4 border-b border-gray-200" key={Math.random()}>
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-bold md:text-xl">{group.title}</h3>
        <Switch checked={isAllowed || isNecessary} disabled={isNecessary} onCheckedChange={handleSwitchChange} />
      </div>
      <p className="text-sm mb-2">{group.content}</p>

      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="details">
          <AccordionTrigger className="text-sm underline">Повече Информация</AccordionTrigger>
          <AccordionContent>
            <div className="pl-0 mt-4">
              {Object.entries(group.vendors.config).map(([vendorId, vendor]) => (
                <Accordion key={vendorId} type="single" collapsible className="w-full">
                  <AccordionItem value={vendorId} className="border mb-2 p-4 rounded bg-gray-50">
                    <AccordionTrigger className="flex justify-between py-2">
                      <div className="flex items-center gap-2">
                        <span>{vendor.name}</span>
                        <span className="bg-white px-2 py-1 rounded-full text-xs">{vendor.cookieCount}</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      {Object.values(vendor.cookieDetails).map(
                        (cookie) =>
                          cookie && (
                            <div key={cookie.id} className="bg-white p-4 mb-2 rounded border">
                              <div className="font-bold">{cookie.name}</div>
                              <p className="text-sm mb-2">{cookie.description}</p>
                              <hr className="my-2" />
                              <div className="flex justify-between text-sm">
                                <div>
                                  <span className="font-bold mr-1">Срок на валидност:</span>
                                  <span>{cookie.expiration}</span>
                                </div>
                                <div>
                                  <span className="font-bold mr-1">Тип:</span>
                                  <span>{cookie.type}</span>
                                </div>
                              </div>
                            </div>
                          )
                      )}
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  )
}
