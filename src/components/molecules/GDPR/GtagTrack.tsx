'use client'

import { useEffect } from 'react'

/**
 * Based on https://developers.google.com/analytics/devguides/collection/ga4/reference/events?client_type=gtag
 */

// Common property types
type BaseProperties = {
  currency?: string
  value?: number
}

// Item interface used in many GA4 events
type Item = {
  item_id?: string
  item_name?: string
  affiliation?: string
  coupon?: string
  discount?: number
  index?: number
  item_brand?: string
  item_category?: string
  item_category2?: string
  item_category3?: string
  item_category4?: string
  item_category5?: string
  item_list_id?: string
  item_list_name?: string
  item_variant?: string
  location_id?: string
  price?: number
  quantity?: number
  promotion_id?: string
  promotion_name?: string
}

// Define a discriminated union for all possible event + properties combinations
type GtagAction =
  | {
      eventName: 'add_payment_info'
      properties: BaseProperties & {
        coupon?: string
        payment_type?: string
        items: Item[]
      }
    }
  | {
      eventName: 'add_shipping_info'
      properties: BaseProperties & {
        coupon?: string
        shipping_tier?: string
        items: Item[]
      }
    }
  | {
      eventName: 'add_to_cart'
      properties: BaseProperties & {
        items: Item[]
      }
    }
  | {
      eventName: 'add_to_wishlist'
      properties: BaseProperties & {
        items: Item[]
      }
    }
  | {
      eventName: 'begin_checkout'
      properties: BaseProperties & {
        coupon?: string
        items: Item[]
      }
    }
  | {
      eventName: 'close_convert_lead'
      properties: BaseProperties
    }
  | {
      eventName: 'close_unconvert_lead'
      properties: BaseProperties & {
        unconvert_lead_reason?: string
      }
    }
  | {
      eventName: 'disqualify_lead'
      properties: BaseProperties & {
        disqualified_lead_reason?: string
      }
    }
  | {
      eventName: 'earn_virtual_currency'
      properties: {
        virtual_currency_name?: string
        value?: number
      }
    }
  | {
      eventName: 'generate_lead'
      properties: BaseProperties & {
        lead_source?: string
      }
    }
  | {
      eventName: 'join_group'
      properties: {
        group_id?: string
      }
    }
  | {
      eventName: 'level_end'
      properties: {
        level_name?: string
        success?: boolean
      }
    }
  | {
      eventName: 'level_start'
      properties: {
        level_name?: string
      }
    }
  | {
      eventName: 'level_up'
      properties: {
        level?: number
        character?: string
      }
    }
  | {
      eventName: 'login'
      properties: {
        method?: string
      }
    }
  | {
      eventName: 'post_score'
      properties: {
        score: number
        level?: number
        character?: string
      }
    }
  | {
      eventName: 'purchase'
      properties: BaseProperties & {
        transaction_id: string
        coupon?: string
        shipping?: number
        tax?: number
        items: Item[]
      }
    }
  | {
      eventName: 'qualify_lead'
      properties: BaseProperties
    }
  | {
      eventName: 'refund'
      properties: BaseProperties & {
        transaction_id: string
        coupon?: string
        shipping?: number
        tax?: number
        items?: Item[]
      }
    }
  | {
      eventName: 'remove_from_cart'
      properties: BaseProperties & {
        items: Item[]
      }
    }
  | {
      eventName: 'search'
      properties: {
        search_term: string
      }
    }
  | {
      eventName: 'select_content'
      properties: {
        content_type?: string
        content_id?: string
      }
    }
  | {
      eventName: 'select_item'
      properties: {
        item_list_id?: string
        item_list_name?: string
        items: Item[]
      }
    }
  | {
      eventName: 'select_promotion'
      properties: {
        creative_name?: string
        creative_slot?: string
        promotion_id?: string
        promotion_name?: string
        items?: Item[]
      }
    }
  | {
      eventName: 'share'
      properties: {
        method?: string
        content_type?: string
        item_id?: string
      }
    }
  | {
      eventName: 'sign_up'
      properties: {
        method?: string
      }
    }
  | {
      eventName: 'spend_virtual_currency'
      properties: {
        value: number
        virtual_currency_name: string
        item_name?: string
      }
    }
  | {
      eventName: 'tutorial_begin'
      properties: Record<string, never>
    }
  | {
      eventName: 'tutorial_complete'
      properties: Record<string, never>
    }
  | {
      eventName: 'unlock_achievement'
      properties: {
        achievement_id: string
      }
    }
  | {
      eventName: 'view_cart'
      properties: BaseProperties & {
        items: Item[]
      }
    }
  | {
      eventName: 'view_item'
      properties: BaseProperties & {
        items: Item[]
      }
    }
  | {
      eventName: 'view_item_list'
      properties: BaseProperties & {
        item_list_id?: string
        item_list_name?: string
        items: Item[]
      }
    }
  | {
      eventName: 'view_promotion'
      properties: {
        creative_name?: string
        creative_slot?: string
        promotion_id?: string
        promotion_name?: string
        items: Item[]
      }
    }
  | {
      eventName: 'working_lead'
      properties: BaseProperties & {
        lead_status?: string
      }
    }

// Component props
type GtagTrackProps = {
  action: GtagAction
}

// Declare the gtag function to handle TypeScript errors
declare global {
  interface Window {
    gtag: (command: string, action: string, params?: any) => void
  }
}

export const gtagTrack = (action: GtagAction): void => {
  if (typeof window !== 'undefined' && window.gtag) {
    const { eventName, properties } = action
    console.log('[GT Track]', eventName, properties)
    window.gtag('event', eventName, properties)
  } else {
    console.warn('Google Analytics (gtag) is not available')
  }
}

export default function GtagTrack({ action }: GtagTrackProps) {
  useEffect(() => gtagTrack(action), [action])

  return null
}
