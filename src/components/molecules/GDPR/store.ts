import UniversalCookie from 'universal-cookie'
import { create } from 'zustand'
import { CookieGroup, CookieVendorKey } from '@/src/lib/_generated/graphql_sdk'
import fbqCore from '@lib/fbp/fbqCore'

import { GDPRConfig, Grants, StoredConsentData } from './types'

const Cookies = new UniversalCookie()

export const COOKIE_CLARITY = '_cookie_clarity'
export const COOKIE_GTAG = '_cookie_consent_gtag'
export const COOKIE_FB_PIXEL = '_cookie_consent_fb'
export const COOKIE_RELEVA = '_cookie_consent_releva'

export const EXPIRATION_DAYS = 365

export const GOOGLE_VENDOR = 'google'
export const FACEBOOK_VENDOR = 'facebook'
export const CLARITY_VENDOR = 'clarity'
export const RELEVA_VENDOR = 'releva'
export const STORE_VENDOR = 'store' as CookieVendor<PERSON>ey

interface GDPRState {
  tab: 'consent' | 'details'
  isOpen: boolean
  allowedGroups: string[]
  config: GDPRConfig | null
  setConfig: (config: any) => void
  showTab: (tab: 'consent' | 'details') => void
  allowGroup: (group: CookieGroup | CookieGroup[]) => void
  disallowGroup: (group: CookieGroup) => void
  isGroupAllowed: (groupId: string) => boolean
  saveConsentChoice: () => void
  allowAllCookies: () => void
  closeModal: () => void
}

export const useGDPRStore = create<GDPRState>((set, get) => ({
  tab: 'consent',
  isOpen: true,
  allowedGroups: [],
  config: null,

  setConfig: (config) => set({ config }),

  showTab: (tab) => set({ tab }),

  allowGroup: (group) =>
    set((state) => {
      const newGroupIds = Array.isArray(group) ? group.map((g) => g.id) : [group.id]

      const uniqueNewIds = newGroupIds.filter((id) => !state.allowedGroups.includes(id))
      if (uniqueNewIds.length === 0) return state
      return { allowedGroups: [...state.allowedGroups, ...uniqueNewIds] }
    }),

  disallowGroup: (group) =>
    set((state) => ({
      allowedGroups: state.allowedGroups.filter((id) => id !== group.id),
    })),

  isGroupAllowed: (groupId) => {
    return get().allowedGroups.includes(groupId)
  },

  closeModal: () => set({ isOpen: false }),

  saveConsentChoice: () => {
    const { allowedGroups, config, closeModal } = get()

    if (!config) return

    const confGroups = config.modal.cookieGroups
    const allowedGrants: Grants[] = []
    let allowedFacebook = false
    let allowedClarity = false
    let allowedReleva = false

    Object.entries(confGroups).forEach(([group, groupData]: [string, CookieGroup]) => {
      if (allowedGroups.includes(group) || isGroupNecessary(groupData)) {
        groupData.vendors.keys.forEach((vendor: string) => {
          if (vendor === FACEBOOK_VENDOR) {
            allowedFacebook = true
          }

          if (vendor === GOOGLE_VENDOR) {
            groupData.grants.forEach((tag) => {
              if (!allowedGrants.includes(tag)) {
                allowedGrants.push(tag)
              }
            })
          }

          if (vendor === CLARITY_VENDOR) {
            allowedClarity = true
          }

          if (vendor === RELEVA_VENDOR) {
            allowedReleva = true
          }
        })
      }
    })

    const gtagGrants = {} as Record<string, 'granted'>
    allowedGrants.forEach((grant) => {
      gtagGrants[grant] = 'granted'
    })

    const data: StoredConsentData = {
      facebook: allowedFacebook,
      google: allowedGrants.length > 0,
      gtagGrants,
      clarity: allowedClarity,
      releva: allowedReleva,
    }

    saveConsentToCookie(data)
    handleConsentChoice(data, config)
    closeModal()
  },

  allowAllCookies: () => {
    const { config, closeModal } = get()

    if (!config) return

    const data: StoredConsentData = {
      facebook: true,
      google: true,
      gtagGrants: {
        ad_storage: 'granted',
        ad_user_data: 'granted',
        ad_personalization: 'granted',
        analytics_storage: 'granted',
      },
      clarity: true,
      releva: true,
    }

    saveConsentToCookie(data)
    handleConsentChoice(data, config)
    closeModal()
  },
}))

// Helper functions
export function isGroupNecessary(group?: CookieGroup): boolean {
  return (group && group.vendors?.keys?.includes(STORE_VENDOR)) || false
}

function saveConsentToCookie(data: StoredConsentData) {
  Cookies.remove(COOKIE_CLARITY)
  Cookies.set(COOKIE_CLARITY, data.clarity.toString(), {
    expires: new Date(Date.now() + EXPIRATION_DAYS * 24 * 60 * 60 * 1000),
  })

  Cookies.remove(COOKIE_GTAG)
  Cookies.set(COOKIE_GTAG, Object.keys(data.gtagGrants).join(','), {
    expires: new Date(Date.now() + EXPIRATION_DAYS * 24 * 60 * 60 * 1000),
  })

  Cookies.remove(COOKIE_FB_PIXEL)
  Cookies.set(COOKIE_FB_PIXEL, data.facebook.toString(), {
    expires: new Date(Date.now() + EXPIRATION_DAYS * 24 * 60 * 60 * 1000),
  })

  Cookies.remove(COOKIE_RELEVA)
  Cookies.set(COOKIE_RELEVA, data.releva.toString(), {
    expires: new Date(Date.now() + EXPIRATION_DAYS * 24 * 60 * 60 * 1000),
  })
}

export function handleConsentChoice(data: StoredConsentData, config: any) {
  if (!data || !config) return
  if (data.facebook && config.pixelId) {
    initializePixel(config.pixelId, data.facebook)
  }

  if (data.google && config.gtagId) {
    initializeGTag(config.gtagId, config.extraGTagsIds || [], data.gtagGrants)
  }

  if (data.clarity && config.clarityId) {
    initializeClarity(config.clarityId)
  }

  if (data.releva && config.relevaAccessToken) {
    initializeReleva(config.relevaAccessToken)
  }
}
// For Facebook Pixel
function initializePixel(pixelId: string, enabled: boolean) {
  fbqCore.initialize(pixelId, enabled)
}

// For Google Analytics
function initializeGTag(gtagId: string, additionalGTags: string[], grants: Record<string, string> = {}) {
  if (!gtagId || typeof window === 'undefined') return

  // Set up the dataLayer and gtag function
  // @ts-ignore
  window.dataLayer = window.dataLayer || []
  function gtag() {
    // @ts-ignore
    window.dataLayer.push(arguments)
  }
  // @ts-ignore
  window.gtag = gtag

  // Load the gtag script
  const script = document.createElement('script')
  script.async = true
  script.src = `https://www.googletagmanager.com/gtag/js?id=${gtagId}`
  document.head.appendChild(script)

  // Initialize gtag
  // @ts-ignore
  gtag('js', new Date())
  // @ts-ignore
  gtag('config', gtagId)

  // Configure additional tags
  for (const tag of additionalGTags) {
    // @ts-ignore
    gtag('config', tag)
  }

  // Apply consent settings
  if (grants && Object.keys(grants).length > 0) {
    // @ts-ignore
    gtag('consent', 'update', grants)
  }
}

function initializeClarity(clarityId: string) {
  if (!clarityId || typeof window === 'undefined')
    return // @ts-ignore
    // @ts-ignore
  ;(function (c: any, l: any, a: any, r: any, i: any, t: any, y: any) {
    c[a] =
      c[a] ||
      function () {
        ;(c[a].q = c[a].q || []).push(arguments)
      }
    t = l.createElement(r)
    t.async = 1
    t.src = 'https://www.clarity.ms/tag/' + i
    y = l.getElementsByTagName(r)[0]
    y.parentNode.insertBefore(t, y)
  })(window, document, 'clarity', 'script', clarityId)
}

function initializeReleva(accessToken: string) {
  if (!accessToken || typeof window === 'undefined') return

  // Store access token globally for relevaTrack to use
  // @ts-ignore
  window.relevaAccessToken = accessToken

  // Load Releva SDK
  const script = document.createElement('script')
  script.async = true
  script.type = 'text/javascript'
  script.src = 'https://releva.ai/sdk/v0/js/releva-sdk-js.min.js'
  script.onload = () => {
    document.dispatchEvent(new Event('relevaSdkLoaded'))
  }
  document.head.appendChild(script)
}

export function setNecessaryCookies(config: any): StoredConsentData {
  // Create a consent data object with only necessary cookies
  // and Google Analytics with minimal tracking
  const data: StoredConsentData = {
    facebook: false,
    google: true,
    gtagGrants: {
      analytics_storage: 'granted', // Allow basic analytics
      ad_storage: 'granted',
      ad_user_data: 'granted',
      ad_personalization: 'granted',
    },
    clarity: false,
    releva: false,
  }

  // Save these minimal consent settings to cookies
  saveConsentToCookie(data)

  // Initialize Google Analytics with these settings
  if (config && config.gtagId) {
    initializeGTag(config.gtagId, config.extraGTagsIds || [], data.gtagGrants)
  }

  // Initialize Releva if access token is available
  if (config && config.relevaAccessToken) {
    initializeReleva(config.relevaAccessToken)
  }

  return data
}

export function getStoredConsent(): StoredConsentData | undefined {
  if (typeof window === 'undefined') return undefined

  if (!Cookies.get(COOKIE_CLARITY) || !Cookies.get(COOKIE_GTAG) || !Cookies.get(COOKIE_FB_PIXEL) || !Cookies.get(COOKIE_RELEVA)) {
    return undefined
  }

  const initData = {
    facebook: false,
    google: false,
    gtagGrants: {} as Record<string, 'granted'>,
    clarity: false,
    releva: false,
  }

  const clarity = Cookies.get(COOKIE_CLARITY)
  if (clarity === 'true' || clarity === true) {
    initData.clarity = true
  }

  const gtagGrants = Cookies.get(COOKIE_GTAG)
  if (gtagGrants) {
    const grants = gtagGrants.split(',')
    if (grants.length > 0) {
      initData.google = true
      for (const grant of grants) {
        initData.gtagGrants[grant] = 'granted'
      }
    }
  }

  const fbPixel = Cookies.get(COOKIE_FB_PIXEL)
  if (fbPixel === 'true' || fbPixel === true) {
    initData.facebook = true
  }

  const releva = Cookies.get(COOKIE_RELEVA)
  if (releva === 'true' || releva === true) {
    initData.releva = true
  }

  return initData
}
