'use client'

import { useEffect } from 'react'
import { useCartStore } from '@/src/features/cart/cart-state'
import { useCustomerStore } from '@/src/features/customer/customer.store'
import { relevaTrackWhenReady } from './relevaTrack'

export type RelevaPageType = 'homepage' | 'category' | 'product' | 'cart' | 'search' | 'checkout_success'

// TODO must come from BE
const PAGE_TOKENS: Record<RelevaPageType, string> = {
  homepage: '5e8e78ae-d226-4a00-adb0-c78f154f256d',
  category: 'd83c9ec7-9299-4b78-9230-495909608d79',
  product: '1a9a6cc1-a4ac-42c6-b523-9e3435fcbda5',
  cart: '4b34faa8-ad14-4b88-9152-5ec7861c0be1',
  search: '7f1f24a8-9daf-4ddf-882a-b83b31e06ce2',
  checkout_success: '43d93212-e9d0-4820-b5d0-0191e1dd34db',
}

interface RelevaTrackPageProps {
  pageType?: RelevaPageType
  productIds?: string[]
  categories?: string[]
  query?: string | null
  productId?: string
  filters?: any
}

export default function RelevaTrackPage({
  pageType,
  productIds,
  categories,
  query,
  productId,
  filters,
}: RelevaTrackPageProps) {
  const cart = useCartStore()
  const customer = useCustomerStore()

  useEffect(() => {
    const context = {
      page: {
        ...(pageType && { token: PAGE_TOKENS[pageType] }),
        locale: 'bg',
        currency: 'BGN',
        ...(productIds?.length && { ids: productIds }),
        ...(categories?.length && { categories: categories.join('/') }),
        ...(query !== undefined && { query }),
        ...(filters && {
          filter: Array.isArray(filters.nested)
            ? {
                ...filters,
                nested: filters.nested.flatMap((f: any) => {
                  if (f.key === 'price' && f.value.includes('-')) {
                    const [min, max] = f.value.split('-')
                    return [
                      { ...f, operator: 'lte', value: min },
                      { ...f, operator: 'gte', value: max },
                    ]
                  }
                  return f
                }),
              }
            : filters,
        }),
      },
      cart: {
        products: cart.items.map((item) => ({
          id: item.product.id,
          price: parseFloat(item.price?.value?.toString() || '0'),
          currency: item.price?.currency || 'BGN',
          quantity: item.baseQty,
          custom: { string: [] },
        })),
      },
      ...(customer.authenticated &&
        customer.customer && {
          profile: {
            id: customer.id?.toString() || '',
            firstName: customer.customer.firstName || '',
            lastName: customer.customer.lastName || '',
            email: customer.customer.email || '',
            registeredAt: new Date().toISOString(),
          },
        }),
      ...(customer.wishlistSkus.length > 0 && {
        wishlist: {
          products: customer.wishlistSkus.map((sku) => ({ id: sku })),
        },
      }),
      ...(productId && { product: { id: productId } }),
    }

    console.log({ context })
    relevaTrackWhenReady(context)
  }, [
    pageType,
    productIds,
    categories,
    query,
    productId,
    filters,
    cart.items,
    customer.authenticated,
    customer.customer,
    customer.wishlistSkus,
    customer.id,
  ])

  return null
}
