'use client'

import { WindArrowDownIcon } from 'lucide-react'
import { getStoredConsent } from './store'
import { ClientInput } from '@/src/lib/_generated/graphql_sdk'
import { CartState } from '@/src/features/cart/cart-state'
import { ICustomerStore } from '@/src/features/customer/customer.types'

// Declare global Releva types
declare global {
  interface Window {
    Releva: {
      push: (
        accessToken: string,
        context: any,
        successCallback?: (results: any) => void,
        errorCallback?: (error: any) => void,
        options?: { hasConsent?: () => boolean }
      ) => void
      getCookie: (name: string) => string | null
    }
    relevaAccessToken?: string
  }
}

/**
 * Check if user has granted consent for Releva tracking
 */
export function hasRelevaConsent(): boolean {
  const storedConsent = getStoredConsent()
  return storedConsent?.releva || false
}

/**
 * Check if Releva SDK is available
 */
export function isRelevaAvailable(): boolean {
  return typeof window !== 'undefined' && !!window.Releva && !!window.relevaAccessToken
}

/**
 * Track data with Releva
 * @param context - The context object to send to Releva
 */
export function relevaTrack(context: any): void {
  console.log("Releva track", window.Releva, window.relevaAccessToken)
  if (!isRelevaAvailable()) {
    console.warn('Releva SDK is not available')
    return
  }

  if (!hasRelevaConsent()) {
    console.log('Releva tracking blocked - no consent')
    return
  }

  const accessToken = window.relevaAccessToken!
  
  console.log('Releva tracking:', context)
  
  window.Releva.push(
    accessToken,
    context,
    (results) => {
      console.log('Releva success:', results)
    },
    (error) => {
      console.error('Releva error:', error)
    },
    {
      hasConsent: hasRelevaConsent
    }
  )
}

/**
 * Wait for Releva SDK to load and then track
 * @param context - The context object to send to Releva
 */
export function relevaTrackWhenReady(context: any): void {
  console.log("Releva track when ready", window.Releva, window.relevaAccessToken)
  if (isRelevaAvailable()) {
    relevaTrack(context)
  } else {
    document.addEventListener('relevaSdkLoaded', () => {
      relevaTrack(context)
    }, { once: true })
  }
}

/**
 * Track order completion with Releva
 * @param orderId - The order ID
 * @param cart - Cart store instance
 * @param customer - Customer store instance  
 */
export function relevaTrackOrder(orderId: string, cart: CartState, customer: ICustomerStore): void {
  if (!cart.ready || !cart.items.length) return

  // Build cart data - for checkout success, this represents the ordered products
  const orderedProducts = cart.items.map((item: any) => ({
    id: item.product.id,
    price: parseFloat(item.price?.value?.toString() || '0'),
    currency: item.price?.currency || 'BGN',
    quantity: item.baseQty,
    custom: {
      string: []
    }
  }))

  // Build profile data - required even for guest checkout
  let profile: any = undefined
  
  if (customer.authenticated && customer.customer) {
    // Authenticated user
    profile = {
      id: customer.id?.toString() || '',
      firstName: customer.customer.firstName || '',
      lastName: customer.customer.lastName || '',
      email: customer.customer.email || '',
      registeredAt: new Date().toISOString(),
    }
  } else if (cart.personalInfo?.email) {
    // Guest checkout - use data from cart
    profile = {
      firstName: cart.personalInfo.firstName || '',
      lastName: cart.personalInfo.lastName || '',
      email: cart.personalInfo.email,
      ...(cart.personalInfo.phone && {
        phoneNumber: cart.personalInfo.phone.startsWith('+') 
          ? cart.personalInfo.phone 
          : `+359${cart.personalInfo.phone.replace(/^0+/, '')}`
      })
    }
  }

  // Build context for checkout success
  const context: any = {
    page: {
      token: "43d93212-e9d0-4820-b5d0-0191e1dd34db",
      locale: 'bg',
      currency: 'BGN'
    },
    cart: {
      products: orderedProducts
    }
  }

  // Add profile if available
  if (profile) {
    context.profile = profile
  }

  console.log('Releva order tracking:', { orderId, context })

  // Track with Releva
  relevaTrackWhenReady(context)
}