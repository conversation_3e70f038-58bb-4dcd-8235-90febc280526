import { CookieGroup } from '@lib/_generated/graphql_sdk'

export type Grants = 'ad_storage' | 'ad_user_data' | 'ad_personalization' | 'analytics_storage'

export interface ModalConfig {
  title: string
  content: string
  cookieGroups: CookieGroup[]
}

export interface GDPRConfig {
  rootId?: string
  modal: ModalConfig
  pixelId?: string
  gtagId?: string
  extraGTagsIds?: string[]
  clarityId?: string
  relevaAccessToken?: string
}

export interface StoredConsentData {
  facebook: boolean
  google: boolean
  gtagGrants: Record<Grants, 'granted'>
  clarity: boolean
  releva: boolean
}

export type AvailableTab = 'consent' | 'details' | 'about'
