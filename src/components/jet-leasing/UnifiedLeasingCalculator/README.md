# Unified Leasing Calculator

A unified dialog component that combines both TBI Bank and BNP Paribas leasing calculators into a single modal with tabs.

## Overview

The Unified Leasing Calculator provides a streamlined user experience by consolidating both leasing options into one interface. Users can easily switch between TBI Bank and BNP Paribas calculators using tabs, while maintaining all existing functionality from both original calculators.

## Components

### 1. `UnifiedLeasingCalculator`
**Location**: `src/components/jet-leasing/UnifiedLeasingCalculator/UnifiedLeasingCalculator.tsx`

The main component that renders the dialog with tabbed interface.

**Props:**
- `type: 'primary' | 'secondary'` - Visual styling variant
- `product: SimpleProductViewFragment` - Product data
- `quantity?: number` - Product quantity (default: 1)

### 2. `TBICalculatorTab`
**Location**: `src/components/jet-leasing/UnifiedLeasingCalculator/TBICalculatorTab.tsx`

Encapsulates TBI Bank calculator logic and state management.

**Features:**
- Loads TBI credit data via GraphQL API
- Displays TBIBankPayment component when data is available
- Handles loading and error states
- Lazy loading - only fetches data when tab is active

### 3. `BNPCalculatorTab`
**Location**: `src/components/jet-leasing/UnifiedLeasingCalculator/BNPCalculatorTab.tsx`

Encapsulates BNP Paribas calculator logic and state management.

**Features:**
- Loads BNP variant groups via GraphQL API
- Displays BNPPaymentDisplay component when data is available
- Handles loading and error states
- Lazy loading - only fetches data when tab is active

## Usage

### Basic Usage
```tsx
import { UnifiedLeasingCalculator } from '@components/jet-leasing/UnifiedLeasingCalculator'

function ProductCard({ product, quantity, highlighted }) {
  return (
    <div>
      {/* Other product card content */}
      <UnifiedLeasingCalculator 
        product={product} 
        quantity={quantity} 
        type={highlighted ? 'secondary' : 'primary'} 
      />
    </div>
  )
}
```

### Integration in SimpleBuyProductCard
The component replaces the previous separate leasing calculator buttons:

```tsx
// Before (two separate buttons):
<LeasingCalculator quantity={count} product={product} type={type} />
<BNPLeasingCalculator quantity={count} product={product} type={type} />

// After (single unified button):
<UnifiedLeasingCalculator quantity={count} product={product} type={type} />
```

## Features

### Preserved Functionality
- ✅ All existing TBI Bank calculator functionality
- ✅ All existing BNP Paribas calculator functionality
- ✅ Down payment inputs with Bulgarian currency formatting
- ✅ 500ms debouncing for API calls (where applicable)
- ✅ Real-time recalculation with loading states
- ✅ GraphQL placeholder comments maintained
- ✅ TypeScript with proper type definitions

### New Features
- ✅ Tabbed interface for easy switching between calculators
- ✅ Lazy loading - calculators only load data when their tab is active
- ✅ Unified styling and responsive design
- ✅ **Mobile-friendly tabs** (vertical on mobile, horizontal on sm+ screens)
- ✅ Single "Buy on leasing" button instead of two separate buttons
- ✅ Consistent error handling across both calculators

### Mobile Responsiveness
- **Mobile (< sm)**: Tabs are stacked vertically with smaller logos and text
- **Small screens (sm+)**: Tabs are arranged horizontally side by side
- **Responsive logos**: Logos scale appropriately for each screen size
- **Responsive text**: Text size adjusts from xs to sm based on screen size
- **Touch-friendly**: Adequate padding and touch targets for mobile devices

### Technical Details
- Uses Radix UI Tabs for accessibility and keyboard navigation
- Maintains separate state for each calculator tab
- Preserves existing API integration patterns
- Follows established component patterns from jet-leasing directory
- Responsive design with proper mobile support
- Grid layout: `grid-cols-1 sm:grid-cols-2` for mobile-first approach

## API Integration

### TBI Bank API
- **Endpoint**: `GraphQLBackend.GetTBICredit({ sku })`
- **Returns**: `TbiCreditDataFragment`
- **Used by**: `TBICalculatorTab`

### BNP Paribas API
- **Endpoint**: `GraphQLBackend.GetCreditCalculatorBNPParibas({ sku, downPayment, qty })`
- **Returns**: `BnpVariantGroupFragment[]`
- **Used by**: `BNPCalculatorTab`

## File Structure

```
src/components/jet-leasing/UnifiedLeasingCalculator/
├── UnifiedLeasingCalculator.tsx    # Main component with tabs
├── TBICalculatorTab.tsx            # TBI Bank calculator logic
├── BNPCalculatorTab.tsx            # BNP Paribas calculator logic
├── types.ts                        # TypeScript type definitions
├── index.ts                        # Export declarations
└── README.md                       # This documentation
```

## Migration Notes

When migrating from separate calculators to the unified component:

1. Replace both `LeasingCalculator` and `BNPLeasingCalculator` imports with `UnifiedLeasingCalculator`
2. Remove the two separate calculator components
3. Add the single unified component with the same props
4. The minimum amount check (`totalAmount > 100`) should be maintained at the parent level

## Accessibility

- Keyboard navigation support via Radix UI Tabs
- Proper ARIA labels and roles
- Screen reader friendly
- Focus management between tabs
- Touch-friendly interface for mobile devices

## Responsive Design Classes

```css
/* TabsList */
grid-cols-1 sm:grid-cols-2    /* Vertical on mobile, horizontal on sm+ */
gap-1 sm:gap-0                /* Small gap on mobile, no gap on sm+ */

/* TabsTrigger */
py-3 px-3 sm:px-4            /* Responsive padding */
text-xs sm:text-sm           /* Responsive text size */

/* Logos */
w-5 h-3 sm:w-6 sm:h-4        /* Responsive logo container */
width={50} sm:w-[60px]       /* Responsive logo dimensions */
```
