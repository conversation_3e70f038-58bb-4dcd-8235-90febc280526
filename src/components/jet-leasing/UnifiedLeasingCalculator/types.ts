import { SimpleProductViewFragment, TbiCreditDataFragment, BnpVariantGroupFragment } from '@lib/_generated/graphql_sdk'

export interface UnifiedLeasingCalculatorProps {
  type: 'primary' | 'secondary'
  product: SimpleProductViewFragment
  quantity?: number
}

export interface TBICalculatorTabProps {
  product: SimpleProductViewFragment
  quantity: number
}

export interface BNPCalculatorTabProps {
  product: SimpleProductViewFragment
  quantity: number
}

export interface TBICalculatorState {
  creditData?: TbiCreditDataFragment
  loading: boolean
  error: boolean
}

export interface BNPCalculatorState {
  variantGroups?: BnpVariantGroupFragment[]
  loading: boolean
  error: boolean
}
