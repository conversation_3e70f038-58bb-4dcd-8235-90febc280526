'use client'

import React from 'react'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@components/theme/ui/tabs'
import * as Dialog from '@/src/components/theme/ui/dialog'
import TBIBankLogo from '@images/tbi-bank-logo.inline.svg'
import Img from '@atoms/Img'
import pariba from '@images/pariba.png'
import { TBICalculatorTab } from './TBICalculatorTab'
import { BNPCalculatorTab } from './BNPCalculatorTab'
import { UnifiedLeasingCalculatorProps } from './types'

export const UnifiedLeasingCalculator: React.FC<UnifiedLeasingCalculatorProps> = ({ type, product, quantity = 1 }) => {
  return (
    <Dialog.Dialog>
      <Dialog.DialogTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'bg-transparent text-xs w-full py-2 tracking-tighter lg:tracking-widest',
            type === 'primary'
              ? 'text-black border-black hover:bg-black hover:text-white hover:border-white'
              : 'text-white hover:bg-white hover:text-primary hover:border-primary'
          )}
        >
          <Text>Купи на лизинг</Text>
        </Button>
      </Dialog.DialogTrigger>

      <Dialog.DialogContent className="rounded-lg max-w-[95vw] sm:max-w-4xl max-h-[90vh] bg-white overflow-hidden">
        <Dialog.DialogHeader className="pb-2">
          <Dialog.DialogTitle className="font-bold text-lg">Вземи на лизинг</Dialog.DialogTitle>
        </Dialog.DialogHeader>

        <div className="overflow-hidden">
          <Tabs defaultValue="tbi" className="w-full">
            <TabsList className="grid w-full grid-cols-1 sm:grid-cols-2 mb-4 h-auto gap-1 sm:gap-0">
              <TabsTrigger
                value="tbi"
                className="flex items-center justify-start gap-2 py-3 px-3 sm:px-4 h-auto text-left"
              >
                <TBIBankLogo width={50} height={12} className="sm:w-[60px] sm:h-[15px]" />
                <span className="text-xs sm:text-sm">TBI Лизингов калкулатор</span>
              </TabsTrigger>
              <TabsTrigger
                value="bnp"
                className="flex items-center justify-start gap-2 py-3 px-3 sm:px-4 h-auto text-left"
              >
                <div className="inline-flex items-center justify-center w-5 h-3 sm:w-6 sm:h-4 bg-blue-600 text-white rounded text-xs font-bold">
                  <Img src={pariba} alt="BNP Paribas" width={50} height={10} className="sm:w-[60px] sm:h-[12px]" />
                </div>
                <span className="text-xs sm:text-sm">ПБ Лични Финаси</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="tbi" className="mt-0">
              <div className="max-h-[70vh] overflow-y-auto">
                <TBICalculatorTab product={product} quantity={quantity} />
              </div>
            </TabsContent>

            <TabsContent value="bnp" className="mt-0">
              <div className="max-h-[70vh] overflow-y-auto">
                <BNPCalculatorTab product={product} quantity={quantity} />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </Dialog.DialogContent>
    </Dialog.Dialog>
  )
}
