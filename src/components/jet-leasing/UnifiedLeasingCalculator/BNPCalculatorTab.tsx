'use client'

import React, { useCallback, useEffect } from 'react'
import Text from '@atoms/Text'
import { GraphQLBackend } from '@lib/api/graphql'
import { LucideCircleX, LucideLoaderCircle } from 'lucide-react'
import { Callout } from '@components/molecules/Callout'
import { BNPPaymentDisplay } from '@components/molecules/ProductBuyCards/components/BNPPaymentDisplay'
import { BNPCalculatorTabProps, BNPCalculatorState } from './types'

export const BNPCalculatorTab: React.FC<BNPCalculatorTabProps> = ({ product, quantity }) => {
  const [state, setState] = React.useState<BNPCalculatorState>({
    loading: false,
    error: false
  })

  const { sku } = product
  const productPrice = product.price.special?.value || product.price.price.value
  const totalPrice = productPrice * quantity

  const loadBNPData = useCallback(async () => {
    setState(prev => ({ ...prev, error: false, loading: true, variantGroups: [] }))

    try {
      const response = await GraphQLBackend.GetCreditCalculatorBNPParibas({
        sku: sku,
        downPayment: 0, // Default to 0 down payment for initial calculation
        qty: quantity, // This ensures the API gets the correct quantity
      })

      if (response.getCreditCalculatorBNPParibas) {
        setState(prev => ({
          ...prev,
          variantGroups: response.getCreditCalculatorBNPParibas,
          loading: false
        }))
      } else {
        setState(prev => ({ ...prev, error: true, loading: false }))
      }
    } catch (error) {
      console.error('BNP Calculator error:', error)
      setState(prev => ({ ...prev, error: true, loading: false, variantGroups: undefined }))
    }
  }, [sku, quantity])

  // Load data when tab becomes active (only once)
  useEffect(() => {
    if (!state.variantGroups && !state.loading && !state.error) {
      loadBNPData()
    }
  }, [loadBNPData, state.variantGroups, state.loading, state.error])

  if (state.loading) {
    return (
      <Callout
        variant="warning"
        title="Моля, изчакайте..."
        icon={<LucideLoaderCircle className="animate-spin" />}
        className="mb-0"
      >
        <Text>Зареждане на информация за лизинг...</Text>
      </Callout>
    )
  }

  if (state.error) {
    return (
      <Callout
        variant="error"
        title="Грешка при зареждане на информация"
        icon={<LucideCircleX />}
        className="mb-0"
      >
        <Text>В момента нямаме информация за лизинг на този продукт. Моля, опитайте по-късно.</Text>
      </Callout>
    )
  }

  if (state.variantGroups) {
    return (
      <BNPPaymentDisplay
        product={product}
        quantity={quantity}
        variantGroups={state.variantGroups}
        totalPrice={totalPrice}
      />
    )
  }

  return null
}
