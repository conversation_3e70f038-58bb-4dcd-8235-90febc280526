'use client'

import React, { useCallback, useEffect } from 'react'
import Text from '@atoms/Text'
import { GraphQLBackend } from '@lib/api/graphql'
import { TBIBankPayment } from '@/src/app/showcase/TBIPayment'
import { LucideCircleX, LucideLoaderCircle } from 'lucide-react'
import { Callout } from '@components/molecules/Callout'
import { TBICalculatorTabProps, TBICalculatorState } from './types'

export const TBICalculatorTab: React.FC<TBICalculatorTabProps> = ({ product, quantity }) => {
  const [state, setState] = React.useState<TBICalculatorState>({
    loading: false,
    error: false
  })

  const { sku } = product

  const loadTBIData = useCallback(async () => {
    setState(prev => ({ ...prev, error: false, loading: true }))

    try {
      const response = await GraphQLBackend.GetTBICredit({ sku })
      if (response.getCreditCalculatorTBIBank) {
        setState(prev => ({
          ...prev,
          creditData: response.getCreditCalculatorTBIBank,
          loading: false
        }))
      } else {
        setState(prev => ({ ...prev, error: true, loading: false }))
      }
    } catch (error) {
      console.error('TBI Calculator error:', error)
      setState(prev => ({ ...prev, error: true, loading: false, creditData: undefined }))
    }
  }, [sku])

  // Load data when tab becomes active (only once)
  useEffect(() => {
    if (!state.creditData && !state.loading && !state.error) {
      loadTBIData()
    }
  }, [loadTBIData, state.creditData, state.loading, state.error])

  if (state.loading) {
    return (
      <Callout
        variant="warning"
        title="Моля, изчакайте..."
        icon={<LucideLoaderCircle className="animate-spin" />}
        className="mb-0"
      >
        <Text>Зареждане на информация...</Text>
      </Callout>
    )
  }

  if (state.error) {
    return (
      <Callout 
        variant="error" 
        title="Грешка при зареждане на информация" 
        icon={<LucideCircleX />} 
        className="mb-0"
      >
        <Text>В момента нямаме информация за този продукт. Моля, опитайте по-късно.</Text>
      </Callout>
    )
  }

  if (state.creditData) {
    return (
      <TBIBankPayment 
        product={product} 
        quantity={quantity} 
        creditData={state.creditData} 
      />
    )
  }

  return null
}
