# Payment Calculator Component

A React component that dynamically renders an interactive payment calculator based on JSON data from the `stenik_leasingjetcredit` API.

## Overview

The Payment Calculator component provides a user-friendly interface for customers to:
- View different payment schemes and installment options
- Select their preferred payment variant
- See real-time calculations and payment schedules
- Input downpayment amounts (when enabled)
- Preview total costs and monthly payments

## Components

### 1. `RenderCalculator`
**Location**: `src/features/checkout/components/forms/payment/RenderCaltulator.tsx`

The core component that renders the calculator interface based on JSON data.

**Props:**
- `json?: CalculatorApiResponse` - Calculator data from API
- `onVariantSelect?: (variantId: string) => void` - Callback when variant is selected
- `className?: string` - Additional CSS classes

**Features:**
- ✅ Interactive payment variant selection with radio buttons
- ✅ Real-time calculation display with formatted prices
- ✅ Payment schedule preview showing monthly installments
- ✅ Responsive design that works on mobile and desktop
- ✅ TypeScript support with full type definitions
- ✅ Error handling for malformed or missing data
- ✅ Loading states and user feedback
- ✅ Hidden form inputs for seamless form submission
- ✅ Downpayment input field (when enabled by configuration)
- ✅ Promotional variant highlighting
- ✅ APR and interest rate display

### 2. `PaymentCalculator`
**Location**: `src/components/jet-leasing/PaymentCalculator/PaymentCalculator.tsx`

A wrapper component that handles API calls and loading states.

**Props:**
- `quoteId?: string` - Quote ID for API call
- `productId?: string` - Product ID for calculation
- `quantity?: number` - Product quantity (default: 1)
- `onCalculatorLoad?: (data: any) => void` - Callback when data loads
- `onVariantSelect?: (variantId: string) => void` - Callback when variant is selected
- `className?: string` - Additional CSS classes

**Features:**
- ✅ Automatic API data fetching
- ✅ Loading and error state management
- ✅ Retry functionality on errors
- ✅ Configurable API endpoints

## JSON Structure

The component expects JSON data in the following format:

```typescript
interface CalculatorApiResponse {
  success: boolean
  calculator: {
    principal: number                    // Total amount
    downpayment: number                 // Initial downpayment
    formatted_principal: string         // HTML formatted price
    formatted_downpayment: string       // HTML formatted downpayment
    configuration: {
      quote_id: string
      hide_down_payment: boolean        // Whether to show downpayment input
      input_prefix: string              // Form input name prefix
      input_suffix: string              // Form input name suffix
      choose_input_name: string         // Variant selection input name
      downpayment_input_name: string    // Downpayment input name
      // ... other config options
    }
    variants: Array<{
      id: string                        // Unique variant ID
      installment_amount: string        // Monthly payment amount
      maturity: string                  // Payment term in months
      apr: string                       // Annual percentage rate
      total_repayment_amount: string    // Total amount to be paid
      pricing_scheme_name: string       // Display name
      is_promo: boolean                 // Whether it's a promotional offer
    }>
    variants_by_scheme: Array<{
      scheme_id: string
      scheme_name: string
      variants: CalculatorVariant[]
    }>
    recalc_url: string                  // API endpoint for recalculation
    has_variants: boolean
  }
  meta: {
    timestamp: number
    currency_code: string
    store_id: string
  }
}
```

## Usage Examples

### Basic Usage with Default Data
```tsx
import { RenderCalculator } from '@/src/features/checkout/components/forms/payment/RenderCaltulator'

function MyComponent() {
  return (
    <RenderCalculator
      onVariantSelect={(variantId) => {
        console.log('Selected variant:', variantId)
      }}
    />
  )
}
```

### API-based Usage
```tsx
import PaymentCalculator from '@components/jet-leasing/PaymentCalculator'

function CheckoutComponent() {
  return (
    <PaymentCalculator
      quoteId="1144929"
      productId="example-product"
      quantity={1}
      onCalculatorLoad={(data) => {
        console.log('Calculator loaded:', data)
      }}
      onVariantSelect={(variantId) => {
        // Store selected variant for form submission
        setSelectedPaymentVariant(variantId)
      }}
    />
  )
}
```

### Integration in Payment Form
```tsx
// In PaymentData.tsx
{paymentMethod === 'stenik_leasingjetcredit' && (
  <div className="col-span-2">
    <RenderCalculator 
      json={calculatorData} 
      onVariantSelect={(variantId) => {
        // Handle variant selection
        setSelectedPaymentVariant(variantId)
      }}
      className="mt-4"
    />
  </div>
)}
```

## Form Integration

The calculator automatically generates hidden form inputs for seamless integration:

- `payment[variant_id]` - Selected payment variant ID
- `payment[downpayment]` - Downpayment amount (if enabled)

These inputs are automatically included in form submissions.

## Styling

The component uses Tailwind CSS classes and follows the existing design patterns:

- **Colors**: Orange (`text-orange-600`) for primary elements
- **Layout**: Responsive grid system
- **Typography**: Consistent with existing components
- **Interactive elements**: Hover states and focus indicators

## API Integration

### Current Implementation
The component currently fetches data from:
```
https://praktis2-demo.pfgbulgaria.com/stenik_leasingjetcredit/calculator/getCalculatorHtml/quote_id/{quoteId}/
```

### Future GraphQL Integration
TODO: Replace with GraphQL query:
```graphql
query GetLeasingCalculator($quoteId: String!, $productId: String, $quantity: Int) {
  getLeasingCalculator(quoteId: $quoteId, productId: $productId, quantity: $quantity) {
    success
    calculator {
      principal
      downpayment
      # ... other fields
    }
  }
}
```

## Error Handling

The component handles various error scenarios:

1. **API Errors**: Shows error message with retry option
2. **Invalid JSON**: Graceful fallback with error display
3. **Missing Data**: Default values and empty states
4. **Network Issues**: Loading states and timeout handling

## Accessibility

- ✅ Proper ARIA labels for form controls
- ✅ Keyboard navigation support
- ✅ Screen reader compatible
- ✅ High contrast color scheme
- ✅ Focus indicators

## Browser Support

- ✅ Modern browsers (Chrome, Firefox, Safari, Edge)
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)
- ✅ Responsive design for all screen sizes

## Testing

To test the component:

1. **Unit Tests**: Test individual component functions
2. **Integration Tests**: Test API integration and form submission
3. **Visual Tests**: Test responsive design and styling
4. **Accessibility Tests**: Test keyboard navigation and screen readers

## Future Enhancements

- [ ] Add animation transitions for better UX
- [ ] Implement calculator comparison mode
- [ ] Add export functionality for payment schedules
- [ ] Integrate with analytics for tracking user selections
- [ ] Add multi-language support
- [ ] Implement advanced filtering options
