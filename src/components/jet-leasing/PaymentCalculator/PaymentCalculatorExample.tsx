'use client'
import React, { useState } from 'react'
import PaymentCalculator from './PaymentCalculator'
import { RenderCalculator } from '@/src/features/checkout/components/forms/payment/RenderCaltulator'

/**
 * Example component demonstrating how to use the PaymentCalculator
 * This shows both the standalone calculator and the direct JSON renderer
 */
export const PaymentCalculatorExample: React.FC = () => {
  const [selectedVariant, setSelectedVariant] = useState<string>('')
  const [calculatorData, setCalculatorData] = useState<any>(null)

  const handleVariantSelect = (variantId: string) => {
    setSelectedVariant(variantId)
    console.log('Selected variant:', variantId)
  }

  const handleCalculatorLoad = (data: any) => {
    setCalculatorData(data)
    console.log('Calculator loaded:', data)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Payment Calculator Examples
        </h1>
        <p className="text-gray-600 mb-8">
          Demonstration of the payment calculator components for the stenik_leasingjetcredit API.
        </p>
      </div>

      {/* Example 1: Direct JSON Renderer */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          1. Direct JSON Renderer (with default data)
        </h2>
        <p className="text-gray-600 mb-4">
          This renders the calculator directly with the provided JSON data:
        </p>
        <RenderCalculator
          onVariantSelect={handleVariantSelect}
          className="mb-4"
        />
        {selectedVariant && (
          <div className="p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Selected Variant ID:</strong> {selectedVariant}
            </p>
          </div>
        )}
      </div>

      {/* Example 2: API-based Calculator */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          2. API-based Calculator (with loading states)
        </h2>
        <p className="text-gray-600 mb-4">
          This fetches calculator data from the API and handles loading/error states:
        </p>
        <PaymentCalculator
          quoteId="1144929"
          productId="example-product"
          quantity={1}
          onCalculatorLoad={handleCalculatorLoad}
          onVariantSelect={handleVariantSelect}
          className="mb-4"
        />
      </div>

      {/* Example 3: Integration Guide */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          3. Integration Guide
        </h2>
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="font-semibold mb-3">How to integrate into checkout:</h3>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium text-gray-900">Step 1: Import the component</h4>
              <pre className="bg-gray-800 text-green-400 p-3 rounded mt-2 overflow-x-auto">
{`import PaymentCalculator from '@components/jet-leasing/PaymentCalculator'`}
              </pre>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900">Step 2: Use in PaymentData component</h4>
              <pre className="bg-gray-800 text-green-400 p-3 rounded mt-2 overflow-x-auto">
{`{paymentMethod === 'stenik_leasingjetcredit' && (
  <PaymentCalculator
    quoteId={cartData?.quote_id}
    productId={product?.id}
    quantity={quantity}
    onVariantSelect={(variantId) => {
      // Handle variant selection
      setSelectedPaymentVariant(variantId)
    }}
  />
)}`}
              </pre>
            </div>

            <div>
              <h4 className="font-medium text-gray-900">Step 3: Handle form submission</h4>
              <pre className="bg-gray-800 text-green-400 p-3 rounded mt-2 overflow-x-auto">
{`// The calculator automatically adds hidden inputs:
// payment[variant_id] = selected variant ID
// payment[downpayment] = downpayment amount (if enabled)`}
              </pre>
            </div>
          </div>
        </div>
      </div>

      {/* Example 4: Features Overview */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          4. Features Overview
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-2">✅ Implemented Features</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Interactive payment variant selection</li>
              <li>• Real-time calculation display</li>
              <li>• Payment schedule preview</li>
              <li>• Responsive design</li>
              <li>• TypeScript support</li>
              <li>• Error handling</li>
              <li>• Loading states</li>
              <li>• Hidden form inputs for submission</li>
              <li>• Downpayment input (when enabled)</li>
              <li>• Promotional variant highlighting</li>
            </ul>
          </div>
          
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-2">🔄 API Integration</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Fetches data from stenik_leasingjetcredit API</li>
              <li>• Supports recalculation on downpayment change</li>
              <li>• Handles API errors gracefully</li>
              <li>• Configurable quote ID and product parameters</li>
              <li>• Automatic retry functionality</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PaymentCalculatorExample
