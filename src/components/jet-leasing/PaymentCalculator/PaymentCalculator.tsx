'use client'
import React, { useState, useEffect, useCallback } from 'react'
import { RenderCalculator } from '@/src/features/checkout/components/forms/payment/RenderCaltulator'

interface PaymentCalculatorProps {
  quoteId?: string
  productId?: string
  quantity?: number
  onCalculatorLoad?: (data: any) => void
  onVariantSelect?: (variantId: string) => void
  className?: string
}

export const PaymentCalculator: React.FC<PaymentCalculatorProps> = ({
  quoteId,
  productId,
  quantity = 1,
  onCalculatorLoad,
  onVariantSelect,
  className = '',
}) => {
  const [calculatorData, setCalculatorData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadCalculatorData = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      // TODO: GraphQL query placeholder - fetch calculator data
      const url = `https://praktis2-demo.pfgbulgaria.com/stenik_leasingjetcredit/calculator/getCalculatorHtml/quote_id/${quoteId}/`

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      setCalculatorData(data)
      onCalculatorLoad?.(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Грешка при зареждане на калкулатора')
      console.error('Error loading calculator data:', err)
    } finally {
      setLoading(false)
    }
  }, [onCalculatorLoad, quoteId])

  const handleVariantSelect = (variantId: string) => {
    onVariantSelect?.(variantId)
  }

  useEffect(() => {
    if (quoteId) {
      loadCalculatorData()
    }
  }, [quoteId, productId, quantity, loadCalculatorData])

  if (loading) {
    return (
      <div className={`p-6 border border-gray-200 rounded-lg bg-white ${className}`}>
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
          <span className="ml-3 text-gray-600">Зареждане на калкулатора...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`p-6 border border-red-200 rounded-lg bg-red-50 ${className}`}>
        <div className="flex items-center">
          <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
          <div>
            <h4 className="text-red-800 font-medium">Грешка при зареждане</h4>
            <p className="text-red-600 text-sm">{error}</p>
            <button onClick={loadCalculatorData} className="mt-2 text-sm text-red-700 underline hover:text-red-900">
              Опитайте отново
            </button>
          </div>
        </div>
      </div>
    )
  }

  return <RenderCalculator json={calculatorData} onVariantSelect={handleVariantSelect} className={className} />
}

export default PaymentCalculator
