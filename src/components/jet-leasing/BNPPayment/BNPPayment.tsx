'use client'
import React, { useState } from 'react'
import { useFormContext, useFormState, useWatch } from 'react-hook-form'
import { BNPPaymentProps, BNPPaymentState } from './types'
import { PaymentSchema } from '@features/checkout/components/forms/payment/payment.schema'
import { ParibaError } from '@components/jet-leasing/BNPPayment/components/ParibaError'
import { ParibaStepVariants } from './components/ParibaStepVariants'
import { BNPDownPayment } from '@components/jet-leasing/BNPPayment/BNPDownPayment'

export const BNPPayment: React.FC<BNPPaymentProps> = ({
  totalPrice,
  onVariantSelectAction,
  onEgnChangeAction,
  onAddressChangeAction,
  selectedVariantId,
  egn,
  address,
  className = '',
}) => {
  // Get form context for error handling
  const form = useFormContext<PaymentSchema>()
  const { errors } = useFormState({ control: form.control })
  const [state, setState] = useState<BNPPaymentState>({
    loading: false,
    error: null,
    loanCalculation: null,
  })

  const onDownPaymentChangeAction = (downPayment: number) => {
    form.setValue('bnpSelectedVariantId', '')
    form.setValue('bnpDownPayment', downPayment)
  }

  const downPayment = useWatch({
    control: form.control,
    name: 'bnpDownPayment',
  })

  const updateState = (updates: Partial<BNPPaymentState>) => {
    setState((prev) => ({ ...prev, ...updates }))
  }

  const handleEgnBlur = () => {
    form.trigger('bnpEgn')
  }

  // TEMPORARILY COMMENTED OUT - Address blur handler
  // const handleAddressBlur = () => {
  //   form.trigger('bnpAddress')
  // }

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      {state.error && <ParibaError error={state.error} />}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-gray-900 text-center">Изчислете вашите вноски</h3>
        <BNPDownPayment
          downPayment={downPayment || null}
          onDownPaymentChangeAction={(downPayment) => onDownPaymentChangeAction?.(downPayment)}
          totalPrice={totalPrice}
        />
        <ParibaStepVariants
          downPayment={downPayment || null}
          loading={state.loading}
          onLoanCalculation={(loanCalculation) => updateState({ loanCalculation })}
          onSelectVariant={(variant) => onVariantSelectAction?.(variant.id)}
          selectedVariantId={selectedVariantId}
        />

        {errors.bnpSelectedVariantId && (
          <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{errors.bnpSelectedVariantId.message}</p>
          </div>
        )}

        <div className="space-y-4 mt-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Допълнителна информация</h4>

            <div className="mb-4">
              <label htmlFor="bnp-egn" className="block text-sm font-medium text-gray-700 mb-1">
                ЕГН <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="bnp-egn"
                name="bnp-egn"
                value={egn || ''}
                onChange={(e) => onEgnChangeAction(e.target.value)}
                onBlur={handleEgnBlur}
                placeholder="Въведете вашия ЕГН"
                className={`w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                  errors.bnpEgn ? 'border-red-500 bg-red-50' : 'border-gray-300'
                }`}
              />
              {errors.bnpEgn && <p className="mt-1 text-sm text-red-600">{errors.bnpEgn.message}</p>}
            </div>

            {/* TEMPORARILY COMMENTED OUT - Address field */}
            {/*
            <div className="mb-4">
              <label htmlFor="bnp-address" className="block text-sm font-medium text-gray-700 mb-1">
                Адрес <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="bnp-address"
                name="bnp-address"
                value={address || ''}
                onChange={(e) => onAddressChangeAction(e.target.value)}
                onBlur={handleAddressBlur}
                placeholder="Въведете вашия адрес"
                className={`w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                  errors.bnpAddress ? 'border-red-500 bg-red-50' : 'border-gray-300'
                }`}
              />
              {errors.bnpAddress && <p className="mt-1 text-sm text-red-600">{errors.bnpAddress.message}</p>}
            </div>
            */}
          </div>
        </div>
      </div>
    </div>
  )
}
