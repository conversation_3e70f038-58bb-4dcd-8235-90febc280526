'use client'
import React from 'react'
import { BNPVariantSelectorProps, BNPPricingVariant } from './types'
import { formatPrice, formatPercentage } from './utils'
import {LucideCheck} from "lucide-react";

export const BNPVariantSelector: React.FC<BNPVariantSelectorProps> = ({
  variantGroups,
  selectedVariant,
  onVariantSelectAction,
  loading = true,
  loadingVariants = true,
  className = '',
}) => {
  if (loadingVariants) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!variantGroups.length && !loadingVariants) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">
            За да използвате лизинг чрез BNP Paribas, сумата на продукта трябва да бъде най-малко 150 лева или общата
            сума на артикулите в количката ви трябва да бъде най-малко 150 лева.
          </p>
        </div>
      </div>
    )
  }

  const renderVariantTable = (variants: BNPPricingVariant[]) => {
    return (
      <div className="overflow-x-auto">
        <table className="w-full border-collapse bg-white rounded-lg shadow-sm">
          <thead>
            <tr className="p-0 m-0 border-b border-gray-200">
              <th className="p-0 m-0 text-left font-semibold text-gray-700"></th>
              {variants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                const maturity = isPromo ? `${variant.maturity}+1` : variant.maturity
                return (
                  <th
                    key={variant.id}
                    className={`p-0 m-0 h-[90px] text-center font-semibold cursor-pointer ${isPromo ? 'bg-green-50 text-green-800' : 'text-gray-700'}`}
                    onClick={() => onVariantSelectAction(variant)}
                  >
                    <div className={`flex flex-col items-center justify-center p-3 h-full min-h-full ${selectedVariant?.id === variant.id ? 'border-r border-t border-l border-[#F1B201] bg-[#F1B201] text-white rounded-t-2xl' : 'bg-gray-50 border-b'}`}>
                      {isPromo && (
                        <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full mb-1">ПРОМО</span>
                      )}
                      <span>{maturity} месеца</span>
                    </div>
                  </th>
                )
              })}
            </tr>
          </thead>
          <tbody>
            {/* Installment Amount Row */}
            <tr className='hover:bg-gray-50'>
              <td className="p-0 m-0 font-medium text-gray-700">
                <div className='h-[65px] flex justify-start items-center border-b border-gray-200'>
                  Месечна вноска
                </div>
              </td>
              {variants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                return (
                  <td
                    key={variant.id}
                    className={`p-0 m-0 text-center ${isPromo ? 'bg-green-50' : ''}`}
                    onClick={() => onVariantSelectAction(variant)}
                  >
                    <div className={`p-3 h-[65px] flex justify-center items-center ${selectedVariant?.id === variant.id ? 'border-r border-l border-b border-[#F1B201] border-b-gray-200' : 'border-b border-gray-200'}`}>
                      <label htmlFor={`variant_${variant.id}`} className="cursor-pointer block">
                        <span className="font-semibold text-orange-600">{formatPrice(variant.installmentAmount)}</span>
                      </label>
                    </div>
                  </td>
                )
              })}
            </tr>

            {/* APR Row */}
            <tr className='hover:bg-gray-50'>
              <td className="p-0 m-0 font-medium text-gray-700">
                <div className='h-[65px] flex justify-start items-center border-b border-gray-200'>
                  ГПР
                </div>
              </td>
              {variants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                return (
                  <td
                    key={variant.id}
                    className={`p-0 m-0 text-center ${isPromo ? 'bg-green-50' : ''}`}
                    onClick={() => onVariantSelectAction(variant)}
                  >
                    <div className={`p-3 h-[65px] flex justify-center items-center ${selectedVariant?.id === variant.id ? 'border-r border-l border-b border-[#F1B201] border-b-gray-200' : 'border-b border-gray-200'}`}>
                      <label htmlFor={`variant_${variant.id}`} className="cursor-pointer block">
                        <span className="font-semibold">{formatPercentage(variant.apr)}</span>
                      </label>
                    </div>
                  </td>
                )
              })}
            </tr>

            {/* NIR Row */}
            <tr className='hover:bg-gray-50'>
              <td className="p-0 m-0 font-medium text-gray-700">
                <div className='h-[65px] flex justify-start items-center border-b border-gray-200'>
                  НЛР
                </div>
              </td>
              {variants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                return (
                  <td
                    key={variant.id}
                    className={`p-0 m-0 text-center ${isPromo ? 'bg-green-50' : ''}`}
                    onClick={() => onVariantSelectAction(variant)}
                  >
                    <div className={`p-3 h-[65px] w-full flex justify-center items-center ${selectedVariant?.id === variant.id ? 'border-r border-l border-b border-[#F1B201] border-b-gray-200' : 'border-b border-gray-200'}`}>
                      <label htmlFor={`variant_${variant.id}`} className="cursor-pointer block">
                        <span className="font-semibold">{formatPercentage(variant.nir)}</span>
                      </label>
                    </div>
                  </td>
                )
              })}
            </tr>

            {/* Processing Fee Row */}
            <tr className='hover:bg-gray-50'>
              <td className="p-0 m-0 font-medium text-gray-700">
                <div className='h-[65px] flex justify-start items-center border-b border-gray-200'>
                  Такса обработка
                </div>
              </td>
              {variants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                return (
                  <td
                    key={variant.id}
                    className={`p-0 m-0 text-center ${isPromo ? 'bg-green-50' : ''}`}
                    onClick={() => onVariantSelectAction(variant)}
                  >
                    <div className={`p-3 h-[65px] flex justify-center items-center ${selectedVariant?.id === variant.id ? 'border-r border-l border-b border-[#F1B201] border-b-gray-200' : 'border-b border-gray-200'}`}>
                      <label htmlFor={`variant_${variant.id}`} className="cursor-pointer block">
                        <span className="font-semibold">{formatPrice(variant.processingFeeAmount || '50.00')}</span>
                      </label>
                    </div>
                  </td>
                )
              })}
            </tr>

            {/* Total Repayment Row */}
            <tr className='hover:bg-gray-50'>
              <td className="p-0 m-0 font-medium text-gray-700">
                <div className='h-[65px] flex justify-start items-center'>
                  Обща сума за възстановяване
                </div>
              </td>
              {variants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                return (
                  <td
                    key={variant.id}
                    className={`p-0 m-0 text-center ${isPromo ? 'bg-green-50' : ''}`}
                    onClick={() => onVariantSelectAction(variant)}
                  >
                    <div className={`p-3 h-[65px] flex justify-center items-center ${selectedVariant?.id === variant.id ? 'border-r border-b border-l border-[#F1B201] rounded-b-2xl' : ''}`}>
                      <label htmlFor={`variant_${variant.id}`} className="cursor-pointer block">
                        <span className="font-semibold">{formatPrice(variant.totalRepaymentAmount)}</span>
                      </label>
                    </div>
                  </td>
                )
              })}
            </tr>
          </tbody>

          {/* Radio Button Footer */}
          <tfoot>
            <tr>
              <td className="p-3 font-medium text-gray-700">
                <span className="text-red-500 text-sm">Моля, изберете една от опциите.</span>
              </td>
              {variants.map((variant) => selectedVariant?.id === variant.id ?
                  (<td key={variant.id} className="text-center p-3">
                    <div className='w-full flex justify-center'>
                      <div className='bg-[#F1B201] rounded-full w-8 h-8 flex items-center justify-center' id={`variant_${variant.id}`}>
                        <LucideCheck color='#FFFFFF' />
                      </div>
                    </div>
                  </td>) : (
                <td key={variant.id} className="text-center p-3">
                  <input
                    type="radio"
                    id={`variant_${variant.id}`}
                    name="bnp_variant"
                    value={variant.id}
                    checked={selectedVariant?.id === variant.id}
                    onChange={() => onVariantSelectAction(variant)}
                    className="w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500"
                  />
                </td>
              ))}
            </tr>
          </tfoot>
        </table>
      </div>
    )
  }

  // Combine all variants from all groups and sort them
  const allVariants = variantGroups.flatMap(group => group.variants)

  // Sort variants - put promo variants (0% APR) first, then by maturity
  const sortedVariants = [...allVariants].sort((a, b) => {
    const aIsPromo = parseFloat(a.apr) === 0
    const bIsPromo = parseFloat(b.apr) === 0
    if (aIsPromo && !bIsPromo) return -1
    if (!aIsPromo && bIsPromo) return 1
    return parseInt(a.maturity) - parseInt(b.maturity)
  })

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Single table with all variants */}
      {sortedVariants.length > 0 && renderVariantTable(sortedVariants)}
    </div>
  )
}
