'use client'
import React from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { useCartStore } from '@features/cart/cart-state'
import { CartTotalCode } from '@lib/_generated/graphql_sdk'
import { BNPPayment } from '@components/jet-leasing/BNPPayment/BNPPayment'
import { PaymentSchema } from '@features/checkout/components/forms/payment/payment.schema'

interface BNPPaymentWrapperProps {
  className?: string
}

export const BNPPaymentWrapper: React.FC<BNPPaymentWrapperProps> = ({ className = '' }) => {
  const cartTotals = useCartStore((state) => state.totals)
  const cartToken = useCartStore((state) => state.token)
  const form = useFormContext<PaymentSchema>()

  // Watch for changes in simplified BNP payment fields
  const bnpSelectedVariantId = useWatch({
    control: form.control,
    name: 'bnpSelectedVariantId',
  })

  const bnpEgn = useWatch({
    control: form.control,
    name: 'bnpEgn',
  })

  // TEMPORARILY COMMENTED OUT - Address field not used
  // const bnpAddress = useWatch({
  //   control: form.control,
  //   name: 'bnpAddress',
  // })

  if (!cartToken) {
    return (
      <div className="col-span-2">
        <div className="text-center py-8">
          <p className="text-gray-600">Зареждане на количката...</p>
        </div>
      </div>
    )
  }

  const handleVariantSelect = (variantId: string) => {
    form.setValue('bnpSelectedVariantId', variantId, { shouldValidate: true, shouldDirty: true })
  }

  const handleEgnChange = (egn: string) => {
    form.setValue('bnpEgn', egn)
  }

  // TEMPORARILY COMMENTED OUT - Address field not used
  // const handleAddressChange = (address: string) => {
  //   form.setValue('bnpAddress', address)
  // }

  return (
    <div className={`col-span-2 ${className}`}>
      <BNPPayment
        totalPrice={cartTotals.find((total) => total.code === CartTotalCode.GrantTotal)?.amount?.value || 0}
        onVariantSelectAction={handleVariantSelect}
        onEgnChangeAction={handleEgnChange}
        // onAddressChangeAction={handleAddressChange} // TEMPORARILY COMMENTED OUT - Address field not used
        selectedVariantId={bnpSelectedVariantId}
        egn={bnpEgn}
        // address={bnpAddress} // TEMPORARILY COMMENTED OUT - Address field not used
        className="mt-4"
      />
    </div>
  )
}
