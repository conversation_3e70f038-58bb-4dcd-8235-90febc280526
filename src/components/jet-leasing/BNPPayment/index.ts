// Main exports for BNP Paribas payment components

export { BNPPayment } from './BNPPayment'
export { BNPVariantSelector } from './BNPVariantSelector'

// Export types
export type {
  BNPPaymentProps,
  BNPCalculatorProps,
  BNPCustomerFormProps,
  BNPVariantSelectorProps,
  BNPPricingScheme,
  BNPPricingVariant,
  BNPVariantGroup,
  BNPLoanCalculation,
  BNPCustomerData,
  BNPPaymentInput,
  BNPPaymentState,
  BNPPaymentStep,
  BNPFormErrors,
} from './types'

// Export utilities
export {
  formatPrice,
  formatPercentage,
  formatMaturity,
  formatCurrencyInput,
  parseCurrencyInput,
  validateCustomerForm,
  hasFormErrors,
  formatPhoneNumber,
  generateGoodTypeIds,
  handleBNPGoodTypeError,
  calculateTotalWithFee,
  generatePaymentSchedule,
  debounce,
} from './utils'
