/**
 * Integration test for BNP Payment flow
 * Tests the complete cart and order flow for BNP payments
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BNPPayment } from '../BNPPayment'
import { GraphQLBackend } from '@lib/api/graphql'

// Mock GraphQL Backend
jest.mock('@lib/api/graphql', () => ({
  GraphQLBackend: {
    GetBNPPricingSchemes: jest.fn(),
    CartSaveBNPPayment: jest.fn(),
    CalculateBNPLoan: jest.fn(),
  },
}))

const mockGraphQLBackend = GraphQLBackend as jest.Mocked<typeof GraphQLBackend>

describe('BNP Payment Flow Integration', () => {
  const mockProduct = {
    id: '123',
    sku: 'TEST-SKU',
    price: 1000,
    name: 'Test Product',
    brand: { name: 'Test Brand' },
  }

  const mockCartToken = 'test-cart-token-123'

  const mockPricingSchemes = [
    { id: '1', name: 'Standard Leasing' },
    { id: '2', name: 'Premium Leasing' },
  ]

  const mockLoanCalculation = {
    apr: '12.5',
    correctDownpaymentAmount: '100.00',
    installmentAmount: '45.50',
    maturity: '24',
    nir: '10.2',
    pricingSchemeId: '1',
    pricingSchemeName: 'Standard Leasing',
    pricingVariantId: '101',
    processingFeeAmount: '25.00',
    totalRepaymentAmount: '1092.00',
  }

  const mockCartSaveResponse = {
    cartSaveBNPPayment: {
      id: 'cart-123',
      token: mockCartToken,
      paymentMethod: 'stenik_leasingjetcredit',
      // ... other cart fields
    },
  }

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup default mock responses
    mockGraphQLBackend.GetBNPPricingSchemes.mockResolvedValue({
      getBNPPricingSchemes: mockPricingSchemes,
    })
    
    mockGraphQLBackend.CalculateBNPLoan.mockResolvedValue({
      calculateBNPLoan: mockLoanCalculation,
    })
    
    mockGraphQLBackend.CartSaveBNPPayment.mockResolvedValue(mockCartSaveResponse)
  })

  it('should complete the full payment flow successfully', async () => {
    const onPaymentSelect = jest.fn()
    const onError = jest.fn()

    render(
      <BNPPayment
        product={mockProduct}
        quantity={1}
        cartToken={mockCartToken}
        onPaymentSelect={onPaymentSelect}
        onError={onError}
      />
    )

    // Wait for pricing schemes to load
    await waitFor(() => {
      expect(mockGraphQLBackend.GetBNPPricingSchemes).toHaveBeenCalledWith({
        goodTypeIds: expect.any(String),
        principal: 1000,
        downPayment: 0,
      })
    })

    // Should show schemes step
    expect(screen.getByText('Standard Leasing')).toBeInTheDocument()
    expect(screen.getByText('Premium Leasing')).toBeInTheDocument()

    // Click on first scheme to proceed to variants
    fireEvent.click(screen.getByText('Standard Leasing'))

    // Should proceed to variants step
    await waitFor(() => {
      expect(screen.getByText('Варианти')).toBeInTheDocument()
    })

    // Mock variant selection and proceed to customer form
    // (This would require more complex mocking of the BNPCalculator component)
    
    // For now, just verify that the GraphQL calls are made correctly
    expect(onError).not.toHaveBeenCalled()
  })

  it('should handle cart save payment correctly', async () => {
    const onPaymentSelect = jest.fn()
    
    // Mock a complete flow where customer data is submitted
    const mockCustomerData = {
      firstName: 'John',
      lastName: 'Doe',
      phone: '+359888123456',
      email: '<EMAIL>',
      address: 'Test Address 123',
      city: 'Sofia',
      postCode: '1000',
    }

    // Simulate the CartSaveBNPPayment call that would happen in handleCustomerFormSubmit
    const paymentData = {
      goodTypeIds: '101',
      principal: 1000,
      downPayment: 0,
      pricingVariantId: 101,
      customerData: mockCustomerData,
    }

    const result = await mockGraphQLBackend.CartSaveBNPPayment({
      cartToken: mockCartToken,
      paymentData,
    })

    expect(mockGraphQLBackend.CartSaveBNPPayment).toHaveBeenCalledWith({
      cartToken: mockCartToken,
      paymentData,
    })

    expect(result.cartSaveBNPPayment).toBeDefined()
    expect(result.cartSaveBNPPayment.paymentMethod).toBe('stenik_leasingjetcredit')
  })

  it('should handle errors gracefully', async () => {
    const onError = jest.fn()
    
    // Mock API error
    mockGraphQLBackend.GetBNPPricingSchemes.mockRejectedValue(
      new Error('API Error: Service unavailable')
    )

    render(
      <BNPPayment
        product={mockProduct}
        quantity={1}
        cartToken={mockCartToken}
        onError={onError}
      />
    )

    // Wait for error to be handled
    await waitFor(() => {
      expect(onError).toHaveBeenCalledWith(expect.stringContaining('API Error'))
    })
  })

  it('should validate required props', () => {
    const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {})
    
    expect(() => {
      render(
        <BNPPayment
          product={mockProduct}
          quantity={1}
          cartToken="" // Empty cart token should cause issues
        />
      )
    }).not.toThrow()

    consoleError.mockRestore()
  })
})
