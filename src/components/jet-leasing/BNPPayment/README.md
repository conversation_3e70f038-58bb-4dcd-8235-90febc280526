# BNP Paribas Payment Component

A comprehensive React component suite for integrating BNP Paribas leasing payment functionality into the jet leasing application.

## Overview

The BNP Payment component provides a complete payment flow for BNP Paribas leasing services, including:
- Payment variant selection with real-time calculations
- Customer data collection with Bulgarian-specific validation
- Multi-step payment process with progress indication
- Integration with GraphQL API endpoints
- Responsive design and accessibility features

## Components

### 1. `BNPPayment` (Main Component)
**Location**: `src/components/jet-leasing/BNPPayment/BNPPayment.tsx`

The main orchestrator component that manages the entire payment flow.

**Props:**
- `product?: { id: string, sku: string, price: number }` - Product information
- `quantity?: number` - Product quantity (default: 1)
- `cartToken?: string` - Cart token for API calls
- `onPaymentSelect?: (paymentData: BNPPaymentInput) => void` - Callback when payment is selected
- `onError?: (error: string) => void` - Error callback
- `className?: string` - Additional CSS classes

**Features:**
- ✅ Multi-step wizard interface (Variants → Customer Data → Confirmation)
- ✅ Progress indicator with step completion status
- ✅ Error handling and user feedback
- ✅ State management for the entire payment flow
- ✅ Integration with all sub-components

### 2. `BNPCalculator`
**Location**: `src/components/jet-leasing/BNPPayment/BNPCalculator.tsx`

Handles payment calculation and variant selection.

**Props:**
- `sku: string` - Product SKU
- `downPayment: number` - Initial down payment amount
- `quantity: number` - Product quantity
- `onVariantSelect?: (variant: BNPPricingVariant) => void` - Variant selection callback
- `onCalculationComplete?: (calculation: BNPLoanCalculation) => void` - Calculation callback
- `onDownPaymentChange?: (downPayment: number) => void` - Down payment change callback
- `className?: string` - Additional CSS classes

**Features:**
- ✅ Interactive down payment input with Bulgarian currency formatting
- ✅ Real-time validation and min/max limits based on product price
- ✅ Debounced API calls (500ms) to prevent excessive requests
- ✅ Automatic recalculation when down payment changes
- ✅ Visual feedback showing down payment impact on loan terms
- ✅ Automatic loading of pricing variants
- ✅ Real-time loan calculations
- ✅ Integration with BNPVariantSelector
- ✅ Loading states and error handling
- ✅ Currency input formatting and validation

### 3. `BNPVariantSelector`
**Location**: `src/components/jet-leasing/BNPPayment/BNPVariantSelector.tsx`

Interactive component for selecting payment variants.

**Props:**
- `variants: BNPPricingVariant[]` - Available payment variants
- `selectedVariantId?: string` - Currently selected variant ID
- `onVariantSelect: (variant: BNPPricingVariant) => void` - Selection callback
- `loading?: boolean` - Loading state
- `className?: string` - Additional CSS classes

**Features:**
- ✅ Radio button selection with visual feedback
- ✅ Detailed payment information display
- ✅ Payment schedule preview
- ✅ Responsive grid layout
- ✅ Hover and selection states

### 4. `BNPCustomerForm`
**Location**: `src/components/jet-leasing/BNPPayment/BNPCustomerForm.tsx`

Customer data collection form with Bulgarian-specific validation.

**Props:**
- `onSubmit: (customerData: BNPCustomerData) => void` - Form submission callback
- `onBack: () => void` - Back button callback
- `loading?: boolean` - Loading state
- `className?: string` - Additional CSS classes

**Features:**
- ✅ Form data collection (validation disabled)
- ✅ Clean form interface
- ✅ Accessibility features (ARIA labels, keyboard navigation)
- ✅ Privacy notice display

## GraphQL Integration Points

The component includes GraphQL queries and mutations for the following operations:

### Queries
1. **getBNPPricingSchemes** - Get available pricing schemes
   ```graphql
   query GetBNPPricingSchemes($goodTypeIds: String!, $principal: Float!, $downPayment: Float!)
   ```

2. **getCreditCalculatorBNPParibas** - Get payment variants for a product
   ```graphql
   query GetCreditCalculatorBNPParibas($sku: String!, $downPayment: Float!, $qty: Int!)
   ```

3. **calculateBNPLoan** - Calculate loan details for selected variant
   ```graphql
   query CalculateBNPLoan($goodTypeIds: String!, $principal: Float!, $downPayment: Float!, $pricingVariantId: Int!)
   ```

### Mutations
1. **cartSaveBNPPayment** - Save payment data to cart
   ```graphql
   mutation CartSaveBNPPayment($cartToken: String!, $paymentData: BNPPaymentInput!)
   ```

2. **submitBNPApplication** - Submit final application
   ```graphql
   mutation SubmitBNPApplication($orderNumber: String!)
   ```

## Usage Examples

### Basic Integration
```tsx
import { BNPPayment } from '@components/jet-leasing/BNPPayment'

function CheckoutPage() {
  const handlePaymentSelect = (paymentData) => {
    console.log('Payment selected:', paymentData)
    // Process payment data
  }

  const handleError = (error) => {
    console.error('Payment error:', error)
    // Handle error
  }

  return (
    <BNPPayment
      product={{
        id: "123",
        sku: "360242",
        price: 2500.00
      }}
      quantity={1}
      cartToken="cart_token_123"
      onPaymentSelect={handlePaymentSelect}
      onError={handleError}
    />
  )
}
```

### Integration in Payment Form
```tsx
// In PaymentData.tsx
{paymentMethod === 'bnp_paribas_leasing' && (
  <div className="col-span-2">
    <BNPPayment
      product={productData}
      quantity={quantity}
      cartToken={cartToken}
      onPaymentSelect={(paymentData) => {
        // Store payment data for form submission
        setSelectedPaymentData(paymentData)
      }}
      onError={(error) => {
        // Show error message
        setPaymentError(error)
      }}
      className="mt-4"
    />
  </div>
)}
```

### Standalone Calculator
```tsx
import { BNPCalculator } from '@components/jet-leasing/BNPPayment'

function ProductPage() {
  return (
    <BNPCalculator
      sku="360242"
      downPayment={500}
      quantity={1}
      onVariantSelect={(variant) => {
        console.log('Selected variant:', variant)
      }}
      onCalculationComplete={(calculation) => {
        console.log('Calculation complete:', calculation)
      }}
    />
  )
}
```

## Down Payment Features

### Interactive Input
- **Currency Formatting**: Automatic Bulgarian locale formatting (e.g., "500,00 лв.")
- **Real-time Validation**: Min/max limits based on product price (10%-80%)
- **Visual Feedback**: Shows percentage of total price and validation errors
- **Debounced API Calls**: 500ms delay to prevent excessive requests while typing

### Dynamic Recalculation
- **Automatic Updates**: Triggers new API calls when down payment changes
- **Loading States**: Shows recalculation progress with visual indicators
- **Variant Preservation**: Maintains selected variant if still available after recalculation
- **Impact Display**: Shows how down payment affects financed amount, monthly payments, and total interest

### Input Validation
- **Numeric Only**: Accepts only valid numeric input with Bulgarian decimal separator (comma)
- **Range Validation**: Enforces minimum 10% and maximum 80% of product price
- **Error Messages**: Clear Bulgarian error messages for invalid inputs
- **Real-time Feedback**: Immediate validation as user types

## Validation Features (Disabled)

Form validation has been disabled as requested. The following features were previously available:

### Bulgarian EGN Validation
- 10-digit format validation
- Checksum verification
- Real-time feedback

### Phone Number Validation
- Bulgarian mobile and landline formats
- International (+359) and national (0X) formats
- Automatic formatting

### Address Validation
- Bulgarian postal code format (4 digits)
- Required field validation
- Minimum length requirements

## Styling and Design

The component follows the established design patterns:
- **Colors**: Orange (`#F1B201`) for primary elements, blue for BNP branding
- **Layout**: Responsive grid system with Tailwind CSS
- **Typography**: Consistent with existing components
- **Interactive elements**: Hover states, focus indicators, loading animations
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support

## Error Handling

- Network error handling with retry functionality
- Form validation with field-specific error messages
- User-friendly error messages in Bulgarian
- Graceful degradation for API failures

## Testing Recommendations

### Unit Tests
- Form validation functions
- Utility functions (EGN validation, phone formatting)
- Component rendering with different props
- Error state handling

### Integration Tests
- Complete payment flow
- API integration (with mocked responses)
- Form submission and validation
- Step navigation

### E2E Tests
- Full payment process from variant selection to confirmation
- Error scenarios and recovery
- Responsive design across devices
- Accessibility compliance

## Implementation Status

### ✅ Completed
- GraphQL schema definitions and fragments
- Query and mutation files created
- Component structure and UI implementation
- Mock data integration for development
- Form validation disabled as requested

### 🔄 Pending
- GraphQL SDK regeneration to include new BNP queries
- Backend API endpoint implementation
- Real API integration (currently using mock data)

### 📝 Next Steps
1. **Regenerate GraphQL SDK**: Run codegen to include new BNP queries
2. **Backend Implementation**: Implement the actual GraphQL resolvers
3. **Replace Mock Data**: Update components to use real API calls
4. **Testing**: Comprehensive testing with real data

## Future Enhancements

1. **Form Validation**: Re-enable validation if needed in the future
2. **Internationalization**: Support for multiple languages
3. **Advanced Features**: Additional business rules and constraints
4. **Analytics**: Payment flow tracking and conversion metrics
5. **Caching**: Optimize API calls with intelligent caching
6. **Offline Support**: Handle network connectivity issues
