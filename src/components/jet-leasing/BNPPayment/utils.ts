// Utility functions for BNP Paribas payment component

import { BNPCustomerData, BNPFormErrors } from './types'

/**
 * Format price with Bulgarian locale
 */
export const formatPrice = (amount: string | number): string => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return `${num.toFixed(2).replace('.', ',')} лв.`
}

/**
 * Parse Bulgarian currency input to number
 */
export const parseCurrencyInput = (value: string): number => {
  const cleaned = value.replace(/[^\d,]/g, '').replace(',', '.')
  const number = parseFloat(cleaned)
  return isNaN(number) ? 0 : number
}

/**
 * Format percentage with 2 decimal places
 */
export const formatPercentage = (rate: string | number): string => {
  const num = typeof rate === 'string' ? parseFloat(rate) : rate
  return `${num.toFixed(2)}%`
}

/**
 * Comprehensive form validation - disabled
 */
export const validateCustomerForm = (data: Partial<BNPCustomerData>): BNPFormErrors => {
  // Validation disabled - return empty errors object
  return {}
}

/**
 * Check if form has any errors - disabled
 */
export const hasFormErrors = (errors: BNPFormErrors): boolean => {
  // Always return false since validation is disabled
  return false
}

/**
 * Format phone number for display
 */
export const formatPhoneNumber = (phone: string): string => {
  const cleanPhone = phone.replace(/\D/g, '')

  if (cleanPhone.startsWith('359')) {
    // International format
    return `+${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 4)} ${cleanPhone.slice(4, 7)} ${cleanPhone.slice(7)}`
  } else if (cleanPhone.startsWith('0')) {
    // National format
    return `${cleanPhone.slice(0, 4)} ${cleanPhone.slice(4, 7)} ${cleanPhone.slice(7)}`
  }

  return phone
}

/**
 * Generate good type IDs from product data
 * Uses a simple fallback mechanism since API-based mapping has been removed
 */
export const generateGoodTypeIds = (productId?: string, sku?: string, productName?: string, brand?: string): string => {
  // Always return the safe fallback good type ID
  // This is a general category that BNP is most likely to accept
  return '101' // Power Tools - General category
}

/**
 * Validate if a good type ID is valid for BNP API
 */
export const validateGoodTypeId = (goodTypeId: string): boolean => {
  // Check if it's a valid format (numeric string)
  if (!/^\d+$/.test(goodTypeId)) {
    return false
  }

  // For now, accept any numeric ID since we removed the static mapping
  return true
}

/**
 * Get good type ID with validation and error handling
 */
export const getValidatedGoodTypeId = (
  productId?: string,
  sku?: string,
  productName?: string,
  brand?: string
): { goodTypeId: string; isValid: boolean; source: string } => {
  const goodTypeId = generateGoodTypeIds(productId, sku, productName, brand)
  const isValid = validateGoodTypeId(goodTypeId)

  return { goodTypeId, isValid, source: 'fallback' }
}

/**
 * Get a safe fallback good type ID that BNP is likely to accept
 */
export const getSafeFallbackGoodTypeId = (): string => {
  // Use Power Tools General (101) as the safest fallback
  // This is a broad category that BNP is most likely to accept
  return '101'
}

/**
 * Handle BNP API errors related to good type IDs
 */
export const handleBNPGoodTypeError = (error: any): string => {
  const errorMessage = error?.message || error?.toString() || 'Unknown error'

  // Check for specific BNP error codes
  if (errorMessage.includes('[6032]') || errorMessage.includes('Не е намерена стока')) {
    const safeFallbackId = getSafeFallbackGoodTypeId()
    return `Продуктът не е поддържан от BNP Paribas финансиране. Моля, свържете се с нас за алтернативни опции за плащане. (Код: ${safeFallbackId}, Източник: fallback)`
  }

  // Generic error handling
  if (errorMessage.includes('Network') || errorMessage.includes('fetch')) {
    return 'Проблем с мрежовата връзка. Моля, опитайте отново.'
  }

  return 'Възникна грешка при зареждане на опциите за финансиране. Моля, опитайте отново или се свържете с нас.'
}

/**
 * Enhanced format currency input as user types
 */
export const formatCurrencyInput = (value: string): string => {
  // Remove all non-digit and non-comma characters
  const cleaned = value.replace(/[^\d,]/g, '')

  // Split by comma to handle decimal part
  const parts = cleaned.split(',')

  // Format the integer part with thousands separators
  if (parts[0]) {
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ' ')
  }

  // Limit decimal places to 2
  if (parts[1]) {
    parts[1] = parts[1].substring(0, 2)
  }

  return parts.join(',')
}

/**
 * Calculate total amount including processing fee
 */
export const calculateTotalWithFee = (totalRepaymentAmount: string, processingFeeAmount: string): number => {
  const total = parseFloat(totalRepaymentAmount) || 0
  const fee = parseFloat(processingFeeAmount) || 0
  return total + fee
}

/**
 * Format maturity period for display
 */
export const formatMaturity = (maturity: string | number): string => {
  const months = typeof maturity === 'string' ? parseInt(maturity) : maturity
  return `${months} ${months === 1 ? 'месец' : 'месеца'}`
}

/**
 * Generate payment schedule preview
 */
export const generatePaymentSchedule = (
  installmentAmount: string,
  maturity: string,
  downPayment: number = 0
): Array<{ month: number; amount: number; type: 'downpayment' | 'installment' }> => {
  const schedule = []
  const installment = parseFloat(installmentAmount)
  const months = parseInt(maturity)

  if (downPayment > 0) {
    schedule.push({
      month: 0,
      amount: downPayment,
      type: 'downpayment' as const,
    })
  }

  for (let i = 1; i <= months; i++) {
    schedule.push({
      month: i,
      amount: installment,
      type: 'installment' as const,
    })
  }

  return schedule
}

/**
 * Debounce function for API calls
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}
