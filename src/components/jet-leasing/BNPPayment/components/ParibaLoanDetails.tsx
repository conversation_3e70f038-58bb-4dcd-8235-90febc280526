import { BNPLoanCalculation, formatPercentage, formatPrice } from '@components/jet-leasing/BNPPayment'
import React from 'react'

export const ParibaLoanDetails: React.FC<{ loanCalculation: BNPLoanCalculation }> = ({ loanCalculation }) => {
  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-6">
      <h3 className="font-bold text-gray-900 mb-4 flex flex-col gap-2 lg:flex-row lg:gap-0">
        <div className='flex flex-row align-items-center'>
          <svg className="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
            />
          </svg>
          Детайли на кредита
        </div>
        <div>
        {loanCalculation.pricingSchemeName && (
          <span className="ml-2 font-normal text-gray-600">({loanCalculation.pricingSchemeName})</span>
        )}
        </div>
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
        <div className="bg-white rounded-lg p-3">
          <span className="text-gray-600 block">Месечна вноска:</span>
          <div className="text-lg font-bold text-green-600">{formatPrice(loanCalculation.installmentAmount)}</div>
        </div>

        <div className="bg-white rounded-lg p-3">
          <span className="text-gray-600 block">Такса обработка:</span>
          <div className="text-lg font-semibold text-gray-900">{formatPrice(loanCalculation.processingFeeAmount)}</div>
        </div>

        <div className="bg-white rounded-lg p-3">
          <span className="text-gray-600 block">Обща сума:</span>
          <div className="text-lg font-semibold text-gray-900">{formatPrice(loanCalculation.totalRepaymentAmount)}</div>
        </div>

        <div className="bg-white rounded-lg p-3">
          <span className="text-gray-600 block">ГПР:</span>
          <div className="text-lg font-semibold text-gray-900">{formatPercentage(loanCalculation.apr)}</div>
        </div>
      </div>
    </div>
  )
}
