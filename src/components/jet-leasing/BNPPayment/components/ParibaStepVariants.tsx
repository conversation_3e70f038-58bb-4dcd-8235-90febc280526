import {
  BNPLoanCalculation,
  BNPPricingVariant,
  BNPVariantGroup,
  BNPVariantSelector,
} from '@components/jet-leasing/BNPPayment'
import React, { useCallback, useEffect } from 'react'
import { GraphQLBackend } from '@lib/api/graphql'

import Cookies from 'universal-cookie'
import { ParibaLoanDetails } from '@components/jet-leasing/BNPPayment/components/ParibaLoanDetails'
const cookie = new Cookies()

interface ParibaVariantsStepProps {
  downPayment: number | null
  onSelectVariant: (variant: BNPPricingVariant) => void
  onLoanCalculation: (calculation: BNPLoanCalculation) => void
  loading: boolean
  selectedVariantId?: string
}

export const ParibaStepVariants: React.FC<ParibaVariantsStepProps> = ({
  downPayment,
  onSelectVariant,
  onLoanCalculation,
  loading,
  selectedVariantId,
}) => {
  const [loadingLoan, setLoadingLoan] = React.useState(false)
  const [loadingVariants, setLoadingVariants] = React.useState(false)
  const [error, setError] = React.useState<string | null>()
  const [variantGroups, setVariantGroups] = React.useState<BNPVariantGroup[]>([])

  const selectedVariant = React.useMemo(() => {
    if (selectedVariantId && variantGroups.length > 0) {
      for (const group of variantGroups) {
        const variant = group.variants.find((v) => v.id === selectedVariantId)
        if (variant) {
          return variant
        }
      }
    }
    return null
  }, [selectedVariantId, variantGroups])

  const [loanCalculation, setLoanCalculation] = React.useState<BNPLoanCalculation | null>(null)

  const loadPricingVariants = useCallback(async (downPayment: number | null) => {
    setLoadingVariants(true)
    setLoanCalculation(null)
    setError(null)
    try {
      // Use the generated GraphQL function
      const response = await GraphQLBackend.GetCreditCalculatorQuoteBNPParibas({
        cartToken: cookie.get('cartToken') || '',
        downPayment: downPayment || 0,
      })

      if (response.getCreditCalculatorBNPParibasForQuote) {
        // Transform response to variant groups with promo detection
        const groups: BNPVariantGroup[] = response.getCreditCalculatorBNPParibasForQuote.map((group) => {
          const enhancedVariants = group.variants.map((variant) => ({
            ...variant,
            isPromo: parseFloat(variant.apr) === 0,
            processingFeeAmount: variant.processingFeeAmount || '50.00',
          }))

          return {
            schemeId: group.schemeId,
            schemeName: enhancedVariants[0]?.pricingSchemeName || `Схема ${group.schemeId}`,
            variants: enhancedVariants,
          }
        })
        setVariantGroups(groups)
      } else {
        setVariantGroups([])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Грешка при зареждане на варианти')
      setVariantGroups([])
    } finally {
      setLoadingVariants(false)
    }
  }, [])

  const onVariantSelect = useCallback(
    async (variant: BNPPricingVariant) => {
      setLoadingLoan(true)
      // setselectedVariant(variant)
      onSelectVariant(variant)
      setLoanCalculation(null)

      let response
      try {
        response = await GraphQLBackend.CalculateBNPLoan({
          cartToken: cookie.get('cartToken') || '',
          downPayment: downPayment || 0,
          pricingVariantId: parseInt(variant.id),
        })
        setLoanCalculation(response.calculateBNPLoan)
        onLoanCalculation(response.calculateBNPLoan)
      } catch (e) {
        setError('Грешка при зареждане на кредита')
      } finally {
        setLoadingLoan(false)
      }
    },
    [downPayment, onLoanCalculation, onSelectVariant]
  )

  useEffect(() => {
    loadPricingVariants(downPayment)
  }, [loadPricingVariants, downPayment])

  return (
    <div className="space-y-6">
      {error && (
        <div className="p-4 border border-red-200 rounded-lg bg-red-50">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102
                0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            <span className="text-red-800 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Variant Selector */}
      <BNPVariantSelector
        variantGroups={variantGroups}
        selectedVariant={selectedVariant}
        onVariantSelectAction={onVariantSelect}
        loading={loading}
        loadingVariants={loadingVariants}
      />

      {loadingLoan && (
        <div className={`p-6 border border-gray-200 rounded-lg bg-white`}>
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
            <span className="ml-3 text-gray-600">Преизчисляване на кредита...</span>
          </div>
        </div>
      )}

      {!loadingLoan && loanCalculation && <ParibaLoanDetails loanCalculation={loanCalculation} />}
    </div>
  )
}
