import React from 'react'
import { Store, WebSite } from 'schema-dts'

import { SchemaOrg } from '@/src/components/atoms/SchemaOrg'
import { getStaticContent } from '@/src/features/static/api'
import { HomePageContainer } from '@atoms/HomePageContaner'
import FeaturedBrandsWidget from '@components/molecules/Widget/widgets/FeaturedBrands/FeaturedBrandsWidget'
import { Brand, HomePage } from '@lib/_generated/graphql_sdk'

import { Carousel } from '../molecules/Widget/Carousel'
import { AppWidgets, Widget } from '../molecules/Widget/Widget'
import { CMSBlock } from '@components/molecules/Widget/markup/instanced'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'

export async function HomePageBody({
  widgets,
  brands,
  slider,
}: {
  slider: HomePage['heroSlider']
  widgets: AppWidgets[]
  brands: Brand[]
}) {
  const staticContent = await getStaticContent()

  const firstWidgets: AppWidgets = widgets[0]
  const restWidgets: AppWidgets[] = widgets.slice(1)

  console.log('HomePageBody rendered at:', new Date().toISOString(), {
    widgetsLength: widgets.length,
    brandsLength: brands.length,
    hasSlider: !!slider
  })
  return (
    <div className="overflow-hidden">
      {/* Schema.org structured data */}
      <SchemaOrg<WebSite>
        schema={{
          '@context': 'https://schema.org',
          '@type': 'WebSite',
          'url': staticContent.store.baseUrl,
          'image': staticContent.logo?.url,
          'name': 'Praktis',
          'potentialAction': {
            '@type': 'SearchAction',
            'target': `${staticContent.store.baseUrl}/catalogsearch/result/?q={search_term_string}`,
            'query': 'required name=search_term_string',
          },
          'mainEntity': {
            '@type': 'Organization',
            'name': 'Praktis',
            'contactPoint': {
              '@type': 'ContactPoint',
              'email': '<EMAIL>',
              'telephone': staticContent?.store.contacts.onlineStore?.phone || '0894198027',
            },
          },
        }}
      />
      <meta name="robots" content="INDEX,FOLLOW" />
      <SchemaOrg<Store>
        schema={{
          '@context': 'https://schema.org',
          '@type': 'Store',
          'name': 'Praktis',
          'url': staticContent.store.baseUrl,
          'image': staticContent.logo?.url,
          'description': 'Онлайн магазин за строителни материали, инструменти, бои и стоки за дома и градината.',
          'priceRange': '$$',
          'email': '<EMAIL>',
          'telephone': staticContent?.store.contacts.onlineStore?.phone || '0894198027',
        }}
      />

      {slider && (
        <HomePageContainer>
          <Carousel widget={slider} />
        </HomePageContainer>
      )}

      <Widget.List widgets={[firstWidgets]} />

      <CMSBlock id="newtheme_homepage_banners" />
      <Widget.List widgets={restWidgets} />

      {brands && (
        <HomePageContainer>
          <FeaturedBrandsWidget brands={brands} />
        </HomePageContainer>
      )}

      <RelevaTrackPage pageType="homepage" />
    </div>
  )
}
