import React from 'react'

import { cookies } from 'next/headers'
import Container from '@atoms/Container'
import { CartInit } from '@/src/app/(checkout)/checkout/onepage/success/components/CartInit'

const OrderCreatedPage = async () => {
  const cookieStore = await cookies()
  const cookieOrderId = cookieStore.get('order_id')?.value
  const orderId = cookieOrderId ? String(cookieOrderId) : undefined

  return (
    <Container maxWidth="2xl">
      <CartInit orderId={orderId} />
    </Container>
  )
}

export default OrderCreatedPage
