'use client'

import { useCartStore } from '@features/cart/cart-state'
import React, { useEffect, useRef, useState } from 'react'
import { OrderCookiesHandler } from '@/src/app/(checkout)/checkout/onepage/success/components/OrderCookieHandler'
import { useRouter } from 'next/navigation'
import Img from '@atoms/Img'
import Text from '@atoms/Text'
import AppLink from '@atoms/AppLink'
import { Button } from '@components/theme/ui/button'
import { Contacts } from '@/src/app/(checkout)/checkout/onepage/success/components/Contacts'

export const CartInit = ({ orderId: initialOrderId }: { orderId: string | undefined }) => {
  const { initCart, ready } = useCartStore()
  const [orderId, setOrderId] = useState<undefined | string>()
  const router = useRouter()

  useEffect(() => {
    if (!initialOrderId && orderId) return
    if (initialOrderId && !orderId) {
      setOrderId(initialOrderId)
      return
    }
    if (!initialOrderId && !orderId) {
      router.replace('/')
    }
  }, [initialOrderId, orderId, router])

  useEffect(() => {
    if (!ready) {
      console.log('CartInit Now >', { ready })
      initCart()
    }
  }, [initCart, ready])

  return (
    <div className="flex justify-center">
      {ready && orderId && <OrderCookiesHandler orderId={orderId} />}
      <div className="hidden lg:block relative w-[438px] h-[592px] mt-10">
        <Img src="/images/happy-technician.png" alt="Thank you!" fill />
      </div>
      <div className="flex flex-col items-center justify-center gap-10 py-10">
        <div className="flex flex-col gap-4 items-center">
          <Text className="font-bold text-2xl text-center">Вашата поръчка е приета успешно!</Text>
          {orderId && (
            <div>
              Номер на поръчката <span className="font-bold">#{orderId}</span>
            </div>
          )}
        </div>

        <div className="flex flex-col items-center gap-3">
          <div className="text-center">Благодарим Ви, че избрахте Praktis.bg</div>
          <div className="text-center">
            Ще се свържем с Вас на посочения телефонен номер за потвърждение на поръчката.
          </div>
          <div className="text-center">Ще получите имейл с информация за Вашата поръчката.</div>
        </div>

        <AppLink href="/">
          <Button>Продължи с пазаруването</Button>
        </AppLink>

        <Contacts />
      </div>
    </div>
  )
}
