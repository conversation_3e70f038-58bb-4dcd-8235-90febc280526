'use client'

import { useEffect, useRef } from 'react'

interface StoreEmailProps {
  value: string
}

export const StoreEmail: React.FC<StoreEmailProps> = ({ value }) => {
  const ref = useRef<HTMLAnchorElement>(null)
  useEffect(() => {
    const decode = atob(value)
    ref.current?.setAttribute('href', `mailto:${decode}`)
    if (ref.current) {
      ref.current.innerHTML = decodeURIComponent(decode)
    }
  }, [value])

  return <a ref={ref} href="#" target="_blank" className="underline"></a>
}
