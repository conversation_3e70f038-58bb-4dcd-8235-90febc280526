'use client'
import { useEffect } from 'react'
import { useCartStore } from '@features/cart/cart-state'
import { useCustomerStore } from '@features/customer/customer.store'
import { trackPurchase } from '@lib/fbp/faceBookPixelHelper'
import { gtagTrack } from '@components/molecules/GDPR/GtagTrack'
import { relevaTrackOrder } from '@components/molecules/GDPR/relevaTrack'

import { deleteOrderIdCookie } from '@/src/app/(checkout)/checkout/onepage/success/actions'
export const OrderCookiesHandler = ({ orderId }: { orderId: string }) => {
  const customer = useCustomerStore()
  const cartStore = useCartStore()
  const { items, resetCart } = cartStore

  useEffect(() => {
    if (items.length === 0) return
    trackPurchase({
      contents:
        items?.map((item) => ({ id: item.product.id, quantity: item.baseQty, item_price: item.price.value })) || [],
      currency: items?.[0]?.price?.currency || 'BGN',
      value: items?.reduce((acc, item) => acc + Number(item.price.value) * item.baseQty, 0) || 0,
      content_type: 'product',
    })

    gtagTrack({
      eventName: 'purchase',
      properties: {
        transaction_id: orderId,
        currency: items?.[0]?.price?.currency || 'BGN',
        value: items?.reduce((acc, item) => acc + Number(item.price.value) * item.baseQty, 0) || 0,
        items:
          items?.map((item) => ({
            item_id: item.product.id,
            item_name: item.product.name,
            currency: item.price.currency,
            price: Number(item.price.value),
            quantity: item.baseQty,
          })) || [],
      },
    })
  }, [items, orderId])

  relevaTrackOrder(orderId, cartStore, customer)

  useEffect(() => {
    deleteOrderIdCookie().then(() => {
      resetCart()
    })
  }, [resetCart])

  return null
}
