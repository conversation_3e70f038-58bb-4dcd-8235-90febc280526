'use client'

import React from 'react'

import { StoreEmail } from '@/src/app/(checkout)/checkout/onepage/success/components/StoreEmail'
import AppLink from '@atoms/AppLink'
import Static from '@features/static'
import { StaticContentProps } from '@features/static/types'
import { useStaticContentStore } from '@features/static/static.store'

function StorePhone({ staticContent }: StaticContentProps) {
  return (
    <AppLink className="underline" dontAppendSlash href={`tel:${staticContent?.store.contacts.onlineStore?.phone}`}>
      {staticContent?.store.contacts.onlineStore?.phone}
    </AppLink>
  )
}

export const Contacts = () => {
  const staticContent = useStaticContentStore((data) => data.staticContent)
  return (
    <div className="flex flex-col items-center gap-3">
      <div>За допълнителни въпроси:</div>
      <div>
        Телефон: <StorePhone staticContent={staticContent} /> или <StoreEmail value="************************" />
      </div>
    </div>
  )
}
