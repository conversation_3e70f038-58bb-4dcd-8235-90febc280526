import '../../style/_main.css'
import type { Metada<PERSON> } from 'next'
import { Montserrat } from 'next/font/google'
import Script from 'next/script'
import React from 'react'
import { cn } from '@components/lib/utils'

const nextFont = Montserrat({
  subsets: ['latin', 'cyrillic', 'cyrillic-ext'],
  weight: ['400', '500', '700'],
  display: 'auto',
  preload: true,
})

export const metadata: Metadata = {
  title: 'PRAKTIS, DIY Store - За повече усмивки в дома и градината',
  description: 'PRAKTIS, DIY Store - За повече усмивки в дома и градината',
  icons: ['/favicon.png'],
}

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="bg">
      <head>
        <meta charSet="utf-8" />
      </head>
      <body className={nextFont.className}>{children}</body>
    </html>
  )
}
