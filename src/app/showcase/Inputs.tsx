'use client'

import { LucideBatteryWarning, LucideShieldAlert } from 'lucide-react'
import { useState } from 'react'

import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { Input } from '@components/theme/ui/input'

const ShowcaseInputs = () => {
  const [size, setSize] = useState('default')
  const [withIcon, setWithIcon] = useState(false)
  const [withText, setWithText] = useState(true)
  const [buttonText, setButtonText] = useState('')

  return (
    <div className="flex flex-col">
      <div className="p-3 font-bold flex justify-between">
        <div>Inputs</div>
        <div className="flex flex-col">
          <div>Icon</div>
          <div className="flex gap-2">
            <input
              type="checkbox"
              id="with-icon"
              onClick={() => {
                setWithIcon(!withIcon)
              }}
            />
            <label htmlFor="with-icon">with icon?</label>
          </div>
        </div>
        <div className="flex flex-col">
          <div>Size</div>
          <div className="flex gap-3">
            <button className={cn(size === 'default' && 'underline')} onClick={() => setSize('default')}>
              default
            </button>
            <button className={cn(size === 'sm' && 'underline')} onClick={() => setSize('sm')}>
              sm
            </button>
            <button className={cn(size === 'lg' && 'underline')} onClick={() => setSize('lg')}>
              lg
            </button>
            <button className={cn(size === 'icon' && 'underline')} onClick={() => setSize('icon')}>
              icon
            </button>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <Input type="text" placeholder="Placeholder here for your beautiful input..." />
        <div className="text-xs flex gap-3 flex-wrap">
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="flex">flex</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="h-9">h-9</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="w-full">w-full</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="rounded-md">rounded-md</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="border">border</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="border-input">border-input</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="bg-transparent">bg-transparent</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="px-3">px-3</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="py-1">py-1</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="text-base">text-base</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="shadow-sm">shadow-sm</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="transition-colors">transition-colors</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="file:border-0">file:border-0</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="file:bg-transparent">file:bg-transparent</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="file:text-sm">file:text-sm</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="file:font-medium">file:font-medium</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="file:text-foreground">file:text-foreground</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="placeholder:text-muted-foreground">placeholder:text-muted-foreground</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="focus-visible:outline-none">focus-visible:outline-none</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="focus-visible:ring-1">focus-visible:ring-1</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="focus-visible:ring-ring">focus-visible:ring-ring</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="disabled:cursor-not-allowed">disabled:cursor-not-allowed</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="disabled:opacity-50">disabled:opacity-50</span>
          </span>
          <span className="hover:bg-gray-200 px-2 py-1 rounded-xl">
            <span className="md:text-sm">md:text-sm</span>
          </span>
        </div>
      </div>
    </div>
  )
}

export default ShowcaseInputs
