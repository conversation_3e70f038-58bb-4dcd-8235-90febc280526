import React, { useMemo } from 'react'
import { SimpleProductViewFragment, ProductViewFragment, SimpleProduct } from '@lib/_generated/graphql_sdk'
import { Card } from '@components/theme/ui/card'
import { Input } from '@components/theme/ui/input'
import { RadioGroup, RadioGroupItem } from '@components/theme/ui/radio-group'
import { Label } from '@components/theme/ui/label'

// Import the SiteStatusHelper from our converted TypeScript file
// In a real application, you'd import from your actual file path
const SiteStatusHelper = (() => {
  // Status constants
  const NO_STATUS = 0
  const NOT_ACTIVE = 1
  const DEPLETED = 2
  const AVAILABLE = 3
  const AVAILABLE_PICKUP = 4
  const AWAITING_DELIVERY = 5
  const AWAITING_DELIVERY_PICKUP = 6
  const INDIVIDUAL_ORDER = 7
  const INDIVIDUAL_ORDER_PICKUP = 8
  const ONLY_IN_STORE = 9
  const LIMITED_VISIBILITY = 10

  // Zeron site status constants
  const ZERON_SITE_STATUS_1 = '1'
  const ZERON_SITE_STATUS_2 = '2'
  const ZERON_SITE_STATUS_3 = '3'
  const ZERON_SITE_STATUS_4 = '4'
  const ZERON_SITE_STATUS_5 = '5'

  class Helper {
    cachedStatus: { [key: string]: number }
    constructor() {
      this.cachedStatus = {}
    }

    isSalable(product: SimpleProductViewFragment) {
      return (
        !this.isDepleted(product) &&
        !this.isNotActive(product) &&
        !this.isLimitedVisibility(product) &&
        !this.isOnlyInStore(product)
      )
    }

    isProductOnlyAvailableForStorePickup(product: SimpleProductViewFragment) {
      return (
        this.isAvailableFromStore(product) ||
        this.isAwaitingDeliveryFromStore(product) ||
        this.isAllowedForIndividualOrderFromStore(product)
      )
    }

    isOnlyInStore(product: SimpleProductViewFragment) {
      return this.getStatus(product) === ONLY_IN_STORE
    }

    isLimitedVisibility(product: SimpleProductViewFragment) {
      return this.getStatus(product) === LIMITED_VISIBILITY
    }

    isAvailable(product: SimpleProductViewFragment) {
      return this.getStatus(product) === AVAILABLE
    }

    isAvailableFromStore(product: SimpleProductViewFragment) {
      return this.getStatus(product) === AVAILABLE_PICKUP
    }

    isAwaitingDelivery(product: SimpleProductViewFragment) {
      return this.getStatus(product) === AWAITING_DELIVERY
    }

    isAwaitingDeliveryFromStore(product: SimpleProductViewFragment) {
      return this.getStatus(product) === AWAITING_DELIVERY_PICKUP
    }

    isDepleted(product: SimpleProductViewFragment) {
      return this.getStatus(product) === DEPLETED
    }

    isNotActive(product: SimpleProductViewFragment) {
      return this.getStatus(product) === NOT_ACTIVE
    }

    isAllowedForIndividualOrder(product: SimpleProductViewFragment) {
      return this.getStatus(product) === INDIVIDUAL_ORDER
    }

    isAllowedForIndividualOrderFromStore(product: SimpleProductViewFragment) {
      return this.getStatus(product) === INDIVIDUAL_ORDER_PICKUP
    }

    getStatus(product: SimpleProductViewFragment) {
      if (this.cachedStatus[product.id]) {
        return this.cachedStatus[product.id]
      }

      const { inStock, manageStock } = product.stock
      const { blockForSale: blockedForSales, zeronSiteStatus, zeronBlockedDelivery } = product.stock
      const hasImages = this.hasImages(product)

      const value = this.getStatusValue(
        blockedForSales,
        zeronSiteStatus,
        zeronBlockedDelivery,
        manageStock,
        inStock,
        hasImages
      )

      this.setStatus(product, value)
      return value
    }

    setStatus(product: SimpleProductViewFragment, value: number) {
      this.cachedStatus[product.id] = value
      return this
    }

    getStatusValue(
      blockedForSales: boolean,
      zeronSiteStatus: string,
      zeronBlockedDelivery: boolean,
      manageStock: boolean,
      inStock: boolean,
      hasImages: boolean
    ) {
      const showOutOfStock = true
      let status

      if (!hasImages) {
        status = NOT_ACTIVE
      } else if (blockedForSales) {
        status = DEPLETED
      } else if (manageStock) {
        if (zeronSiteStatus === ZERON_SITE_STATUS_1) {
          if (inStock) {
            status = AVAILABLE
          } else if (showOutOfStock) {
            if (zeronBlockedDelivery) {
              status = DEPLETED
            } else {
              status = AWAITING_DELIVERY
            }
          } else {
            status = DEPLETED
          }
        } else if (zeronSiteStatus === ZERON_SITE_STATUS_3) {
          if (!inStock) {
            status = DEPLETED
          } else {
            status = ONLY_IN_STORE
          }
        } else if (zeronSiteStatus === ZERON_SITE_STATUS_4 || zeronSiteStatus === ZERON_SITE_STATUS_5) {
          status = LIMITED_VISIBILITY
        } else {
          if (inStock) {
            status = AVAILABLE_PICKUP
          } else if (showOutOfStock) {
            if (zeronBlockedDelivery) {
              status = DEPLETED
            } else {
              status = AWAITING_DELIVERY_PICKUP
            }
          } else {
            status = DEPLETED
          }
        }
      } else {
        if (zeronBlockedDelivery) {
          status = DEPLETED
        } else if (zeronSiteStatus === ZERON_SITE_STATUS_3) {
          if (!inStock) {
            status = DEPLETED
          } else {
            status = ONLY_IN_STORE
          }
        } else if (zeronSiteStatus === ZERON_SITE_STATUS_4 || zeronSiteStatus === ZERON_SITE_STATUS_5) {
          status = LIMITED_VISIBILITY
        } else {
          if (zeronSiteStatus === ZERON_SITE_STATUS_1) {
            status = INDIVIDUAL_ORDER
          } else {
            status = INDIVIDUAL_ORDER_PICKUP
          }
        }
      }

      return status
    }

    hasImages(product: SimpleProductViewFragment) {
      return product.gallery && product.gallery.length > 0
    }

    getStatusText(status: number) {
      switch (status) {
        case AVAILABLE:
          return 'Available'
        case AVAILABLE_PICKUP:
          return 'Available for Pickup'
        case AWAITING_DELIVERY:
          return 'Awaiting Delivery'
        case AWAITING_DELIVERY_PICKUP:
          return 'Awaiting Delivery (Pickup)'
        case INDIVIDUAL_ORDER:
          return 'Available by Individual Order'
        case INDIVIDUAL_ORDER_PICKUP:
          return 'Available by Individual Order (Pickup)'
        case ONLY_IN_STORE:
          return 'Available Only In Store'
        case DEPLETED:
          return 'Out of Stock'
        case NOT_ACTIVE:
          return 'Not Active'
        case LIMITED_VISIBILITY:
          return 'Limited Visibility'
        default:
          return 'Unknown Status'
      }
    }
  }

  return {
    Helper,
    NO_STATUS,
    NOT_ACTIVE,
    DEPLETED,
    AVAILABLE,
    AVAILABLE_PICKUP,
    AWAITING_DELIVERY,
    AWAITING_DELIVERY_PICKUP,
    INDIVIDUAL_ORDER,
    INDIVIDUAL_ORDER_PICKUP,
    ONLY_IN_STORE,
    LIMITED_VISIBILITY,
    ZERON_SITE_STATUS_1,
    ZERON_SITE_STATUS_2,
    ZERON_SITE_STATUS_3,
    ZERON_SITE_STATUS_4,
    ZERON_SITE_STATUS_5,
  }
})()

const ProductStatusExample = () => {
  const [config, setConfig] = React.useState({
    blockForSale: false,
    hasImages: true,
    inStock: true,
    manageStock: true,
    qty: 10,
    zeronBlockedDelivery: false,
    zeronSiteStatus: '1',
  })

  const setConfigParam = (param: string, value: any) => {
    setConfig((prev) => ({
      ...prev,
      [param]: value,
    }))
  }

  // Sample product data structure based on the GraphQL type
  const sampleProduct = useMemo(
    () => ({
      id: '1',
      name: 'Sample Product',
      gallery: [{ position: 1, image: { src: 'example.jpg' } }],
      stock: {
        ...config,
      },
    }),
    [config]
  ) as SimpleProductViewFragment

  // Create an instance of the helper
  const statusHelper = new SiteStatusHelper.Helper()

  // Get the product status
  const status = statusHelper.getStatus(sampleProduct)

  // Get a user-friendly status text
  const statusText = statusHelper.getStatusText(status)

  // Check if the product is salable
  const isSalable = statusHelper.isSalable(sampleProduct)

  return (
    <div className="flex gap-3">
      <Card className="flex-1">
        <table>
          <tbody>
            <tr>
              <td>blockForSale</td>
              <td>
                <RadioGroup className="flex">
                  <RadioGroupItem
                    value="true"
                    id="blockForSale-true"
                    onClick={() => setConfigParam('blockForSale', true)}
                    title="x"
                  />
                  <Label htmlFor="blockForSale-true">True</Label>
                  <RadioGroupItem
                    value="false"
                    id="blockForSale-false"
                    onClick={() => setConfigParam('blockForSale', false)}
                  />
                  <Label htmlFor="blockForSale-false">False</Label>
                </RadioGroup>
              </td>
            </tr>
            <tr>
              <td>hasImages</td>
              <td>
                <RadioGroup className="flex">
                  <RadioGroupItem value="true" id="hasImages-true" onClick={() => setConfigParam('hasImages', true)} />
                  <Label htmlFor="hasImages-true">True</Label>
                  <RadioGroupItem
                    value="false"
                    id="hasImages-false"
                    onClick={() => setConfigParam('hasImages', false)}
                  />
                  <Label htmlFor="hasImages-false">False</Label>
                </RadioGroup>
              </td>
            </tr>
            <tr>
              <td>inStock</td>
              <td>
                <RadioGroup className="flex">
                  <RadioGroupItem value="true" id="inStock-true" onClick={() => setConfigParam('inStock', true)} />
                  <Label htmlFor="inStock-true">True</Label>
                  <RadioGroupItem value="false" id="inStock-false" onClick={() => setConfigParam('inStock', false)} />
                  <Label htmlFor="inStock-false">False</Label>
                </RadioGroup>
              </td>
            </tr>
            <tr>
              <td>manageStock</td>
              <td>
                <RadioGroup className="flex">
                  <RadioGroupItem
                    value="true"
                    id="manageStock-true"
                    onClick={() => setConfigParam('manageStock', true)}
                  />
                  <Label htmlFor="manageStock-true">True</Label>
                  <RadioGroupItem
                    value="false"
                    id="manageStock-false"
                    onClick={() => setConfigParam('manageStock', false)}
                  />
                  <Label htmlFor="manageStock-false">False</Label>
                </RadioGroup>
              </td>
            </tr>
            <tr>
              <td>qty</td>
              <td>
                <Input
                  type="number"
                  onChange={(v) => setConfigParam('qty', parseInt(v.target.value, 10))}
                  value={config.qty || 0}
                />
              </td>
            </tr>
            <tr>
              <td>zeronBlockedDelivery</td>
              <td>
                <RadioGroup className="flex">
                  <RadioGroupItem
                    value="true"
                    id="zeronBlockedDelivery-true"
                    onClick={() => setConfigParam('zeronBlockedDelivery', true)}
                  />
                  <Label htmlFor="zeronBlockedDelivery-true">True</Label>
                  <RadioGroupItem
                    value="false"
                    id="zeronBlockedDelivery-false"
                    onClick={() => setConfigParam('zeronBlockedDelivery', false)}
                  />
                  <Label htmlFor="zeronBlockedDelivery-false">False</Label>
                </RadioGroup>
              </td>
            </tr>
            <tr>
              <td>zeronSiteStatus</td>
              <td>
                <RadioGroup className="flex">
                  <RadioGroupItem
                    value="1"
                    id="zeronSiteStatus-1"
                    onClick={() => setConfigParam('zeronSiteStatus', '1')}
                  />
                  <Label htmlFor="zeronSiteStatus-1">1</Label>
                  <RadioGroupItem
                    value="2"
                    id="zeronSiteStatus-2"
                    onClick={() => setConfigParam('zeronSiteStatus', '2')}
                  />
                  <Label htmlFor="zeronSiteStatus-2">2</Label>
                  <RadioGroupItem
                    value="3"
                    id="zeronSiteStatus-3"
                    onClick={() => setConfigParam('zeronSiteStatus', '3')}
                  />
                  <Label htmlFor="zeronSiteStatus-3">3</Label>
                  <RadioGroupItem
                    value="4"
                    id="zeronSiteStatus-4"
                    onClick={() => setConfigParam('zeronSiteStatus', '4')}
                  />
                  <Label htmlFor="zeronSiteStatus-4">4</Label>
                  <RadioGroupItem
                    value="5"
                    id="zeronSiteStatus-5"
                    onClick={() => setConfigParam('zeronSiteStatus', '5')}
                  />
                  <Label htmlFor="zeronSiteStatus-5">5</Label>
                </RadioGroup>
              </td>
            </tr>
          </tbody>
        </table>
      </Card>
      <div className="flex-1 p-4 border rounded-md shadow-sm bg-white">
        <h2 className="text-xl font-bold mb-4">Product Status</h2>

        <div className="mb-2">
          <span className="font-semibold">Product:</span> {sampleProduct.name}
        </div>

        <div className="mb-2">
          <span className="font-semibold">Status:</span>{' '}
          <span
            className={`px-2 py-1 rounded-full text-sm ${
              isSalable ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}
          >
            {statusText}
          </span>
        </div>

        <div className="mb-2">
          <span className="font-semibold">Salable:</span>{' '}
          {isSalable ? <span className="text-green-600">Yes</span> : <span className="text-red-600">No</span>}
        </div>

        <div className="mb-2">
          <span className="font-semibold">In Stock:</span>{' '}
          {sampleProduct.stock.inStock ? (
            <span className="text-green-600">Yes</span>
          ) : (
            <span className="text-red-600">No</span>
          )}
        </div>

        <div className="mb-2">
          <span className="font-semibold">Quantity:</span> {sampleProduct.stock.qty}
        </div>

        {statusHelper.isAwaitingDelivery(sampleProduct) && (
          <div className="mt-4 p-2 bg-yellow-100 text-yellow-800 rounded">
            This product is currently awaiting delivery.
          </div>
        )}

        {statusHelper.isProductOnlyAvailableForStorePickup(sampleProduct) && (
          <div className="mt-4 p-2 bg-blue-100 text-blue-800 rounded">
            This product is only available for store pickup.
          </div>
        )}
      </div>
    </div>
  )
}

export default ProductStatusExample
