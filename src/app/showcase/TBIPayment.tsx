import React, { useState, useEffect } from 'react'
import { SimpleProductViewFragment } from '@lib/_generated/graphql_sdk'
import TBIBankLogo from '@images/tbi-bank-logo.inline.svg'
import { Button } from '@components/theme/ui/button'
import useBreakpoint from '@/src/hooks/useBreakpoint'

interface TBIBankPaymentProps {
  product: SimpleProductViewFragment
  quantity?: number
  clientId?: string
  creditData?: {
    tbi_bnpl: string
    tbi_minstojnost_bnpl: string
    tbi_maxstojnost_bnpl: string
    tbi_minstojnost: string
    tbi_maxstojnost: string
  }
}
// Use this component in your app
export const TBIBankPayment: React.FC<TBIBankPaymentProps> = ({
  product,
  quantity = 1,
  clientId = 'a552fb11-8445-46d4-802c-098f796f417c', // Default from your HTML
  creditData,
}) => {
  const [mounted, setMounted] = useState(false)
  const [selectedOption, setSelectedOption] = useState('lizing') // default to leasing option
  const [installmentData, setInstallmentData] = useState({
    firstInstallment: 0,
    remaining: 0,
  })

  const { maxBreakpoint } = useBreakpoint()

  useEffect(() => {
    setMounted(true)

    // Calculate installments based on price
    if (product && product.price) {
      const price = product.price.special?.value || product.price.price.value || 0
      const totalPrice = price * quantity
      const firstInstallment = totalPrice / 4
      const remaining = totalPrice - totalPrice / 4

      setInstallmentData({
        firstInstallment,
        remaining,
      })
    }
  }, [product, quantity])

  if (!mounted || !product) {
    return null
  }

  const price = product.price.special?.value || product.price.price.value || 0
  const totalPrice = price * quantity
  const encodedProductName = encodeURIComponent(product.name)
  const productCategory = ''

  // Handle view payment schemes
  const handleViewPaymentSchemes = () => {
    const baseUrl = process.env.NEXT_PUBLIC_TBI_BANK_URL || 'https://bnpl.tbibank.support'

    // Construct URL based on client type
    let url
    if (maxBreakpoint('xl')) {
      url = `${baseUrl}/application_infom.php?bnpl_product_m=&bnpl_product_c=${productCategory}&name=${encodedProductName}&price=${totalPrice}&qt=${quantity}&unicid=${clientId}`
    } else {
      url = `${baseUrl}/application_info.php?bnpl_product_m=&bnpl_product_c=${productCategory}&name=${encodedProductName}&price=${totalPrice}&qt=${quantity}&unicid=${clientId}`
    }

    // Open in new window/tab
    if (typeof window !== 'undefined') {
      window.open(url, '_blank')
    }
  }

  // const handleDirectCredit = async () => {
  //   const baseUrl = process.env.NEXT_PUBLIC_TBI_BANK_URL || 'https://bnpl.tbibank.support'
  //
  //   // Determine BNPL option based on selection
  //   let bnplOption = 3 // Default to leasing
  //   if (selectedOption === 'bnpl1') {
  //     bnplOption = 1
  //   } else if (selectedOption === 'bnpl2') {
  //     bnplOption = 2
  //   }
  //
  //   // Only proceed client-side
  //   if (typeof window !== 'undefined') {
  //     try {
  //       // Create FormData for the POST request
  //       const formData = new FormData()
  //       formData.append('bnpl_cid', clientId)
  //       formData.append('bnpl_price', String(totalPrice))
  //       formData.append('bnpl_product_id', product.id)
  //       formData.append('bnpl_product_name_txt', product.name)
  //       formData.append('bnpl_product_c', productCategory)
  //       formData.append('bnpl_product_i', '') // Product image as base64
  //       formData.append('BNPL_MOD_VERSION', '1.0')
  //       formData.append('bnpl_type_client', clientType)
  //       formData.append('bnpl_product_q', quantity)
  //
  //       // Make POST request
  //       const response = await fetch(`${baseUrl}/function/adddirectorder.php`, {
  //         method: 'POST',
  //         body: formData,
  //       })
  //
  //       const data = await response.json()
  //
  //       if (parseInt(data.order_id) !== 0) {
  //         // Determine redirect URL based on client type and BNPL option
  //         let redirectUrl
  //
  //         if (maxBreakpoint('xl')) {
  //           if (bnplOption === 1 || bnplOption === 2) {
  //             redirectUrl = `${baseUrl}/applicationdirectinfom.php?oid=${data.order_id}&cid=${clientId}&bnpl=${bnplOption}`
  //           } else {
  //             redirectUrl = `${baseUrl}/applicationm_step1.php?oid=${data.order_id}&cid=${clientId}`
  //           }
  //         } else {
  //           if (bnplOption === 1 || bnplOption === 2) {
  //             redirectUrl = `${baseUrl}/applicationdirectinfo.php?oid=${data.order_id}&cid=${clientId}&bnpl=${bnplOption}`
  //           } else {
  //             redirectUrl = `${baseUrl}/application_step1.php?oid=${data.order_id}&cid=${clientId}`
  //           }
  //         }
  //
  //         // Redirect to TBI Bank
  //         window.location.href = redirectUrl
  //       } else {
  //         alert('Не можете да изпратите заявката! Моля опитайте по-късно.')
  //       }
  //     } catch (error) {
  //       console.error('Error submitting credit application:', error)
  //       alert('Error occurred. Please try again later.')
  //     }
  //   }
  // }

  // const showBNPL =
  //   creditData?.tbi_bnpl === 'Yes' &&
  //   totalPrice >= parseFloat(creditData?.tbi_minstojnost_bnpl || '40') &&
  //   totalPrice <= parseFloat(creditData?.tbi_maxstojnost_bnpl || '400')

  return (
    <div className="flex flex-col gap-3">
      <TBIBankLogo width={160} className="rounded-2xl" />
      <p>Вземи желания продукт сега, избери подходяща за теб схема на изплащане:</p>

      {/* Standard Leasing Option - Always shown */}
      <div>
        <div className="flex gap-2">
          <input
            type="radio"
            name="schema"
            id="lizing"
            checked={selectedOption === 'lizing'}
            onChange={() => setSelectedOption('lizing')}
          />
          <label htmlFor="lizing" className="text-primary font-bold">
            На равни вноски до 48 месеца
          </label>
        </div>
        <div></div>
      </div>

      <p className="leading-6">
        Не е нужно да избираш, може да имаш всичко!
        <br />
        {creditData?.tbi_minstojnost && creditData?.tbi_maxstojnost && (
          <>
            Купи на равни вноски стоки от {creditData?.tbi_minstojnost} до {creditData?.tbi_maxstojnost} лв., избери
            най-подходящата за теб вноска.
          </>
        )}
        <br />
        С TBI Pay, пазаруването е лесно, гъвкаво и напълно онлайн.
        <br />
        Контролът е в твоите ръце, не чакай.
        <br />
        Пазарувай!
      </p>
      <div className="flex items-end justify-end">
        <Button onClick={handleViewPaymentSchemes}>Виж схемите, които предлагаме</Button>
      </div>
    </div>
  )
}
