'use client'

import { LucideShieldAlert } from 'lucide-react'
import { useState } from 'react'

import { cn } from '@components/lib/utils'
import { ButtonIcon } from '@components/molecules/ButtonIcon'
import { Button } from '@components/theme/ui/button'
import HeadphonesIcon from '@icons/headphones.inline.svg'

const ShowcaseButtons = () => {
  const [buttonSize, setButtonSize] = useState<'default' | 'sm' | 'lg' | 'icon' | null | undefined>('default')
  const [withIcon, setWithIcon] = useState(false)
  const [withText, setWithText] = useState(true)
  const [buttonText, setButtonText] = useState('')

  return (
    <div className="flex flex-col">
      <div className="p-3 font-bold flex justify-between">
        <div>Buttons</div>
        <div className="flex flex-col">
          <div>Text</div>
          <div className="flex gap-2">
            <input
              type="text"
              id="button-text"
              className="border border-gray-300"
              onChange={(e) => {
                setButtonText(e.target.value)
              }}
            />
            <input
              type="checkbox"
              id="with-text"
              checked={withText}
              onChange={() => {
                setWithText(!withText)
              }}
            />
            <label htmlFor="with-text">with Text?</label>
          </div>
        </div>
        <div className="flex flex-col">
          <div>Icon</div>
          <div className="flex gap-2">
            <input
              type="checkbox"
              id="with-icon"
              onClick={() => {
                setWithIcon(!withIcon)
              }}
            />
            <label htmlFor="with-icon">with icon?</label>
          </div>
        </div>
        <div className="flex flex-col">
          <div>Size</div>
          <div className="flex gap-3">
            <button className={cn(buttonSize === 'default' && 'underline')} onClick={() => setButtonSize('default')}>
              default
            </button>
            <button className={cn(buttonSize === 'sm' && 'underline')} onClick={() => setButtonSize('sm')}>
              sm
            </button>
            <button className={cn(buttonSize === 'lg' && 'underline')} onClick={() => setButtonSize('lg')}>
              lg
            </button>
            <button className={cn(buttonSize === 'icon' && 'underline')} onClick={() => setButtonSize('icon')}>
              icon
            </button>
          </div>
        </div>
      </div>
      <div className="flex gap-2">
        <Button size={buttonSize} variant="default">
          {withIcon && <LucideShieldAlert />}
          {withText ? buttonText || 'Primary' : undefined}
        </Button>
        <Button size={buttonSize} variant="outline">
          {withIcon && <LucideShieldAlert />}
          {withText ? buttonText || 'Outline' : undefined}
        </Button>
        <Button size={buttonSize} variant="secondary">
          {withIcon && <LucideShieldAlert />}
          {withText ? buttonText || 'Secondary' : undefined}
        </Button>
        <Button size={buttonSize} variant="tertiary">
          {withIcon && <LucideShieldAlert />}
          {withText ? buttonText || 'Tertiary' : undefined}
        </Button>
        <Button size={buttonSize} variant="inverse">
          {withIcon && <LucideShieldAlert />}
          {withText ? buttonText || 'Inverse' : undefined}
        </Button>
        <Button size={buttonSize} variant="link">
          {withIcon && <LucideShieldAlert />}
          {withText ? buttonText || 'Link' : undefined}
        </Button>
        <Button size={buttonSize} variant="destructive">
          {withIcon && <LucideShieldAlert />}
          {withText ? buttonText || 'Destructive' : undefined}
        </Button>
        <Button size={buttonSize} variant="ghost">
          {withIcon && <LucideShieldAlert />}
          {withText ? buttonText || 'Ghost' : undefined}
        </Button>
      </div>
      <div className="flex gap-12 items-start mt-5">
        <ButtonIcon
          size={buttonSize}
          icon={<HeadphonesIcon />}
          label="label sec line thirdif possible forth"
          variant="default"
        />
        <ButtonIcon
          size={buttonSize}
          icon={<HeadphonesIcon />}
          label="label sec line thirdif possib"
          variant="outline"
        />
        <ButtonIcon size={buttonSize} icon={<HeadphonesIcon />} label="label sec line" variant="secondary" />
        <ButtonIcon size={buttonSize} icon={<HeadphonesIcon />} label="label sec line" variant="tertiary" />
        <ButtonIcon size={buttonSize} icon={<HeadphonesIcon />} label="label sec" variant="inverse" />
        <ButtonIcon size={buttonSize} icon={<HeadphonesIcon />} label="label sec" variant="link" />
        <ButtonIcon size={buttonSize} icon={<HeadphonesIcon />} label="label" variant="destructive" />
        <ButtonIcon size={buttonSize} icon={<HeadphonesIcon />} label="label" variant="ghost" />
      </div>
    </div>
  )
}

export default ShowcaseButtons
