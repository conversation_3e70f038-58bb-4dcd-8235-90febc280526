'use client'

import { LucideBatteryWarning, LucideShieldAlert } from 'lucide-react'
import { useEffect, useState } from 'react'

import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { Input } from '@components/theme/ui/input'

const ShowcaseTexts = () => {
  const [size, setSize] = useState('default')
  const [withIcon, setWithIcon] = useState(false)
  const [withText, setWithText] = useState(true)
  const [buttonText, setButtonText] = useState('')

  useEffect(() => {
    setTimeout(() => {
      const t = ['2xs', '3xs', 'xs', 'sm', 'base', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl', '6xl']
      t.forEach((size) => {
        const element = document.getElementById(`text-${size}`)
        if (element) {
          const style = window.getComputedStyle(element)
          const fs = style.getPropertyValue('font-size')
          const lh = style.getPropertyValue('line-height')
          const j = document.getElementById(`css-text-${size}`)
          if (j) j.innerText = `${fs} / ${lh} / ${parseInt(fs, 10) / 16}rem`
        }
      })
    }, 1000)
    // if (element) {
    //   const style = window.getComputedStyle(element);
    //   const top = style.getPropertyValue('top');
    //   console.log(top);
    // }
  }, [])
  return (
    <div className="grid grid-cols-4 gap-x-4">
      <span className="text-3xs text-right">3xs</span>
      <span id="text-3xs" className="text-3xs col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-3xs">
        ...
      </span>

      <span className="text-2xs text-right">2xs</span>
      <span id="text-2xs" className="text-2xs col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-2xs">
        ...
      </span>

      <span className="text-xs text-right">xs</span>
      <span id="text-xs" className="text-xs col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-xs">
        ...
      </span>

      <span className="text-sm text-right">sm</span>
      <span id="text-sm" className="text-sm col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-sm">
        ...
      </span>

      <span className="text-base text-right">base</span>
      <span id="text-base" className="text-base col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-base">
        ...
      </span>

      <span className="text-lg text-right">lg</span>
      <span id="text-lg" className="text-lg col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-lg">
        ...
      </span>

      <span className="text-xl text-right">xl</span>
      <span id="text-xl" className="text-xl col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-xl">
        ...
      </span>

      <span className="text-2xl text-right">2xl</span>
      <span id="text-2xl" className="text-2xl col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-2xl">
        ...
      </span>

      <span className="text-3xl text-right">3xl</span>
      <span id="text-3xl" className="text-3xl col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-3xl">
        ...
      </span>

      <span className="text-4xl text-right">4xl</span>
      <span id="text-4xl" className="text-4xl col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-4xl">
        ...
      </span>

      <span className="text-5xl text-right">5xl</span>
      <span id="text-5xl" className="text-5xl col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-5xl">
        ...
      </span>

      <span className="text-6xl text-right">6xl</span>
      <span id="text-6xl" className="text-6xl col-span-2">
        Lorem ipsum
      </span>
      <span className="text-xs flex items-center font-[monospace]" id="css-text-6xl">
        ...
      </span>
    </div>
  )
}

export default ShowcaseTexts
