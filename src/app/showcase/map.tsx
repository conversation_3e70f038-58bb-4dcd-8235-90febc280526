'use client'

import React, { useState } from 'react'
import { cn } from '@components/lib/utils'

const tableData = [
  { id: 1, field1: 'Row 1', field2: 'Row 1 Field 2', field3: 'Row 1 Field 3' },
  { id: 2, field1: 'Row 2', field2: 'Row 2 Field 2', field3: 'Row 2 Field 3' },
  { id: 3, field1: 'Row 3', field2: 'Row 3 Field 2', field3: 'Row 3 Field 3' },
  { id: 4, field1: 'Row 4', field2: 'Row 4 Field 2', field3: 'Row 4 Field 3' },
  { id: 5, field1: 'Row 5', field2: 'Row 5 Field 2', field3: 'Row 5 Field 3' },
]

const Map = ({ order }: { order: string }) => {
  return (
    <div className={cn(`border border-green-600 order-${order} md:absolute w-[40%] top-0 bottom-0 md:right-0`)}>
      Map {order}
    </div>
  )
}

const ResponsiveTableWithMap = () => {
  const [order, setOrder] = useState(4)

  // Determine map order
  const mapOrder = order === 0 ? 0 : order === tableData.length - 1 ? 'last' : order

  // Calculate row order based on map position
  const getOrderRow = (index: number) => {
    // First row is always first
    if (index === 0) return 'first'

    // Last row
    if (index === tableData.length - 1) {
      // If map is positioned at last, use index-1
      return order === tableData.length - 1 ? index - 1 : 'last'
    }

    // Middle rows
    // If map is before this row, use index
    // If map is at or after this row, use index-1
    return order < index ? index : index - 1
  }

  return (
    <div className="flex flex-1 w-full">
      <div className="flex flex-col border border-red-600 w-full relative">
        {tableData.map((row, i) => {
          const rowOrder = getOrderRow(i)
          return (
            <div key={row.id} onClick={() => setOrder(i)} className={cn(`order-${rowOrder}`)}>
              <div className="flex flex-row gap-4">
                <div className="w-12">{rowOrder}</div>
                <div className="flex-1">{row.field1}</div>
                <div className="flex-1">{i}</div>
              </div>
            </div>
          )
        })}
        <Map order={String(mapOrder)} />
      </div>
    </div>
  )
}

export default ResponsiveTableWithMap
