import Image from 'next/image'
import Link from 'next/link'
import { notFound } from 'next/navigation'

import Container from '@components/atoms/Container'
import Title from '@components/atoms/Title'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import { Button } from '@components/theme/ui/button'
import { GraphQLBackend } from '@lib/api/graphql'
import { CMSMarkup } from '@/src/components/molecules/Widget/markup/instanced'
import { getStaticContent } from '@/src/features/static/api'
import { staticSelectors } from '@/src/features/static/selectors'

type BlogPostPageProps = {
  params: Promise<{ slug: string }>
}

const BlogPostPage = async ({ params }: BlogPostPageProps) => {
  const { slug } = await params
  const staticContent = await getStaticContent()
  const baseUrl = staticSelectors.selectStoreBaseUrl(staticContent)

  const post = await GraphQLBackend.GetBlogPost({
    identifier: slug,
  })
    .then((response) => {
      if (!response?.getBlogPost) {
        notFound()
      }
      return response.getBlogPost
    })
    .catch(notFound)

  return (
    <Container>
      <Breadcrumb
        breadcrumbs={[
          { url: '/', label: '' },
          { url: '/news', label: 'Новини' },
          { url: '#', label: post.title },
        ]}
      />

      <meta name="robots" content="INDEX,FOLLOW" />
      <link rel="canonical" href={`${baseUrl}/news/${slug}`} />
      <article className="max-w-4xl mx-auto py-8">
        {post.mainImageUrl && (
          <div className="mb-8 h-80 md:h-96 overflow-hidden rounded-lg relative">
            <Image src={post.mainImageUrl} alt={post.title} fill className="object-cover" />
          </div>
        )}

        <div className="mb-6">
          <p className="text-gray-500 mb-2">{post.publishedAt}</p>
          <Title className="text-3xl font-bold mb-4">{post.title}</Title>
          <p className="text-xl text-gray-700 mb-8">{post.summary}</p>
        </div>
        <CMSMarkup markup={post.content} />

        <div className="mt-10 pt-6 border-t border-gray-200">
          <Link href="/news">
            <Button variant="default">Още Новини</Button>
          </Link>
        </div>
      </article>
    </Container>
  )
}

export default BlogPostPage
