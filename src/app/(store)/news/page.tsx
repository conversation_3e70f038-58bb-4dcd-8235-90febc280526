import Image from 'next/image'
import Link from 'next/link'
import { notFound } from 'next/navigation'

import Container from '@components/atoms/Container'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import { Button } from '@components/theme/ui/button'
import { SortDirection } from '@lib/_generated/graphql_sdk'
import { GraphQLBackend } from '@lib/api/graphql'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'
import { getStaticContent } from '@/src/features/static/api'
import { staticSelectors } from '@/src/features/static/selectors'

type NewsPageProps = {
  searchParams: Promise<{ p?: string }>
}

const PageSize = 12

const NewsPage = async ({ searchParams }: NewsPageProps) => {
  const currentPage = await searchParams.then((s) => (s.p ? parseInt(s.p) : 1))
  const staticContent = await getStaticContent()
  const baseUrl = staticSelectors.selectStoreBaseUrl(staticContent)

  const { posts, totalPages } = await GraphQLBackend.GetBlogPosts({
    page: currentPage,
    size: PageSize,
  })
    .then((response) => ({
      posts: response?.getBlogPosts?.posts ?? [],
      totalPages: response?.getBlogPosts?.totalPages ?? 1,
    }))
    .catch(notFound)

  return (
    <Container>
      <Breadcrumb
        breadcrumbs={[
          { url: '/', label: '' },
          { url: '#', label: 'Новини' },
        ]}
        hasPagination={true}
        pagination={{
          pager: {
            page: currentPage,
            pageSize: PageSize,
            totalPages: totalPages,
            totalItems: 0,
          },
          sort: {
            value: 'publishedAt',
            dir: SortDirection.Asc,
          },
        }}
      />
      <meta name="robots" content="INDEX,FOLLOW" />
      <link rel="canonical" href={`${baseUrl}/news${currentPage > 1 ? `?p=${currentPage}` : ''}`} />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
        {posts.map((post, index) => (
          <div key={index} className="border rounded-lg overflow-hidden shadow-md flex flex-col h-full">
            <div className="h-64 relative">
              {post.previewImageUrl && (
                <Image src={post.previewImageUrl} alt={post.title} fill className="object-cover" />
              )}
            </div>
            <div className="p-4 flex flex-col flex-grow">
              <p className="text-sm text-gray-500 mb-2">{post.publishedAt}</p>
              <h3 className="text-xl font-semibold mb-2 line-clamp-2">{post.title}</h3>
              <p className="text-gray-600 mb-4 line-clamp-3">{post.summary}</p>
              <div className="mt-auto">
                <Link href={post.urlKey}>
                  <Button variant="default" size="default">
                    Виж повече
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
      <RelevaTrackPage />
    </Container>
  )
}

export default NewsPage
