import React from 'react'

import Container from '@atoms/Container'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import { NextPageProps, SlugParam } from '@lib/utils/page'

export default async function ProductPage({ params }: NextPageProps<SlugParam>) {
  return (
    <Container className="pb-10">
      <div className="mb-4 mt-4 desktop:mt-0">
        <Breadcrumb breadcrumbs={[{ url: '/', label: 'Home' }]} />
      </div>
      {/*<ProductPageContent product={product} />*/}
      {/*<div className="mt-4 desktop:mt-14">*/}
      {/*  <ProductsSliderWidget widget={product.recommendedProducts} />*/}
      {/*</div>*/}
    </Container>
  )
}
