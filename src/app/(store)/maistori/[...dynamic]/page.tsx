import Link from 'next/dist/client/link'

import { getServices, getWorkers } from '@/src/app/(store)/maistori/api'
import { Workers } from '@/src/models/workersInterface'
import Container from '@atoms/Container'
import Text from '@atoms/Text'
import Title from '@atoms/Title'
import { cn } from '@components/lib/utils'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import { Card } from '@components/theme/ui/card'
import { ServiceFragment } from '@lib/_generated/graphql_sdk'
import { NextPageProps, SlugParam } from '@lib/utils/page'

import WorkersList from '../_components/WorkersList'

import RegisterWorkerBlock from './_components/RegisterWorkerBlock'

export default async function WorkersPage(props: NextPageProps<SlugParam>) {
  const params = await props.params
  const { dynamic } = params
  let services: ServiceFragment[] = []
  try {
    services = await getServices()
  } catch (error) {
    console.error(error)
  }

  let workers: Workers[] = []
  try {
    workers = await getWorkers(dynamic[0])
  } catch (error) {
    console.error(error)
  }

  return (
    <Container>
      <Breadcrumb
        breadcrumbs={[
          { url: '/', label: 'Home' },
          { url: '#', label: 'Майстори' },
        ]}
      />
      <div className={cn('grid grid-cols-1 gap-6 my-6', 'xl:grid-cols-[30%_1fr]')}>
        <div>
          <div className="grid gap-6">
            <RegisterWorkerBlock services={services} />
            <Card className="w-full p-10 rounded-2xl shadow-xs border-none">
              <Title size="h3" className="m-0 mb-4 font-bold">
                Ако си търсите майстор
              </Title>

              <div>
                {services.length === 0 && <Text className="text-sm">Няма добавени услуги</Text>}
                {services &&
                  services.map((type: ServiceFragment, index: number) => (
                    <div key={type.id + index}>
                      <Link
                        href={type.url}
                        className={cn('rounded-3xl inline-flex px-4 py-3 w-full', 'hover:text-white hover:bg-primary')}
                      >
                        {type.name}
                      </Link>
                    </div>
                  ))}
              </div>
            </Card>
          </div>
        </div>
        <div className="overflow-auto">{workers && <WorkersList workersList={workers} />}</div>
      </div>
    </Container>
  )
}
