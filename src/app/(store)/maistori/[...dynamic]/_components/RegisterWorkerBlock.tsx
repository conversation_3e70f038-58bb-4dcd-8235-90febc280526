'use client'
import React from 'react'

import CustomButton from '@atoms/Button/CustomButton'
import Paper from '@atoms/Paper'
import Text from '@atoms/Text'
import Title from '@atoms/Title'
import Store_Modal from '@components/organisms/Store_Modal'
import { Button } from '@components/theme/ui/button'
import { Card } from '@components/theme/ui/card'
import { ServiceFragment } from '@lib/_generated/graphql_sdk'

import RegisterWorkerForm from './RegisterWorkerForm'

interface Props {
  services: ServiceFragment[]
}

const RegisterWorkerBlock: React.FC<Props> = ({ services }) => {
  return (
    <Card className="w-full p-10 rounded-2xl shadow-xs border-none">
      <Title size="h3" className="m-0 mb-4 text-xl font-bold">
        Ако сте майстор
      </Title>
      <Text className="text-sm">
        Регистрирайте се в нашия сайт, като попълните по желание своите име, фамилия, телефон, град, е-mail, вид услуга
        във формата за регистрация на майстори!
      </Text>
      <div className="mt-5">
        <Store_Modal
          title={'Ако сте майстор'}
          image={'/images/forms/worker-image.png'}
          trigger={<Button>Регистрирайте</Button>}
        >
          <Text>
            Регистрирайте се в нашия сайт, като попълните по желание своите име, фамилия, телефон, град, e-mail, вид
            услуга във формата за регистрация на майстори!
          </Text>
          <RegisterWorkerForm services={services} />
        </Store_Modal>
      </div>
    </Card>
  )
}

export default RegisterWorkerBlock
