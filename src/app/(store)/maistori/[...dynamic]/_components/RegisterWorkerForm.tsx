'use client'
import * as Yup from 'yup'

import CustomButton from '@atoms/Button/CustomButton'
import Checkbox from '@atoms/Checkbox'
import FormContext from '@atoms/FormContext'
import TextField from '@atoms/TextField'
import { ServiceFragment } from '@lib/_generated/graphql_sdk'

export interface CreateAccountFormData {
  name: string
  email: string
  telephone: string
  photo: File | null
  cardNumber: string
  serviceType: string
  newsletter_subscriber_checkbox: boolean
  terms: boolean
}

interface Props {
  services: ServiceFragment[]
}

const RegisterWorkerForm: React.FC<Props> = ({ services }) => {
  const handleSubmitCreateAccountFormData = async (data: CreateAccountFormData) => {
    try {
      console.log(data, 'data')
    } catch (error) {
      console.error('Failed to create customer:', error)
    }
  }

  const validationSchema = Yup.object({
    name: Yup.string().required('Това поле е задължително.'),
    email: Yup.string().required('Това поле е задължително.'),
    telephone: Yup.string().required('Това поле е задължително.'),
    terms: Yup.boolean().oneOf([true], 'Трябва да се съгласите с условията.').required('Това поле е задължително.'),
  })

  return (
    <div className="flex flex-col w-full mt-10">
      <FormContext<CreateAccountFormData>
        onSubmit={(data) => {
          handleSubmitCreateAccountFormData(data)
        }}
        validationSchema={validationSchema}
        data={{
          name: '',
          email: '',
          telephone: '',
          photo: null,
          serviceType: '',
          cardNumber: '',
          newsletter_subscriber_checkbox: false,
          terms: false,
        }}
      >
        <div className="mb-6">
          <TextField label="Вашето име:" name="name" type="text" required />
        </div>
        <div className="grid desktop:gap-6 desktop:grid-cols-2 grid-cols-1 w-full justify-between">
          <div className="mb-6 w-full">
            <TextField label="Email:" name="email" type="email" required />
          </div>
          <div className="mb-6 w-full">
            <TextField label="Телефон:" name="telephone" type="tel" required />
          </div>
        </div>
        <div className="grid desktop:gap-6 desktop:grid-cols-2 grid-cols-1 w-full justify-between">
          <div className="mb-6 w-full">
            <TextField label="Град:" name="city" type="text" />
          </div>
          <div className="mb-6 w-full">
            <TextField label="Снимка:" name="photo" type="file" accept="image/png, image/jpeg" />
          </div>
        </div>

        <div className="grid desktop:gap-6 desktop:grid-cols-2 grid-cols-1 w-full justify-between">
          <div className="mb-6 w-full">
            <TextField label="Вид услуга:" name="serviceType" grayFieldBg asType="select">
              {services.map((service: ServiceFragment, index: number) => (
                <option value="red" key={index}>
                  {service.name}
                </option>
              ))}
            </TextField>
          </div>
          <div className="mb-6 w-full">
            <TextField label="Номер на карта:" name="cardNumber" type="text" />
          </div>
        </div>

        <Checkbox name="terms" label="Запознах се с общите условия и ги приемам" required />

        <Checkbox name="newsletter_subscriber_checkbox" label="Искам да получавам съобщения на e-mail" />

        <CustomButton className="px-8 py-2 text-white rounded-lg focus:outline-none h-[40px] mt-8" type="submit">
          Изпрати
        </CustomButton>
      </FormContext>
    </div>
  )
}

export default RegisterWorkerForm
