'use client'
import React, { useEffect, useRef, useState } from 'react'

import CloseIcon from '@atoms/Icons/CloseIcon'
import InputFilter from '@components/molecules/InputFilter'
import { addClasses } from '@lib/style/style'

interface Props {
  cities: string[]
  selectedCity: string
  onSelect: (option: string) => void
  onClearSelection: () => void
}

const CitiesFilter: React.FC<Props> = ({ cities, onSelect, selectedCity, onClearSelection }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const wrapperRef = useRef<HTMLDivElement>(null)

  const handleOptionSelect = (city: string) => {
    onSelect(city)
    setIsOpen(false)
  }

  const handleFocus = () => {
    setIsOpen(true)
  }

  const handleClickOutside = (event: MouseEvent) => {
    if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
      setIsOpen(false)
    }
  }

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="relative w-full">
      <div
        className={addClasses(
          'text-black rounded-full py-3 px-8 pr-10 text-ellipsis overflow-hidden',
          'bg-no-repeat bg-[right_1rem_center] cursor-pointer bg-white text-sm'
        )}
        onClick={handleFocus}
      >
        {selectedCity ? (
          <div
            className="
          flex items-center"
          >
            {selectedCity}
            <div
              className={addClasses(
                'p-2 hover:bg-gray-200 rounded-full w-[28px] h-[28px] ',
                'flex justify-center items-center ml-4'
              )}
              onClick={onClearSelection}
            >
              <CloseIcon />
            </div>
          </div>
        ) : (
          'Всички градове'
        )}
      </div>
      {isOpen && (
        <div className="absolute top-[45px] w-full" ref={wrapperRef}>
          <InputFilter options={cities} onSelect={handleOptionSelect} />
        </div>
      )}
      <div
        onClick={handleFocus}
        className="bg-arrow-down bg-no-repeat absolute w-[19px] h-[19px] flex right-4 top-4 cursor-pointer"
      ></div>
    </div>
  )
}

export default CitiesFilter
