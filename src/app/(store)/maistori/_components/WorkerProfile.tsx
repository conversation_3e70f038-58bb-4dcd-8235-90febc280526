import React from 'react'

import { Workers } from '@/src/models/workersInterface'
import Store_Modal from '@components/organisms/Store_Modal'
import { Button } from '@components/theme/ui/button'

interface Props {
  worker: Workers
}

const WorkerProfile: React.FC<Props> = ({ worker }) => {
  return (
    <div className="relative w-full">
      <Store_Modal
        title={worker.name}
        image={worker.image}
        profileImage
        trigger={<Button className="py-2 text-white focus:outline-none h-[34px] !leading-7">Профил</Button>}
      >
        {worker.description && <p className="mb-8">{worker.description}</p>}
        <div className="font-bold mb-5 text-md">Контакт</div>
        <div className="flex justify-between border-b border-gray-200 py-3 text-sm">
          <div className="font-bold">Адрес:</div>
          <div>{worker.city}</div>
        </div>
        <div className="flex justify-between border-b border-gray-200 py-3 text-sm">
          <div className="font-bold">Телефон:</div>
          <div>{worker.phone}</div>
        </div>
        <div className="flex justify-between py-3 text-sm">
          <div className="font-bold">Email:</div>
          <div>{worker.email}</div>
        </div>

        <div className="font-bold mt-10 mb-5 text-md">Услуги</div>
        <ul className="list-disc pl-4">
          {worker.services.map((service, index) => (
            <li key={index} className="text-sm">
              {service}
            </li>
          ))}
        </ul>
      </Store_Modal>
    </div>
  )
}

export default WorkerProfile
