'use client'
import Link from 'next/dist/client/link'
import React, { useState } from 'react'

import CitiesFilter from '@/src/app/(store)/maistori/[...dynamic]/_components/CitiesFilter'
import { Workers } from '@/src/models/workersInterface'
import Paper from '@atoms/Paper'
import Text from '@atoms/Text'
import { addClasses } from '@lib/style/style'

import WorkerProfile from './WorkerProfile'

interface Props {
  workersList: Workers[]
}

const WorkersList: React.FC<Props> = ({ workersList }) => {
  const [selectedCity, setSelectedCity] = useState<string>('')
  const [filteredWorkers, setFilteredWorkers] = useState<Workers[]>(workersList)

  const getUniqueCities = (arr: Workers[]) => {
    const seen = new Set()

    return arr
      .map((item) => item.city)
      .filter((city) => {
        if (seen.has(city)) {
          return false
        } else {
          seen.add(city)
          return true
        }
      })
  }

  const handleCitySelect = (city: string) => {
    setSelectedCity(city)
    setFilteredWorkers(workersList.filter((worker) => worker.city === city))
  }

  const handleClearCitySelection = () => {
    setSelectedCity('')
    setFilteredWorkers(workersList)
  }

  return (
    <Paper className="p-0 pb-10 w-max md:w-max xl:w-full">
      <Paper
        className={addClasses(
          'xl:grid-cols-[27%_30%_20%_23%] grid-cols-[200px_300px_200px_230px]',
          'bg-background-dark h-[90px] w-full text-white flex p-0 whitespace-nowrap',
          'grid rounded-2xl'
        )}
      >
        <div className="border-r border-gray xl:px-10 px-6 flex items-center font-bold text-xl">
          <CitiesFilter
            cities={getUniqueCities(workersList)}
            onSelect={handleCitySelect}
            onClearSelection={handleClearCitySelection}
            selectedCity={selectedCity}
          />
        </div>
        <div className="border-r border-gray xl:px-10 px-6 flex items-center font-bold">
          <Text>Име и Фамилия</Text>
        </div>
        <div className="border-r border-gray xl:px-10 px-6 flex items-center font-bold">
          <Text>Телефон</Text>
        </div>
        <div className="px-10 flex items-center font-bold">
          <Text>Email</Text>
        </div>
      </Paper>
      <div>
        {filteredWorkers.map((worker: Workers, index: number) => (
          <div
            key={index}
            className={addClasses(
              'grid xl:grid-cols-[27%_30%_20%_23%] grid-cols-[200px_300px_200px_230px]',
              'hover:bg-[#eaeaea] group',
              'even:bg-background border-b border-gray-200'
            )}
          >
            <div className="px-2 py-2 xl:px-5 xl:py-3 flex items-center text-sm font-bold">{worker.city}</div>
            <div className="px-2 py-2 xl:px-5 xl:py-3 flex items-center">
              <Link
                href={worker.link}
                className={addClasses(
                  'h-[34px] rounded-3xl inline-flex px-4 leading-10',
                  'underline text-primary flex items-center text-sm'
                )}
              >
                {worker.name}
              </Link>
              <div className="hidden group-hover:flex">
                <WorkerProfile worker={worker} />
              </div>
            </div>
            <div className="px-2 py-2 xl:px-5 xl:py-3 flex items-center text-sm">{worker.phone}</div>
            <div className="px-2 py-2 xl:px-5 xl:py-3 flex items-center text-sm">{worker.email}</div>
          </div>
        ))}
      </div>
    </Paper>
  )
}

export default WorkersList
