import { Workers } from '@/src/models/workersInterface'
import { ServiceFragment } from '@lib/_generated/graphql_sdk'
import { GraphQLBackend } from '@lib/api/graphql'

export async function getServices(): Promise<ServiceFragment[]> {
  try {
    const response = await GraphQLBackend.GetAvailableServices()
    if (response && response.availableServices) {
      return response.availableServices
    }
  } catch (error) {
    console.error(error)
  }

  return [] as ServiceFragment[]
}

export async function getWorkers(type: string): Promise<Workers[]> {
  try {
    const response = [
      {
        name: 'Цветан Дзезов',
        description:
          'Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века',
        link: 'hristo-ivanov-61',
        city: 'София',
        phone: '0895399916',
        email: '<EMAIL>',
        services: ['Електро', 'Вик', 'Циклене'],
        image:
          'https://w7.pngwing.com/pngs/439/971/png-transparent-graphy-laborer-construction-worker-general-contractor-construction-worker-miscellaneous-hat-photography-thumbnail.png',
      },
      {
        name: 'Мирослав Метлоков',
        description:
          'Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века',
        link: 'hristo-ivanov-61',
        city: 'Пловдив',
        phone: '0895399916',
        email: '<EMAIL>',
        services: ['Електро'],
        image:
          'https://w7.pngwing.com/pngs/439/971/png-transparent-graphy-laborer-construction-worker-general-contractor-construction-worker-miscellaneous-hat-photography-thumbnail.png',
      },
      {
        name: 'Цветан Дзезов',
        description:
          'Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века',
        link: 'hristo-ivanov-61',
        city: 'Варна',
        phone: '0895399916',
        email: '<EMAIL>',
        services: ['Електро'],
        image:
          'https://w7.pngwing.com/pngs/439/971/png-transparent-graphy-laborer-construction-worker-general-contractor-construction-worker-miscellaneous-hat-photography-thumbnail.png',
      },
      {
        name: 'Цветан Дзезов',
        description:
          'Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века',
        link: 'hristo-ivanov-61',
        city: 'Габрово',
        phone: '0895399916',
        email: '<EMAIL>',
        services: ['Електро'],
        image:
          'https://w7.pngwing.com/pngs/439/971/png-transparent-graphy-laborer-construction-worker-general-contractor-construction-worker-miscellaneous-hat-photography-thumbnail.png',
      },
      {
        name: 'Цветан Дзезов',
        description:
          'Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века',
        link: 'hristo-ivanov-61',
        city: 'София',
        phone: '0895399916',
        email: '<EMAIL>',
        services: ['Електро'],
        image:
          'https://w7.pngwing.com/pngs/439/971/png-transparent-graphy-laborer-construction-worker-general-contractor-construction-worker-miscellaneous-hat-photography-thumbnail.png',
      },
      {
        name: 'Цветан Дзезов',
        description:
          'Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века',
        link: 'hristo-ivanov-61',
        city: 'София',
        phone: '0895399916',
        email: '<EMAIL>',
        services: ['Електро'],
        image:
          'https://w7.pngwing.com/pngs/439/971/png-transparent-graphy-laborer-construction-worker-general-contractor-construction-worker-miscellaneous-hat-photography-thumbnail.png',
      },
    ]

    return response
  } catch (error) {
    console.error(error)
  }

  return []
}
