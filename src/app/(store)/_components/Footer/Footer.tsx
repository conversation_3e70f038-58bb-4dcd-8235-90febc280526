import React from 'react'

import { staticSelectors } from '@/src/features/static/selectors'
import { StaticContentProps } from '@/src/features/static/types'
import AppLink from '@atoms/AppLink'
import Container from '@atoms/Container'
import DesignByIcon from '@atoms/Icons/DesignByIcon'
import Text from '@atoms/Text'
import Logo from '@components/molecules/Logo'
import { addClasses } from '@lib/style/style'

import ContactBox from './ContactBox'
import PaymentMethods from './PaymentMethods'
import { Breakpoint } from '@atoms/_debug/Breakpoint'
import { CMSBlock } from '@/src/components/molecules/Widget/markup/instanced'
import { withStaticData } from '@/src/features/static/withStaticData'

const Footer: React.FC<StaticContentProps> = ({ staticContent }) => {
  const year = new Date().getFullYear()
  const footerData = staticSelectors.selectFooter(staticContent)

  const generalPhone = footerData?.contacts?.general?.phone ?? ''
  const onlineStorePhone = footerData?.contacts?.onlineStore?.phone ?? ''
  return (
    <div className="bg-background-dark text-white flex-shrink">
      {/* <Breakpoint /> */}
      <div className="bg-background-dark bg-footer-logo bg-contain bg-bottom bg-no-repeat" title="footer">
        <Container maxWidth="2xl">
          <div className="grid grid-cols-1 xl:grid-cols-3 xl:mt-16 xl:mb-5">
            <div className="flex flex-col gap-4 items-center xl:items-start py-12 xl:py-0">
              <div className="flex items-center justify-center ">
                <Logo href="/" className="md:mt-4" width={200} />
              </div>

              <div className="flex flex-col justify-center">
                <div className="inline-flex flex-col">
                  <CMSBlock id="footer-find-us" />
                </div>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:col-span-2 gap-4">
              {generalPhone && (
                <ContactBox
                  title="Връзка с национален телефон:"
                  phone={generalPhone}
                  description="Без увеличение на тарифата"
                />
              )}
              {onlineStorePhone && (
                <ContactBox
                  title="Връзка с онлайн магазин:"
                  phone={onlineStorePhone}
                  description="Онлайн магазин: Понеделник - Неделя от 09:00 до 18:00 ч."
                />
              )}
            </div>
          </div>
        </Container>
        <Container maxWidth="2xl">
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
            <div className="order-last px-4 md:grid md:grid-cols-2 md:col-span-2 md:gap-x-8 xl:order-first xl:grid-cols-1 xl:col-span-1 xl:px-0">
              <PaymentMethods />
            </div>

            <CMSBlock id="footer-mid" />
          </div>
        </Container>
      </div>
      <Container maxWidth="2xl" className="flex flex-col md:flex-row items-center justify-between py-8">
        <Text className="text-xs xl:text-sm">&copy; 2012-{year} PRAKTIS.BG. Всички права запазени.</Text>
        <div className="flex items-center gap-3">
          <Text className="text-xs xl:text-sm">Уеб дизайн:</Text>
          <DesignByIcon />
        </div>
      </Container>
    </div>
  )
}

export default withStaticData(Footer)
