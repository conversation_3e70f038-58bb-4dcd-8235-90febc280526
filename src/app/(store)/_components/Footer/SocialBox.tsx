import React from 'react'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import Text from '@atoms/Text'
import { SocialLinks } from '@lib/_generated/graphql_sdk'

interface Props {
  socialData?: SocialLinks | null
}

const SocialBox: React.FC<Props> = (props) => {
  return (
    <div className="flex flex-col justify-center">
      <div className="inline-flex flex-col">
        <Text className="flex pb-2">Намерете ни:</Text>
        <div className="flex items-center">
          {props.socialData &&
            [
              { label: 'Facebook', href: props.socialData.facebook, iconSrc: '/images/icons/facebook-icon.svg' },
              { label: 'Youtube', href: props.socialData.youtube, iconSrc: '/images/icons/youtube-icon.svg' },
              { label: 'Viber', href: props.socialData.viber, iconSrc: '/images/icons/viber-icon.svg' },
            ].map(({ label, href, iconSrc }, i) => (
              <div key={i} className="flex gap-2">
                <Img
                  className="h-5 w-5 aspect-square"
                  alt="Facebook"
                  src={iconSrc}
                  width={12}
                  height={12}
                  itemProp={'logo'}
                />
                <AppLink
                  target="_blank"
                  className="text-primary underline underline-offset-2 decoration-white pr-6 text-md"
                  href={href || ''}
                >
                  {label}
                </AppLink>
              </div>
            ))}
        </div>
      </div>
    </div>
  )
}

export default SocialBox
