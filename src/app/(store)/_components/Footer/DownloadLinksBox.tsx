import React from 'react'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { ClassName } from '@lib/types/ClassName'

interface Props {
  android?: string
  ios?: string
  className?: ClassName
}

const DownloadLinksBox: React.FC<Props> = (props) => {
  return (
    <div className={cn('p-4 xl:p-0', props.className)}>
      <div className="mb-2">
        <Text className="text-sm">Свали нашият App:</Text>
      </div>

      <div className="flex flex-row gap-4">
        {props.android && (
          <AppLink href={props.android} target="_blank">
            <Img
              alt="android store logo"
              className="flex mt-1"
              src={'/images/android-store.svg'}
              width={108}
              height={32}
              itemProp={'android store logo'}
            />
          </AppLink>
        )}
        {props.ios && (
          <AppLink href={props.ios} target="_blank">
            <Img
              alt="apple store logo"
              className="flex"
              src={'/images/ios-store.svg'}
              width={101}
              height={42}
              itemProp={'apple store logo'}
            />
          </AppLink>
        )}
      </div>
    </div>
  )
}

export default DownloadLinksBox
