import React from 'react'

const FooterColumn = ({ children }: { children: React.ReactNode }) => {
  // Extract the first child (title) and rest of children (links)
  const [title, linksList] = React.Children.toArray(children)

  return (
    <div className="column px-8 md:py-4">
      <h2 className="flex items-center text-primary font-bold text-2xl mb-6 mt-4">
        {title}
        <div className="flex-grow h-px bg-primary ml-10"></div>
      </h2>
      {React.isValidElement(linksList) &&
        React.cloneElement(linksList as any, {
          className: 'flex flex-col gap-3 text-sm text-white',
        })}
    </div>
  )
}

export default FooterColumn
