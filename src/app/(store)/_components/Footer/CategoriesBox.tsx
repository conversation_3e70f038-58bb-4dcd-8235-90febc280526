import React from 'react'

import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import Paper from '@atoms/Paper'
import Title from '@atoms/Title'
import { Maybe, MenuItem } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'

interface Props {
  categories: Maybe<Maybe<MenuItem>[]> | undefined
}

interface Props {}

const CategoriesBox: React.FC<Props> = (props) => {
  return (
    <Paper className="w-full p-0">
      <Title className={addClasses('leading-none p-10 text-3xl font-bold text-black', 'border-b border-gray-300')}>
        Вие търсите:
      </Title>

      <div
        className={addClasses('grid minTablet:grid-rows-3 grid-flow-col', 'grid-rows-6 grid-flow-col overflow-hidden')}
      >
        {props.categories &&
          props.categories.map(
            (category, index: number) =>
              category && (
                <AppLink href={category.url} key={index}>
                  <div
                    className={addClasses(
                      'flex items-center font-bold min-h-full',
                      'column py-8 px-10 phone:pr-4 phone:pl-6',
                      'border-solid border-b border-r border-gray-300',
                      'cursor-pointer	hover:underline',
                      'mr-[-1px] mb-[-1px]'
                    )}
                  >
                    <Img
                      alt="category"
                      className="flex mr-4"
                      src={category.thumbnail}
                      width={25}
                      height={25}
                      itemProp={category.name}
                    />
                    {category.name}
                  </div>
                </AppLink>
              )
          )}
      </div>
    </Paper>
  )
}

export default CategoriesBox
