import React from 'react'

interface Props {
  title?: string
  phone?: string
  description?: string
}

const ContactBox: React.FC<Props> = (props) => {
  return (
    <div className="bg-black/20 text-white p-6 pb-4 rounded-3xl">
      <h4 className="font-bold text-md">{props.title}</h4>
      <div className="text-primary font-bold text-3xl pb-3 pt-1">
        <a href={`tel:${props.phone}`}>{props.phone}</a>
      </div>
      <div className="text-xs text-white/75">{props.description}</div>
    </div>
  )
}

export default ContactBox
