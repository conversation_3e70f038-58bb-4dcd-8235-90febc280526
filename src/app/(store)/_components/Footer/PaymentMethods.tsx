import React from 'react'

import Img from '@atoms/Img'
import Text from '@atoms/Text'
import { addClasses } from '@lib/style/style'
import { CMSBlock } from '@/src/components/molecules/Widget/markup/instanced'
import Image from 'next/image'

export function PaymentImageBox(props: { src?: string; text: string; className?: string }) {
  return (
    <div
      className={addClasses(
        'bg-white rounded-[4px] w-[54px] h-[32px]',
        'flex items-center justify-center',
        props.className
      )}
    >
      {!!props.src ? (
        <Img
          src={props.src}
          alt={props.text}
          width={41}
          height={23}
          itemProp={props.text}
          className=" object-cover w-full h-full"
        />
      ) : (
        <Text color={'black'} bold className="text-center text-[9px]">
          {props.text}
        </Text>
      )}
    </div>
  )
}

const PaymentMethods: React.FC = () => {
  return (
    <div className="p-4 flex flex-col self-end">
      <div className="mb-3">
        <Text className="text-sm">Начини на плащане:</Text>
      </div>

      <div className="flex flex-wrap gap-3 pt-1">
        <CMSBlock id="footer-bottom" />
        {/* examples: */}
        {/* <PaymentImageBox src="/images/footer/mastercard.png" text="mastercard logo" />
        <PaymentImageBox src="/images/footer/visa.png" text="mastercard logo" />
        <PaymentImageBox src="/images/payment-bcard.svg" text="bcard logo" className="p-0" />
        <PaymentImageBox text="налжен платеж" /> */}
        {/* or just normal cms images */}
      </div>
    </div>
  )
}

export default PaymentMethods
