'use client'
import { LucideLoaderCircle } from 'lucide-react'
import React, { FormEvent } from 'react'

import { staticSelectors } from '@/src/features/static/selectors'
import { StaticContentProps } from '@/src/features/static/types'
import useRecaptcha from '@/src/hooks/useRecaptcha'
import CustomButton from '@atoms/Button/CustomButton'
import Img from '@atoms/Img'
import Paragraph from '@atoms/Paragraph'
import Title from '@atoms/Title'
import { Button } from '@components/theme/ui/button'
import { GraphQLBackend } from '@lib/api/graphql'
import { handleAPIError } from '@lib/types/isAPIError'
import { showErrorToast, showSuccessToast } from '@lib/utils/toaster'

const NewsletterBox: React.FC<StaticContentProps> = ({ staticContent }) => {
  const [email, setEmail] = React.useState('')
  const [loading, setLoading] = React.useState<boolean>(false)
  const { newsletter } = staticSelectors.selectMessages(staticContent)
  const { googleRecaptchaKey: siteKey } = staticSelectors.selectApiKeys(staticContent)

  const { executeRecaptcha } = useRecaptcha(siteKey)

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)
    try {
      const recaptchaToken = await executeRecaptcha('newsletter_subscribe')

      const response = await GraphQLBackend.Subscribe({ email }, { 'x-captcha-token': recaptchaToken })

      if (response.storeNewsletterSubscribe) {
        showSuccessToast({
          title: 'Благодарим Ви!',
          description: 'Вие се абонирахте успешно!',
        })
        setEmail('')
      } else {
        showErrorToast({
          title: 'Нещо се обърка!',
          description: 'Моля, опитайте по-късно',
        })
      }
    } catch (err: unknown) {
      handleAPIError(err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="rounded-3xl w-full bg-gradient-to-r from-[#F39200] to-[#EEC800]">
      <div className="p-12 flex lg:flex-row flex-col">
        <div className="flex lg:flex-row basis-3/5 flex-col gap-5 items-center">
          <Img
            alt="Newsletter"
            className="flex mt-1 mr-8 mb-8 sm:mb-0"
            src={'/images/icons/newsletter-icon.svg'}
            width={162}
            height={125}
          />
          <div>
            <p className="leading-none pt-2 pb-6 text-xl font-bold text-black">
              Абонирайте се за нашите специални предложения:
            </p>
            <Paragraph color="white">{newsletter}</Paragraph>
          </div>
        </div>
        <div className="flex basis-2/5">
          <div className="w-full">
            <form
              onSubmit={handleSubmit}
              className="bg-transparent sm:bg-white rounded-full p-2 flex flex-col sm:flex-row gap-3 items-center justify-between xl:ml-10 mt-12"
            >
              <input
                className="w-full pl-5 sm:pl-2 py-4 rounded-full md:pl-8 text-black outline-0"
                type="email"
                placeholder="Вашият e-mail"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={loading}
              />
              <Button type="submit" variant="inverse" disabled={loading}>
                {loading && <LucideLoaderCircle className="animate-spin" />}
                Абонирайте се
              </Button>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NewsletterBox
