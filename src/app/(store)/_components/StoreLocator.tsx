'use client'
import React, { useEffect, useState } from 'react'

import AppLink from '@atoms/AppLink'
import Text from '@atoms/Text'
import Title from '@atoms/Title'
import { DecorationCard } from '@components/molecules/DecorationCard'
import { Button } from '@components/theme/ui/button'
import { AppStorePreviewFragment } from '@lib/_generated/graphql_sdk'

interface Props {
  stores: AppStorePreviewFragment[]
  onChange?: (storeName: string) => void
}

const StoreLocator: React.FC<Props> = ({ stores, onChange }) => {
  const sortedStores = stores.sort((a, b) => a.displayOrder - b.displayOrder)
  const [selectedStoreId, setSelectedStoreId] = useState(sortedStores[0].identity)

  const handleChangeStore = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedStoreId(event.target.value)
  }

  const selectedStore = stores.find((store) => store.identity === selectedStoreId)

  const { name: selectedStoreName } = selectedStore || {}

  useEffect(() => {
    if (onChange && selectedStoreName) {
      onChange(selectedStoreName)
    }
  }, [onChange, selectedStoreName])

  console.log({ selectedStore, selectedStoreId }, sortedStores[0].identity)

  if (!selectedStore) {
    return
  }

  const image = selectedStore.gallery.length > 0 ? selectedStore.gallery[0].src : ''

  return (
    <DecorationCard
      title={
        <>
          <Text className="text-primary mb-4 flex">моят магазин</Text>
          <Text className="text-xl font-bold">Praktis</Text>
        </>
      }
      description={
        <div>
          <div className="mb-5">
            <select
              value={selectedStoreId}
              onChange={handleChangeStore}
              className="border-none w-full focus:outline-none text-lg font-bold px-0 pt-0 rounded-none"
            >
              {sortedStores.map((store, index) => (
                <option value={store.identity} key={store.identity}>
                  {store.name}
                </option>
              ))}
            </select>
            <div className="w-full h-[2px] bg-black" />
          </div>

          <div className="pb-1.5 text-sm">
            <Text>Адрес: </Text>
            <strong>{selectedStore.address}</strong>
          </div>
          <div className="pb-1.5 text-sm">
            <Text>Телефони: </Text>
            <strong>{selectedStore.phone}</strong>
          </div>
          <div className="pb-1.5 text-sm">
            <Text>Email: </Text>
            <strong>{selectedStore.email}</strong>
          </div>

          <div className="pb-2 mt-4 text-sm">
            {selectedStore.businessHours.map((schedule, index) => (
              <div key={index} className="flex justify-between">
                <Text>{schedule.day}</Text>
                <strong>
                  {schedule.open} - {schedule.close}
                </strong>
              </div>
            ))}
          </div>
        </div>
      }
      action={
        <AppLink href={'/shops'}>
          <Button variant="outline" className="text-xs tracking-normal sm:text-base sm:tracking-wide">
            Всички магазини
          </Button>
        </AppLink>
      }
      image={image}
    />
  )
}

export default StoreLocator
