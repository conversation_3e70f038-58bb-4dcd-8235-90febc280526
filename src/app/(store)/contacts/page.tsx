import { ContactPage } from 'schema-dts'

import { SchemaOrg } from '@/src/components/atoms/SchemaOrg'
import { getStaticContent } from '@/src/features/static/api'
import AppLink from '@atoms/AppLink'
import Container from '@atoms/Container'
import Paragraph from '@atoms/Paragraph'
import Title from '@atoms/Title'

import ContactForm from './_components/ContactForm'
import GoogleMap from './_components/GoogleMap'
import { CMSBlock } from '@/src/components/molecules/Widget/markup/instanced'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'

export default async function Contacts() {
  const staticContent = await getStaticContent()
  //@TODO - use static content
  return (
    <div>
      <Container maxWidth="xl">
        <SchemaOrg<ContactPage>
          schema={{
            '@context': 'https://schema.org',
            '@type': 'ContactPage',
            'name': '<PERSON><PERSON><PERSON>',
            'url': staticContent.store.baseUrl,
            'image': staticContent.logo?.url,
            'mainEntity': {
              '@type': 'Organization',
              'name': '<PERSON><PERSON><PERSON>',
              'contactPoint': {
                '@type': 'ContactPoint',
                'email': '<EMAIL>',
                'telephone': staticContent?.store.contacts.onlineStore?.phone || '0894198027',
              },
            },
          }}
        />
        <div className="flex flex-row phone:flex-col gap-10 justify-between">
          <div className="rounded-3xl w-full bg-white my-10 phone:mb-0 basis-1/2 p-10">
            <CMSBlock id="contacts_info" />
          </div>
          <div className="rounded-3xl w-full bg-white my-10 phone:mt-0 basis-1/2 p-10">
            <Title size="h2" className="leading-none pt-2 pb-6 text-2xl font-bold text-black">
              Контактна форма
            </Title>
            <ContactForm staticContent={staticContent} />
          </div>
        </div>
      </Container>
      <div className="w-full h-96 mb-10">
        <GoogleMap id="CONTACTS_MAP" center={{ lat: 42.7105545, lng: 23.350580199999968 }} />
      </div>
      <RelevaTrackPage />
    </div>
  )
}
