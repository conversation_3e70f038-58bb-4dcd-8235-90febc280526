'use client'
import { zodResolver } from '@hookform/resolvers/zod'
import { LucideLoaderCircle } from 'lucide-react'
import React, { useCallback, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { z } from 'zod'

import { FormTextArea } from '@components/molecules/FormControllers/FormTextArea'
import { FormTextField } from '@components/molecules/FormControllers/FormTextField'
import { Button } from '@components/theme/ui/button'
import { staticSelectors } from '@features/static/selectors'
import { StaticContentProps } from '@features/static/types'
import useRecaptcha from '@/src/hooks/useRecaptcha'
import { GraphQLBackend } from '@lib/api/graphql'
import { showErrorToast, showSuccessToast } from '@lib/utils/toaster'
import { trackContact } from '@lib/fbp/faceBookPixelHelper'

// Define schema and type within the same file
const contactRequestSchema = z.object({
  name: z.string().min(2, { message: 'Името е задължително' }),
  phone: z.string().min(5, { message: 'Телефонът е задължителен' }),
  email: z.string().email({ message: 'Невалиден имейл адрес' }),
  message: z.string().min(1, { message: 'Съобщението е задължително' }),
})

type ContactRequest = z.infer<typeof contactRequestSchema>

const contactRequestDefaults: ContactRequest = {
  name: '',
  phone: '',
  email: '',
  message: '',
}

const ContactForm: React.FC<StaticContentProps> = ({ staticContent }) => {
  const [loading, setLoading] = useState(false)

  // Get site key and initialize recaptcha hook
  const { googleRecaptchaKey: siteKey } = staticSelectors.selectApiKeys(staticContent)
  const { executeRecaptcha } = useRecaptcha(siteKey)

  const form = useForm<ContactRequest>({
    resolver: zodResolver(contactRequestSchema),
    defaultValues: contactRequestDefaults,
    disabled: loading,
  })

  const onSubmit = useCallback(
    async (data: ContactRequest) => {
      try {
        setLoading(true)

        const recaptchaToken = await executeRecaptcha('send_contact_message')

        const response = await GraphQLBackend.SendContactMessage(
          {
            input: {
              name: data.name,
              email: data.email,
              telephone: data.phone,
              comment: data.message,
            },
          },
          { 'x-captcha-token': recaptchaToken }
        )

        if (!response?.storeSendContactMessage) {
          throw new Error('Възникна грешка при изпращането на съобщението.')
        }

        showSuccessToast({
          title: 'Успешно изпратено',
          description: 'Вашето съобщение е изпратено успешно.',
        })
        trackContact()
        form.reset()
      } catch (error) {
        console.error(error)
        showErrorToast({
          title: 'Грешка',
          description: 'Възникна грешка при изпращането.',
        })
      } finally {
        setLoading(false)
      }
    },
    [executeRecaptcha, form]
  )

  const onError = useCallback(() => {}, [])

  return (
    <div className="full-w">
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit, onError)}>
          <div className="space-y-4">
            <div>
              <FormTextField name="name" label="Име" />
            </div>
            <div>
              <FormTextField name="phone" label="Телефон" />
            </div>
            <div>
              <FormTextField name="email" label="Email" />
            </div>
            <div>
              <FormTextArea name="message" label="Съобщение" />
            </div>

            <Button
              type="submit"
              className="px-4 py-2 text-white rounded-lg focus:outline-none flex items-center gap-2"
              disabled={loading}
            >
              {loading && <LucideLoaderCircle className="animate-spin h-4 w-4" />}
              Изпрати
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  )
}

export default ContactForm
