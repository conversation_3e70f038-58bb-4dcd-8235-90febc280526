'use client'
import { AdvancedMarker, APIProvider, Map } from '@vis.gl/react-google-maps'
import React, { useState, useEffect } from 'react'

import '../google-map.css'
import { cn } from '@components/lib/utils'
import { ClassName } from '@lib/types/ClassName'
import { useStaticContentStore } from '@features/static/static.store'

interface GoogleMapProps {
  id: string
  center: {
    lat: number
    lng: number
  }
  zoom?: number
  markers?: { lat: number; lng: number }[] // Support multiple markers
  className?: ClassName
}

const GoogleMap = ({ id, center, zoom = 13, markers = [], className }: GoogleMapProps) => {
  const [cameraProps, setCameraProps] = useState({
    center,
    zoom,
  })

  const apiKey = useStaticContentStore((state) => state.staticContent?.apiKeys.googleMaps)

  useEffect(() => {
    setCameraProps((prevProps) => ({
      ...prevProps,
      center,
      zoom,
    }))
  }, [center, zoom])

  if (!apiKey) {
    console.warn('Google Maps API key is missing')
    return null
  }

  return (
    <APIProvider apiKey={apiKey}>
      {id && (
        <Map
          center={cameraProps.center}
          zoom={cameraProps.zoom}
          mapId={id}
          className={cn('map-container', className)}
          onBoundsChanged={(event) => {
            setCameraProps({
              center: event.detail.center,
              zoom: event.detail.zoom,
            })
          }}
        >
          {markers.length > 0 ? (
            markers.map((marker, index) => <AdvancedMarker key={index} position={marker} />)
          ) : (
            <AdvancedMarker position={center} />
          )}
        </Map>
      )}
    </APIProvider>
  )
}

export default GoogleMap
