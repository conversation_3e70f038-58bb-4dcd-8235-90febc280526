import { getBrands } from '@/src/app/(store)/[...dynamic]/api'
import Container from '@atoms/Container'
import Title from '@atoms/Title'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import BrandsPreview from '@components/molecules/Widget/widgets/FeaturedBrands/BrandsPreview'
import { Brand } from '@lib/_generated/graphql_sdk'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'

export default async function Brands() {
  let featuredBrands: Brand[] = []
  let brands: Brand[] = []
  try {
    featuredBrands = await getBrands({ featured: true })
    brands = await getBrands()
  } catch (error) {
    console.error(error)
  }

  return (
    <div className="mt-10">
      <Container>
        <Breadcrumb
          breadcrumbs={[
            { url: '/', label: 'Home' },
            { url: '#', label: 'Марки' },
          ]}
        />
        <Title className="text-2xl font-bold p-4 pt-8 mb-4">Избрани марки</Title>
        <div className="grid gap-4 desktop:gap-6 mb-20 desktop:grid-cols-8 tablet:grid-cols-6 grid-cols-3 phone:mb-6">
          {featuredBrands.map((brand, i) => (
            <div key={i}>
              <BrandsPreview brand={brand} />
            </div>
          ))}
        </div>
        <div className="grid gap-4 desktop:gap-6 mb-20 desktop:grid-cols-8 tablet:grid-cols-6 grid-cols-3 phone:mb-6">
          {brands.map((brand, i) => (
            <div key={i}>
              <BrandsPreview brand={brand} />
            </div>
          ))}
        </div>
      </Container>
      <RelevaTrackPage />
    </div>
  )
}
