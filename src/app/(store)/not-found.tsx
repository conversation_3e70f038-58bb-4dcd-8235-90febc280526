import React from 'react'

import CategoriesBox from '@/src/app/(store)/_components/Footer/CategoriesBox'
import Footer from '@/src/app/(store)/_components/Footer/Footer'
import CustomButton from '@atoms/Button/CustomButton'
import Container from '@atoms/Container'
import Paragraph from '@atoms/Paragraph'
import Title from '@atoms/Title'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import Header from '@components/organisms/Header/Header'
import Static from '@features/static'
import { getStaticContent } from '@features/static/api'

const Header404 = () => {
  return <Static component={Header} />
}

const Footer404 = () => {
  return <Static component={Footer} />
}

export const Body404 = async () => {
  const csmStaticContent = await getStaticContent()
  return (
    <main className="bg-404-image bg-cover bg-center bg-no-repeat ">
      <Container className="w-full relative gap-6 flex flex-col justify-center items-center minTablet:py-5">
        <div className="w-full flex my-5">
          <Breadcrumb
            breadcrumbs={[
              { url: '/', label: 'Home' },
              { url: '#', label: '404' },
            ]}
          />
        </div>

        <div className="flex flex-col justify-center items-center pb-10">
          <div>
            <Title color={'black'} className="leading-none text-center text-2xl font-bold mb-2 mt-10">
              Страницата
            </Title>
            <Title size="h2" color={'primary'} className="leading-none text-center text-2xl font-bold">
              не е намерена
            </Title>
          </div>
          <div className="text-center w-[290px] py-6">
            <Paragraph color={'paragraph'}>
              Използвайте лентата за търсене в горната част на страницата, за да намерите вашите продукти или
              разгледайте нашите топ предложения.
            </Paragraph>
          </div>

          <CustomButton color={'primary'} className="py-4 px-8">
            Към начална страница
          </CustomButton>
        </div>
      </Container>
      <Container maxWidth="xl" className="mb-20">
        <CategoriesBox categories={csmStaticContent.menu.categories} />
      </Container>
    </main>
  )
}

export default async function Custom404() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header404 />
      <Body404 />
      <Footer404 />
    </div>
  )
}
