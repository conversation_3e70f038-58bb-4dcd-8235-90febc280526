import {
  AppStoreFragment,
  AppStorePreviewFragment,
  PraktisStore,
  StorePageDataFragment,
} from '@lib/_generated/graphql_sdk'
import { GraphQLBackend } from '@lib/api/graphql'

export async function getStores(): Promise<AppStorePreviewFragment[]> {
  try {
    const response = await GraphQLBackend.GetAvailableStores()

    if (response && response.availableStores) {
      return response.availableStores
    }
  } catch (error) {
    console.error(error)
  }

  return [] as PraktisStore[]
}

export async function getStore(identity: string): Promise<StorePageDataFragment> {
  try {
    const response = await GraphQLBackend.GetStore({ uri: identity })

    if (response && response.getStore) {
      return response.getStore
    }
  } catch (error) {
    console.error(error)
  }

  return {} as StorePageDataFragment
}
