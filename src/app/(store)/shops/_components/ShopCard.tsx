import { LucidePlus } from 'lucide-react'
import React from 'react'

import AppLink from '@atoms/AppLink'
import PhoneIcon from '@atoms/Icons/PhoneIcon'
import Img from '@atoms/Img'
import Paper from '@atoms/Paper'
import Title from '@atoms/Title'
import { cn } from '@components/lib/utils'
import { ButtonIcon } from '@components/molecules/ButtonIcon'

interface Props {
  primaryImage: string
  identity: string
  name: string
  city: string
  address: string
  phone: string
}

const ShopCard: React.FC<Props> = ({ primaryImage, name, identity, city, address, phone }) => {
  return (
    <div className="relative w-full pb-full overflow-hidden rounded-3xl pb-full" style={{ paddingBottom: '100%' }}>
      <AppLink href={'shop/' + `${identity}.html`}>
        <div
          className={cn(
            'bg-no-repeat bg-cover bg-center absolute left-0 right-0 top-0 bottom-0 p-6',
            'flex flex-col justify-end'
          )}
        >
          <Img fill src={primaryImage || ''} alt={identity} />
          <Paper className="w-full rounded-xl py-6 px-8 relative z-10">
            <Img
              src={'/images/logo.png'}
              width={80}
              height={20}
              style={{
                objectFit: 'contain',
              }}
              alt="logo"
              itemProp={'logo'}
            />
            <Title className={cn('py-2 text-2xl font-bold text-black')}>{name}</Title>

            <div className="text-sm">{address}</div>
            <div className="flex">
              <div className="flex flex-1 items-center mt-2">
                <PhoneIcon className="mr-4" stroke="black" />
                <span className="text-primary text-sm font-bold">{phone}</span>
              </div>
              <ButtonIcon icon={<LucidePlus />} />
            </div>
          </Paper>
        </div>
      </AppLink>
    </div>
  )
}

export default ShopCard
