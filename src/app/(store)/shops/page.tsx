import { notFound } from 'next/navigation'
import React from 'react'
import { LocalBusiness, Organization } from 'schema-dts'

import { getStores } from '@/src/app/(store)/shops/api'
import { SchemaOrg } from '@/src/components/atoms/SchemaOrg'
import { getStaticContent } from '@/src/features/static/api'
import { staticSelectors } from '@/src/features/static/selectors'
import Container from '@atoms/Container'
import Title from '@atoms/Title'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import { AppStorePreviewFragment, StoreImage } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'

import ShopCardRaw from './_components/ShopCard'

export default async function Shops() {
  const stores = await getStores().catch(notFound)
  const sortedStores = stores.sort((a, b) => a.displayOrder - b.displayOrder)
  const ShopCard = ({ store }: { store: AppStorePreviewFragment }) => {
    return (
      <ShopCardRaw
        primaryImage={store.gallery ? getPrimaryImage(store.gallery) : ''}
        identity={store.identity}
        city={store.city}
        name={store.name}
        address={store.address}
        phone={store.phone}
      />
    )
  }
  const staticContent = await getStaticContent()
  const baseUrl = staticSelectors.selectStoreBaseUrl(staticContent)
  return (
    <Container>
      <SchemaOrg<Organization>
        schema={{
          '@context': 'https://schema.org',
          '@type': 'Organization',
          'name': 'Praktis',
          'contactPoint': {
            '@type': 'ContactPoint',
            'email': '<EMAIL>',
            'telephone': staticContent?.store.contacts.onlineStore?.phone || '0894198027',
          },
        }}
      />
      <meta name="robots" content="INDEX,FOLLOW" />
      <link rel="canonical" href={`${baseUrl}/shops`} />
      {sortedStores.map((store, i) => (
        <SchemaOrg<LocalBusiness>
          key={i}
          schema={{
            '@context': 'https://schema.org',
            '@type': 'LocalBusiness',
            'name': store.name,
            'image': store.gallery ? getPrimaryImage(store.gallery) : '',
            'address': {
              '@type': 'PostalAddress',
              'streetAddress': store.address,
              'addressLocality': store.city,
            },
            'contactPoint': {
              '@type': 'ContactPoint',
              'telephone': store.phone,
            },
          }}
        />
      ))}
      <Breadcrumb
        breadcrumbs={[
          { url: '/', label: 'Home' },
          { url: '#', label: 'Магазини' },
        ]}
      />

      <Title className="text-3xl font-bold p-4 pt-8 mb-4">Магазини</Title>

      <div
        className={addClasses(
          'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 4xl:grid-cols-5 5xl:grid-cols-7 6xl:grid-cols-10',
          'mb-14 gap-4 xl:gap-6'
        )}
      >
        {sortedStores.map((store) => (
          <ShopCard key={store.identity} store={store} />
        ))}
      </div>
    </Container>
  )
}

const getPrimaryImage = (gallery: Partial<StoreImage>[]): string => {
  const primaryImage = gallery.find((image) => image.isPrimary)
  if (primaryImage && primaryImage.src) {
    return primaryImage.src
  } else {
    const fallbackImage =
      gallery.length > 0
        ? gallery.reduce((prev, current) => {
            const prevPosition = prev.position ?? 0
            const currentPosition = current.position ?? 0
            return prevPosition > currentPosition && currentPosition ? prev : current
          })
        : null
    return fallbackImage && fallbackImage.src ? fallbackImage.src : ''
  }
}
