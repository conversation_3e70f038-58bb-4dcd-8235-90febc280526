import { CartTotalCode, InvoiceType, OrderFullFragment, OrderStatus } from '@lib/_generated/graphql_sdk'
import { useState } from 'react'
import { Card, CardContent, CardFooter, CardHeader } from '@components/theme/ui/card'
import Title from '@atoms/Title'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@components/theme/ui/collapsible'
import {
  LucideChevronDown,
  LucideChevronUp,
  LucideInfo,
  LucideMapPin,
  LucidePackage,
  LucideReceipt,
  LucideTruck,
  LucideWallet,
} from 'lucide-react'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@components/theme/ui/tabs'
import { OrderItem } from '@/src/app/(store)/sales/order/history/components/OrderItem'

export const OrderCard = ({ order }: { order: OrderFullFragment }) => {
  const [isOpen, setIsOpen] = useState(false)

  const formatDate = (dateString: string) => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('bg-BG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date)
  }

  const getStatusColor = (code: OrderStatus['code']) => {
    switch (code?.toLowerCase()) {
      case 'complete':
        return 'text-green-600'
      case 'processing':
        return 'text-blue-600'
      case 'pending':
        return 'text-yellow-600'
      case 'canceled':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getTotalByCode = (code: CartTotalCode) => {
    const total = order.totals?.find((total) => total.code === code)
    if (total?.amount) {
      return `${total.amount.value} ${total.amount.currency}`
    }
    return ''
  }

  return (
    <Card className="mb-4 hover:shadow-md transition-shadow">
      <CardHeader className="pb-2 flex flex-col md:flex-row justify-between md:items-center gap-2">
        <div>
          <Title className="text-base md:text-lg font-bold">Поръчка #{order.incrementId}</Title>
          <Text className="text-xs md:text-sm text-gray-500">{formatDate(order.createAt)}</Text>
        </div>
        <div className="flex flex-row md:flex-col items-start md:items-end justify-between">
          <div className={cn('font-medium text-sm md:text-base', getStatusColor(order.status?.code))}>
            {order.status?.label || 'Неизвестен статус'}
          </div>
          <Collapsible>
            <CollapsibleTrigger
              onClick={() => setIsOpen(!isOpen)}
              className="flex items-center text-xs md:text-sm text-primary mt-1 cursor-pointer"
            >
              {isOpen ? (
                <>
                  <span>Скрий детайли</span>
                  <LucideChevronUp size={16} className="ml-1" />
                </>
              ) : (
                <>
                  <span>Покажи детайли</span>
                  <LucideChevronDown size={16} className="ml-1" />
                </>
              )}
            </CollapsibleTrigger>
          </Collapsible>
        </div>
      </CardHeader>

      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleContent>
          <CardContent>
            <Tabs defaultValue="items" className="w-full">
              <TabsList className="grid grid-cols-2 md:grid-cols-4 mb-4 gap-1 md:gap-0">
                <TabsTrigger value="items" className="flex items-center text-xs md:text-sm">
                  <LucidePackage size={14} className="mr-1 md:mr-2 md:size-8" />
                  Продукти
                </TabsTrigger>
                <TabsTrigger value="shipping" className="flex items-center text-xs md:text-sm">
                  <LucideTruck size={14} className="mr-1 md:mr-2 md:size-8" />
                  Доставка
                </TabsTrigger>
                <TabsTrigger value="payment" className="flex items-center text-xs md:text-sm">
                  <LucideWallet size={14} className="mr-1 md:mr-2 md:size-8" />
                  Плащане
                </TabsTrigger>
                <TabsTrigger value="details" className="flex items-center text-xs md:text-sm">
                  <LucideInfo size={14} className="mr-1 md:mr-2 md:size-8" />
                  Детайли
                </TabsTrigger>
              </TabsList>

              <TabsContent value="items" className="space-y-4">
                <div className="border rounded-md p-3 bg-gray-50">
                  {order.items?.map((item) => <OrderItem key={item.id} item={item} />)}
                </div>
              </TabsContent>

              <TabsContent value="shipping" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {order.shippingAddress && (
                    <div className="border rounded-md p-4">
                      <div className="flex items-center mb-2">
                        <LucideMapPin size={18} className="mr-2 text-primary" />
                        <div className="font-medium">Адрес за доставка</div>
                      </div>
                      <div className="text-sm space-y-1">
                        <div>
                          {order.shippingAddress.firstName} {order.shippingAddress.lastName}
                        </div>
                        <div>{order.shippingAddress.street}</div>
                        <div>
                          {order.shippingAddress.city}, {order.shippingAddress.postcode}
                        </div>
                        <div>Телефон: {order.shippingAddress.telephone}</div>
                        <div>Email: {order.shippingAddress.email}</div>
                      </div>
                    </div>
                  )}

                  {order.shippingMethod && (
                    <div className="border rounded-md p-4">
                      <div className="flex items-center mb-2">
                        <LucideTruck size={18} className="mr-2 text-primary" />
                        <div className="font-medium">Метод на доставка</div>
                      </div>
                      <div className="text-sm">
                        <div>{order.shippingMethod.name}</div>
                        <div className="text-gray-500">Код: {order.shippingMethod.code}</div>
                        <div className="mt-2 font-medium">
                          Цена за доставка: {getTotalByCode(CartTotalCode.ShippingTotal)}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="payment" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {order.paymentMethod && (
                    <div className="border rounded-md p-4">
                      <div className="flex items-center mb-2">
                        <LucideWallet size={18} className="mr-2 text-primary" />
                        <div className="font-medium">Метод на плащане</div>
                      </div>
                      <div className="text-sm">
                        <div>Метод на плащане: {order.paymentMethod.name}</div>
                        <div className="text-gray-500">Код: {order.paymentMethod.code}</div>
                      </div>
                    </div>
                  )}

                  {order.invoice && (
                    <div className="border rounded-md p-4">
                      <div className="flex items-center mb-2">
                        <LucideReceipt size={18} className="mr-2 text-primary" />
                        <div className="font-medium">Данни за фактура</div>
                      </div>
                      <div className="text-sm space-y-1">
                        {order.invoice.type === InvoiceType.Company && order.invoice.company && (
                          <>
                            <div className="font-medium">{order.invoice.company.name}</div>
                            <div>ЕИК: {order.invoice.company.eik}</div>
                            <div>ДДС номер: {order.invoice.company.vat}</div>
                            <div>МОЛ: {order.invoice.company.mol}</div>
                          </>
                        )}
                        {order.invoice.type === InvoiceType.Personal && order.invoice.individual && (
                          <>
                            <div className="font-medium">{order.invoice.individual.name}</div>
                            <div>ЕГН: {order.invoice.individual.egn}</div>
                            {order.invoice.individual.vat && <div>ДДС номер: {order.invoice.individual.vat}</div>}
                          </>
                        )}
                        <div>{order.invoice.address}</div>
                        <div>{order.invoice.city}</div>
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="details" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-md p-4">
                    <div className="font-medium mb-2">Информация за поръчката</div>
                    <div className="text-sm space-y-1">
                      <div>
                        <span className="text-gray-500">Номер на поръчка:</span> {order.incrementId}
                      </div>
                      <div>
                        <span className="text-gray-500">Дата на създаване:</span> {formatDate(order.createAt)}
                      </div>
                      <div>
                        <span className="text-gray-500">Статус:</span>{' '}
                        <span className={getStatusColor(order.status?.code)}>{order.status?.label}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Състояние:</span> {order.state}
                      </div>
                      {order.couponCode && (
                        <div>
                          <span className="text-gray-500">Код за отстъпка:</span> {order.couponCode}
                        </div>
                      )}
                      {order.protectCode && (
                        <div>
                          <span className="text-gray-500">Код за защита:</span> {order.protectCode}
                        </div>
                      )}
                    </div>
                  </div>

                  {order.note && (
                    <div className="border rounded-md p-4">
                      <div className="font-medium mb-2">Бележка към поръчката</div>
                      <div className="text-sm">{order.note}</div>
                    </div>
                  )}
                </div>

                <div className="border rounded-md p-4">
                  <div className="font-medium mb-2">Суми</div>
                  <div className="space-y-1">
                    {order.totals?.map((total, i) => (
                      <div key={i} className="flex justify-between text-sm">
                        <span>{total.label}:</span>
                        <span className={total.code === CartTotalCode.GrantTotal ? 'font-bold' : ''}>
                          {total.amount.value} {total.amount.currency}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>

      <CardFooter className="flex flex-col md:flex-row justify-between gap-2 md:gap-0">
        <div className="text-sm md:text-base">
          {order.items?.length} {order.items?.length === 1 ? 'продукт' : 'продукта'}
        </div>
        <div className="font-bold text-sm md:text-base">Общо: {getTotalByCode(CartTotalCode.GrantTotal)}</div>
      </CardFooter>
    </Card>
  )
}
