import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, <PERSON>Header } from '@components/theme/ui/card'
import { Skeleton } from '@components/theme/ui/skeleton'

export const OrderSkeleton = () => {
  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <Skeleton className="h-6 w-1/3" />
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <Skeleton className="h-4 w-1/4" />
          <Skeleton className="h-4 w-1/2" />
          <div className="pt-2">
            <Skeleton className="h-16 w-full" />
          </div>
          <div className="pt-2">
            <Skeleton className="h-24 w-full" />
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Skeleton className="h-4 w-1/4" />
      </CardFooter>
    </Card>
  )
}
