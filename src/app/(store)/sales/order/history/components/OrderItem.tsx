import Img from '@atoms/Img'
import { LucidePackage } from 'lucide-react'
import { OrderItemFullFragment } from '@lib/_generated/graphql_sdk'

export const OrderItem = ({ item }: { item: OrderItemFullFragment }) => {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center py-3 border-b border-gray-100 last:border-0 gap-3 sm:gap-0">
      <div className="flex-shrink-0 sm:mr-4 relative">
        {item.product?.gallery && item.product.gallery[0]?.image?.src ? (
          <div className="w-12 h-12 sm:w-16 sm:h-16 relative rounded-md overflow-hidden border border-gray-200">
            <Img
              src={item.product.gallery[0].image.src}
              mobileSrc={item.product.gallery[0].image.mobileSrc || item.product.gallery[0].image.src}
              alt={item.product.name || 'Product image'}
              fill
              className="object-cover"
            />
          </div>
        ) : (
          <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-100 flex items-center justify-center rounded-md">
            <LucidePackage className="text-gray-400" size={20} />
          </div>
        )}
      </div>
      <div className="flex flex-row justify-between sm:flex-auto">
        <div className="flex-1">
          <div className="font-medium text-sm sm:text-base">{item.product?.name || item.sku}</div>
          <div className="text-xs sm:text-sm text-gray-500">SKU: {item.sku}</div>
          {item.discountPercent > 0 && (
            <div className="text-xs text-green-600 font-medium">Отстъпка: {item.discountPercent}%</div>
          )}
        </div>
        <div className="text-right">
          <div className="font-medium text-sm sm:text-base">{item.baseQty} бр.</div>
          {item.price && (
            <div className="text-xs sm:text-sm">
              {item.price.value} {item.price.currency}
            </div>
          )}
          {item.rowTotal && (
            <div className="text-xs sm:text-sm font-bold">
              Общо: {item.rowTotal.value} {item.rowTotal.currency}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
