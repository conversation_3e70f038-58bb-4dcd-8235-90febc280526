'use client'
import { useCustomerStore } from '@features/customer/customer.store'
import { useEffect, useState } from 'react'
import { Card, CardContent } from '@components/theme/ui/card'
import Text from '@atoms/Text'
import Title from '@atoms/Title'
import { OrderCard } from '@/src/app/(store)/sales/order/history/components/OrderCard'
import { OrderSkeleton } from '@/src/app/(store)/sales/order/history/components/OrderSkeleton'

export const dynamic = 'force-dynamic'

const OrderHistory = () => {
  const [loading, setLoading] = useState(false)
  const orders = useCustomerStore((state) => state.orders)
  const getOrders = useCustomerStore((state) => state.getCustomerOrders)

  useEffect(() => {
    setLoading(true)
    getOrders().finally(() => setLoading(false))
  }, [getOrders])

  return (
    <div className="w-full py-4 md:py-6 px-2 md:px-4">
      <Title className="text-xl md:text-2xl font-bold mb-4 md:mb-6">История на поръчките</Title>

      {loading ? (
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <OrderSkeleton key={i} />
          ))}
        </div>
      ) : orders.length > 0 ? (
        <div className="space-y-4">
          {orders.map((order) => (
            <OrderCard key={order.incrementId} order={order} />
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="py-6 md:py-8 text-center">
            <Text className="text-gray-500">Нямате направени поръчки</Text>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default OrderHistory
