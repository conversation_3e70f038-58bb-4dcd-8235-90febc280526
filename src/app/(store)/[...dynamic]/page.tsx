import React from 'react'

import CMSView from '@/src/app/(store)/[...dynamic]/(cms)/CMSView'
import { getPageData } from '@/src/app/(store)/[...dynamic]/api'
import { isCatalogPage, isCMSPage, isProductPage } from '@/src/features/static/page'
import Text from '@atoms/Text'
import { CatalogPage } from '@components/pages/Catalog/CatalogPage'
import ProductPage from '@components/pages/Product/ProductPage'
import { NextPageProps, SlugParam } from '@lib/utils/page'
import { notFound } from 'next/navigation'

import { generateMetadata as _generateMetadata } from './api'
export const generateMetadata = (props: NextPageProps<SlugParam>) => _generateMetadata(props)
export default async function DynamicPage(props: NextPageProps<SlugParam>) {
  const { dynamic: slug } = await props.params
  const searchParams = await props.searchParams
  console.log({ searchParams })

  const route = await getPageData({
    slug,
    searchParams: searchParams as { [key: string]: string },
  })

  const breadcrumbs = route.breadcrumbs

  console.log({ route })
  if (isCatalogPage(route.data)) {
    return <CatalogPage data={route.data} breadcrumbs={breadcrumbs} />
  } else if (isCMSPage(route.data)) {
    return <CMSView slug={slug} data={route.data} breadcrumbs={breadcrumbs} />
  } else if (isProductPage(route.data)) {
    return <ProductPage data={route.data} breadcrumbs={breadcrumbs} />
  }

  return (
    <div>
      <Text>Unknown page</Text>
    </div>
  )
}
