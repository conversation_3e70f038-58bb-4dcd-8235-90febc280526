import { QueryParam } from '@lib/_generated/graphql_sdk'
import { NextPageProps, SearchParams } from '@lib/utils/page'

export function toQueryParams(searchParams?: SearchParams): QueryParam[] {
  if (!searchParams) {
    return []
  }

  return Object.entries(searchParams).map(([key, value]) => ({
    name: key,
    value: String(value),
  }))
}

export function slugToUrl(slug: string[]): string {
  return slug.join('/')
}

export function toPrice(price: number): number {
  return Math.round((price + Number.EPSILON) * 100) / 100
}
