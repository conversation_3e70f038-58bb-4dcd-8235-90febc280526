'use client'

import React from 'react'

import { addClasses } from '@/src/lib/style/style'
import AppLink from '@atoms/AppLink'
import ArrowCircle from '@atoms/Icons/ArrowCircle'
import Paper from '@atoms/Paper'
import { useSimpleMenuContext } from '@components/context/SimpleMenuContext'
import { Link } from '@lib/_generated/graphql_sdk'

interface Props {
  links?: Array<Link>
  title?: string
  identifier?: string
}

const CMSView: React.FC<Props> = ({ links, title, identifier }) => {
  const { isOpen, toggle } = useSimpleMenuContext()

  return (
    <div className="min-h-[80px] w-full">
      <div className={isOpen ? 'min-h-[65px] absolute top-0 w-full' : ''}>
        <Paper
          className={addClasses('flex w-full phone:mb-6 tablet:mb-6', 'min-w-[30%] p-8 flex-col phone:p-0 tablet:p-0')}
        >
          <div
            onClick={toggle}
            className={addClasses(
              'cursor-pointer text-2xl font-bold desktop:hidden',
              'phone:py-6 tablet:py-6 phone:px-8 tablet:px-8',
              'flex justify-between items-center w-full t-0'
            )}
          >
            {title}
            <ArrowCircle dark className={isOpen ? 'rotate-180' : ''} />
          </div>

          <ul
            className={addClasses(
              'w-full flex flex-col',
              'phone:p-5 tablet:p-5 phone:pt-0 tablet:pt-0',
              !isOpen && 'phone:hidden tablet:hidden',
              isOpen && 'phone:flex tablet:flex'
            )}
          >
            {links?.map((link, i) => {
              return (
                <li key={i} className="w-full">
                  <AppLink
                    className={addClasses(
                      'rounded-full px-4 hover:bg-primary hover:text-white',
                      'transition-all duration-200 ease-in-out flex',
                      'py-2 phone:py-4 tablet:py-4 mb-1',
                      link.href === `/${identifier}` && 'desktop:bg-primary desktop:text-white'
                    )}
                    {...link}
                  />
                </li>
              )
            })}
          </ul>
        </Paper>
      </div>
    </div>
  )
}

export default CMSView
