import React from 'react'

import { CMSViewProps } from '@/src/app/(store)/[...dynamic]/(cms)/tyoes'
import { addClasses } from '@/src/lib/style/style'
import Container from '@atoms/Container'
import Paper from '@atoms/Paper'
import RawContent from '@atoms/RawContent'
import Title from '@atoms/Title'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'

import CMSMenu from './CMSMenu'
import { CMSMarkup } from '@/src/components/molecules/Widget/markup/instanced'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'

const CMSView: React.FC<CMSViewProps> = ({ slug, data, breadcrumbs }) => {
  // console.log('CMSView', data?.content)
  return (
    <Container className="h-full min-h-fit">
      <div className="my-5">
        <Breadcrumb breadcrumbs={breadcrumbs} />
      </div>
      <meta name="robots" content="INDEX,FOLLOW" />
      <div className="flex flex-col md:flex-row gap-4 min-h-fit h-full">
        {!(slug[0] === 'uslugi' || slug[0] === 'montajni_uslugi' || slug[0] === 'brochure') && (
          <div className="md:w-[20%]">
            {data?.links && <CMSMenu links={data.links} title={data.title} identifier={data.identifier} />}
          </div>
        )}
        <div className={addClasses(' bg-white rounded-2xl shadow-lg w-full p-4 md:p-8 xl:p-20 h-fit mb-8', 'xl:pt-14')}>
          <Title className="text-3xl xl:text-4xl font-bold mb-4">{data?.title}</Title>
          {/* <RawContent content={data?.content} /> */}
          <CMSMarkup markup={data?.content || ''} />
        </div>
      </div>
      <RelevaTrackPage />
    </Container>
  )
}

export default CMSView
