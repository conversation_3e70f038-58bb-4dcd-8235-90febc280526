import React from 'react'

import SimpleMenu from '@components/context/SimpleMenuContext'
import { Link } from '@lib/_generated/graphql_sdk'

import CMSLinks from './CMSLinks'

interface Props {
  links?: Array<Link>
  title?: string
  identifier?: string
}

const CMSView: React.FC<Props> = ({ links, title, identifier }) => {
  return (
    <SimpleMenu>
      <CMSLinks links={links} title={title} identifier={identifier} />
    </SimpleMenu>
  )
}

export default CMSView
