'use client'

import React, { useCallback } from 'react'
import { Pager } from '@lib/_generated/graphql_sdk'
import { usePathname, useSearchParams } from 'next/navigation'
import Link from 'next/link'

interface PaginationHorizontalProps {
  pagination?: Pager
}

const Ellipsis = () => {
  return <div className="flex items-center sm:px-3">...</div>
}

export const PaginationHorizontal: React.FC<PaginationHorizontalProps> = ({ pagination }) => {
  const pathname = usePathname()
  const searchParams = useSearchParams();

  const getPageUrl = useCallback((page: number) => {
    const params = new URLSearchParams(searchParams)
    if (page === 1) {
      params.delete('p')
      return pathname + (params.toString() ? '?' + params.toString() : '')
    }
    params.set('p', page.toString())
    return pathname + '?' + params.toString()
  }, [pathname, searchParams])

  const getPageNumbers = () => {
    const totalPages = pagination?.totalPages || 0
    const currentPage = pagination?.page || 1

    // If less than 15 pages, show all
    if (totalPages <= 15) {
      return Array.from({ length: totalPages }, (_, i) => i + 1)
    }

    const pageNumbers: (number | string)[] = []

    // Always show first 3 pages
    pageNumbers.push(1, 2, 3)

    console.log(currentPage, totalPages, totalPages - 4)
    // Handle special cases EXACTLY as specified
    if (currentPage === 3) {
      // 1,2,3,4,5,...,47,48,49
      pageNumbers.push(4)
      pageNumbers.push('ellipsis1')
    } else if (currentPage === 4) {
      // 1,2,3,4,5,6,...,47,48,49
      pageNumbers.push(4, 5)
      pageNumbers.push('ellipsis1')
    } else if (currentPage === 5) {
      // 1,2,3,4,5,6,...,47,48,49
      pageNumbers.push(4, 5, 6)
      pageNumbers.push('ellipsis1')
    } else if (currentPage === 6) {
      // 1,2,3,...,5,6,7,...,47,48,49
      pageNumbers.push('ellipsis1')
      pageNumbers.push(5, 6, 7)
      pageNumbers.push('ellipsis2')
    } else if (currentPage === totalPages - 4) {
      // Page 44
      // 1,2,3,...,43,44,45,...,47,48,49
      pageNumbers.push('ellipsis1')
      pageNumbers.push(totalPages - 5, totalPages - 4, totalPages - 3)
      pageNumbers.push('ellipsis2')
    } else if (currentPage === totalPages - 3) {
      console.log('currentPage === totalPages - 4', currentPage === totalPages - 4)
      // Page 45
      // 1,2,3,...,44,45,46,47,48,49
      pageNumbers.push('ellipsis1')
      pageNumbers.push(totalPages - 5, totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages)
      return pageNumbers // Return early to avoid duplicates
    } else if (currentPage === totalPages - 2) {
      // Page 46
      // 1,2,3,...,45,46,47,48,49
      pageNumbers.push('ellipsis1')
      pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages)
      return pageNumbers // Return early to avoid duplicates
    } else if (currentPage <= 3) {
      // Current page is in first 3, just add dots
      pageNumbers.push('ellipsis1')
    } else if (currentPage >= totalPages - 2) {
      // Current page is in last 3, just add dots
      pageNumbers.push('ellipsis1')
    } else {
      // Standard middle case
      pageNumbers.push('ellipsis1')
      pageNumbers.push(currentPage - 1, currentPage, currentPage + 1)
      pageNumbers.push('ellipsis2')
    }

    // Always show last 3 pages (avoiding duplicates)
    for (let i = totalPages - 2; i <= totalPages; i++) {
      if (i > 0 && !pageNumbers.includes(i)) {
        pageNumbers.push(i)
      }
    }

    return pageNumbers
  }

  const pageNumbers = getPageNumbers()

  return (
    <div className="col-span-full flex justify-center gap-0 sm:gap-2 mt-4">
      {pageNumbers.map((pageNum, index) => {
        if (pageNum === 'ellipsis1' || pageNum === 'ellipsis2') {
          return <Ellipsis key={`${pageNum}-${index}`} />
        }

        const page = pageNum as number
        return (
          <Link
            key={index}
            href={getPageUrl(page)}
            className={`inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 h-9 px-3 ${
              pagination?.page === page
                ? 'bg-primary text-primary-foreground hover:bg-primary/90' 
                : 'border border-input bg-background hover:bg-gray-400 shadow-none hover:text-accent-foreground'
            }`}
          >
            {page}
          </Link>
        )
      })}
    </div>
  )
}
