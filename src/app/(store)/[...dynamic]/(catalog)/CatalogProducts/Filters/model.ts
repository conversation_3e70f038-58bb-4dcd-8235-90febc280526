import { useFilterStore } from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/store/CatalogStore'

export const getSortingQuery = (state = useFilterStore.getState()): string => {
  const { sorting } = state
  const sortingQuery =
    sorting?.field && sorting.order
      ? `dir=${encodeURIComponent(`${sorting.order}`)}&order=${encodeURIComponent(`${sorting.field}`)}`
      : ''
  return [sortingQuery].filter(Boolean).join('&')
}

export const getPriceQuery = (state = useFilterStore.getState()) => {
  const { priceRange } = state
  const priceRangeQuery =
    priceRange?.min !== undefined && priceRange.max !== undefined
      ? `price=${encodeURIComponent(`${Math.round(priceRange.min)}-${Math.round(priceRange.max)}`)}`
      : ''
  return [priceRangeQuery].filter(Boolean).join('&')
}

export const getCheckboxQuery = (state = useFilterStore.getState()): string => {
  const { checkboxes } = state
  const filterQueries = Object.entries(checkboxes).map(
    ([type, values]) => `${encodeURIComponent(type)}=${encodeURIComponent(values.join(','))}`
  )
  return [...filterQueries].filter(Boolean).join('&')
}

export const getLimitQuery = (state = useFilterStore.getState()): string => {
  const { limit } = state
  const limitQuery = limit ? `limit=${encodeURIComponent(`${limit}`)}` : ''
  return [limitQuery].filter(Boolean).join('&')
}

export const getPageQuery = (state = useFilterStore.getState()): string => {
  const { page } = state
  const pageQuery = page ? `p=${encodeURIComponent(`${page}`)}` : ''
  return [pageQuery].filter(Boolean).join('&')
}

export const getAllQueryParams = () => {
  const filterStore = useFilterStore.getState()
  const sortingQuery = getSortingQuery(filterStore)
  const priceQuery = getPriceQuery(filterStore)
  const checkboxQuery = getCheckboxQuery(filterStore)
  const limitQuery = getLimitQuery(filterStore)
  const pageQuery = getPageQuery(filterStore)

  return [sortingQuery, priceQuery, checkboxQuery, limitQuery, pageQuery].filter(Boolean).join('&')
}
