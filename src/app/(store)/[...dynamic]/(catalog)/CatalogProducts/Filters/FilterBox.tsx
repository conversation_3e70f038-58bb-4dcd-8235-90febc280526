'use client'
import React from 'react'

import ArrowCircle from '@atoms/Icons/ArrowCircle'
import Paper from '@atoms/Paper'
import { useSimpleMenuContext } from '@components/context/SimpleMenuContext'
import { addClasses } from '@lib/style/style'

interface Props {
  label: string
  children: React.ReactNode
}

const CheckboxFilterBox: React.FC<Props> = ({ label, children }) => {
  const { isOpen, toggle } = useSimpleMenuContext()

  return (
    <Paper className="w-full shadow-none p-0 min-w-[216px]">
      <div
        onClick={toggle}
        className={addClasses('flex font-bold uppercase text-sm p-6', 'cursor-pointer justify-between items-center')}
      >
        {label}
        <ArrowCircle className={isOpen ? 'rotate-180' : ''} />
      </div>

      {isOpen && children}
    </Paper>
  )
}

export default CheckboxFilterBox
