'use client'
import { usePathname, useRouter } from 'next/navigation'
import React, { useCallback, useMemo } from 'react'

import CustomButton from '@atoms/Button/CustomButton'
import ArrowCircle from '@atoms/Icons/ArrowCircle'
import Img from '@atoms/Img'
import { cn } from '@components/lib/utils'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@components/theme/ui/select'
import { Pager } from '@lib/_generated/graphql_sdk'
import Link from 'next/link'

interface Props {
  pager?: Pager
}

const Pagination: React.FC<Props> = ({ pager }) => {
  const router = useRouter()
  const pathname = usePathname()
  const [isSelectOpen, setIsSelectOpen] = React.useState(false)
  const onChangePage = useCallback(
    (page: number) => {
      const url = new URL(window.location.href)
      const searchParams = url.searchParams

      searchParams.set('p', page.toString())
      if (page === 1) searchParams.delete('p')
      router.push(`${url.pathname}?${searchParams.toString()}`, { scroll: false })
    },
    [router]
  )

  const { totalPages, page } = pager || { totalPages: 1, page: 1 }

  const prevUrl = useMemo(() => {
    if (page === 1) {
      return
    } else if (page === 2) {
      return pathname
    }
    return `${pathname}?p=${page - 1}`
  }, [page, pathname])

  const nextUrl = useMemo(() => {
    if (page === totalPages) {
      return
    }

    return `${pathname}?p=${page + 1}`
  }, [page, totalPages, pathname])

  const handleSubmit = (e: any) => {
    e.preventDefault()
  }
  return (
    <div
      className={cn(
        'border-t border-gray-200 px-4 desktop:p-0 desktop:border-none',
        'flex justify-between desktop:justify-end'
      )}
    >
      <div className="flex items-center">
        {prevUrl && <link key={prevUrl} rel="prev" href={prevUrl} />}
        <Link href={prevUrl || '#'} className="rotate-90 w-[37px] h-[37px] flex justify-center items-center">
          <ArrowCircle />
        </Link>
        <div className="px-4">
          <span className="text-xl">{page}</span>
          <span className="text-zinc-400 text-sm"> / {totalPages}</span>
        </div>
        {nextUrl && <link key={nextUrl} rel="next" href={nextUrl} />}
        <Link href={nextUrl || '#'} className="rotate-90 w-[37px] h-[37px] flex justify-center items-center">
          <ArrowCircle className="rotate-180" />
        </Link>
      </div>
      <div className="flex items-center pl-4 xl:pr-2">
        <form onSubmit={handleSubmit} className="flex items-center relative">
          <Select
            value={page.toString()}
            onOpenChange={setIsSelectOpen}
            onValueChange={(value) => {
              const p = Number(value)
              if (p > 0 && p <= totalPages) {
                onChangePage(p)
              }
            }}
          >
            <SelectTrigger
              className={cn(
                'w-[60px] rounded-full border border-zinc-300 px-4 my-2 font-bold',
                'md:text-xs h-auto [&>svg]:hidden' // Hide the default dropdown icon
              )}
            >
              <SelectValue placeholder={`Page (${page})`} />
            </SelectTrigger>
            <SelectContent className="max-h-[200px] overflow-y-auto bg-white" position="popper" sideOffset={5}>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (
                <SelectItem key={pageNum} value={pageNum.toString()}>
                  {pageNum}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Img
            alt="Go to page"
            className={cn(
              'absolute right-[10px] p-[10px] cursor-pointer pointer-events-none',
              isSelectOpen && 'rotate-90'
            )}
            src={'/images/icons/arrow-black.svg'}
            width={27}
            height={26}
            style={{ objectFit: 'contain' }}
          />
        </form>
      </div>
    </div>
  )
}

export default Pagination
