'use client'
import React, { useCallback } from 'react'

import Img from '@atoms/Img'
import { AppliedFilter, AvailableFilter } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'

interface Props {
  filter: AvailableFilter
  appliedFilters: Array<AppliedFilter>
  handleCheckboxChange: (event: React.ChangeEvent<HTMLInputElement>, type: string, value: string) => void
}

const FilterItem_Checkbox: React.FC<Props> = ({ filter, appliedFilters, handleCheckboxChange }) => {
  const checkboxCheck = useCallback(
    (optionValue: string) =>
      appliedFilters.some((af) => af.attributeCode === filter.attributeCode && af.value.includes(optionValue)),
    [appliedFilters, filter.attributeCode]
  )

  return (
    <div className="px-6 pb-6">
      <div className="overflow-auto max-h-[232px]">
        {filter.options.map((option, index) => (
          <label key={index} className="flex items-center cursor-pointer mb-3 last:mb-0">
            <div
              className={addClasses(
                'min-w-[18px] h-[18px] flex rounded bg-background',
                'border border-gray-300 mr-3 items-center justify-center',
                `${checkboxCheck(option.value) ? 'bg-primary border-none' : ''}`
              )}
            >
              <Img
                src={'/images/icons/checkbox-white.svg'}
                width={11}
                height={11}
                alt="checkbox"
                className={checkboxCheck(option.value) ? '' : 'hidden'}
              />
              <input
                className="hidden"
                type="checkbox"
                checked={checkboxCheck(option.value)}
                onChange={(e) => handleCheckboxChange(e, filter.attributeCode, option.value)}
              />
            </div>
            <div className={addClasses('text-sm', `${checkboxCheck(option.value) ? 'font-bold' : ''}`)}>
              {option.label}
            </div>
          </label>
        ))}
      </div>
    </div>
  )
}

export default FilterItem_Checkbox
