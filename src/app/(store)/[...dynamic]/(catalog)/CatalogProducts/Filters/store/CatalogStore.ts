import { create } from 'zustand'

import { SortDirection } from '@lib/_generated/graphql_sdk'

interface FilterState {
  location: string
  checkboxes: Record<string, string[]>
  priceRange: { min: number; max: number } | null
  sorting: { field: string; order: SortDirection } | null
  limit: string | null
  page: number
  isLoading: boolean

  addFilter: (type: string, value: string) => void
  removeFilter: (type: string, value: string) => void
  setLoading: (isLoading: boolean) => void
  setPriceRange: (min: number, max: number) => void
  setDefaultPriceRange: (min: number, max: number) => void
  setSorting: (field: string, order: SortDirection) => void
  setLimit: (limit: string) => void
  setLocation: (location: string) => void
  setPage: (page: number) => void
}

export const useFilterStore = create<FilterState>((set) => ({
  location: '',
  checkboxes: {},
  priceRange: null,
  sorting: null,
  limit: null,
  page: 1,
  isLoading: false,
  addFilter: (type, value) =>
    set((state) => {
      const updatedCheckboxes = { ...state.checkboxes }
      if (!updatedCheckboxes[type]) {
        updatedCheckboxes[type] = [value]
      } else if (!updatedCheckboxes[type].includes(value)) {
        updatedCheckboxes[type].push(value)
      }
      return { checkboxes: updatedCheckboxes }
    }),
  removeFilter: (type, value) =>
    set((state) => {
      const updatedCheckboxes = { ...state.checkboxes }
      if (updatedCheckboxes[type]) {
        updatedCheckboxes[type] = updatedCheckboxes[type].filter((v) => v !== value)
        if (updatedCheckboxes[type].length === 0) {
          delete updatedCheckboxes[type]
        }
      }
      return { checkboxes: updatedCheckboxes }
    }),
  setLoading: (isLoading) => set({ isLoading }),
  setPriceRange: (min, max) => set({ priceRange: { min, max } }),
  setDefaultPriceRange: (min: number, max: number) => {
    set({ priceRange: { min, max } })
  },
  setSorting: (field, order) => set({ sorting: { field, order } }),
  setLimit: (limit: string) => set({ limit }),
  setLocation: (location) =>
    set((state) => {
      if (state.location !== location) {
        return {
          location,
          checkboxes: {},
          priceRange: null,
          sorting: null,
          limit: null,
        }
      }
      return { location }
    }),
  setPage: (page) => set({ page }),
}))
