'use client'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import { useRouter } from 'next/navigation'
import React, { useCallback, useEffect } from 'react'

import { FullScreenLoader } from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/components/FullScreenLoader'
import { getAllQueryParams } from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/model'
import { useFilterStore } from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/store/CatalogStore'
import SimpleMenu from '@components/context/SimpleMenuContext'
import { AppliedFilter, AvailableFilter, FilterRenderType } from '@lib/_generated/graphql_sdk'

import AppliedFilters from './AppliedFilters'
import FilterBox from './FilterBox'
import FilterItem_Checkbox from './FilterItem_Checkbox'
import FilterItemSlider from './FilterItem_Slider'

interface Props {
  filters: Array<AvailableFilter>
  appliedFilters: Array<AppliedFilter>
}

const updateUrl = (router: AppRouterInstance) => {
  const queryParams = getAllQueryParams()
  const newUrl = `${window.location.pathname}${queryParams ? `?${queryParams}` : ''}`
  router.push(newUrl, { scroll: false })
}

const CatalogFilters: React.FC<Props> = ({ filters, appliedFilters }) => {
  const defaultOpenedFilterBoxes = 3
  const router = useRouter()
  const { isLoading, setLoading, addFilter, removeFilter, setPriceRange, setLocation } = useFilterStore()

  const checkFilterFromGroupApplied = useCallback(
    (filter: AvailableFilter) => {
      return appliedFilters.some((af) => af.attributeCode === filter.attributeCode)
    },
    [appliedFilters]
  )

  const handleCheckboxChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, type: string, value: string) => {
      setLoading(true)
      if (event.target.checked) {
        addFilter(type, value)
      } else {
        removeFilter(type, value)
      }
      updateUrl(router)
    },
    [addFilter, removeFilter, setLoading, router]
  )

  const handlePriceChange = useCallback(
    (min: number, max: number) => {
      setPriceRange(min, max)
      updateUrl(router)
    },
    [setPriceRange, router]
  )

  useEffect(() => {
    setLoading(false)
    setLocation(window.location.pathname)
  }, [filters, setLoading, setLocation])

  return (
    <div className="grid gap-6">
      {isLoading && <FullScreenLoader />}
      <AppliedFilters appliedFilters={appliedFilters} />
      {filters.map((filter, index) => (
        <div key={index}>
          <SimpleMenu defaultIsOpen={index < defaultOpenedFilterBoxes || checkFilterFromGroupApplied(filter)}>
            {filter.type === FilterRenderType.Slider ? (
              <FilterBox label={filter.label}>
                <FilterItemSlider
                  filter={filter}
                  minValue={Number(filter.options[0].label)}
                  maxValue={Number(filter.options[1].label)}
                  handleSLiderChange={handlePriceChange}
                />
              </FilterBox>
            ) : (
              <FilterBox label={filter.label}>
                <FilterItem_Checkbox
                  filter={filter}
                  appliedFilters={appliedFilters}
                  handleCheckboxChange={handleCheckboxChange}
                />
              </FilterBox>
            )}
          </SimpleMenu>
        </div>
      ))}
    </div>
  )
}

export default CatalogFilters
