import { LucideLoaderCircle } from 'lucide-react'
import React from 'react'

import Text from '@atoms/Text'

export const FullScreenLoader = () => {
  return (
    <div className="fixed top-0 left-0 bg-white/80 w-full h-full flex z-50 flex justify-center items-center">
      <LucideLoaderCircle size={170} className="animate-spin text-primary" />
      <div className="flex justify-center items-center absolute w-full h-full">
        <span className="text-primary font-bold text-sm">
          <Text>Зареждане</Text>...
        </span>
      </div>
    </div>
  )
}
