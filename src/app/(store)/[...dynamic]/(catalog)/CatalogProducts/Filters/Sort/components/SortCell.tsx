import React, { PropsWithChildren } from 'react'

import { cn } from '@components/lib/utils'
import { PropsWithClassName } from '@lib/types/ClassName'

export const SortCell: React.FC<PropsWithChildren<PropsWithClassName>> = ({ children, className }) => {
  return (
    <div
      className={cn(
        'col-span-12 flex justify-end flex-col gap-2 xl:flex-row border-blue-700',
        'md:col-span-5 xl:col-span-6 2xl:col-span-4',
        className
      )}
    >
      {children}
    </div>
  )
}
