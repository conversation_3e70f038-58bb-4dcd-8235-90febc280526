'use client'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

import { SortCell } from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/Sort/components/SortCell'
import { SortLabel } from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/Sort/components/SortLabel'
import { cn } from '@components/lib/utils'
import { CollectionSort, SortDirection } from '@lib/_generated/graphql_sdk'

interface Props {
  defaultSort: CollectionSort
  pageSize: number
}

const Sort: React.FC<Props> = ({ defaultSort, pageSize }) => {
  const router = useRouter()
  const [sort, setSort] = useState<CollectionSort>({
    value: defaultSort.value,
    dir: defaultSort.dir.toUpperCase() as SortDirection,
  })
  const [limit, setLimit] = useState(pageSize)

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const [sort, order] = e.target.value.split('-')
    const url = new URL(window.location.href)
    const searchParams = url.searchParams
    searchParams.set('sort', sort)
    searchParams.set('order', order)
    searchParams.delete('p')
    setSort({
      value: sort,
      dir: order === SortDirection.Asc ? SortDirection.Asc : SortDirection.Desc,
    })
    router.push(url.toString().toLowerCase(), { scroll: false })
  }

  const handleLimitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const url = new URL(window.location.href)
    const searchParams = url.searchParams
    searchParams.set('limit', e.target.value)
    setLimit(parseInt(e.target.value))
    router.push(url.toString(), { scroll: false })
  }

  return (
    <div className="grid grid-cols-24 gap-3 w-full">
      <SortCell className="md:col-start-14 xl:col-start-12 2xl:col-start-15 md:col-span-6 xl:col-span-7 2xl:col-span-6 ">
        <SortLabel htmlFor="sort">Подреди по:</SortLabel>
        <select
          id="sort"
          className={cn('font-bold w-full xl:w-1/2', sort.dir === SortDirection.Desc ? 'bg-arrow-down' : 'bg-arrow-up')}
          value={`${sort.value}-${sort.dir.toUpperCase()}`}
          onChange={handleSortChange}
        >
          <option value="position-DESC">Най-нови</option>
          <option value="price-ASC">Цена възходяща</option>
          <option value="price-DESC">Цена низходяща</option>
          <option value="best_value-DESC">Най-голяма отстъпка</option>
        </select>
      </SortCell>
      <SortCell>
        <SortLabel htmlFor="limit">Покажи по:</SortLabel>
        <select id="limit" className="font-bold w-full xl:w-1/2" value={limit.toString()} onChange={handleLimitChange}>
          <option value="24">24</option>
          <option value="36">36</option>
          <option value="48">48</option>
        </select>
      </SortCell>
    </div>
  )
}

export default Sort
