'use client'
import React, { useCallback, useState } from 'react'

import { formatPrice } from '@/src/features/product/filters'
import { AvailableFilter } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'
import { getDebounceCaller } from '@lib/utils/func'

import './slider-filter.css'

interface Props {
  filter: AvailableFilter
  minValue: number
  maxValue: number
  handleSLiderChange: (min: number, max: number) => void
}

enum FilterType {
  Min = 'min',
  Max = 'max',
}

const FilterItemSlider: React.FC<Props> = ({ minValue, maxValue, handleSLiderChange }) => {
  const [values, setValues] = useState({
    min: minValue,
    max: maxValue,
  })

  const setFilterValues = useCallback((type: FilterType, value: string) => {
    setValues((prevValues) => ({
      ...prevValues,
      [type]: value,
    }))
  }, [])

  const debouncedSetPriceRange = getDebounceCaller((min: number, max: number) => {
    handleSLiderChange(min, max)
  }, 1000)

  const handleChangeMin = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (Number(e.target.value) <= values.max) {
      setFilterValues(FilterType.Min, e.target.value)
    }
  }

  const handleChangeMax = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (Number(e.target.value) >= values.min) {
      setFilterValues(FilterType.Max, e.target.value)
    }
  }

  const handlePriceFinalize = () => {
    debouncedSetPriceRange(Number(values.min), Number(values.max))
  }

  const fillSlider = useCallback(() => {
    const fromPercentage = ((values.min - minValue) / (maxValue - minValue)) * 100
    const toPercentage = ((values.max - minValue) / (maxValue - minValue)) * 100

    return `linear-gradient( to right,
        rgb(243,243,243) 0%,
        rgb(243,243,243) ${fromPercentage}%,
        rgb(238 127 0) ${fromPercentage}%,
        rgb(238 127 0) ${toPercentage}%,
        rgb(243,243,243) ${toPercentage}%,
        rgb(243,243,243) 100%)`
  }, [maxValue, minValue, values.max, values.min])

  return (
    <div className="slider">
      <div className={addClasses('flex w-full justify-between px-6 ', 'text-xs font-bold text-zinc-400')}>
        <span>{formatPrice(Number(minValue))}</span>
        <span>{formatPrice(Number(maxValue))}</span>
      </div>

      <div className="flex flex-col">
        <div className="relative my-6 mx-4">
          <div className="slider__input">
            <input
              className="slider__from_slider !h-[0px] z-10"
              type="range"
              min={minValue}
              max={maxValue}
              step="1"
              value={Math.ceil(Number(values.min))}
              onChange={handleChangeMin}
              onMouseUp={handlePriceFinalize}
              onTouchEnd={handlePriceFinalize}
            />
            <input
              style={{
                background: `${fillSlider()}`,
              }}
              className={addClasses(`${fillSlider()}`, 'top-[-2px] max-slider')}
              type="range"
              min={minValue}
              max={maxValue}
              step="1"
              value={Math.ceil(Number(values.max))}
              onChange={handleChangeMax}
              onMouseUp={handlePriceFinalize}
              onTouchEnd={handlePriceFinalize}
            />
          </div>
        </div>
        <div className="flex justify-between items-center p-6 pt-0">
          <input
            className="w-[80px] h-[32px] rounded-full border border-zinc-300 p-4"
            type="number"
            onChange={handleChangeMin}
            defaultValue={Math.round(Number(values.min))}
            // value={Math.round(Number(values.min))}
            max={maxValue}
            onBlur={(e) => {
              Number(e.target.value) <= values.min
                ? handlePriceFinalize()
                : alert('Min price must be smaller then max price')
            }}
          />
          <span className="px-2">-</span>
          <input
            className="w-[80px] h-[32px] rounded-full border border-zinc-300 p-4"
            type="number"
            onChange={handleChangeMax}
            defaultValue={Math.round(Number(values.max))}
            // value={Math.round(Number(values.max))}
            onBlur={(e) => {
              Number(e.target.value) >= values.min
                ? handlePriceFinalize()
                : alert('Max price must be bigger then min price')
            }}
          />
        </div>
      </div>
    </div>
  )
}

export default FilterItemSlider
