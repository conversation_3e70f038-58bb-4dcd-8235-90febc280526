'use client'
import React from 'react'

import CustomButton from '@atoms/Button/CustomButton'
import Img from '@atoms/Img'
import { useSimpleMenuContext } from '@components/context/SimpleMenuContext'
import { AppliedFilter, AvailableFilter } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'

import CatalogFilters from './CatalogFilters'

interface Props {
  filters: Array<AvailableFilter>
  appliedFilters: Array<AppliedFilter>
}

const MobileFilters: React.FC<Props> = ({ filters, appliedFilters }) => {
  const { isOpen, toggle } = useSimpleMenuContext()
  return (
    <div className="fixed left-0 bottom-0 w-full z-30">
      {isOpen && (
        <div className="bg-background h-[calc(100dvh-230px)] overflow-auto">
          <CatalogFilters filters={filters} appliedFilters={appliedFilters} />
        </div>
      )}
      <div className="p-8 pt-4">
        <div className="bg-white rounded-xl">
          <CustomButton
            onClick={toggle}
            className={addClasses(
              'xl:hidden w-full h-[50px] !rounded-lg !bg-opacity-100',
              '!normal-case sticky top-[140px] z-10 px-10'
            )}
          >
            <Img
              className={addClasses('absolute left-4 md:left-8', isOpen && 'rotate-180')}
              alt="down"
              src={'/images/icons/arrow-down-big.svg'}
              width={20}
              height={11}
              style={{ objectFit: 'contain' }}
            />

            <span>{isOpen ? 'Затвори филтрите' : 'Отвори филтри'}</span>
          </CustomButton>
        </div>
      </div>
    </div>
  )
}

export default MobileFilters
