'use client'
import { useRouter } from 'next/navigation'
import React, { useEffect } from 'react'

import { getAllQueryParams } from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/model'
import { useFilterStore } from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/store/CatalogStore'
import Img from '@atoms/Img'
import SimpleMenu from '@components/context/SimpleMenuContext'
import { AppliedFilter } from '@lib/_generated/graphql_sdk'

import FilterBox from './FilterBox'

interface Props {
  appliedFilters: Array<AppliedFilter>
}

const AppliedFilters: React.FC<Props> = ({ appliedFilters }) => {
  const { removeFilter, setLoading } = useFilterStore()
  const router = useRouter()

  const handleRemoveFilter = (attributeCode: string, value: string) => {
    setLoading(true)
    removeFilter(attributeCode, value)
    updateUrl()
  }

  const updateUrl = () => {
    const queryParams = getAllQueryParams()
    const newUrl = `${window.location.pathname}${queryParams ? `?${queryParams}` : ''}`
    router.push(newUrl, { scroll: false })
  }

  useEffect(() => {
    setLoading(false)
  }, [appliedFilters, setLoading])

  return (
    appliedFilters.length > 0 && (
      <SimpleMenu defaultIsOpen={true}>
        <FilterBox label="Текущи филтри">
          <div className="p-6 pt-0">
            {appliedFilters.map((filter, i) => (
              <div key={i} className="mb-4 pb-4 border-b border-gray-300">
                <p className="font-bold pb-2">{filter.attributeCode}:</p>
                <div className="flex flex-col">
                  {filter.value.split(',').map((val, index) => (
                    <div key={index} className="flex justify-between w-full mt-2">
                      <div>{val}</div>
                      <Img
                        alt="remove"
                        className="cursor-pointer"
                        src={'/images/icons/remove-icon.svg'}
                        width={16}
                        height={16}
                        onClick={() => handleRemoveFilter(filter.attributeCode, val)}
                      />
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </FilterBox>
      </SimpleMenu>
    )
  )
}

export default AppliedFilters
