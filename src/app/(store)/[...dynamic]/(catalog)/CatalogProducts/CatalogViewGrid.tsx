import React from 'react'

import ProductCard from '@components/molecules/ProductCard/ProductCard'
import { Pager, ProductViewFragment } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'
import { ServerGraphQLBackend } from '@lib/api/graphql_server'
import { PaginationHorizontal } from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/PaginationHorizontal'

interface Props {
  products: ProductViewFragment[]
  pagination?: Pager
}

const CatalogViewGrid: React.FC<Props> = async ({ products, pagination }) => {
  let wishlistSkus: string[] = []
  try {
    const response = await ServerGraphQLBackend.GetWishlistSkus()
    wishlistSkus = response.customerWishlist.skus
  } catch (e) {}

  return (
    <div
      className={addClasses(
        'grid place-items-center gap-y-4 gap-x-2',
        'grid-cols-2',
        'sm:grid-cols-3 sm:gap-x-3 sm:gap-y-4',
        'md:grid-cols-3 md:gap-x-3 md:gap-y-6',
        'lg:grid-cols-3 lg:gap-x-4 lg:gap-y-4',
        'xl:grid-cols-5 xl:gap-x-5 xl:gap-y-5',
        '2xl:grid-cols-6',
        '4xl:grid-cols-10 4xl:gap-y-10',
        '5xl:grid-cols-12 5xl:gap-y-14',
        '6xl:grid-cols-12 6xl:gap-y-16'
      )}
    >
      {products.map((product, index) => (
        <ProductCard key={index} product={product} inWishList={wishlistSkus.includes(product.sku)} />
      ))}
      <PaginationHorizontal pagination={pagination} />
    </div>
  )
}

export default CatalogViewGrid
