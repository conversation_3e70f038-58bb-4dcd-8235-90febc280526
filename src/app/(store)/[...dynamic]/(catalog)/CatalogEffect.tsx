'use client'
import React, { useEffect } from 'react'

const CatalogEffect: React.FC = () => {
  useEffect(() => {
    const accordionElements = document.querySelectorAll('.accordion')

    const handleAccordionClick = (index: number) => {
      accordionElements.forEach((accordion, i) => {
        const acc = accordion.classList
        if (i === index) {
          if (acc.contains('active')) {
            setTimeout(() => {
              acc.remove('active')
            }, 10)
          } else {
            setTimeout(() => {
              acc.add('active')
            }, 20)
          }
        } else {
          acc.remove('active')
        }
      })
    }

    accordionElements.forEach((accordion, index) => {
      accordion.addEventListener('click', () => handleAccordionClick(index))
      return () => {
        accordion.removeEventListener('click', () => handleAccordionClick(index))
      }
    })
  }, [])

  return <></>
}

export default CatalogEffect
