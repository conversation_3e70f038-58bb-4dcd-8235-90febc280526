import * as console from 'node:console'

import { Metadata } from 'next'
import { isRedirectError } from 'next/dist/client/components/redirect-error'
import { notFound, permanentRedirect, redirect } from 'next/navigation'

import { slugToUrl, toQueryParams } from '@/src/app/(store)/[...dynamic]/helpers'
import { AppMetaFragment, Brand, DynamicRouteDataFragment, QueryParam } from '@lib/_generated/graphql_sdk'
import { GraphQLBackend } from '@lib/api/graphql'
import { NextPageProps, SearchParams, SlugParam } from '@lib/utils/page'

interface getPageDataProps {
  slug: string[]
  searchParams: SearchParams
}

export async function getPageMetaData(url: string, query: QueryParam[]): Promise<AppMetaFragment> {
  try {
    const response = await GraphQLBackend.GetRouteMeta({
      url: url,
      query: query,
    })

    if (response.routeMeta) {
      return response.routeMeta
    }
  } catch (error) {
    console.error('Error fetching page meta data', error)
  }

  return {
    title: '404 Страницата не е намерена',
    description: 'Търсената от вас страница не е намерена.',
    image: {
      src: '',
    },
    keywords: '',
    canonicalUrl: url,
    robots: 'noindex, nofollow',
  }
}

export async function getPageData({ slug, searchParams }: getPageDataProps): Promise<DynamicRouteDataFragment> {
  try {
    const { route } = await GraphQLBackend.GetRouteData({
      url: slugToUrl(slug),
      query: toQueryParams(searchParams),
    })

    if (route.status?.redirectUrl) {
      const statusCode = route.status.statusCode == 301 ? 301 : 302
      const redirectUrl = !route.status.redirectUrl.startsWith('/')
        ? `/${route.status.redirectUrl}`
        : route.status.redirectUrl

      if (statusCode === 301) {
        permanentRedirect(redirectUrl)
      }
      redirect(redirectUrl)
    } else if (!route.data) {
      permanentRedirect('/404')
    }

    return route
  } catch (error) {
    if (isRedirectError(error)) {
      throw error
    } else {
      console.error('Error fetching page data: ', error)
      notFound()
    }
  }
}

export async function generateMetadata(props?: NextPageProps<SlugParam>): Promise<Metadata> {
  const searchParams = await props?.searchParams
  const params = await props?.params
  const meta = await getPageMetaData(params ? slugToUrl(params.dynamic) : '/', toQueryParams(searchParams))

  return {
    title: meta.title,
    description: meta.description,
    keywords: meta.keywords,
    alternates: {
      canonical: meta.canonicalUrl,
    },
    // OpenGraph metadata
    openGraph: {
      title: meta.title,
      description: meta.description,
      url: meta.canonicalUrl,
      siteName: 'Praktis',
      images: meta.image?.src
        ? [
            {
              url: meta.image.src,
              width: 1200,
              height: 630,
              alt: meta.title,
            },
          ]
        : [
            {
              url: 'https://praktis.bg/images/logo.png',
              width: 1200,
              height: 630,
              alt: meta.title,
            },
          ],
      locale: 'bg_BG',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: meta.title,
      description: meta.description,
      images: meta.image?.src ? [meta.image.src] : [],
    },
    robots: meta.robots,
    metadataBase: new URL('https://praktis.bg'),
  }
}

type GetBrandsProps = { featured?: boolean } | undefined

export async function getBrands({ featured }: GetBrandsProps = {}): Promise<Brand[]> {
  try {
    const response = await GraphQLBackend.GetBrands({
      featured: featured ?? false,
    })

    if (response && response.getBrands) {
      return response.getBrands
    }
  } catch (error) {
    console.error(error)
  }

  return []
}
