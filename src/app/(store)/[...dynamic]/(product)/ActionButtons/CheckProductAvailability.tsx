'use client'
import React from 'react'

import { addClasses } from '@/src/lib/style/style'
import CustomButton from '@atoms/Button/CustomButton'
import { scrollToElement } from '@components/context/ScrollUtils'

const CheckProductAvailability = () => {
  return (
    <CustomButton
      variant="border-primary"
      onClick={() => scrollToElement('availability-description-block')}
      className={addClasses('mt-4 h-[42px] w-full mb-6', 'cursor-pointer normal-case')}
    >
      Провери наличност в магазините
    </CustomButton>
  )
}

export default CheckProductAvailability
