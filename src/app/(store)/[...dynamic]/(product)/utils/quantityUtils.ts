export const incrementQuantity = (quantity: number, sqMValue: number) => {
  const newQuantity = quantity + 1
  const newSqMeters = sqMValue * newQuantity
  return { newQuantity, newSqMeters: Math.ceil(newSqMeters) }
}

export const decrementQuantity = (quantity: number, sqMValue: number) => {
  if (quantity > 1) {
    const newQuantity = quantity - 1
    const newSqMeters = sqMValue * newQuantity
    return { newQuantity, newSqMeters: Math.ceil(newSqMeters) }
  }
  return { newQuantity: quantity, newSqMeters: sqMValue * quantity }
}

export const incrementSqMeters = (sqMeters: number, sqMValue: number) => {
  const newSqMeters = sqMeters + 1
  const newQuantity = Math.ceil(newSqMeters / sqMValue)
  return { newSqMeters, newQuantity }
}

export const decrementSqMeters = (sqMeters: number, sqMValue: number) => {
  if (sqMeters > 1) {
    const newSqMeters = sqMeters - 1
    const newQuantity = Math.ceil(newSqMeters / sqMValue)
    return { newSqMeters, newQuantity }
  }
  return { newSqMeters: sqMeters, newQuantity: Math.ceil(sqMeters / sqMValue) }
}
