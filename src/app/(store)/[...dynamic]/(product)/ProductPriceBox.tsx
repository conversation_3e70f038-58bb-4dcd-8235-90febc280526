import React from 'react'

import { formatPrice } from '@/src/features/product/filters'
import { ProductPrice } from '@/src/lib/_generated/graphql_sdk'
import { addClasses } from '@/src/lib/style/style'
import Text from '@atoms/Text'

interface Props {
  price: ProductPrice
}

const ProductPriceBox: React.FC<Props> = ({ price }) => {
  const normal = price.price
  const specialPrice = price.special

  return (
    <>
      <div
        className={addClasses(
          'row w-full whitespace-nowrap',
          specialPrice ? 'justify-between' : 'text-white justify-end'
        )}
      >
        <div className="flex flex-col justify-end">
          {specialPrice && (
            <>
              <div className="text-white font-bold text-3xl">{formatPrice(specialPrice)}</div>
              <Text className="text-white">Промо цена</Text>
            </>
          )}
        </div>

        <div className="flex flex-col justify-end">
          <div
            className={addClasses(
              'font-bold text-2xl ',
              specialPrice ? 'flex line-through text-black items-end pl-6' : 'font-bold text-3xl'
            )}
          >
            {formatPrice(normal)}
          </div>
          <div className={addClasses(specialPrice ? 'pl-6' : '')}>
            {specialPrice && <Text className="text-white">Предишна цена</Text>}
          </div>
        </div>
      </div>
    </>
  )
}

export default ProductPriceBox
