'use client'
import { LucideLoaderCircle } from 'lucide-react'
import React from 'react'

import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { AppProductPriceFragment } from '@lib/_generated/graphql_sdk'

import { ProductPrice } from '@components/molecules/ProductPrice'

interface Props {
  price: AppProductPriceFragment
  onClick: () => void
  loading: boolean
}

const AddToCardBoxSimple: React.FC<Props> = ({ price, onClick, loading }) => {
  return (
    <div className="bg-primary w-ful flex rounded-2xl p-6 justify-between items-center flex-col gap-3 md:flex-row">
      <ProductPrice
        data={price}
        showLabels
        regularPriceLabel="Обща цена:"
        variant="productFbtTotal"
        classes={{
          'price-info-regular-price-container': 'lg:flex-col',
        }}
      />

      <Button variant="inverse" onClick={onClick} className={cn('h-[42px] !px-6 cursor-pointer text-white uppercase')}>
        {loading && <LucideLoaderCircle className="animate-spin" />}
        Добави в количката
      </Button>
    </div>
  )
}

export default AddToCardBoxSimple
