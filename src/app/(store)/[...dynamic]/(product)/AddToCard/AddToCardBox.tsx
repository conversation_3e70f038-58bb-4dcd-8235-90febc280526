'use client'
import React, { useEffect } from 'react'

import { ProductPrice } from '@/src/lib/_generated/graphql_sdk'
import { addClasses } from '@/src/lib/style/style'
import CustomButton, { ButtonVariants } from '@atoms/Button/CustomButton'
import Text from '@atoms/Text'

import { useProductStore } from '../store/ProductStore'

import AddToCardQtyFields from './AddToCardQtyFields'

interface Props {
  sqMValue?: number
  price: ProductPrice
  showSaving?: boolean
  btnVariant?: ButtonVariants
  btnLabel?: string
  onAddToCardClick: (quantity: number) => void
  quantityDisabled?: boolean
}

const AddToCardBox: React.FC<Props> = ({
  sqMValue,
  price,
  showSaving = true,
  btnVariant = 'black',
  btnLabel = 'Купи продукта',
  onAddToCardClick,
  quantityDisabled = false,
}) => {
  const quantity = useProductStore((state) => state.quantity)
  const sqMeters = useProductStore((state) => state.sqMeters)
  const setSqMValue = useProductStore((state) => state.setSqMValue)
  const setQuantity = useProductStore((state) => state.setQuantity)

  useEffect(() => {
    setSqMValue(sqMValue ? sqMValue : 1)
    setQuantity(1)
  }, [sqMValue, setSqMValue, setQuantity])

  return (
    <div
      className={addClasses(
        'flex justify-between items-center whitespace-nowrap',
        'desktop:min-w-[350px] desktop:mt-0 mt-8',
        'flex-col desktop:flex-row phone:items-start tablet:items-start'
      )}
    >
      <AddToCardQtyFields quantity={quantity} sqMeters={sqMValue ? sqMeters : 0} disabled={quantityDisabled} />

      <div className="flex flex-col phone:w-full tablet:w-full">
        {sqMValue && price ? (
          <>
            <div className="text-md flex justify-between items-center">
              <Text>Обща цена: </Text>
              <div className="text-xl inline font-bold pl-2">
                {quantity * (price.special ? price.special.value : price.price.value)} {price.price.currency}
              </div>
            </div>

            {showSaving && price.special && (
              <div className="text-md flex justify-between items-center">
                <Text>Вие спестявате:</Text>
                <div className="inline font-bold text-xl pl-2">
                  {(quantity * price.price.value - quantity * price.special?.value).toFixed(2)}
                  {price.special.currency}
                </div>
              </div>
            )}
          </>
        ) : null}

        <div className="flex justify-end mt-4">
          <CustomButton
            variant={btnVariant}
            onClick={() => onAddToCardClick(quantity)}
            className={addClasses('h-[42px] !px-10', 'cursor-pointer normal-case')}
          >
            {btnLabel}
          </CustomButton>
        </div>
      </div>
    </div>
  )
}

export default AddToCardBox
