'use client'
import React from 'react'

import { useProductStore } from '@/src/app/(store)/[...dynamic]/(product)/store/ProductStore'
import { addClasses } from '@/src/lib/style/style'
import Text from '@atoms/Text'

interface Props {
  sqMeters?: number
  quantity: number
  disabled?: boolean
}

const AddToCardQtyFields: React.FC<Props> = ({ sqMeters, quantity, disabled = false }) => {
  const iconStyles = [
    'text-black border-2 border-black w-[20px] h-[20px]',
    'rounded-full font-bold text-xl',
    'flex justify-center items-center leading-4',
  ]

  const incrementQuantity = useProductStore((state) => state.incrementQuantity)
  const decrementQuantity = useProductStore((state) => state.decrementQuantity)
  const incrementSqMeters = useProductStore((state) => state.incrementSqMeters)
  const decrementSqMeters = useProductStore((state) => state.decrementSqMeters)

  return (
    <div
      className={addClasses(
        'flex flex-col justify-center',
        `${sqMeters ? 'bg-lock-calc-black bg-no-repeat bg-left pl-6' : 'pl-6'}`
      )}
    >
      <div className="flex items-center">
        <div
          className={addClasses(
            'bg-white h-[42px] items-center flex rounded-3xl p-3',
            `${disabled ? 'opacity-50' : ''}`
          )}
        >
          <button onClick={decrementQuantity} className={addClasses(iconStyles)} disabled={disabled}>
            -
          </button>
          <input
            type="text"
            value={quantity}
            readOnly
            className="w-12 text-center border-none rounded text-xl font-bold text-black"
          />
          <button onClick={incrementQuantity} className={addClasses(iconStyles)} disabled={disabled}>
            +
          </button>
        </div>
        {sqMeters ? <Text className=" pl-2">/ пакет</Text> : <Text className=" pl-2">/ бр.</Text>}
      </div>

      {sqMeters ? (
        <div
          className={addClasses(
            `${sqMeters ? 'mt-2 flex items-center' : 'flex items-center'}`,
            `${disabled ? 'opacity-50' : ''}`
          )}
        >
          <div className="bg-white h-[42px] items-center flex rounded-3xl p-3">
            <button onClick={decrementSqMeters} className={addClasses(iconStyles)} disabled={disabled}>
              -
            </button>
            <input
              type="text"
              value={sqMeters}
              readOnly
              className="w-12 text-center border-none rounded text-xl font-bold text-black"
            />
            <button onClick={incrementSqMeters} className={addClasses(iconStyles)} disabled={disabled}>
              +
            </button>
          </div>
          <span className=" pl-2">
            / M<sup>2</sup>
          </span>
        </div>
      ) : (
        ''
      )}
    </div>
  )
}

export default AddToCardQtyFields
