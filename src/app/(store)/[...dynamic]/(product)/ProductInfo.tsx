import React, { ReactElement } from 'react'

import Img from '@/src/components/atoms/Img'
import RawContent from '@/src/components/atoms/RawContent'
import { AppBrandFragment } from '@/src/lib/_generated/graphql_sdk'
import { addClasses } from '@/src/lib/style/style'
import Title from '@atoms/Title'

import FullDescriptionButton from './FullDescriptionButton'

interface Props {
  title?: string
  code?: string
  brand?: AppBrandFragment | null
  shordDesc?: string
  children?: ReactElement<any>
  showReadMoreBtn?: boolean
}

const ProductInfo: React.FC<Props> = ({ title, code, brand, shordDesc, children, showReadMoreBtn }) => {
  return (
    <>
      <div>
        <div
          className={addClasses(
            'grid grid-cols-2 pb-6 mb-6',
            'border-b border-gray-300',
            'grid-cols-[1fr_130px] phone:p-6 phone:pt-8'
          )}
        >
          {title && (
            <Title size="h1" className="text-3xl font-bold mb-5 col-span-2 desktop:col-auto pr-4">
              {title}
            </Title>
          )}
          {brand && (
            <div className={addClasses('flex justify-start mt-1 max-w-[130px]')}>
              <Img src={brand.image.src} width={130} height={40} alt={brand.name} objectFit={'contain'} />
            </div>
          )}
          {code && (
            <div className="cols-span-2">
              Прод.код:
              <span className={addClasses('bg-primary inline-flex text-white', 'font-bold px-2 py-1 rounded-lg ml-2')}>
                {code}
              </span>
            </div>
          )}
        </div>

        <div className="phone:px-6 phone:py-2">
          {shordDesc && <RawContent content={shordDesc} />}
          {showReadMoreBtn && <FullDescriptionButton />}
        </div>
      </div>

      {children}
    </>
  )
}

export default ProductInfo
