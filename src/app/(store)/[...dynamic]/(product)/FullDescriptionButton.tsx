'use client'
import React from 'react'

import PlusIcon from '@/src/components/atoms/Icons/PlusIcon'
import { addClasses } from '@/src/lib/style/style'
import CustomButton from '@atoms/Button/CustomButton'
import Text from '@atoms/Text'
import { scrollToElement } from '@components/context/ScrollUtils'

const FullDescriptionButton = () => {
  return (
    <>
      <CustomButton
        variant="white"
        onClick={() => scrollToElement('detailed-description-block')}
        className={addClasses('!p-0', 'cursor-pointer bg-background normal-case')}
      >
        <PlusIcon size={26} className="mr-2" />
        <Text className="text-primary normal-case tracking-normal font-normal text-sm">Пълно описание</Text>
      </CustomButton>
    </>
  )
}

export default FullDescriptionButton
