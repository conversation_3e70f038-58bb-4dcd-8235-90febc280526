import { create } from 'zustand'

import { incrementQuantity, decrementQuantity, incrementSqMeters, decrementSqMeters } from '../utils/quantityUtils'

interface QuantityState {
  quantity: number
  sqMeters: number
  sqMValue: number
  setQuantity: (quantity: number) => void
  setSqMeters: (sqMeters: number) => void
  setSqMValue: (sqMValue: number) => void
  incrementQuantity: () => void
  decrementQuantity: () => void
  incrementSqMeters: () => void
  decrementSqMeters: () => void
}

export const useProductStore = create<QuantityState>((set, get) => ({
  quantity: 1,
  sqMeters: 1,
  sqMValue: 1,
  setQuantity: (quantity) => set({ quantity }),
  setSqMeters: (sqMeters) => set({ sqMeters }),
  setSqMValue: (sqMValue) => set({ sqMValue }),

  incrementQuantity: () => {
    const { quantity, sqMValue } = get()
    const { newQuantity, newSqMeters } = incrementQuantity(quantity, sqMValue)
    set({ quantity: newQuantity, sqMeters: newSqMeters })
  },

  decrementQuantity: () => {
    const { quantity, sqMValue } = get()
    const { newQuantity, newSqMeters } = decrementQuantity(quantity, sqMValue)
    set({ quantity: newQuantity, sqMeters: newSqMeters })
  },

  incrementSqMeters: () => {
    const { sqMeters, sqMValue } = get()
    const { newSqMeters, newQuantity } = incrementSqMeters(sqMeters, sqMValue)
    set({ sqMeters: newSqMeters, quantity: newQuantity })
  },

  decrementSqMeters: () => {
    const { sqMeters, sqMValue } = get()
    const { newSqMeters, newQuantity } = decrementSqMeters(sqMeters, sqMValue)
    set({ sqMeters: newSqMeters, quantity: newQuantity })
  },
}))
