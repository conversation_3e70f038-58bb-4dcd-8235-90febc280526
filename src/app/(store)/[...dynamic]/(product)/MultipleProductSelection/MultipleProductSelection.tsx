'use client'
import React, { useMemo, useState } from 'react'

import { SimpleProductViewFragment, SimpleProduct } from '@/src/lib/_generated/graphql_sdk'
import Text from '@atoms/Text'
import { useCartStore } from '@features/cart/cart-state'

import AddToCardBoxSimple from '../AddToCard/AddToCardBoxSimple'

import BundleProductCard from './BundleProductCard'
import FrequentlyBoughtTogetherCard from './FrequentlyBoughtTogetherCard'
import { cn } from '@components/lib/utils'

interface Props {
  subProducts: SimpleProductViewFragment[]
  mainProduct: SimpleProductViewFragment
  isBundle?: boolean
  title?: string
}

const MultipleProductSelection: React.FC<Props> = ({ subProducts, isBundle = false, title, mainProduct }) => {
  const [selectedProducts, setSelectedProducts] = useState<SimpleProductViewFragment['sku'][]>(
    subProducts.map(({ sku }) => sku)
  )
  const { addItem } = useCartStore()
  const [loading, setLoading] = useState(false)
  const [loadingProducts, setLoadingProducts] = useState<SimpleProductViewFragment['sku'][]>([])
  const totalPrice = useMemo(() => {
    return subProducts
      .filter((product) => selectedProducts.includes(product.sku))
      .reduce(
        (acc, product) => acc + (product.price.special ? product.price.special.value : product.price.price.value),
        0
      )
  }, [subProducts, selectedProducts])

  const handleAddToCart = async () => {
    setLoading(true)
    setLoadingProducts(selectedProducts)
    for (let i = 0; i < selectedProducts.length; i++) {
      await addItem(selectedProducts[i], 1)
      setLoadingProducts((prev) => {
        return prev.filter((sku) => sku !== selectedProducts[i])
      })
    }
    setLoading(false)
  }

  return (
    <div>
      <div>
        {title && <Text className="text-xl font-bold mb-6 flex mt-10">{title}</Text>}
        <div
          className={cn(
            'grid pb-10 hide-second-plus-sm',
            'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 3xl:grid-cols-6 4xl:grid-cols-7 5xl:grid-cols-9 6xl:grid-cols-12' // up to 4k
          )}
        >
          {subProducts
            .filter((product) => product.__typename === 'SimpleProduct')
            .map((product, i) => (
              <FrequentlyBoughtTogetherCard
                product={product}
                key={i}
                isLast={i === subProducts.length - 1}
                selected={selectedProducts.includes(product.sku)}
                onClick={(productSku) => {
                  if (selectedProducts.includes(productSku)) {
                    setSelectedProducts(selectedProducts.filter((selectedId) => selectedId !== productSku))
                  } else {
                    setSelectedProducts([...selectedProducts, productSku])
                  }
                }}
                loading={loadingProducts.includes(product.sku)}
              />
            ))}
        </div>
      </div>
      <AddToCardBoxSimple
        price={{
          price: {
            value: selectedProducts ? totalPrice : 0,
            currency: mainProduct.price.price.currency,
          },
        }}
        loading={loading}
        onClick={handleAddToCart}
      />
    </div>
  )
}

export default MultipleProductSelection
