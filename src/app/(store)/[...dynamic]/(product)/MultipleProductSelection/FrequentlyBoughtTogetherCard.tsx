'use client'
import { LucideLoaderCircle } from 'lucide-react'
import React from 'react'

import Img from '@/src/components/atoms/Img'
import CheckboxElement from '@atoms/CheckboxElement'
import { cn } from '@components/lib/utils'
import { SimpleProductViewFragment, Product, SimpleProduct } from '@lib/_generated/graphql_sdk'
import { ProductPrice } from '@components/molecules/ProductPrice'
import Link from 'next/link'

interface Props {
  product: SimpleProductViewFragment
  isLast?: boolean
  onClick: (productSku: Product['sku']) => void
  selected: boolean
  loading: boolean
}

const FrequentlyBoughtTogetherCard: React.FC<Props> = ({ product, isLast = false, onClick, selected, loading }) => {
  return (
    <div className="group flex w-full">
      <div className="flex flex-col">
        <div className="flex justify-center">
          <div
            className={cn(
              'w-full border border-gray-100 rounded-2xl flex items-center justify-center',
              { 'group-hover:border-primary': !loading },
              'transition-all duration-300 ease-in-out',
              'w-[120px] bg-white relative aspect-square'
            )}
          >
            {loading && <LucideLoaderCircle className="animate-spin text-primary absolute z-10" size={50} />}
            <Link href={!loading ? product.urlKey : '#'}>
              <Img
                mobileSrc={product.image.src || ''} //TODO mobile src
                src={product.image.src}
                className={cn('rounded-2xl object-contain', { 'opacity-10': loading })}
                alt={product.image.alt || ''}
                fill
              />
            </Link>
          </div>
        </div>
        <div className="flex flex-1 mt-4 mb-2 items-start pr-1">
          <CheckboxElement
            checked={selected}
            onClick={() => {
              if (!loading) {
                onClick(product.sku)
              }
            }}
          />
          <div className="flex flex-col gap-4 px-2">
            <div className="text-sm break-all">
              <Link href={!loading ? product.urlKey : '#'}>{product.name}</Link>
            </div>
            <ProductPrice data={product.price} variant="productFbtCard" />
          </div>
        </div>
      </div>
      {!isLast && (
        <div className="flex flex-col self-stretch justify-center sm:justify-center items-center text-3xl separator-plus">
          <div className="flex flex-1 items-center justify-center">+</div>
          <div className="flex-1"></div>
        </div>
      )}
    </div>
  )
}

export default FrequentlyBoughtTogetherCard
