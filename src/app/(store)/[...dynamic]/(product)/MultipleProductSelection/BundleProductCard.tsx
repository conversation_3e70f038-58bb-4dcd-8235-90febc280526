'use client'
import React, { useEffect, useState } from 'react'

import Img from '@/src/components/atoms/Img'
import { formatPrice } from '@/src/features/product/filters'
import { addClasses } from '@/src/lib/style/style'
import CustomButton from '@atoms/Button/CustomButton'
import Text from '@atoms/Text'

import AddToCardQtyFields from '../AddToCard/AddToCardQtyFields'
import { incrementQuantity, decrementQuantity, incrementSqMeters, decrementSqMeters } from '../utils/quantityUtils'

interface Props {
  product: any
  onAddToCardClick: (quantity: number, id: number) => void
}

const BundleProducts: React.FC<Props> = ({ product, onAddToCardClick }) => {
  const [quantity, setQuantity] = useState<number>(1)
  const [sqMeters, setSqMeters] = useState<number>(1)
  const [sqMValue, setSqMValue] = useState<number>(1)

  const handleIncrementQuantity = () => {
    const { newQuantity, newSqMeters } = incrementQuantity(quantity, sqMValue)
    setQuantity(newQuantity)
    setSqMeters(newSqMeters)
  }

  const handleDecrementQuantity = () => {
    const { newQuantity, newSqMeters } = decrementQuantity(quantity, sqMValue)
    setQuantity(newQuantity)
    setSqMeters(newSqMeters)
  }

  const handleIncrementSqMeters = () => {
    const { newSqMeters, newQuantity } = incrementSqMeters(sqMeters, sqMValue)
    setSqMeters(newSqMeters)
    setQuantity(newQuantity)
  }

  const handleDecrementSqMeters = () => {
    const { newSqMeters, newQuantity } = decrementSqMeters(sqMeters, sqMValue)
    setSqMeters(newSqMeters)
    setQuantity(newQuantity)
  }

  useEffect(() => {
    setSqMValue(product.sqMeterValue)
    setSqMeters(product.sqMeterValue)
  }, [product])

  return (
    <div
      className={addClasses(
        'flex relative justify-between bg-background p-8 mb-4 rounded-[2rem]',
        'flex-col desktop:flex-row desktop:mb-8',
        'border-[1px] hover:border-primary',
        `${product.selected ? 'border-primary' : 'border-gray-100'}`
      )}
    >
      {product.selected && (
        <div
          className={addClasses(
            'min-w-[28px] h-[28px] flex rounded bg-background absolute',
            'items-center justify-center rounded-full bg-primary',
            'left-0 -top-4'
          )}
        >
          <Img
            alt="selected"
            src={'/images/icons/checkbox-white.svg'}
            width={11}
            height={11}
            className={product.selected ? '' : 'hidden'}
          />
        </div>
      )}
      <div className="flex desktop:w-3/5">
        <div className="w-[110px] h-[110px] min-w-[110px] bg-white rounded-3xl -m-3 mr-6">
          <Img
            alt="product"
            className="w-full rounded-3xl max-h-[218px]"
            src={product.image?.src ?? ''}
            width={110}
            height={110}
            style={{ objectFit: 'cover' }}
          />
        </div>
        <div className="flex flex-col justify-between">
          <div className="text-lg">{product.name}</div>
          <div className="text-2xl">
            <span className="font-bold">
              {formatPrice(product.price.special ? product.price.special.value : product.price.price.value)}
            </span>
            <span className="text-xl">
              {' '}
              / M<sup>2</sup>
            </span>
          </div>
        </div>
      </div>

      <div className="flex flex-row justify-between mt-10 desktop:mt-0 desktop:w-2/5">
        <AddToCardQtyFields
          // onIncrementQuantity={handleIncrementQuantity}
          // onDecrementQuantity={handleDecrementQuantity}
          // onIncrementSqMeters={handleIncrementSqMeters}
          // onDecrementSqMeters={handleDecrementSqMeters}
          quantity={quantity}
          sqMeters={sqMeters ? sqMeters : 0}
          disabled={product.selected}
        />

        <div className="flex flex-col phone:w-full tablet:w-full">
          {product.price && product.price.special ? (
            <div>
              <div>
                <Text>Промо цена:</Text>
                <div className="inline font-bold pl-2 text-xl text-primary">
                  {(quantity * product.price.special.value).toFixed(2)}
                  {product.price.special.currency}
                </div>
              </div>
              <div className="flex justify-end">
                <Text>Цена:</Text>
                <span className="line-through font-bold pl-2">{(quantity * product.price.price.value).toFixed(2)}</span>
                <span className="font-bold">{product.price.special.currency}</span>
              </div>
            </div>
          ) : (
            <div>
              <Text>Цена:</Text>
              <div className="inline font-bold pl-2 text-xl">
                {(quantity * product.price.price.value).toFixed(2)}
                {product.price.price.currency}
              </div>
            </div>
          )}

          <div className="flex justify-end mt-4">
            <CustomButton
              variant="primary"
              onClick={() => {
                onAddToCardClick(product.id, quantity)
              }}
              className={addClasses('h-[42px] !px-10', 'cursor-pointer normal-case')}
            >
              {product.selected ? 'Премахнете' : 'Добавете'}
            </CustomButton>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BundleProducts
