'use client'
import React, { useState } from 'react'
import { cn } from '@components/lib/utils'
import { ClassName } from '@lib/types/ClassName'
import GoogleMap from '@/src/app/(store)/contacts/_components/GoogleMap'
import Img from '@atoms/Img'

interface MapPlaceholderProps {
  id: string
  center: {
    lat: number
    lng: number
  }
  zoom?: number
  className?: ClassName
}

const MapPlaceholder: React.FC<MapPlaceholderProps> = ({ id, center, zoom = 13, className }) => {
  const [showInteractiveMap, setShowInteractiveMap] = useState(false)

  const handleMapClick = () => {
    setShowInteractiveMap(true)
  }

  if (showInteractiveMap) {
    return <GoogleMap id={id} center={center} zoom={zoom} className={className} />
  }

  return (
    <div className={cn('absolute inset-0 cursor-pointer group', className)} onClick={handleMapClick}>
      {/* Static blurred map image */}
      <Img
        src="/images/blurred_map.jpg"
        width={200}
        height={300}
        alt="Местоположение на магазина"
        className="w-full h-full object-cover"
        loading="lazy"
      />

      {/* Overlay with location icon */}
      <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center rounded-2xl group-hover:bg-opacity-30 transition-all duration-200">
        <div className="bg-white bg-opacity-90 rounded-full p-4 shadow-lg group-hover:bg-opacity-100 transition-all duration-200">
          <svg className="w-8 h-8 text-gray-700" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
          </svg>
        </div>
      </div>

      {/* Click hint text */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-white bg-opacity-90 px-3 py-1 rounded-full text-sm text-gray-700 shadow-md text-center">
        Кликнете за интерактивна карта
      </div>
    </div>
  )
}

export default MapPlaceholder
