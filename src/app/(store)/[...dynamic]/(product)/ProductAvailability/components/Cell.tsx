import React, { PropsWithChildren } from 'react'
import { PropsWithClassName } from '@lib/types/ClassName'
import { cn } from '@components/lib/utils'

export const Cell: React.FC<PropsWithChildren & PropsWithClassName & { onClick?: () => void; autoWidth?: boolean }> = ({
  children,
  className,
  onClick,
  autoWidth,
}) => {
  return (
    <div
      className={cn(
        'border-gray-200 flex items-center',
        'px-2 py-3',
        'sm:px-4',
        'lg:px-3',
        'xl:px-3',
        '3xl:px-6 3xl:py-3.5',
        { 'flex-1': autoWidth },
        className
      )}
      onClick={onClick}
    >
      <div className={cn({ 'w-[20px] 3xl:w-[30px]': !autoWidth })}>{children}</div>
    </div>
  )
}
