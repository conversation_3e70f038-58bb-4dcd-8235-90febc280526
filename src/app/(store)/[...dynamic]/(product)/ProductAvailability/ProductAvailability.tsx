'use client'
import React, { useCallback, useEffect, useState } from 'react'

import MapPlaceholder from '@/src/app/(store)/[...dynamic]/(product)/ProductAvailability/components/MapPlaceholder'
import ArrowRightCircle from '@atoms/Icons/ArrowRightCircle'
import AvailabelIcon from '@atoms/Icons/AvailabelIcon'
import LimitedQuantity from '@atoms/Icons/LimitedQuantity'
import UnavailableIcon from '@atoms/Icons/UnavailableIcon'
import Text from '@atoms/Text'
import { ButtonIcon } from '@components/molecules/ButtonIcon'
import { Button } from '@components/theme/ui/button'
import TriangleDown from '@icons/triangle-down.inline.svg'
import TriangleUp from '@icons/triangle-up.inline.svg'
import { AvailabilityStatus, StoreAvailabilityFragment } from '@lib/_generated/graphql_sdk'
import { cn } from '@components/lib/utils'
import { Cell } from '@/src/app/(store)/[...dynamic]/(product)/ProductAvailability/components/Cell'
import { trackFindLocation } from '@lib/fbp/faceBookPixelHelper'

interface Props {
  storeAvailability: StoreAvailabilityFragment[]
}

const ProductAvailability: React.FC<Props> = ({ storeAvailability }) => {
  const [selectedStoreIndex, setSelectedStoreIndex] = useState(0)
  let activeStore: { location: { lat: number; lng: number } } | null = null
  if (storeAvailability.length > 0 && selectedStoreIndex > -1) {
    activeStore = storeAvailability[selectedStoreIndex].store
  }

  const setSelectedStore = useCallback(
    (index: number) => {
      setSelectedStoreIndex(index)
      if (index > -1) {
        trackFindLocation({
          content_name: storeAvailability[index].store.name,
        })
      }
    },
    [storeAvailability]
  )

  const openMaps = (lat: number, lng: number) => {
    const isMobileDevice = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)

    console.log({ isMobileDevice })
    if (isMobileDevice && window) {
      // Try opening in a mobile app
      window.location.href = `geo:${lat},${lng}?q=${lat},${lng}`
    } else {
      // Fallback to Google Maps URL
      window.open(`https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`, '_blank')
    }
  }

  const tableData = storeAvailability
  // Determine map order
  const mapOrder = selectedStoreIndex === tableData.length - 1 ? 'last' : selectedStoreIndex

  // Calculate row order based on map position
  const getOrderRow = (index: number) => {
    // First row is always first
    if (index === 0) return 'first'

    // Last row
    if (index === tableData.length - 1) {
      // If map is positioned at last, use index-1
      return selectedStoreIndex === tableData.length - 1 ? index - 1 : 'last'
    }

    // Middle rows
    // If map is before this row, use index
    // If map is at or after this row, use index-1
    return selectedStoreIndex < index ? index : index - 1
  }

  const p: { [key: string]: string } = {
    first: 'order-first',
    0: 'order-0',
    1: 'order-1',
    2: 'order-2',
    3: 'order-3',
    4: 'order-4',
    5: 'order-5',
    6: 'order-6',
    7: 'order-7',
    8: 'order-8',
    9: 'order-9',
    10: 'order-10',
    11: 'order-11',
    12: 'order-12',
    13: 'order-13',
    14: 'order-14',
    15: 'order-15',
    16: 'order-16',
    17: 'order-17',
    18: 'order-18',
    19: 'order-19',
    20: 'order-20',
    21: 'order-21',
    22: 'order-22',
    23: 'order-23',
    24: 'order-24',
    last: 'order-last',
  }

  return (
    <div>
      <div className="flex xl:w-[49%]">
        <Cell autoWidth>
          <Text className="leading-10 uppercase  text-sm md:text-base">Магазин</Text>
        </Cell>
        <Cell>
          <div className="-ml-[35px] lg:-ml-[25px]">
            <Text className="text-center leading-10 uppercase text-xs md:text-sm">наличен</Text>
          </div>
        </Cell>
        <Cell>
          <Text className="text-center leading-10 uppercase text-xs md:text-sm">мостра</Text>
        </Cell>
        <Cell />
      </div>
      <div className="flex flex-col relative">
        {tableData?.map((store: StoreAvailabilityFragment, i) => {
          let unavailable = true
          let status = <UnavailableIcon />
          switch (store.available) {
            case AvailabilityStatus.Available:
              unavailable = false
              status = <AvailabelIcon />
              break
            case AvailabilityStatus.Unavailable:
              status = <UnavailableIcon />
              break
            case AvailabilityStatus.LimitedAvailability:
              unavailable = false
              status = <LimitedQuantity />
              break
            case AvailabilityStatus.IndividualOrder:
              unavailable = false
              status = <AvailabelIcon fill="#9B99FF" />
              break
            default:
              console.error('Unknown availability status: ' + store.available)
              break
          }

          if (store.sample) {
            unavailable = false
          }

          const rowOrder = getOrderRow(i)
          return (
            <div
              key={i}
              className={cn(
                p[rowOrder],
                storeAvailability.length - 1 > i && 'border-b border-gray-200',
                'flex xl:w-[49%]'
              )}
            >
              <Cell
                onClick={() => {
                  setSelectedStore(i === selectedStoreIndex ? -1 : i)
                }}
                className={cn(
                  'border-r font-bold cursor-pointer hover:underline px-0',
                  unavailable ? 'font-medium text-gray-400' : '',
                  'block xl:hidden' // Mobile
                )}
                autoWidth
              >
                {store.store.name.replace('-', ' - ')}
              </Cell>
              <Cell
                onClick={() => {
                  console.log('setSelectedStore', selectedStoreIndex)
                  setSelectedStore(i)
                }}
                className={cn(
                  'border-r font-bold cursor-pointer hover:underline px-0',
                  unavailable ? 'font-medium text-gray-400' : '',
                  'hidden xl:block' // Desktop
                )}
                autoWidth
              >
                {store.store.name.replace('-', ' - ')}
              </Cell>
              <Cell className="border-r">{!store.store.acceptOrders ? <UnavailableIcon /> : status}</Cell>
              <Cell className="border-r">
                {!store.store.acceptOrders ? (
                  <UnavailableIcon />
                ) : store.sample ? (
                  <AvailabelIcon />
                ) : (
                  <UnavailableIcon />
                )}
              </Cell>
              <Cell
                onClick={() => {
                  setSelectedStore(i === selectedStoreIndex ? -1 : i)
                }}
              >
                <ButtonIcon
                  className="xl:hidden"
                  iconClassName="text-black"
                  variant="ghost"
                  size="sm"
                  icon={selectedStoreIndex === i ? <TriangleUp /> : <TriangleDown />}
                />
                {selectedStoreIndex === i && <ArrowRightCircle className="hidden xl:block " />}
              </Cell>
            </div>
          )
        })}
        {activeStore && (
          <div
            className={cn(
              'h-[200px] relative flex justify-center items-center',
              'xl:h-full xl:w-[50%] xl:absolute xl:top-0 xl:right-0 xl:bottom-0',
              p[mapOrder]
            )}
          >
            <MapPlaceholder
              id="AVAILABILITY_MAP"
              center={{
                lat: activeStore.location.lat,
                lng: activeStore.location.lng,
              }}
            />
            <Button
              variant="inverse"
              className="xl:hidden absolute -bottom-3 text-white"
              onClick={() => openMaps(activeStore.location.lat, activeStore.location.lng)}
            >
              Заведи ме там
            </Button>
          </div>
        )}
      </div>
      <div className="xl:row bg-background rounded-lg mt-6 py-6 px-10">
        <div className="flex items-center xl:justify-center mr-10 mb-3 xl:mb-0 text-[#05CC19]">
          <AvailabelIcon className="mr-0" /> Наличен
        </div>
        <div className="flex items-center xl:justify-center mr-10 mb-3 xl:mb-0 text-[#FC4040]">
          <UnavailableIcon className="mr-2" /> Не е наличен
        </div>
        <div className="flex items-center xl:justify-center mr-10 mb-3 xl:mb-0 text-[#9B99FF]">
          <AvailabelIcon fill="#9B99FF" className="mr-2" /> По индивидуална поръчка
        </div>

        <div className="flex items-center xl:justify-center text-[#EEC800]">
          <LimitedQuantity className="mr-2" /> Ограничено количество
        </div>
      </div>
    </div>
  )
}

export default ProductAvailability
