import React from 'react'

import { StoreModal } from '@/src/app/(store)/shop/[identifier]/_components/StoreModal'
import Text from '@atoms/Text'
import { DecorationCard, DecorationCardProps } from '@components/molecules/DecorationCard'
import { StaticContentProps } from '@features/static/types'

type InquireCardProps = Omit<DecorationCardProps, 'action' | 'image' | 'description'> & { storeName: string }

export const InquireCard: React.FC<InquireCardProps & StaticContentProps> = ({
  storeName,
  title,
  staticContent,
  ...props
}) => {
  return (
    <DecorationCard
      {...props}
      image="/images/forms/store-request-offer-bg.png"
      title={title}
      description={<Text className="text-sm">{staticContent?.messages.sendInquiryMessage}</Text>}
      action={storeName && <StoreModal storeName={storeName} staticContent={staticContent} />}
    />
  )
}
