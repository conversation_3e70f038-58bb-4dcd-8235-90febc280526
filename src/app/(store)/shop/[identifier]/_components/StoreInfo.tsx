'use client'
import React from 'react'

import Paper from '@atoms/Paper'
import RawContent from '@atoms/RawContent'
import Text from '@atoms/Text'
import Title from '@atoms/Title'
import { StoreSchedule } from '@lib/_generated/graphql_sdk'
import './store-info.css'

interface Props {
  name: string
  address: string
  phone: string
  email: string
  businessHours: StoreSchedule[]
  transportInformation?: string
}

const StoreInfo: React.FC<Props> = ({ name, address, phone, email, businessHours, transportInformation }) => {
  return (
    <Paper className="w-full rounded-3xl p-12 flex flex-col shadow-none">
      <Title size="h1" className="text-xl font-bold mb-2">
        {name}
      </Title>
      <div>{address}</div>
      <div className="mt-10 mb-2 font-bold">
        <Text>Контакти:</Text>
      </div>
      <div className="border-b border-dashed border-gray-300 flex justify-between py-3 text-xs">
        <span>
          <Text>Телефон:</Text>
        </span>
        <span className="font-bold text-primary">
          {phone.split(',').map((p) => (
            <div key={p}>
              <a href={`tel:${p.replace(/[\s\/]+/g, '')}`}>{p}</a>
            </div>
          ))}
        </span>
      </div>
      <div className="flex justify-between py-3 text-xs">
        <span>Email:</span>
        <span className="text-primary">
          <a href={`mailto:${email}`}>{email}</a>
        </span>
      </div>
      <div className="mt-10 mb-2 font-bold">
        <Text>Работно време:</Text>
      </div>
      {businessHours.map((item: StoreSchedule, i: number) => (
        <div className="info-row" key={i}>
          <span>{item.day}</span>
          <span>
            {item.open} - {item.close} <Text>часа</Text>
          </span>
        </div>
      ))}
      {transportInformation && (
        <div>
          <div className="mt-10 mb-2 font-bold">
            <Text>Транспорт:</Text>
          </div>
          <RawContent content={transportInformation} />
        </div>
      )}
    </Paper>
  )
}

export default StoreInfo
