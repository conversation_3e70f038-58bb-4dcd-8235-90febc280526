'use client'
import React from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'

import { ServiceTile } from '@components/molecules/ServiceTile'
import { Service } from '@lib/_generated/graphql_sdk'

interface StoreServicesProps {
  services: Omit<Service, 'image'>[]
}

export const StoreServices: React.FC<StoreServicesProps> = ({ services }) => {
  return (
    <Swiper
      slidesPerView={2.2}
      spaceBetween={10}
      breakpoints={{
        640: {
          slidesPerView: 3.2,
        },
        768: {
          slidesPerView: 4.2,
        },
        1024: {
          slidesPerView: 5.2,
          spaceBetween: 20,
        },
        1280: {
          slidesPerView: 4.2,
        },
        1536: {
          slidesPerView: 5.2,
          spaceBetween: 20,
        },
        1920: {
          slidesPerView: 6.2,
          spaceBetween: 20,
        },
        2560: {
          slidesPerView: 8.2,
          spaceBetween: 20,
        },
        3440: {
          slidesPerView: 10.2,
          spaceBetween: 25,
        },
      }}
    >
      {services.map((service, index) => (
        <SwiperSlide key={index} className="!h-auto">
          <ServiceTile title={service.name} icon={service.iconUrl} />
        </SwiperSlide>
      ))}
    </Swiper>
  )
}
