import * as DialogPrimitive from '@radix-ui/react-dialog'
import { LucideX } from 'lucide-react'
import React from 'react'

import { Button } from '@/src/components/theme/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from '@/src/components/theme/ui/dialog'
import Img from '@atoms/Img'
import { ButtonIcon } from '@components/molecules/ButtonIcon'
import { ServiceRequestForm } from '@components/organisms/Forms/ServiceRequest/ServiceRequestForm'

import { StaticContentProps } from '@features/static/types'

const StoreModalDescription: React.FC<StaticContentProps> = ({ staticContent }) => {
  const inquiryFormText = staticContent?.messages.sendInquiryMessage
  return <span>{inquiryFormText}</span>
}

export const StoreModal = ({ storeName, staticContent }: StaticContentProps & { storeName: string }) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="text-xs tracking-normal sm:text-base sm:tracking-wide">Изпратете запитване</Button>
      </DialogTrigger>
      <DialogContent
        disableCloseButton
        className="sm:max-w-[930px] p-0 mx-0 w-11/12 border-none h-4/6 !rounded-2xl overflow-auto bg-white"
      >
        <div className="flex">
          <div className="hidden md:block relative w-4/12 h-full">
            <Img src="/images/forms/request-offer.png" alt="Image" fill className="rounded-2xl object-cover" />
          </div>
          <div className="p-3 xl:p-7 flex-1">
            <DialogHeader>
              <div className="flex justify-between items-center">
                <DialogTitle>Запитване за оферта</DialogTitle>
                <DialogPrimitive.Close
                  asChild
                  className="rounded-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
                >
                  <ButtonIcon
                    iconClassName="bg-gray-300 text-primary hover:text-white"
                    icon={<LucideX className="h-5 w-5" />}
                  />
                </DialogPrimitive.Close>
              </div>
              <DialogDescription className="xl:py-6">
                <StoreModalDescription staticContent={staticContent} />
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <ServiceRequestForm storeName={storeName} staticContent={staticContent} />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
