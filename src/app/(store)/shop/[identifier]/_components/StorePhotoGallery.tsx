'use client'
import React, { ReactElement, useState } from 'react'

import Img from '@atoms/Img'
import LightboxPhotoSwipe from '@atoms/Lightbox/LightboxPhotoSwipe'
import { GalleryImage, StoreImage } from '@lib/_generated/graphql_sdk'
import {
  <PERSON>wi<PERSON>,
  SwiperNextArrow,
  SwiperOverlay,
  SwiperPrevArrow,
} from '@/src/components/organisms/ProductPhotoGallery/_Swiper'
import { cn } from '@/src/components/lib/utils'
import ReactDOM from 'react-dom'
import { ButtonIcon } from '@/src/components/molecules/ButtonIcon'
import { ArrowLeft, ArrowRight, LucideX } from 'lucide-react'
import OnTap from '@/src/components/atoms/OnTap'
import { Button } from '@/src/components/theme/ui/button'
import dynamic from 'next/dynamic'

interface StorePhotoGalleryProps {
  gallery: StoreImage[]
}
const PrevButton = (
  <ButtonIcon
    variant="ghost"
    icon={<ArrowLeft />}
    className="z-10 h-14 w-14"
    iconClassName="text-primary h-14 w-14 p-5 bg-white shadow rounded-full"
  />
)

const NextButton = (
  <ButtonIcon
    variant="ghost"
    icon={<ArrowRight />}
    className="z-10 h-14 w-14"
    iconClassName="text-primary h-14 w-14 p-5 bg-white shadow rounded-full"
  />
)

export const StorePhotoGallery: React.FC<StorePhotoGalleryProps> = ({ gallery }) => {
  const images = gallery.map((img) => ({ src: img.src, alt: img.alt || '' }))
  const [lightboxOpen, setLightboxOpen] = useState(false)

  const clickHandler = (e: any) => {
    if (images.length === 0) return
    e.preventDefault()
    setLightboxOpen(true)
  }

  return (
    <div className="relative ">
      <div className="relative -top-2">
        {images.length > 1 && (
          <div>
            {/* Thumbnails */}
            <Swiper slidesPerView="1.5 md:2.5 lg:3.5" sync="shop_gallery">
              <SwiperOverlay className="hidden lg:block">
                <SwiperPrevArrow className="absolute top-1/2 -left-6 transform -translate-y-1/2 scale-75">
                  {PrevButton}
                </SwiperPrevArrow>
                <SwiperNextArrow className="absolute top-1/2 -right-6 transform -translate-y-1/2 scale-75">
                  {NextButton}
                </SwiperNextArrow>
              </SwiperOverlay>
              {images.map((image, index) => (
                <OnTap key={`lightbox-thumb-${index}`} onTap={clickHandler}>
                  <div
                    onClick={clickHandler}
                    key={`lightbox-thumb-${index}`}
                    className={cn(
                      'flex-shrink-0 w-auto aspect-[4/3]  relative rounded-3xl  transition-all duration-200 '
                    )}
                  >
                    <Img src={image.src} alt={image.alt || ''} fill className="rounded-3xl object-cover" />
                  </div>
                </OnTap>
              ))}
            </Swiper>
          </div>
        )}
      </div>

      {/* Lightbox Component */}
      <Lightbox
        images={images}
        isOpen={lightboxOpen}
        onClose={() => setLightboxOpen(false)}
        prevButton={PrevButton}
        nextButton={NextButton}
      />
    </div>
  )
}

interface LightboxProps {
  images: { src: string; alt?: string }[]
  isOpen: boolean
  onClose: () => void
  prevButton?: ReactElement<{ onClick: () => void; className?: string }>
  nextButton?: ReactElement<{ onClick: () => void; className?: string }>
}

const Lightbox = dynamic(
  () =>
    Promise.resolve(({ images, isOpen, onClose, prevButton: PrevButton = <></>, nextButton: NextButton = <>

        </> }: LightboxProps) => {
      return ReactDOM.createPortal(
        <div
          className={cn('fixed inset-0 z-[9999] flex items-center justify-center bg-black/80', !isOpen && 'hidden')}
          onClick={onClose}
        >
          <div
            className="relative w-full max-w-[400px] md:max-w-[60vw] lg:w-[50vw] xl:max-w-[40vw] p-4 mx-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Main Image Container */}
            <div className="relative w-full  mb-4">
              <Swiper slidesPerView="1" sync="shop_gallery">
                <SwiperOverlay className="">
                  <SwiperPrevArrow className="lg:hidden absolute top-1/2 -left-3 transform -translate-y-1/2 ">
                    {PrevButton}
                  </SwiperPrevArrow>
                  <SwiperNextArrow className="lg:hidden absolute top-1/2 -right-3 transform -translate-y-1/2 ">
                    {NextButton}
                  </SwiperNextArrow>
                  <Button
                    variant="secondary"
                    className="absolute top-4 right-4 text-primary aspect-square p-0"
                    onClick={() => onClose()}
                  >
                    <LucideX />
                  </Button>
                </SwiperOverlay>
                {images.map((image, index) => (
                  <div key={`lightbox-thumb-${index}`} className={cn('w-full aspect-[4/3] rounded-3xl')}>
                    <Img
                      src={image.src}
                      alt={image.alt || ''}
                      fill
                      className="object-cover w-[95vw] aspect-[4/3] rounded-3xl"
                    />
                  </div>
                ))}
              </Swiper>
            </div>

            {/* Thumbnails */}
            <div className="relative w-full flex justify-center ">
              <div className=" relative w-full lg:w-[80%] pl-2">
                {images.length > 1 && (
                  <Swiper
                    slidesPerView="1.5 md:3"
                    sync="shop_gallery"
                    className=""
                    currentSlideClassName="border-2 border-primary"
                  >
                    <SwiperOverlay className="hidden lg:block">
                      <SwiperPrevArrow className="absolute top-1/2 -left-14 transform -translate-y-1/2 scale-75">
                        {PrevButton}
                      </SwiperPrevArrow>
                      <SwiperNextArrow className="absolute top-1/2 -right-14 transform -translate-y-1/2 scale-75">
                        {NextButton}
                      </SwiperNextArrow>
                    </SwiperOverlay>
                    {images.map((image, index) => (
                      <div
                        key={`lightbox-thumb-${index}`}
                        className={cn(
                          'flex-shrink-0 w-auto aspect-[4/3]  relative rounded-3xl transition-all duration-200 '
                        )}
                      >
                        <Img src={image.src} alt={image.alt || ''} fill className="rounded-3xl object-cover" />
                      </div>
                    ))}
                  </Swiper>
                )}
              </div>
            </div>
          </div>
        </div>,
        document.body
      )
    }),
  { ssr: false }
)
export default StorePhotoGallery
