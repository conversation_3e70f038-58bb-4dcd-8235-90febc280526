import { notFound } from 'next/navigation'
import React from 'react'

import GoogleMap from '@/src/app/(store)/contacts/_components/GoogleMap'
import { InquireCard } from '@/src/app/(store)/shop/[identifier]/_components/InquireCard'
import StoreInfo from '@/src/app/(store)/shop/[identifier]/_components/StoreInfo'
import { StorePhotoGallery } from '@/src/app/(store)/shop/[identifier]/_components/StorePhotoGallery'
import { StoreServices } from '@/src/app/(store)/shop/[identifier]/_components/StoreServices'
import { getStore } from '@/src/app/(store)/shop/api'
import Container from '@atoms/Container'

import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import { Card } from '@components/theme/ui/card'
import { AppStoreFragment } from '@lib/_generated/graphql_sdk'
import { NextPageProps, SlugParam } from '@lib/utils/page'
import Static from '@features/static'
import { GraphQLBackend } from '@/src/lib/api/graphql'
import { getStaticContent } from '@/src/features/static/api'
import { staticSelectors } from '@/src/features/static/selectors'

export default async function ShopPage(props: NextPageProps<SlugParam>) {
  const params = await props.params
  const store: AppStoreFragment = await getStore(params.identifier.replace('.html', ''))
    .then((d) => (d.store.name ? d.store : notFound()))
    .catch(notFound)
  const staticContent = await getStaticContent()
  const baseUrl = staticSelectors.selectStoreBaseUrl(staticContent)
  return (
    <Container>
      <Breadcrumb
        breadcrumbs={[
          { url: '/', label: 'Home' },
          { url: '/shops', label: 'Магазини' },
          { url: '#', label: store.name },
        ]}
      />
      <meta name="robots" content="INDEX,FOLLOW" />
      <link rel="canonical" href={`${baseUrl}/shop/${params.identifier}`} />
      <div className="grid grid-cols-1 xl:grid-cols-11 gap-4 xl:gap-5 xl:mb-8 mb-4">
        <div className="col-span-12 xl:col-span-4 relative min-h-[400px] order-1 xl:order-0">
          {!store.virtualTour && (
            <div className="flex flex-col gap-5">
              <Card className="p-4 rounded-3xl shadow-none border-none">
                <StorePhotoGallery gallery={store.gallery} />
              </Card>
            </div>
          )}
          {store.virtualTour && (
            <iframe
              className="rounded-3xl"
              src={store.virtualTour}
              allowFullScreen
              width="100%"
              height="100%"
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
            />
          )}
        </div>
        <div className="col-span-12 xl:col-span-7 order-0 xl:order-1">
          <div className="grid grid-cols-1 xl:grid-cols-12 gap-4 xl:gap-5">
            <div className="col-span-12 xl:col-span-5">
              <StoreInfo
                name={store.name}
                address={store.address}
                phone={store.phone}
                email={store.email}
                businessHours={store.businessHours}
                transportInformation={store.transportInformation ? store.transportInformation : ''}
              />
            </div>
            <div className="col-span-12 xl:col-span-7 rounded-3xl overflow-hidden">
              <GoogleMap id="SHOP_MAP" center={{ lat: store.location.lat, lng: store.location.lng }} />
            </div>
            {/* removed until further notice */}
            {/* {store.services && (
              <div className="col-span-12">
                <StoreServices services={store.services} />
              </div>
            )} */}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-11 gap-5 mb-8">
        <div className="col-span-12 xl:col-span-4 relative">
          {store.virtualTour && <StorePhotoGallery gallery={store.gallery} />}
        </div>
        <div className="col-span-12 xl:col-span-7 relative">
          <Static
            component={InquireCard}
            storeName={store.name}
            title="Изготвяне на оферта"
            className="min-h-[500px]"
          />
        </div>
      </div>
    </Container>
  )
}
