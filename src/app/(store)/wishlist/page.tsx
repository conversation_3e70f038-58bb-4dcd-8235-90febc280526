import { Card, CardContent, CardFooter, CardHeader } from '@components/theme/ui/card'
import CatalogViewGrid from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/CatalogViewGrid'
import { ProductViewFragment } from '@lib/_generated/graphql_sdk'
import { ServerGraphQLBackend } from '@lib/api/graphql_server'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'
export const dynamic = 'force-dynamic'
const Wishlist = async () => {
  let wishlistProducts: ProductViewFragment[] = []
  try {
    const result = await ServerGraphQLBackend.GetWishlistProducts()
    wishlistProducts = result.customerWishlist.products
  } catch (e) {}
  return (
    <div className="w-full">
      <div className="py-4 px-2 md:px-0">
        <h2 className="text-xl md:text-2xl font-bold">Списък с любими продукти</h2>
      </div>
      {wishlistProducts.length === 0 ? (
        <Card className="my-4">
          <CardContent className="p-6 text-center">
            <p className="text-gray-500">Нямате добавени продукти в списъка с любими</p>
          </CardContent>
        </Card>
      ) : (
        <CatalogViewGrid products={wishlistProducts} />
      )}
      <RelevaTrackPage />
    </div>
  )
}

export default Wishlist
