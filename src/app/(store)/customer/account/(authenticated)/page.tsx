'use client'
import { MyProfile } from '@/src/app/(store)/customer/components/MyProfile'
import { MyProfileCard } from '@/src/app/(store)/customer/components/MyProfileCard'
import { useCustomerStore } from '@features/customer/customer.store'
import { Skeleton } from '@components/theme/ui/skeleton'
import React from 'react'

export default function Account() {
  const customerStore = useCustomerStore()
  const defaultBillingAddress = customerStore.customer?.addresses.find(
    (address) => String(address.id) === String(customerStore.customer?.defaultBillingAddressID)
  )
  const defaultShippingAddress = customerStore.customer?.addresses.find(
    (address) => String(address.id) === String(customerStore.customer?.defaultShippingAddressID)
  )
  return (
    <div className="flex flex-col flex-1 gap-4 md:gap-5">
      <div className="flex w-full">
        <MyProfile />
      </div>
      <div className="flex flex-col sm:flex-row flex-1 gap-4 md:gap-5">
        <MyProfileCard
          title="Бюлетин"
          link="/customer/account/edit"
          label={customerStore.customer?.isSubscribed ? 'Отпишете се' : 'Абонирайте се'}
        >
          {customerStore.loading ? (
            <Skeleton className="w-[80%] h-[20px] rounded-full" />
          ) : (
            <>{customerStore.customer?.isSubscribed ? 'Вие сте се абонирали' : 'Вие не сте се абонирали'}</>
          )}
        </MyProfileCard>
        <MyProfileCard
          title="Адрес за доставка"
          link={
            defaultShippingAddress?.id
              ? `/customer/account/edit?t=address&a=edit&i=${defaultShippingAddress?.id}`
              : `/customer/account/edit?t=address&a=create`
          }
          label={defaultBillingAddress?.id ? 'Редактиране' : 'Добави адрес'}
        >
          {customerStore.loading ? (
            <Skeleton className="w-[80%] h-[20px] rounded-full" />
          ) : (
            <div className="flex flex-col gap-1 text-sm md:text-base">
              {defaultShippingAddress ? (
                <>
                  <span>
                    {defaultShippingAddress?.country}, {defaultShippingAddress?.city} {defaultShippingAddress?.postCode}
                  </span>
                  <span>{defaultShippingAddress?.street}</span>
                  <span>
                    {defaultShippingAddress?.firstName} {defaultShippingAddress?.lastName}
                  </span>
                  <span>{defaultShippingAddress?.phone}</span>
                </>
              ) : (
                <span className="text-gray-400">Нямате добавени адреси</span>
              )}
            </div>
          )}
        </MyProfileCard>
        <MyProfileCard
          title="Данни за фактуриране"
          link={
            defaultBillingAddress?.id
              ? `/customer/account/edit?t=address&a=edit&i=${defaultBillingAddress?.id}`
              : `/customer/account/edit?t=address&a=create`
          }
          label={defaultBillingAddress?.id ? 'Редактиране' : 'Добави адрес'}
        >
          {customerStore.loading ? (
            <Skeleton className="w-[80%] h-[20px] rounded-full" />
          ) : (
            <div className="flex flex-col gap-1 text-sm md:text-base">
              {defaultShippingAddress ? (
                <>
                  <span>{defaultBillingAddress?.companyName}</span>
                  <span>
                    {defaultBillingAddress?.country}, {defaultBillingAddress?.city} {defaultBillingAddress?.postCode}
                  </span>
                  <span>{defaultBillingAddress?.street}</span>
                  <span>
                    {defaultBillingAddress?.firstName} {defaultBillingAddress?.lastName}
                  </span>
                  <span>{defaultBillingAddress?.phone}</span>
                </>
              ) : (
                <span className="text-gray-400">Нямате добавени адреси</span>
              )}
            </div>
          )}
        </MyProfileCard>
      </div>
    </div>
  )
}
