import React, { PropsWithChildren } from 'react'

import Menu from '@/src/app/(store)/customer/components/Menu'
import Container from '@atoms/Container'
import { ServerGraphQLBackend } from '@lib/api/graphql_server'
import { redirect } from 'next/navigation'
import { CustomerAreaClient } from '@/src/components/organisms/Header/components/CustomerAreaClient'
import { cookies } from 'next/headers'
import { AuthCookieName } from '@/src/features/cart/ClientCookie'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'

export const dynamic = 'force-dynamic'
export const fetchCache = 'force-no-store'
export const revalidate = 0

const CustomerLayout: React.FC<PropsWithChildren> = async ({ children }) => {
  // Get the auth token directly from cookies
  const cookieStore = cookies()
  const authToken = (await cookieStore).get(AuthCookieName)?.value

  if (!authToken) {
    redirect('/customer/account/login')
  }

  try {
    const customerData = await ServerGraphQLBackend.CustomerData()

    return (
      <Container className="flex flex-col md:flex-row p-4 md:p-10 gap-5">
        <CustomerAreaClient
          showCustomerNav={false}
          customerData={customerData.customerData}
          wishlistSkus={[]}
          className="hidden md:block"
        />
        <div className="w-full md:w-auto">
          <Menu />
        </div>
        <div className="flex flex-col flex-1 mt-4 md:mt-0">{children}</div>
        <RelevaTrackPage />
      </Container>
    )
  } catch (e) {
    redirect('/customer/account/login')
  }
}

export default CustomerLayout
