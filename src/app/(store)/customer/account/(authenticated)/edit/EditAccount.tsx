'use client'

import { useCustomerStore } from '@features/customer/customer.store'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@components/theme/ui/tabs'
import { Card } from '@components/theme/ui/card'
import { CustomerPassword } from '@components/organisms/Forms/CustomerPassword/CustomerPassword'
import { CustomerAddressesManager } from '@/src/app/(store)/customer/account/(authenticated)/edit/components/CustomerAddressesManager'
import { useRouter, useSearchParams } from 'next/navigation'
import { CustomerData } from '@components/organisms/Forms/CustomerData/CustomerData'
import { mapCustomerDataPayloadToForm } from '@components/organisms/Forms/CustomerData/mapper'
import { useCallback } from 'react'
import { Skeleton } from '@components/theme/ui/skeleton'

const tabClass =
  'rounded-md data-[state=active]:bg-primary data-[state=active]:text-white data-[state=active]:shadow-none py-2'
const EditAccountPage = () => {
  const customerStore = useCustomerStore()
  const router = useRouter()
  const searchParams = useSearchParams()
  const tabUrlParam = searchParams.get('t')
  const activeTab = tabUrlParam || 'account'

  const handleTabChange = useCallback(
    (value: string) => {
      router.push(`/customer/account/edit?t=${value}`)
    },
    [router]
  )

  return (
    <Card>
      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className="grid grid-cols-3 h-auto">
          <TabsTrigger value="account" className={tabClass} onClick={() => handleTabChange('account')}>
            <div>Профил</div>
          </TabsTrigger>
          <TabsTrigger value="password" className={tabClass} onClick={() => handleTabChange('password')}>
            <div>Парола</div>
          </TabsTrigger>
          <TabsTrigger value="address" className={tabClass} onClick={() => handleTabChange('address')}>
            <div>Адреси</div>
          </TabsTrigger>
        </TabsList>
        <div className="h-px bg-gray-200 w-full" />
        <TabsContent value="account" className="mt-0">
          {customerStore.customer && (
            <CustomerData
              data={mapCustomerDataPayloadToForm(customerStore.customer)}
              addresses={customerStore.customer.addresses}
            />
          )}
          {customerStore.loading && (
            <div className="flex flex-col gap-3 p-4">
              <div className="flex flex-col gap-1.5">
                <Skeleton className="w-[80%] h-[20px] rounded-full" />
                <Skeleton className="w-[70%] h-[20px] rounded-full" />
                <Skeleton className="w-[60%] h-[20px] rounded-full" />
                <Skeleton className="w-[50%] h-[20px] rounded-full" />
              </div>
            </div>
          )}
        </TabsContent>
        <TabsContent value="password" className="mt-0">
          <CustomerPassword />
        </TabsContent>
        <TabsContent value="address" className="mt-0">
          <CustomerAddressesManager />
        </TabsContent>
      </Tabs>
    </Card>
  )
}

export default EditAccountPage
