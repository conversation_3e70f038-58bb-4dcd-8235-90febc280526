import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@components/theme/ui/dialog'
import { LucideLoaderCircle, LucideTrash, LucideX } from 'lucide-react'
import React, { PropsWithChildren } from 'react'
import { Button } from '@components/theme/ui/button'
import { useCustomerStore } from '@features/customer/customer.store'

interface AddressItemDeleteProps {
  addressId: string
  cantDeleteDefaultAddress?: boolean
}

export const AddressItemDelete: React.FC<PropsWithChildren<AddressItemDeleteProps>> = ({
  children,
  addressId,
  cantDeleteDefaultAddress,
}) => {
  const [loading, setLoading] = React.useState(false)
  const deleteCustomerAddress = useCustomerStore((state) => state.deleteCustomerAddress)
  const closeRef = React.useRef<HTMLButtonElement>(null)

  const handleDelete = () => {
    setLoading(true)
    deleteCustomerAddress(addressId).finally(() => {
      setLoading(false)
      closeRef.current?.click()
    })
  }

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      {cantDeleteDefaultAddress ? (
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Не може да изтриете адреса</DialogTitle>
            <DialogDescription>
              Този адрес е зададен като адрес за доставка или фактуриране. За да изтриете адреса, моля, първо задайте
              друг адрес като основен
            </DialogDescription>
            <div className="flex justify-end gap-2">
              <DialogClose ref={closeRef} asChild>
                <Button>
                  <LucideX />
                  Затвори
                </Button>
              </DialogClose>
            </div>
          </DialogHeader>
        </DialogContent>
      ) : (
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Потвърждавате ли изтриването на адреса?</DialogTitle>
            <DialogDescription>Това действие не може да бъде отменено.</DialogDescription>
            <div className="flex justify-end gap-2 !mt-10">
              <Button variant="destructive" onClick={handleDelete} disabled={loading}>
                {loading ? <LucideLoaderCircle className="animate-spin" /> : <LucideTrash />}
                {loading ? 'Изтриване...' : 'Изтриване'}
              </Button>
              <DialogClose ref={closeRef} asChild>
                <Button>
                  <LucideX />
                  Отказ
                </Button>
              </DialogClose>
            </div>
          </DialogHeader>
        </DialogContent>
      )}
    </Dialog>
  )
}
