import { useSearchParams } from 'next/navigation'
import { CustomerAddressesList } from '@/src/app/(store)/customer/account/(authenticated)/edit/components/CustomerAddressesList'
import { CustomerAddress } from '@components/organisms/Forms/CustomerAddress/CustomerAddress'
import { useCustomerStore } from '@features/customer/customer.store'
import { mapAddressPayloadToForm } from '@components/organisms/Forms/CustomerAddress/mapper'

export const CustomerAddressesManager = () => {
  const searchParams = useSearchParams()
  const action = searchParams.get('a')
  const addressId = Number(searchParams.get('i'))
  const addresses = useCustomerStore((state) => state.customer?.addresses)
  const defaultBillingAddressID = useCustomerStore((state) => state.customer?.defaultBillingAddressID)
  const defaultShippingAddressID = useCustomerStore((state) => state.customer?.defaultShippingAddressID)

  // Find the target address from the customer's addresses array
  const targetAddress =
    addressId && addresses ? addresses.find((addr) => String(addr.id) === String(addressId)) : undefined

  // Map the address to the form data format if it exists
  const formData = targetAddress ? mapAddressPayloadToForm({ data: targetAddress }) : undefined

  // Set default billing/shipping flags if this is the default address
  if (formData && addresses) {
    formData.isDefaultBilling = String(defaultBillingAddressID) === String(targetAddress?.id)
    formData.isDefaultShipping = String(defaultShippingAddressID) === String(targetAddress?.id)
  }

  return (
    <div className="my-3 flex flex-col gap-3">
      {!action && <CustomerAddressesList />}
      {action === 'create' && <CustomerAddress mode="create" />}
      {action === 'edit' && formData && <CustomerAddress mode="update" data={formData} />}
    </div>
  )
}
