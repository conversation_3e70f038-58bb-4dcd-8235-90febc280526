import AppLink from '@atoms/AppLink'
import { controlsClasses } from '@/src/app/(store)/customer/account/(authenticated)/edit/components/styles'
import { LucideEdit } from 'lucide-react'
import React from 'react'
import Text from '@atoms/Text'

interface AddressItemEditProps {
  addressId: string
}

export const AddressItemEdit: React.FC<AddressItemEditProps> = ({ addressId }) => {
  return (
    <AppLink href={`/customer/account/edit?t=address&a=edit&i=${addressId}`} className={controlsClasses}>
      <LucideEdit size={13} />
      <Text>Редактиране</Text>
    </AppLink>
  )
}
