import React from 'react'
import { Luc<PERSON><PERSON>heck, LucideTrash } from 'lucide-react'
import Text from '@atoms/Text'

import { CustomerAddress } from '@lib/_generated/graphql_sdk'
import { AddressItemDelete } from './AddressItemDelete'
import { AddressItemEdit } from '@/src/app/(store)/customer/account/(authenticated)/edit/components/AddressItemEdit'
import { cn } from '@components/lib/utils'
import { controlsClasses } from '@/src/app/(store)/customer/account/(authenticated)/edit/components/styles'

interface AddressListItemProps {
  address: CustomerAddress
  index: number
  isDefaultBilling?: boolean
  isDefaultShipping?: boolean
  onClick?: (event: React.MouseEvent) => void
}

export const AddressListItem: React.FC<AddressListItemProps> = ({
  address,
  index,
  isDefaultBilling,
  isDefaultShipping,
  onClick,
}) => {
  return (
    <div>
      {index > 0 && <div className="my-3 h-px bg-gray-200 w-full" />}
      <div className="flex group p-4 hover:bg-primary/10 hover:rounded-xl cursor-pointer">
        <div className="flex flex-col sm:flex-row flex-1" onClick={onClick}>
          <div className="flex flex-col gap-2">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-bold">
                {address.firstName} {address.lastName}
              </h3>
              {/*<AddressListItemMenu>*/}
              {/*  <LucideMenu size={16} />*/}
              {/*</AddressListItemMenu>*/}
            </div>
            <p>{address.street}</p>
            <p>
              {address.city}, {address.postCode}
            </p>
            <p>{address.phone}</p>
          </div>
          {(isDefaultBilling || isDefaultShipping) && (
            <div className="flex flex-row justify-center items-center sm:pl-10 gap-2 text-primary font-bold text-sm sm:text-base mt-2 sm:mt-0">
              <LucideCheck className="text-primary" />
              Основен адрес
              {isDefaultBilling && isDefaultShipping && ' за фактуриране и доставка'}
              {isDefaultBilling && !isDefaultShipping && ' за фактуриране'}
              {!isDefaultBilling && isDefaultShipping && ' за доставка'}
            </div>
          )}
          <div className="flex-1" />
        </div>
        <div className="flex-col justify-center hidden sm:group-hover:flex gap-3">
          <AddressItemEdit addressId={address.id} />
          <AddressItemDelete addressId={address.id} cantDeleteDefaultAddress={isDefaultBilling || isDefaultShipping}>
            <div className={cn(controlsClasses, 'group/item hover:text-red-500')}>
              <LucideTrash size={13} className="text-primary group/item-hover:text-blue-500" />
              <Text>Изтриване</Text>
            </div>
          </AddressItemDelete>
        </div>
      </div>
    </div>
  )
}
