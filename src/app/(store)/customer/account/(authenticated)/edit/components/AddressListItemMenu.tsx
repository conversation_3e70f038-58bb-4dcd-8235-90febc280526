import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/src/components/theme/ui/dropdown-menu'
import { DropdownMenuItem, DropdownMenuLabel } from '@radix-ui/react-dropdown-menu'
import React, { PropsWithChildren } from 'react'
import { LucideEdit, LucideTrash } from 'lucide-react'

export const AddressListItemMenu: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent className="bg-white p-3 flex flex-col gap-1">
        <DropdownMenuLabel className="font-bold">Управление</DropdownMenuLabel>
        <DropdownMenuSeparator className="px-1 bg-gray-300" />
        <DropdownMenuItem>
          <div className="flex items-center gap-2 py-2">
            <LucideEdit size={18} /> Редактиране
          </div>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <div className="flex items-center gap-2 py-2">
            <LucideTrash size={18} />
            Изтриване
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
