import { Button } from '@components/theme/ui/button'
import { useRouter } from 'next/navigation'
import { useCustomerStore } from '@features/customer/customer.store'

import { AddressListItem } from '@/src/app/(store)/customer/account/(authenticated)/edit/components/AddressListItem'
import { Skeleton } from '@components/theme/ui/skeleton'
import { useCallback } from 'react'

export const CustomerAddressesList = () => {
  const router = useRouter()
  const customerLoading = useCustomerStore((state) => state.loading)
  const customerAddresses = useCustomerStore((state) => state.customer?.addresses)
  const defaultBillingAddress = useCustomerStore((state) => state.customer?.defaultBillingAddressID)
  const defaultShippingAddress = useCustomerStore((state) => state.customer?.defaultShippingAddressID)

  const handleClick = useCallback(
    (addressId: string) => {
      router.push(`/customer/account/edit?t=address&a=edit&i=${addressId}`)
    },
    [router]
  )

  return (
    <>
      <div className="flex flex-col px-3">
        {customerLoading && (
          <div className="flex flex-col gap-3 p-4">
            <div className="flex flex-col gap-1.5">
              <Skeleton className="w-[80%] h-[20px] rounded-full" />
              <Skeleton className="w-[70%] h-[20px] rounded-full" />
              <Skeleton className="w-[60%] h-[20px] rounded-full" />
              <Skeleton className="w-[50%] h-[20px] rounded-full" />
            </div>
          </div>
        )}
        {!customerLoading && customerAddresses?.length === 0 && (
          <div className="flex flex-col gap-3 p-4">
            <div className="flex flex-col gap-1.5">
              <p className="text-gray-400">Нямате добавени адреси.</p>
            </div>
          </div>
        )}
        {customerAddresses?.map((address, index) => {
          const isDefaultBilling = String(defaultBillingAddress) === String(address.id)
          const isDefaultShipping = String(defaultShippingAddress) === String(address.id)
          return (
            <AddressListItem
              key={address.id}
              address={address}
              index={index}
              isDefaultBilling={isDefaultBilling}
              isDefaultShipping={isDefaultShipping}
              onClick={(e) => {
                e.preventDefault()
                e.isPropagationStopped()
                handleClick(address.id)
              }}
            />
          )
        })}
      </div>
      {!customerLoading && (
        <div className="flex justify-center items-center">
          <Button
            onClick={() => {
              router.push(`/customer/account/edit?t=address&a=create`)
            }}
          >
            Добави нов адрес
          </Button>
        </div>
      )}
    </>
  )
}
