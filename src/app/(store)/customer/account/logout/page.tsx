'use client'
import { useCustomerStore } from '@features/customer/customer.store'
import React, { useEffect, useState } from 'react'
import Container from '@atoms/Container'
import Text from '@atoms/Text'
import { Button } from '@components/theme/ui/button'
import { useAsyncRoutePush } from '@/src/hooks/useAsyncRoutePush'
import { LucideArrowLeft, LucideLoaderCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'

const Page = () => {
  const logoutCustomer = useCustomerStore((state) => state.logoutCustomer)
  const routePush = useAsyncRoutePush()
  const router = useRouter()
  const [redirecting, setRedirecting] = useState(false)
  const [loading, setLoading] = useState(true)
  const [status, setStatus] = useState<boolean>()

  const [mounted, setMounted] = useState(false)
  useEffect(() => {
    if (!mounted) {
      setMounted(true)
      return
    }
    if (loading) {
      console.log('logoutCustomer')
      logoutCustomer().then((result) => {
        setTimeout(() => {
          setStatus(result)
          setLoading(false)
        }, 1000)
        setTimeout(() => {
          routePush('/').finally(() => setRedirecting(false))
          return
        }, 3000)
      })
    }
  }, [loading, logoutCustomer, mounted, routePush])

  return (
    <Container>
      <div className="flex flex-col gap-10 items-center justify-center py-32">
        {loading ? (
          <>
            <h1 className="text-2xl font-bold">
              <Text>Излизане от системата</Text>
            </h1>
            <LucideLoaderCircle className="animate-spin" />
            <Text className='className="mt-4"'>Моля изчакайте</Text>
          </>
        ) : (
          <>
            {status && (
              <>
                <h1 className="text-2xl font-bold">
                  <Text>Вие излязохте успешно!</Text>
                </h1>
                <Text className='className="mt-4"'>Благодарим Ви! Очакваме ви отново!</Text>
              </>
            )}
            <Button
              onClick={() => {
                setRedirecting(true)
                routePush('/').finally(() => setRedirecting(false))
              }}
            >
              {redirecting ? <LucideLoaderCircle className="animate-spin" /> : <LucideArrowLeft />}
              Обратно към магазина
            </Button>
          </>
        )}
      </div>
    </Container>
  )
}

export default Page
