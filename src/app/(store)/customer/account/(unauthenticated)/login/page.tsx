import { LoginForm } from '@/src/app/(store)/customer/account/(unauthenticated)/login/components/LoginForm'
import AppLink from '@atoms/AppLink'
import Text from '@atoms/Text'
import { Button } from '@components/theme/ui/button'
import { <PERSON>, CardContent, CardFooter, CardTitle } from '@components/theme/ui/card'
import Static from '@features/static'

export default async function Login({ searchParams: params }: { searchParams: Promise<{ onSuccessUrl: string }> }) {
  const titleClasses = 'leading-none pt-2 pb-6 text-2xl font-bold text-black mt-0'
  const loginSuccessRoute = await params.then((res) => res.onSuccessUrl || '/')
  return (
    <div className="flex flex-col lg:flex-row flex-1 justify-center gap-8">
      <Card className="w-full lg:w-[460px] rounded-3xl px-5 py-6">
        <CardTitle className="flex justify-center py-4 lg:py-10">
          <Text className="text-2xl text-primary tracking-wide">Вход</Text>
        </CardTitle>
        <CardContent>
          <Static component={LoginForm} onSuccessUrl={loginSuccessRoute} />
        </CardContent>
        <CardFooter>
          <div className="flex justify-center mt-5">
            <AppLink href="/customer/account/forgotpassword" className="underline text-sm">
              Забравена парола?
            </AppLink>
          </div>
        </CardFooter>
      </Card>
      <Card className="w-full lg:w-[460px] rounded-3xl p-5 bg-primary">
        <CardTitle className="flex justify-center py-4 lg:py-10">
          <Text className="text-2xl text-white tracking-wide">Нямате профил?</Text>
        </CardTitle>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div className="text-white">
              Регистрирайте се в praktis.bg, за да получите достъп до своите поръчки, да следите пратките или направите
              списък с покупки.
            </div>
            <AppLink href="/customer/account/create">
              <Button className="outline">Регистрация</Button>
            </AppLink>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
