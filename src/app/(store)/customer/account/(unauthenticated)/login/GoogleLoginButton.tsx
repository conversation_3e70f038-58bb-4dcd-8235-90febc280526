'use client'
import { useGoogleLogin, GoogleOAuthProvider } from '@react-oauth/google'

import useGoogleClientId from '@/src/hooks/useGoogleClientId'
import { UserProfile } from '@/src/models/userInterface'
import { <PERSON><PERSON> } from '@components/theme/ui/button'
import { showErrorToast } from '@lib/utils/toaster'

import { fetchTokenUser } from './fetchTokenUser'

interface GoogleLoginButtonProps {
  onLoginSuccess: (userProfile: UserProfile) => void
}

const GoogleLoginButton: React.FC<GoogleLoginButtonProps> = ({ onLoginSuccess }) => {
  const handleLoginSuccess = async (response: any) => {
    try {
      const userProfile = await fetchTokenUser(response.access_token)
      onLoginSuccess(userProfile)
    } catch (error) {
      console.error('Error fetching user profile:', error)
    }
  }

  const login = useGoogleLogin({
    onSuccess: handleLoginSuccess,
    onError: (error) => console.log('Login Failed:', error),
  })

  return <Button onClick={() => login()}>Вход с Google</Button>
}

const GoogleLoginButtonWithProvider: React.FC<GoogleLoginButtonProps> = (props) => {
  const googleClientId = useGoogleClientId()
  if (!googleClientId.value) {
    return <div>Google Client ID is missing. Please set it up in backend.</div>
  }
  return (
    <GoogleOAuthProvider clientId={googleClientId.value}>
      <GoogleLoginButton {...props} />
    </GoogleOAuthProvider>
  )
}

export default GoogleLoginButtonWithProvider
