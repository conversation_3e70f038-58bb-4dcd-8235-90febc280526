import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import Text from '@atoms/Text'
import { Button } from '@components/theme/ui/button'

export default function RegisterBlock() {
  return (
    <div>
      <Text>Регистрирайте се в </Text>
      <AppLink href="/" className="text-primary">
        praktis.bg
      </AppLink>
      <Text>, за да получите достъп до своите поръчки, да следите пратките или направите списък с покупки.</Text>
      <div className="flex my-10">
        <Button>
          <AppLink href="/customer/account/create">Регистрация</AppLink>
        </Button>
      </div>
      <div className="h-[220px] w-full flex relative">
        <Img
          className="w-full rounded-3xl"
          src={'https://praktis.bg/media/wysiwyg/2024/15_new_customer.png'}
          alt={''}
          fill
        />
      </div>
      <Text>
        Важно!: Вашият код ще бъде изпратен на имелът ви след завършване на регистрацията. Кода от 15% не важи за
        намалени продукти, купи изгодно и за продукти с безплатна доставка!
      </Text>
    </div>
  )
}
