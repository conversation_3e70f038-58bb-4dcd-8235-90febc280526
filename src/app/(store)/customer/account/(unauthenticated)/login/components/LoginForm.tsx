'use client'

import { LucideLoaderCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

import useRecaptcha from '@/src/hooks/useRecaptcha'
import { Button } from '@components/theme/ui/button'
import { Input } from '@components/theme/ui/input'
import { Label } from '@components/theme/ui/label'
import { useCustomerStore } from '@features/customer/customer.store'
import { staticSelectors } from '@features/static/selectors'
import { StaticContentProps } from '@features/static/types'

export const LoginForm: React.FC<StaticContentProps & { onSuccessUrl?: string }> = ({
  staticContent,
  onSuccessUrl: loginSuccessRoute = '/',
}) => {
  const { googleRecaptchaKey: siteKey } = staticSelectors.selectApiKeys(staticContent)
  const { executeRecaptcha } = useRecaptcha(siteKey)
  const router = useRouter()

  const { signIn } = useCustomerStore()
  const [loading, setLoading] = useState(false)

  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  const handleLogin = async () => {
    setLoading(true)
    const captchaToken = await executeRecaptcha('login')
    const result = await signIn(email.trim(), password, captchaToken)
    if (result) {
      setLoading(false)
      router.push(loginSuccessRoute)
      return
    }
    setLoading(false)
  }

  return (
    <div className="flex flex-col gap-8">
      <div className="flex flex-col gap-1">
        <Label className="ml-5 text-gray-600">Email:</Label>
        <Input
          disabled={loading}
          className="border border-gray-200 rounded-full h-10"
          placeholder="Въведете потребителско име"
          onChange={(e) => {
            setEmail(e.target.value)
          }}
        />
      </div>
      <div className="flex flex-col gap-1">
        <Label className="ml-5 text-gray-600">Парола:</Label>
        <Input
          disabled={loading}
          type="password"
          className="border border-gray-200 rounded-full h-10 placeholder:text-base text-2xl"
          placeholder="Въведете парола"
          onChange={(e) => {
            setPassword(e.target.value)
          }}
        />
      </div>
      <div className="flex flex-col">
        <Button onClick={handleLogin} disabled={loading}>
          {loading && <LucideLoaderCircle className="animate-spin" />}
          Вход
        </Button>
      </div>
    </div>
  )
}
