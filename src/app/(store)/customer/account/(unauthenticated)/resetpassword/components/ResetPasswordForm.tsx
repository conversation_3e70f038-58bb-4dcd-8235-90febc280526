'use client'
import { zodResolver } from '@hookform/resolvers/zod'
import { useCallback, useEffect, useState } from 'react'
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import { useRouter, useSearchParams } from 'next/navigation'

import { FormTextField } from '@components/molecules/FormControllers/FormTextField'
import { Button } from '@components/theme/ui/button'
import { GraphQLBackend } from '@lib/api/graphql'
import { showErrorToast, showSuccessToast } from '@lib/utils/toaster'
import { handleAPIError } from '@lib/types/isAPIError'

// Define schema with zod
const resetPasswordSchema = z
  .object({
    password: z.string().min(1, { message: 'Това поле е задължително' }),
    passwordConfirm: z.string().min(1, { message: 'Това поле е задължително' }),
  })
  .refine((data) => data.password === data.passwordConfirm, {
    message: 'Паролите не съвпадат',
    path: ['passwordConfirm'],
  })

// Create type from schema
type ResetPasswordSchema = z.infer<typeof resetPasswordSchema>

// Default values
const resetPasswordDefaults: ResetPasswordSchema = {
  password: '',
  passwordConfirm: '',
}

export function ResetPasswordForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(false)
  
  // Get the id and token from URL query parameters
  const customerId = searchParams.get('id')
  const resetToken = searchParams.get('token')
  
  // Redirect if no id or token
  useEffect(() => {
    if (!customerId || !resetToken) {
      showErrorToast({
        title: 'Невалиден линк',
        description: 'Линкът за промяна на парола е невалиден или изтекъл.',
      })
      router.push('/customer/account/login')
    }
  }, [customerId, resetToken, router])

  // Initialize form with react-hook-form
  const form = useForm<ResetPasswordSchema>({
    resolver: zodResolver(resetPasswordSchema),
    mode: 'onBlur',
    defaultValues: resetPasswordDefaults,
  })

  const onSubmit: SubmitHandler<ResetPasswordSchema> = useCallback(
    (data) => {
      if (!customerId || !resetToken) {
        showErrorToast({
          title: 'Невалиден линк',
          description: 'Линкът за промяна на парола е невалиден или изтекъл.',
        })
        return
      }

      setLoading(true)
      GraphQLBackend.CustomerPasswordReset({
        customerId: customerId,
        password: data.password,
        resetToken: resetToken,
      })
        .then((result) => {
          setLoading(false)
          if (result.customerPasswordReset) {
            showSuccessToast({
              title: 'Паролата е променена успешно',
              description: 'Вече можете да влезете с новата си парола.',
            })
            router.push('/customer/account/login')
          } else {
            showErrorToast({
              title: 'Нещо се обърка!',
              description: 'Неуспешна промяна на парола. Моля, опитайте отново.',
            })
          }
        })
        .catch((error) => {
          setLoading(false)
          handleAPIError(error)
        })
    },
    [customerId, resetToken, router]
  )

  const onError = useCallback(() => {
    showErrorToast({
      title: 'Невалидни данни',
      description: 'Моля, проверете въведените данни и опитайте отново.',
    })
  }, [])

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit, onError)}>
        <div className="mb-6">
          <FormTextField name="password" label="Нова парола:" type="password" required />
        </div>
        <div className="mb-6">
          <FormTextField name="passwordConfirm" label="Потвърди нова парола:" type="password" required />
        </div>

        <Button type="submit" className="px-8 py-2 text-white focus:outline-none h-[40px]" disabled={loading}>
          {loading ? 'Изпращане...' : 'Изпрати'}
        </Button>
      </form>
    </FormProvider>
  )
}
