'use client'
import { zodResolver } from '@hookform/resolvers/zod'
import { useCallback, useState } from 'react'
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'

import Container from '@atoms/Container'
import Title from '@atoms/Title'
import { FormTextField } from '@components/molecules/FormControllers/FormTextField'
import { Button } from '@components/theme/ui/button'
import { GraphQLBackend } from '@lib/api/graphql'
import { showErrorToast, showSuccessToast } from '@lib/utils/toaster'
import { handleAPIError } from '@lib/types/isAPIError'
import { LucideLoaderCircle } from 'lucide-react'

// Define schema with zod
const forgotPasswordSchema = z.object({
  email: z.string().email({ message: 'Невалиден имейл адрес' }).min(1, { message: 'Това поле е задължително' }),
})

// Create type from schema
type ForgotPasswordSchema = z.infer<typeof forgotPasswordSchema>

// Default values
const forgotPasswordDefaults: ForgotPasswordSchema = {
  email: '',
}

export default function ForgottenPassword() {
  const [loading, setLoading] = useState(false)

  // Initialize form with react-hook-form
  const form = useForm<ForgotPasswordSchema>({
    resolver: zodResolver(forgotPasswordSchema),
    mode: 'onBlur',
    defaultValues: forgotPasswordDefaults,
    disabled: loading,
  })
  const { resetField } = form

  const onSubmit: SubmitHandler<ForgotPasswordSchema> = useCallback(
    (data) => {
      setLoading(true)
      GraphQLBackend.ForgotPassword({ email: data.email })
        .then((result) => {
          if (result.customerPasswordForgot) {
            showSuccessToast({
              title: 'Заявката за промяна на парола',
              description: 'Моля, проверете вашият имейл',
            })
            resetField('email')
          } else {
            showErrorToast({
              title: 'Нещо се обърка!',
              description: 'Неуспешна заявка за промяна на парола. Моля, опитайте отново.',
            })
          }
        })
        .catch((error) => {
          handleAPIError(error)
        })
        .finally(() => {
          setLoading(false)
        })
      console.log('handleSubmitForgotPassword', data)
    },
    [resetField]
  )

  const onError = useCallback(() => {}, [])

  return (
    <Container maxWidth="xl">
      <div className="rounded-3xl w-full bg-white p-6 desktop:p-10 mb-10 mt-8">
        <Title size="h2" className="leading-none pt-2 pb-6 text-2xl font-bold text-black mt-0">
          Забравена парола?
        </Title>

        <p>
          Моля въведете e-mail адреса, с който сте регистриран в сайта и ще получите линк за генериране на нова парола.
        </p>
        <div className="flex flex-col desktop:w-[30%] w-full mt-10">
          <FormProvider {...form}>
            <form onSubmit={form.handleSubmit(onSubmit, onError)}>
              <div className="mb-6">
                <FormTextField name="email" label="Email:" type="email" required />
              </div>

              <Button type="submit" className="px-8 py-2 text-white rounded-lg focus:outline-none h-[40px]">
                {loading && <LucideLoaderCircle className="animate-spin mr-2" />}
                Изпрати
              </Button>
            </form>
          </FormProvider>
        </div>
      </div>
    </Container>
  )
}
