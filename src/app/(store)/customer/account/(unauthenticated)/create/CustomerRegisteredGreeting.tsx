import { Check } from 'lucide-react'
import React, { useEffect, useState } from 'react'

import Text from '@atoms/Text'

export const CustomerRegisteredGreeting = () => {
  const [circleExpanded, setCircleExpanded] = useState(false)
  const [checkVisible, setCheckVisible] = useState(false)
  const [textVisible, setTextVisible] = useState(false)
  const [registrationVisible, setRegistrationVisible] = useState(false)

  useEffect(() => {
    // Stagger animations - circle first, then check, then text
    const circleTimer = setTimeout(() => setCircleExpanded(true), 100)

    // Start checkbox animation only after circle expansion completes
    const checkTimer = setTimeout(() => setCheckVisible(true), 700)

    // Text animations come last
    const textTimer = setTimeout(() => setTextVisible(true), 1300)
    const registrationTimer = setTimeout(() => setRegistrationVisible(true), 1600)

    return () => {
      clearTimeout(circleTimer)
      clearTimeout(checkTimer)
      clearTimeout(textTimer)
      clearTimeout(registrationTimer)
    }
  }, [])

  return (
    <div className="flex flex-col items-center w-full gap-10">
      <div
        className={`relative flex items-center justify-center w-fit aspect-square bg-primary rounded-full p-12 transition-all duration-500 ease-out ${
          circleExpanded ? 'scale-100' : 'scale-0'
        }`}
        style={{
          transformOrigin: 'center',
          transform: circleExpanded ? 'scale(1)' : 'scale(0)',
        }}
      >
        {circleExpanded && (
          <div
            className={`transition-all duration-700 ease-out ${checkVisible ? 'opacity-100 scale-100 rotate-0' : 'opacity-0 scale-50 rotate-180'}`}
            style={{
              transformOrigin: 'center',
            }}
          >
            <Check
              size={150}
              className="text-white"
              strokeWidth={3}
              style={{
                strokeDasharray: 320,
                strokeDashoffset: checkVisible ? 0 : 320,
                transition: 'stroke-dashoffset 0.7s ease-out',
              }}
            />
          </div>
        )}
      </div>
      <div className="flex flex-col items-center w-full gap-5">
        <Text
          className={`text-lg transition-all duration-500 ease-out transform ${
            textVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
          }`}
        >
          Успешно завършихте своята
        </Text>
        <Text
          className={`text-2xl font-bold text-primary transition-all duration-500 ease-out transform ${
            registrationVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
          }`}
        >
          Регистрация
        </Text>
      </div>
    </div>
  )
}

// Add these CSS rules to your global styles or a stylesheet
const styles = `
@keyframes spinMultiple {
  0% {
    opacity: 0;
    transform: rotate(0deg) scale(0.5);
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 1;
    transform: rotate(1080deg) scale(1);
  }
}

.animate-spin-multiple {
  animation: spinMultiple 0.8s forwards;
}
`
