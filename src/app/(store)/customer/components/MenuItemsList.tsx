import AppLink from '@atoms/AppLink'
import { cn } from '@components/lib/utils'
import React from 'react'
import { menuItems } from '@/src/app/(store)/customer/components/menuItems'
import { PropsWithClassName } from '@lib/types/ClassName'

export const MenuItemsList: React.FC<PropsWithClassName & { onClick?: () => void; currentPath: string }> = ({
  className,
  currentPath,
  onClick,
}) => {
  return (
    <div className={cn(className)}>
      {menuItems.map((item) => (
        // <div key={item.link} className="mb-1 last:mb-0" onClick={() => setIsMenuOpen(false)}>
        <div key={item.link} className="mb-1 last:mb-0" onClick={() => onClick?.()}>
          <AppLink
            href={item.link}
            className={cn(
              'block px-4 py-3 md:py-2 rounded text-base md:text-sm',
              'transition-colors duration-200',
              currentPath === item.link ? 'bg-gray-800 text-white' : 'hover:bg-gray-700 hover:text-white'
            )}
          >
            {item.name}
          </AppLink>
        </div>
      ))}
    </div>
  )
}
