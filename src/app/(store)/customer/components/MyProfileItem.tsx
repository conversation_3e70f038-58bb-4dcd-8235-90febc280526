import { LucideEdit } from 'lucide-react'
import React from 'react'

interface MyProfileItemProps {
  label: string
  value: string
  onClickEdit: () => void
}

export const MyProfileItem: React.FC<MyProfileItemProps> = ({ label, value, onClickEdit }) => {
  return (
    <div className="flex flex-1 gap-3">
      <div className="font-bold text-sm sm:text-base">{label}:</div>
      <div className="text-gray-600 text-sm sm:text-base">{value}</div>
    </div>
  )
}
