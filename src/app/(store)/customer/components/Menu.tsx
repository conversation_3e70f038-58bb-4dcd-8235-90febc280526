'use client'

import { usePathname } from 'next/navigation'
import React, { useState } from 'react'

import AppLink from '@atoms/AppLink'
import Paper from '@atoms/Paper'
import { Card } from '@components/theme/ui/card'
import { useCustomerStore } from '@features/customer/customer.store'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@components/theme/ui/collapsible'
import { LucideChevronDown, LucideChevronUp, LucideMenu } from 'lucide-react'
import { cn } from '@components/lib/utils'
import { MenuItemsList } from '@/src/app/(store)/customer/components/MenuItemsList'

interface Props {}

const Menu: React.FC<Props> = (props) => {
  const currentPath = usePathname()
  const wishlistSkus = useCustomerStore((state) => state.wishlistSkus)
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <Card className="w-full">
      <Collapsible open={isMenuOpen} className="w-full" onChange={() => setIsMenuOpen(false)}>
        <div className="md:hidden p-4">
          <CollapsibleTrigger
            onClick={() => setIsMenuOpen(true)}
            className="flex w-full items-center justify-between py-2 px-4 bg-primary text-white rounded-md cursor-pointer"
          >
            <div className="flex items-center">
              <LucideMenu className="mr-2" size={18} />
              <span>Меню</span>
            </div>
            {isMenuOpen ? <LucideChevronUp size={18} /> : <LucideChevronDown size={18} />}
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent className="p-4 pt-2 xl:hidden">
          <MenuItemsList currentPath={currentPath} onClick={() => setIsMenuOpen(false)} />
        </CollapsibleContent>
        <MenuItemsList className="hidden xl:block p-4" currentPath={currentPath} />
      </Collapsible>
    </Card>
  )
}

export default Menu
