import React, { PropsWithChildren } from 'react'

import AppLink from '@atoms/AppLink'
import { Card, CardFooter, CardHeader } from '@components/theme/ui/card'

interface MyProfileCardProps {
  title: string
  link?: string
  label?: string
}
export const MyProfileCard: React.FC<PropsWithChildren<MyProfileCardProps>> = ({ title, children, link, label }) => {
  return (
    <Card className="flex-1 flex flex-col">
      <CardHeader className="text-xl sm:text-base font-bold">{title}</CardHeader>
      <div className="px-6 pb-4 flex-1 text-sm sm:text-base">{children}</div>
      {link && (
        <CardFooter>
          <AppLink href={link} className="text-primary text-sm">
            {label}
          </AppLink>
        </CardFooter>
      )}
    </Card>
  )
}
