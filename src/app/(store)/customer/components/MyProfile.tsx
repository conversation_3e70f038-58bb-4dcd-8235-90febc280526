'use client'
import { MyProfileItem } from '@/src/app/(store)/customer/components/MyProfileItem'
import { Card, CardContent, CardTitle } from '@components/theme/ui/card'
import AppLink from '@atoms/AppLink'
import React from 'react'
import { useCustomerStore } from '@features/customer/customer.store'
import { Skeleton } from '@components/theme/ui/skeleton'
import { LucideLoaderCircle } from 'lucide-react'

export const MyProfile = () => {
  const customerStore = useCustomerStore()
  const defaultDeliveryAddress = customerStore.customer?.addresses.find(
    (address) => String(address.id) === String(customerStore.customer?.defaultShippingAddressID)
  )
  return (
    <Card className="flex flex-col flex-1">
      <CardTitle className="p-4 pb-0 flex justify-between items-center">
        <h1 className="text-xl sm:text-2xl font-bold">Моят профил</h1>
        {customerStore.loading ? (
          <LucideLoaderCircle className="animate-spin text-primary" />
        ) : (
          <AppLink href="/customer/account/edit" className="text-primary font-normal text-sm">
            Редактирай
          </AppLink>
        )}
      </CardTitle>
      <CardContent className="p-4">
        <div className="flex flex-col gap-1.5">
          {customerStore.loading && (
            <>
              <Skeleton className="w-[80%] h-[20px] rounded-full" />
              <Skeleton className="w-[70%] h-[20px] rounded-full" />
              <Skeleton className="w-[60%] h-[20px] rounded-full" />
              <Skeleton className="w-[50%] h-[20px] rounded-full" />
            </>
          )}
          {!customerStore.loading && customerStore.customer && (
            <>
              <MyProfileItem
                label="Имена"
                value={`${customerStore.customer?.firstName} ${customerStore.customer?.lastName}`}
                onClickEdit={() => {}}
              />
              <MyProfileItem label="Имейл" value={customerStore.customer.email} onClickEdit={() => {}} />
              {defaultDeliveryAddress && (
                <MyProfileItem label="Телефон" value={defaultDeliveryAddress.phone} onClickEdit={() => {}} />
              )}
              {defaultDeliveryAddress && (
                <MyProfileItem
                  label="Адрес"
                  value={`${defaultDeliveryAddress.city} ${defaultDeliveryAddress.postCode}, ${defaultDeliveryAddress.street}`}
                  onClickEdit={() => {}}
                />
              )}
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
