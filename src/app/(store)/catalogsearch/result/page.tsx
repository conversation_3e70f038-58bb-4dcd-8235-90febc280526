import React from 'react'

import { toQueryParams } from '@/src/app/(store)/[...dynamic]/helpers'
import ErrorResults from '@/src/app/(store)/catalogsearch/result/ErrorResults'
import SearchCatalog from '@/src/app/(store)/catalogsearch/result/SearchCatalog'
import { SearchPageQuery } from '@lib/_generated/graphql_sdk'
import { GraphQLBackend } from '@lib/api/graphql'
import { NextPageProps, SearchParams } from '@lib/utils/page'

export type SearchPageData = SearchPageQuery['searchPage']

export default async function SearchPage(
  props: NextPageProps<
    {},
    {
      q?: string
    } & SearchParams
  >
) {
  try {
    const { q, ...rest } = await props.searchParams
    let prepQ = q?.toString()?.trim() ?? ''
    const data = await getSearchPageResults(prepQ, rest)
    if (!data || data.data?.products?.length < 1) {
      throw new Error("Няма намерени резултати за '" + prepQ + "'")
    }

    return <SearchCatalog data={data} q={prepQ} />
  } catch (e: any) {
    return <ErrorResults error={e} />
  }
}

async function getSearchPageResults(q?: string, rest?: SearchParams): Promise<SearchPageData> {
  if (!q || q.length < 3) {
    throw new Error('За търсене трябват минимум 3 символа')
  }

  const response = await GraphQLBackend.SearchPage({
    searchQuery: q,
    query: toQueryParams(rest),
  })

  return response?.searchPage
}
