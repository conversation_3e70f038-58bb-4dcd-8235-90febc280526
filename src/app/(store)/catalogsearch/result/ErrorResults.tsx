import React from 'react'

import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import { getErrorMessage } from '@lib/utils/error'

interface Props {
  title?: string
  error?: any
}

const ErrorResults: React.FC<Props> = ({ error, title }) => {
  const message = getErrorMessage(error)

  return (
    <>
      <div className="my-5">
        <Breadcrumb
          breadcrumbs={[
            {
              label: 'Начало',
              url: '/',
            },
            {
              label: title ?? 'Търсене',
              url: '#',
            },
          ]}
        />
      </div>
      <div className={'container flex flex-col items-center justify-center py-10'}>
        <div>
          <h1 className="text-4xl font-bold">Грешка при търсенето</h1>
          <p className="mt-4 text-lg text-gray-600">{message}</p>
          <p className="mt-4 text-lg text-gray-600">Опитайте с друга ключова дума.</p>
        </div>
      </div>
    </>
  )
}

export default ErrorResults
