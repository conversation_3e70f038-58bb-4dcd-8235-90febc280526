import React from 'react'

import { getBrands } from '@/src/app/(store)/[...dynamic]/api'
import { SearchPageData } from '@/src/app/(store)/catalogsearch/result/page'
import GtagTrack from '@components/molecules/GDPR/GtagTrack'
import Container from '@atoms/Container'
import Breadcrumb from '@components/molecules/Breadcrumb/Breadcrumb'
import FeaturedBrandsWidget from '@components/molecules/Widget/widgets/FeaturedBrands/FeaturedBrandsWidget'
import CategoryProductsList from '@features/catalog/CategoryProductsList'
import { CatalogTitle } from '@features/catalog/components/CatalogTitle'
import { ProductViewFragment } from '@lib/_generated/graphql_sdk'
import { PixelTrackSearchResults } from '@lib/fbp/components/PixelTrackSearchResults'
import { PixelTrackSearchPage } from '@lib/fbp/components/PixelTrackSearchPage'
import RelevaTrackPage from '@components/molecules/GDPR/RelevaTrackPage'

interface Props {
  data: SearchPageData
  q: string
}

export async function SearchCatalog({ data, q }: Props) {
  const brands = await getBrands({ featured: true })

  return (
    <>
      {/* Track search event with Facebook Pixel */}
      <PixelTrackSearchResults data={data.data?.products || []} query={q} />
      <PixelTrackSearchPage data={data.data?.products || []} query={q} />
      {/* Track search event with Google Analytics */}
      <GtagTrack
        action={{
          eventName: 'search',
          properties: {
            search_term: q,
          },
        }}
      />

      {/* Track view_item_list event for search results with Google Analytics */}
      <GtagTrack
        action={{
          eventName: 'view_item_list',
          properties: {
            item_list_id: `search_${q}`,
            item_list_name: `Search Results for "${q}"`,
            items:
              data.data?.products?.map((product, index) => ({
                item_id: product.sku,
                item_name: product.name,
                index,
                price: product.price?.price?.value,
                ...(product.price?.price && {
                  currency: product.price.price.currency,
                }),
              })) || [],
          },
        }}
      />
      <Container>
        <div className="my-5">
          <Breadcrumb
            // breadcrumbs={data.data.}
            breadcrumbs={[
              {
                label: 'Home',
                url: '/',
              },
              {
                label: `Резултати за "${q}"`,
                url: '#',
              },
            ]}
            pagination={data.state}
          />
        </div>
        <CatalogTitle title={data.title} />
        <CategoryProductsList state={data.state} products={data.data?.products as ProductViewFragment[]} />
      </Container>
      <div>
        <FeaturedBrandsWidget brands={brands} />
      </div>

      <RelevaTrackPage
        pageType="search"
        query={q}
        productIds={data.data?.products?.map((p) => p.sku) || []}
        filters={
          data.state.filters.applied.length > 0
            ? {
                operator: 'and',
                nested: data.state.filters.applied.map((filter) => ({
                  key: filter.attributeCode,
                  operator: 'eq',
                  value: filter.value,
                  action: 'include',
                })),
              }
            : undefined
        }
      />
    </>
  )
}

export default SearchCatalog
