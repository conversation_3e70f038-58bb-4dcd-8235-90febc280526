// app/api/protected-image/route.js
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  // Parse the query parameters from the request URL
  const { searchParams } = new URL(request.url)
  const urlParam = searchParams.get('url')
  if (!urlParam) {
    return NextResponse.json({ error: 'Missing url query parameter' }, { status: 400 })
  }

  // Prepare Basic authentication credentials
  const username = process.env.NEXT_IMAGE_USERNAME
  const password = process.env.NEXT_IMAGE_PASSWORD
  const auth = Buffer.from(`${username}:${password}`).toString('base64')

  try {
    // Fetch the image with the Authorization header
    const imageResponse = await fetch(urlParam, {
      headers: {
        Authorization: `Basic ${auth}`,
      },
    })

    if (!imageResponse.ok) {
      return NextResponse.json({ error: 'Failed to fetch image' }, { status: imageResponse.status })
    }

    // Get the content type from the fetched image response
    const contentType = imageResponse.headers.get('content-type') || 'image/jpeg'
    const imageBuffer = await imageResponse.arrayBuffer()

    return new NextResponse(Buffer.from(imageBuffer), {
      status: 200,
      headers: {
        'Content-Type': contentType,
      },
    })
  } catch (error) {
    console.error('Error fetching protected image:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
