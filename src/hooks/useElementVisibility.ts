import { useState, useEffect, useCallback } from 'react'

/**
 * Type definition for the callback function
 * @param isOutOfView - Whether the element is out of the viewport
 */
type VisibilityCallback = (isOutOfView: boolean) => void

/**
 * Custom hook that monitors whether a specific element is visible in the viewport
 *
 * @param elementId - The ID of the element to monitor
 * @param callback - Function to call when element visibility changes
 * @returns Whether the element is currently out of the visible area
 */
const useElementVisibility = (elementId: string, callback?: VisibilityCallback): void => {
  const [isOutOfView, setIsOutOfView] = useState<boolean>(false)

  // Create a memoized check visibility function
  const checkVisibility = useCallback(() => {
    const element = document.getElementById(elementId)

    if (!element) {
      return
    }

    // Get element position relative to the viewport
    const rect = element.getBoundingClientRect()

    // Check if the element is completely out of view
    const isCompletelyOutOfView =
      rect.bottom < 0 || // Above the viewport
      rect.top > window.innerHeight || // Below the viewport
      rect.right < 0 || // Left of the viewport
      rect.left > window.innerWidth // Right of the viewport

    // Update state if visibility changed
    if (isCompletelyOutOfView !== isOutOfView) {
      setIsOutOfView(isCompletelyOutOfView)

      // Call the callback with the new visibility state
      if (callback && typeof callback === 'function') {
        callback(isCompletelyOutOfView)
      }
    }
  }, [elementId, callback, isOutOfView])

  useEffect(() => {
    // Initial check
    checkVisibility()

    // Add scroll and resize event listeners
    window.addEventListener('scroll', checkVisibility, { passive: true })
    window.addEventListener('resize', checkVisibility)

    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('scroll', checkVisibility)
      window.removeEventListener('resize', checkVisibility)
    }
  }, [checkVisibility]) // Re-run effect if checkVisibility changes
}

export default useElementVisibility
