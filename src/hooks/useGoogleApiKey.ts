import { useState, useEffect } from 'react'

import { GraphQLBackend } from '@lib/api/graphql'
import { showErrorToast } from '@lib/utils/toaster'

const useGoogleApiKey = () => {
  const [value, setValue] = useState<string>()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>()

  const fetch = async () => {
    try {
      const response = await GraphQLBackend.GetGoogleApiKey()
      const { googleMaps: key } = response.getStaticContent.apiKeys
      if (key && key.length > 0) {
        setValue(key)
      } else {
        showErrorToast({
          title: 'Google API Key is missing',
          description: 'Please set it up in backend.',
        })
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err))
      return null
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetch()
  }, [])

  return {
    value,
    loading,
    error,
  }
}

export default useGoogleApiKey
