import { useCallback, useState } from 'react'

import { getErrorMessage } from '@lib/utils/error'

interface FormState {
  loading: boolean
  setLoading: (loading: boolean) => void
  error: string
  setError: (err: any) => void
  success: string
  setSuccess: (success: string) => void
  resetMessages: () => void
}

export function useFormState(): FormState {
  const [loading, setLoading] = useState(false)
  const [error, setErrorState] = useState('')
  const [success, setSuccess] = useState('')

  const setError = useCallback((err: any) => {
    setErrorState(getErrorMessage(err))
  }, [])

  const resetMessages = useCallback(() => {
    setError('')
    setSuccess('')
  }, [setError])

  return {
    loading,
    setLoading,
    error,
    setError,
    success,
    setSuccess,
    resetMessages,
  }
}
