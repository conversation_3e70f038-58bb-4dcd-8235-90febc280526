'use client'

import { useState, useEffect } from 'react'

export const breakpoints = [
  { name: 'xs', width: 320, description: 'Extra small devices (small phones)' },
  { name: 'sm', width: 640, description: 'Small devices (larger phones)' },
  { name: 'md', width: 768, description: 'Medium devices (tablets)' },
  { name: 'lg', width: 1024, description: 'Large devices (laptops/small desktops)' },
  { name: 'xl', width: 1280, description: 'Extra large devices (desktops)' },
  { name: '2xl', width: 1536, description: '2x extra large devices (large desktops)' },
  { name: '3xl', width: 1920, description: 'Full HD (1080p)' },
  { name: '4xl', width: 2560, description: '2.5K / WQHD' },
  { name: '5xl', width: 3440, description: 'Ultra wide' },
  { name: '6xl', width: 3840, description: '4K UHD' },
]

type BreakpointName = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl'

interface UseBreakpointResponse {
  ready: boolean
  breakpoint: BreakpointName | null
  innerWidth: number
  width: number
  isXS: boolean
  isSM: boolean
  isMD: boolean
  isLG: boolean
  isXL: boolean
  is2XL: boolean
  is3XL: boolean
  is4XL: boolean
  is5XL: boolean
  is6XL: boolean
  minBreakpoint: (minBreakpoint: BreakpointName) => boolean
  maxBreakpoint: (maxBreakpoint: BreakpointName) => boolean
}

function useBreakpoint(): UseBreakpointResponse {
  // Fallback for SSR
  const getDefaultBreakpoint = () => ({
    ready: false,
    breakpoint: null,
    width: breakpoints[0].width,
    innerWidth: 0,
    isXS: true,
    isSM: false,
    isMD: false,
    isLG: false,
    isXL: false,
    is2XL: false,
    is3XL: false,
    is4XL: false,
    is5XL: false,
    is6XL: false,
    minBreakpoint: () => false,
    maxBreakpoint: () => false,
  })

  const [breakpointState, setBreakpointState] = useState(() =>
    typeof window !== 'undefined' ? getBreakpointState(window.innerWidth) : getDefaultBreakpoint()
  )

  useEffect(() => {
    if (typeof window === 'undefined') return

    function handleResize() {
      const newState = getBreakpointState(window.innerWidth)
      if (newState.width !== breakpointState.width) {
        setBreakpointState(newState)
      }
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [breakpointState])

  return breakpointState
}

function getBreakpointState(width: number) {
  let breakpoint = breakpoints[0] // Default to the smallest breakpoint

  for (let i = breakpoints.length - 1; i >= 0; i--) {
    if (width >= breakpoints[i].width) {
      breakpoint = breakpoints[i]
      break
    }
  }

  // Function to check if current width is greater than or equal to the specified minimum breakpoint
  const minBreakpoint = (minBreakpoint: BreakpointName): boolean => {
    const minBreakpointWidth = breakpoints.find((bp) => bp.name === minBreakpoint)?.width || 0
    return width >= minBreakpointWidth
  }

  // Function to check if current width is greater than or equal to the specified minimum breakpoint
  const maxBreakpoint = (maxBreakpoint: BreakpointName): boolean => {
    const maxBreakpointWidth = breakpoints.find((bp) => bp.name === maxBreakpoint)?.width || 0
    return width <= maxBreakpointWidth
  }

  return {
    ready: true,
    breakpoint: breakpoint.name as BreakpointName,
    width: breakpoint.width,
    innerWidth: width,
    isXS: breakpoint.name === 'xs',
    isSM: breakpoint.name === 'sm',
    isMD: breakpoint.name === 'md',
    isLG: breakpoint.name === 'lg',
    isXL: breakpoint.name === 'xl',
    is2XL: breakpoint.name === '2xl',
    is3XL: breakpoint.name === '3xl',
    is4XL: breakpoint.name === '4xl',
    is5XL: breakpoint.name === '5xl',
    is6XL: breakpoint.name === '6xl',
    minBreakpoint,
    maxBreakpoint,
  }
}

export default useBreakpoint
