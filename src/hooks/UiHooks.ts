import React, { useCallback, useEffect, useState } from 'react'

export interface ToggleHook {
  isOpen: boolean
  open: () => void
  close: () => void
  toggle: () => void
}

export function useToggleHook(params?: { closeOnEscape: boolean; defaultIsOpen?: boolean }): ToggleHook {
  const [isOpen, setIsOpen] = useState(params?.defaultIsOpen ?? false)
  const toggle = useCallback(() => setIsOpen((prev) => !prev), [])
  const open = useCallback(() => setIsOpen(true), [])
  const close = useCallback(() => setIsOpen(false), [])

  useEffect(() => {
    if (params?.closeOnEscape) {
      const listener = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          close()
        }
      }

      document.addEventListener('keydown', listener)

      return () => {
        document.removeEventListener('keydown', listener)
      }
    }
  }, [params?.closeOnEscape, close])

  return { isOpen, open, close, toggle }
}

export const useOnClickOutside = (ref: React.RefObject<HTMLElement | null>, callback: () => void) => {
  const handleClick = useCallback(
    (e: any) => {
      if (ref.current && !ref.current.contains(e.target)) {
        callback()
      }
    },
    [ref, callback]
  )

  useEffect(() => {
    document.addEventListener('click', handleClick)

    return () => {
      document.removeEventListener('click', handleClick)
    }
  }, [handleClick])
}
