import { useState, useEffect, Dispatch, SetStateAction } from 'react'
import Cookies from 'universal-cookie'

const cookies = new Cookies()

function useCookie<T>(key: string, initialValue: T): [T, Dispatch<SetStateAction<T>>] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const cookieValue = cookies.get(key)
      return cookieValue !== undefined ? JSON.parse(cookieValue) : initialValue
    } catch (error) {
      console.error(`Error reading cookie for key "${key}":`, error)
      return initialValue
    }
  })

  const setValue: Dispatch<SetStateAction<T>> = (value) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value

      setStoredValue(valueToStore)

      cookies.set(key, JSON.stringify(valueToStore), {
        path: '/',
        maxAge: 7 * 24 * 60 * 60, // Expire in 7 days
      })
    } catch (error) {
      console.error(`Error setting cookie for key "${key}":`, error)
    }
  }

  useEffect(() => {
    const cookieValue = cookies.get(key)
    if (cookieValue !== undefined) {
      try {
        setStoredValue(JSON.parse(cookieValue))
      } catch (error) {
        console.error(`Error parsing cookie for key "${key}":`, error)
      }
    }
  }, [key])

  return [storedValue, setValue]
}

export default useCookie
