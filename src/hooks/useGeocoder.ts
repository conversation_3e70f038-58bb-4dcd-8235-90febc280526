import { useMapsLibrary } from '@vis.gl/react-google-maps'
import { useState, useEffect, useCallback } from 'react'

// Define our own types instead of relying on the global google namespace
interface AddressComponent {
  long_name: string
  short_name: string
  types: string[]
}

interface GeocoderResult {
  lat: number
  lng: number
  formattedAddress: string
  placeId: string
  locationType: string
  addressComponents: AddressComponent[]
  isCity: boolean
  isCountry: boolean
}

interface UseGeocoderReturn {
  isLoaded: boolean
  error: string | null
  getCoordinates: (address: string) => Promise<GeocoderResult>
}

/**
 * Custom hook for geocoding addresses into coordinates
 *
 * @returns An object containing the geocoding functionality
 */
const useGeocoder = (): UseGeocoderReturn => {
  const [isLoaded, setIsLoaded] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [geocoder, setGeocoder] = useState<any | null>(null)

  // Get the geocoding library
  const geocodingLibrary = useMapsLibrary('geocoding')

  // Initialize the geocoder when the library is loaded
  useEffect(() => {
    if (!geocodingLibrary) return

    try {
      setGeocoder(new geocodingLibrary.Geocoder())
      setIsLoaded(true)
      setError(null)
    } catch (err) {
      setError('Failed to initialize geocoder')
      console.error('Geocoder initialization failed:', err)
    }
  }, [geocodingLibrary])

  /**
   * Geocodes an address string into coordinates
   *
   * @param address - The address to geocode (e.g., "Paris, France")
   * @returns A Promise resolving to the geocoding result with coordinates
   */
  const getCoordinates = useCallback(
    async (address: string): Promise<GeocoderResult> => {
      if (!geocoder) {
        throw new Error('Geocoder not initialized')
      }

      if (!address || typeof address !== 'string' || !address.trim()) {
        throw new Error('Invalid address provided')
      }

      try {
        const response = await geocoder.geocode({ address })

        if (response.results && response.results.length > 0) {
          const location = response.results[0].geometry.location

          return {
            lat: location.lat(),
            lng: location.lng(),
            formattedAddress: response.results[0].formatted_address,
            placeId: response.results[0].place_id,
            // Adding additional useful information
            locationType: response.results[0].geometry.location_type,
            addressComponents: response.results[0].address_components,
            // Determine if this is a city or country for zoom level calculation
            isCity: response.results[0].types.includes('locality'),
            isCountry: response.results[0].types.includes('country'),
          }
        } else {
          throw new Error('No results found')
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Geocoding failed'
        setError(errorMessage)
        throw err
      }
    },
    [geocoder]
  )

  return {
    isLoaded,
    error,
    getCoordinates,
  }
}

export default useGeocoder
