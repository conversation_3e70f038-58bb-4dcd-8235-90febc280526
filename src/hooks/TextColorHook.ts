/**
 * Converts a HEX color to RGB.
 */
function hexToRgb(hex: string): { r: number; g: number; b: number } {
  let r = 0,
    g = 0,
    b = 0
  // 3 digits
  if (hex.length === 4) {
    r = parseInt(hex[1] + hex[1], 16)
    g = parseInt(hex[2] + hex[2], 16)
    b = parseInt(hex[3] + hex[3], 16)
  }
  // 6 digits
  else if (hex.length === 7) {
    r = parseInt(hex[1] + hex[2], 16)
    g = parseInt(hex[3] + hex[4], 16)
    b = parseInt(hex[5] + hex[6], 16)
  }
  return { r, g, b }
}

/**
 * Determines if the text color should be white or black based on the luminance of the background color.
 */
export function useTextColor(hexColor: string): 'text-black' | 'text-white' {
  const { r, g, b } = hexToRgb(hexColor)
  // Calculate the luminance of the color
  const yiq = (r * 299 + g * 587 + b * 114) / 1000
  return yiq >= 128 ? 'text-black' : 'text-white'
}
