import { useState, useEffect } from 'react'

import { GraphQLBackend } from '@lib/api/graphql'
import { showErrorToast } from '@lib/utils/toaster'

const useGoogleClientId = () => {
  const [value, setValue] = useState<string>()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>()

  const fetch = async () => {
    try {
      const response = await GraphQLBackend.GetGoogleClientId()
      const { googleLoginClientId: key } = response.getStaticContent.apiKeys
      if (key && key.length > 0) {
        setValue(key)
      } else {
        showErrorToast({
          title: 'Google Client ID is missing',
          description: 'Please set it up in backend.',
        })
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err))
      return null
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetch()
  }, [])

  return {
    value,
    loading,
    error,
  }
}

export default useGoogleClientId
