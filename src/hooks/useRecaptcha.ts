// hooks/useRecaptcha.ts
import { useState, useCallback } from 'react'

interface UseRecaptchaReturn {
  executeRecaptcha: (action?: string) => Promise<string>
  isLoading: boolean
  error: Error | null
}

const useRecaptcha = (siteKey: string): UseRecaptchaReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<Error | null>(null)
  const [isScriptLoaded, setIsScriptLoaded] = useState<boolean>(false)

  const loadRecaptchaScript = useCallback((): Promise<void> => {
    return new Promise<void>((resolve, reject) => {
      // Check if reCAPTCHA is already loaded
      if (
        typeof window !== 'undefined' &&
        'grecaptcha' in window &&
        window.grecaptcha &&
        typeof window.grecaptcha.execute === 'function'
      ) {
        setIsScriptLoaded(true)
        resolve()
        return
      }

      // If we're already trying to load the script, wait
      const existingScript = document.querySelector(`script[src*="recaptcha/api.js"]`)
      if (existingScript) {
        const checkRecaptchaLoaded = setInterval(() => {
          if (
            typeof window !== 'undefined' &&
            'grecaptcha' in window &&
            window.grecaptcha &&
            typeof window.grecaptcha.execute === 'function'
          ) {
            clearInterval(checkRecaptchaLoaded)
            setIsScriptLoaded(true)
            resolve()
          }
        }, 100)

        return
      }

      // Create script element
      const script = document.createElement('script')
      script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`
      script.async = true
      script.defer = true

      script.onload = () => {
        // Wait for grecaptcha to be ready
        const readyCheck = setInterval(() => {
          if (
            typeof window !== 'undefined' &&
            'grecaptcha' in window &&
            window.grecaptcha &&
            typeof window.grecaptcha.execute === 'function'
          ) {
            clearInterval(readyCheck)
            setIsScriptLoaded(true)
            resolve()
          }
        }, 100)

        // Set a timeout in case it never loads
        setTimeout(() => {
          clearInterval(readyCheck)
          const loadError = new Error('reCAPTCHA failed to initialize')
          setError(loadError)
          reject(loadError)
        }, 5000)
      }

      script.onerror = () => {
        const loadError = new Error('Failed to load reCAPTCHA script')
        setError(loadError)
        reject(loadError)
      }

      document.head.appendChild(script)
    })
  }, [siteKey])

  const executeRecaptcha = useCallback(
    async (action: string = 'submit'): Promise<string> => {
      setIsLoading(true)
      setError(null)

      try {
        // Load the script if it hasn't been loaded yet
        if (!isScriptLoaded) {
          await loadRecaptchaScript()
        }

        // Execute reCAPTCHA
        const token = await window.grecaptcha.execute(siteKey, { action })
        return token
      } catch (err) {
        setError(err instanceof Error ? err : new Error(String(err)))
        throw err
      } finally {
        setIsLoading(false)
      }
    },
    [siteKey, isScriptLoaded, loadRecaptchaScript]
  )

  return {
    executeRecaptcha,
    isLoading,
    error,
  }
}

export default useRecaptcha

// Make sure to add this type declaration in a .d.ts file
// For example, in types/recaptcha.d.ts:
/*
interface Window {
  grecaptcha?: {
    ready?: (callback: () => void) => void;
    execute?: (siteKey: string, options: { action: string }) => Promise<string>;
  };
}
*/
