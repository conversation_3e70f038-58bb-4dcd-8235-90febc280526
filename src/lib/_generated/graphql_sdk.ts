import { GraphQLClient, RequestOptions } from 'graphql-request'
import gql from 'graphql-tag'
export type Maybe<T> = T | null
export type InputMaybe<T> = Maybe<T>
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] }
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> }
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> }
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never }
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never }
type GraphQLClientRequestHeaders = RequestOptions['requestHeaders']
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string }
  String: { input: string; output: string }
  Boolean: { input: boolean; output: boolean }
  Int: { input: number; output: number }
  Float: { input: number; output: number }
}

export type ActionResult = {
  __typename?: 'ActionResult'
  code: ActionResultCode
  message?: Maybe<Scalars['String']['output']>
}

export enum ActionResultCode {
  Error = 'ERROR',
  Queued = 'QUEUED',
  Success = 'SUCCESS',
  Waiting = 'WAITING',
}

export type ApiKeys = {
  __typename?: 'ApiKeys'
  facebookLoginAppId: Scalars['String']['output']
  googleLoginClientId: Scalars['String']['output']
  googleMaps: Scalars['String']['output']
  googleRecaptchaKey: Scalars['String']['output']
}

export type ApplicationLinks = {
  __typename?: 'ApplicationLinks'
  android?: Maybe<Scalars['String']['output']>
  ios?: Maybe<Scalars['String']['output']>
}

export type AppliedFilter = {
  __typename?: 'AppliedFilter'
  attributeCode: Scalars['String']['output']
  label: Scalars['String']['output']
  requestVar: Scalars['String']['output']
  value: Scalars['String']['output']
}

export type Article = {
  __typename?: 'Article'
  description: Scalars['String']['output']
  image: Image
  link: Link
  title: Scalars['String']['output']
}

export type AttributeOption = {
  __typename?: 'AttributeOption'
  label: Scalars['String']['output']
  order: Scalars['Int']['output']
  value: Scalars['String']['output']
}

export type AuthToken = {
  __typename?: 'AuthToken'
  token?: Maybe<Scalars['String']['output']>
}

export enum AvailabilityStatus {
  Available = 'AVAILABLE',
  IndividualOrder = 'INDIVIDUAL_ORDER',
  LimitedAvailability = 'LIMITED_AVAILABILITY',
  Unavailable = 'UNAVAILABLE',
}

export type AvailableFilter = {
  __typename?: 'AvailableFilter'
  attributeCode: Scalars['String']['output']
  label: Scalars['String']['output']
  options: Array<AttributeOption>
  position: Scalars['Int']['output']
  requestVar: Scalars['String']['output']
  type: FilterRenderType
}

export type AvailableShippingMethod = {
  __typename?: 'AvailableShippingMethod'
  errorMsg: Scalars['String']['output']
  method: ShippingMethod
  price: Price
}

export type BnpCustomerDataInput = {
  egn?: InputMaybe<Scalars['String']['input']>
  email: Scalars['String']['input']
  firstName: Scalars['String']['input']
  lastName: Scalars['String']['input']
  phone: Scalars['String']['input']
}

export type BnpGoodCategory = {
  __typename?: 'BNPGoodCategory'
  id: Scalars['ID']['output']
  name: Scalars['String']['output']
}

export type BnpGoodType = {
  __typename?: 'BNPGoodType'
  categoryId: Scalars['ID']['output']
  id: Scalars['ID']['output']
  name: Scalars['String']['output']
}

export type BnpPaymentInput = {
  customerData: BnpCustomerDataInput
  downPayment: Scalars['Float']['input']
  pricingVariantId: Scalars['Int']['input']
}

export type BnpPricingScheme = {
  __typename?: 'BNPPricingScheme'
  id: Scalars['String']['output']
  name: Scalars['String']['output']
}

export type BnpVariantGroup = {
  __typename?: 'BNPVariantGroup'
  schemeId: Scalars['String']['output']
  variants: Array<PricingVariant>
}

export type BlogCategory = {
  __typename?: 'BlogCategory'
  children: Array<BlogCategory>
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
  posts: Array<BlogPost>
  urlKey: Scalars['String']['output']
}

export type BlogPost = {
  __typename?: 'BlogPost'
  content: Scalars['String']['output']
  mainImageUrl: Scalars['String']['output']
  previewImageUrl: Scalars['String']['output']
  publishedAt: Scalars['String']['output']
  summary: Scalars['String']['output']
  title: Scalars['String']['output']
  urlKey: Scalars['String']['output']
}

export type Brand = {
  __typename?: 'Brand'
  image: Image
  name: Scalars['String']['output']
  url: Link
}

export type Breadcrumb = {
  __typename?: 'Breadcrumb'
  children?: Maybe<Array<Breadcrumb>>
  label: Scalars['String']['output']
  siblings?: Maybe<Array<Breadcrumb>>
  url: Scalars['String']['output']
}

export type BundleProduct = {
  __typename?: 'BundleProduct'
  brand?: Maybe<Brand>
  bundled: Array<SimpleProduct>
  description: Scalars['String']['output']
  gallery: Array<GalleryImage>
  id: Scalars['ID']['output']
  image: Image
  labels: MagentoLabels
  name: Scalars['String']['output']
  price: ProductPrice
  shortDescription: Scalars['String']['output']
  sku: Scalars['String']['output']
  skuAvailability: Array<StoreAvailabilityItem>
  stock?: Maybe<ProductStock>
  urlKey: Scalars['String']['output']
  videos?: Maybe<Array<ProductVideo>>
}

export type CmsBlock = {
  __typename?: 'CMSBlock'
  content: Scalars['String']['output']
  identifier: Scalars['String']['output']
  title: Scalars['String']['output']
}

export type CmsPage = {
  __typename?: 'CMSPage'
  content: Scalars['String']['output']
  identifier: Scalars['String']['output']
  links: Array<Link>
  title: Scalars['String']['output']
}

export type CacheConfig = {
  customKey?: InputMaybe<Scalars['String']['input']>
  extraKeys?: InputMaybe<Array<Scalars['String']['input']>>
  ttlMin?: InputMaybe<Scalars['Int']['input']>
  useCache?: InputMaybe<Scalars['Boolean']['input']>
}

export type CarouselSlide = {
  __typename?: 'CarouselSlide'
  brand?: Maybe<Brand>
  description: Scalars['String']['output']
  features?: Maybe<Array<Image>>
  image: Image
  link?: Maybe<Link>
  price?: Maybe<Price>
  priceLabels?: Maybe<MagentoLabels>
  title: Scalars['String']['output']
}

export type CarouselWidget = {
  __typename?: 'CarouselWidget'
  identifier: Scalars['String']['output']
  slides: Array<CarouselSlide>
}

export type CartShipping = {
  __typename?: 'CartShipping'
  address?: Maybe<ShippingAddress>
  availableIn: Array<PraktisStore>
  availableMethods: Array<ShippingMethodType>
  freeShippingAfter?: Maybe<Scalars['Int']['output']>
  hasFreeShipping: Scalars['Boolean']['output']
  minAmountForFreeShippingMessage?: Maybe<Scalars['Int']['output']>
  selectedMethod: Scalars['String']['output']
}

export type CartTotal = {
  __typename?: 'CartTotal'
  amount: Price
  code: CartTotalCode
  label: Scalars['String']['output']
  order: Scalars['Int']['output']
}

export enum CartTotalCode {
  DiscountTotal = 'DISCOUNT_TOTAL',
  GrantTotal = 'GRANT_TOTAL',
  Other = 'OTHER',
  ShippingTotal = 'SHIPPING_TOTAL',
  SubTotal = 'SUB_TOTAL',
}

export type CatalogCategory = Category | SearchCategory | SplashPage

export enum CatalogLayout {
  LandingPage = 'LANDING_PAGE',
  ProductCatalog = 'PRODUCT_CATALOG',
}

export type CatalogPage = {
  __typename?: 'CatalogPage'
  category: CatalogCategory
  layout: CatalogLayout
  products: Array<Product>
  state: CatalogState
}

export type CatalogState = {
  __typename?: 'CatalogState'
  availableSorts: Array<SortField>
  filters: Filters
  pager: Pager
  sort: CollectionSort
}

export type Category = {
  __typename?: 'Category'
  banner?: Maybe<CategoryBanner>
  children?: Maybe<Array<Category>>
  description?: Maybe<Scalars['String']['output']>
  icon?: Maybe<Image>
  id: Scalars['ID']['output']
  image?: Maybe<Image>
  name: Scalars['String']['output']
  url: Scalars['String']['output']
  widgets?: Maybe<Array<CategoryWidget>>
}

export type CategoryBanner = {
  __typename?: 'CategoryBanner'
  image: Image
  url: Scalars['String']['output']
}

export type CategoryLinkWidget = {
  __typename?: 'CategoryLinkWidget'
  image: Image
  title: Scalars['String']['output']
  url: Link
}

export type CategoryTile = {
  __typename?: 'CategoryTile'
  bgColor: Scalars['String']['output']
  categories: Array<Link>
  image: Image
  textColor: Scalars['String']['output']
  title: Scalars['String']['output']
}

export type CategoryWidget = CategoryLinkWidget

export type ClearCacheConfig = {
  clear?: InputMaybe<Scalars['Boolean']['input']>
  extraKeys?: InputMaybe<Array<Scalars['String']['input']>>
}

export type ClientInput = {
  email: Scalars['String']['input']
  firstName: Scalars['String']['input']
  invoice?: InputMaybe<InvoiceInput>
  lastName: Scalars['String']['input']
  password: Scalars['String']['input']
  phone: Scalars['String']['input']
  registerOnOrder: Scalars['Boolean']['input']
}

export type CollectionSort = {
  __typename?: 'CollectionSort'
  dir: SortDirection
  value: Scalars['String']['output']
}

export type CompanyInvoice = {
  __typename?: 'CompanyInvoice'
  eik: Scalars['String']['output']
  mol: Scalars['String']['output']
  name: Scalars['String']['output']
  vat: Scalars['String']['output']
}

export type CompanyInvoiceInput = {
  eik: Scalars['String']['input']
  mol: Scalars['String']['input']
  name: Scalars['String']['input']
  vat: Scalars['String']['input']
}

export enum Constraint {
  Email = 'EMAIL',
  Max = 'MAX',
  Min = 'MIN',
  Numeric = 'NUMERIC',
  OneOf = 'ONE_OF',
  Password = 'PASSWORD',
  Required = 'REQUIRED',
}

export type ContactInfo = {
  __typename?: 'ContactInfo'
  phone?: Maybe<Scalars['String']['output']>
}

export type ContactInput = {
  comment: Scalars['String']['input']
  email: Scalars['String']['input']
  name: Scalars['String']['input']
  telephone: Scalars['String']['input']
}

export type CookieGroup = {
  __typename?: 'CookieGroup'
  content: Scalars['String']['output']
  cookiePatterns: Array<Scalars['String']['output']>
  grants: Array<GdprGrants>
  id: Scalars['String']['output']
  title: Scalars['String']['output']
  vendors: GdprCookieVendors
}

export enum CookieVendorKey {
  Clarity = 'clarity',
  Facebook = 'facebook',
  Google = 'google',
  Store = 'store',
}

export type Customer = {
  __typename?: 'Customer'
  addresses: Array<CustomerAddress>
  defaultBillingAddressID?: Maybe<Scalars['ID']['output']>
  defaultShippingAddressID?: Maybe<Scalars['ID']['output']>
  email: Scalars['String']['output']
  firstName: Scalars['String']['output']
  id: Scalars['ID']['output']
  isSubscribed: Scalars['Boolean']['output']
  lastName: Scalars['String']['output']
  orders: Array<StoreOrder>
  social: SocialLogins
  token: Scalars['String']['output']
  wishlist: CustomerWishlist
}

export type CustomerAddress = {
  __typename?: 'CustomerAddress'
  city: Scalars['String']['output']
  cityID: Scalars['String']['output']
  companyName?: Maybe<Scalars['String']['output']>
  country: Scalars['String']['output']
  firstName: Scalars['String']['output']
  id: Scalars['ID']['output']
  lastName: Scalars['String']['output']
  phone: Scalars['String']['output']
  postCode: Scalars['String']['output']
  street: Scalars['String']['output']
}

export type CustomerAddressInput = {
  city: Scalars['String']['input']
  cityID: Scalars['String']['input']
  companyName?: InputMaybe<Scalars['String']['input']>
  firstName: Scalars['String']['input']
  isDefaultBilling?: InputMaybe<Scalars['Boolean']['input']>
  isDefaultShipping?: InputMaybe<Scalars['Boolean']['input']>
  lastName: Scalars['String']['input']
  phone: Scalars['String']['input']
  postCode: Scalars['String']['input']
  street: Scalars['String']['input']
}

export type CustomerInvoice = {
  __typename?: 'CustomerInvoice'
  address: Scalars['String']['output']
  city: Scalars['String']['output']
  company?: Maybe<CompanyInvoice>
  id: Scalars['ID']['output']
  individual?: Maybe<IndividualInvoice>
  type: InvoiceType
}

export type CustomerRegistrationData = {
  email: Scalars['String']['input']
  firstname: Scalars['String']['input']
  invoice?: InputMaybe<InvoiceInput>
  lastname: Scalars['String']['input']
  newsletterSubscribe: Scalars['Boolean']['input']
  password: Scalars['String']['input']
  phone: Scalars['String']['input']
}

export type CustomerUpdateInput = {
  defaultBillingAddressID?: InputMaybe<Scalars['ID']['input']>
  defaultShippingAddressID?: InputMaybe<Scalars['ID']['input']>
  email?: InputMaybe<Scalars['String']['input']>
  firstName?: InputMaybe<Scalars['String']['input']>
  lastName?: InputMaybe<Scalars['String']['input']>
  newsletterSubscribe?: InputMaybe<Scalars['Boolean']['input']>
}

export type CustomerWishlist = {
  __typename?: 'CustomerWishlist'
  products: Array<Product>
  skus: Array<Scalars['String']['output']>
}

export type DescriptionArea = {
  __typename?: 'DescriptionArea'
  backgroundImage: Scalars['String']['output']
  content: Scalars['String']['output']
  formID?: Maybe<Scalars['String']['output']>
  title: Scalars['String']['output']
}

export type DoubleImageTile = {
  __typename?: 'DoubleImageTile'
  contentOne: TileContent
  contentTwo: TileContent
  imageOne: Image
  imageOnePosition: DoubleImageTilePosition
  imageTwo: Image
  imageTwoPosition: DoubleImageTilePosition
}

export enum DoubleImageTilePosition {
  Left = 'LEFT',
  Right = 'RIGHT',
}

export type EcontCity = {
  __typename?: 'EcontCity'
  country: EcontCountry
  id: Scalars['ID']['output']
  name: Scalars['String']['output']
  nameEn: Scalars['String']['output']
  postCode: Scalars['String']['output']
  regionCode: Scalars['String']['output']
  regionName: Scalars['String']['output']
  regionNameEn: Scalars['String']['output']
  type: Scalars['String']['output']
}

export type EcontCountry = {
  __typename?: 'EcontCountry'
  code2: Scalars['String']['output']
  code3: Scalars['String']['output']
  id: Scalars['ID']['output']
  isEU: Scalars['Boolean']['output']
  name: Scalars['String']['output']
  nameEn: Scalars['String']['output']
}

export type EcontOffice = {
  __typename?: 'EcontOffice'
  adderess: EcontOfficeAddress
  code: Scalars['String']['output']
  id: Scalars['ID']['output']
  isAPS: Scalars['Boolean']['output']
  isMPS: Scalars['Boolean']['output']
  name: Scalars['String']['output']
  nameEn: Scalars['String']['output']
  phones: Array<Scalars['String']['output']>
}

export type EcontOfficeAddress = {
  __typename?: 'EcontOfficeAddress'
  city: EcontCity
  fullAddress: Scalars['String']['output']
  fullAddressEn: Scalars['String']['output']
  location: EcontOfficeLocation
}

export type EcontOfficeLocation = {
  __typename?: 'EcontOfficeLocation'
  latitude: Scalars['Float']['output']
  longitude: Scalars['Float']['output']
}

export type EditExtraValidations = {
  afterSave?: InputMaybe<Array<Scalars['String']['input']>>
  beforeSave?: InputMaybe<Array<Scalars['String']['input']>>
}

export type EnergyLabel = {
  __typename?: 'EnergyLabel'
  image: Image
  infoUrl?: Maybe<Scalars['String']['output']>
  labelUrl?: Maybe<Scalars['String']['output']>
}

export enum EntityFilterSort {
  Asc = 'ASC',
  Desc = 'DESC',
}

export type EntityToModel = {
  enable?: InputMaybe<Scalars['Boolean']['input']>
  excludeFields?: InputMaybe<Array<Scalars['String']['input']>>
}

export type ErrorData = {
  __typename?: 'ErrorData'
  code: Scalars['Int']['output']
  message: Scalars['String']['output']
}

export type Filter = {
  Condition: Scalars['String']['input']
  value?: InputMaybe<Scalars['String']['input']>
}

export enum FilterRenderType {
  List = 'LIST',
  Slider = 'SLIDER',
}

export type Filters = {
  __typename?: 'Filters'
  applied: Array<AppliedFilter>
  available: Array<AvailableFilter>
}

export type Footer = {
  __typename?: 'Footer'
  appLinks?: Maybe<ApplicationLinks>
  columns?: Maybe<Array<Maybe<FooterColumn>>>
  contacts?: Maybe<StoreContacts>
  social?: Maybe<SocialLinks>
}

export type FooterColumn = {
  __typename?: 'FooterColumn'
  links?: Maybe<Array<Maybe<Link>>>
  title?: Maybe<Scalars['String']['output']>
}

export type FormData = {
  __typename?: 'FormData'
  formId: Scalars['String']['output']
  text: Scalars['String']['output']
  title: Scalars['String']['output']
}

export type GdprConfig = {
  __typename?: 'GDPRConfig'
  clarityId: Scalars['String']['output']
  extraGTagsIds: Array<Scalars['String']['output']>
  gtagId: Scalars['String']['output']
  modal: GdprModalConfig
  pixelId: Scalars['String']['output']
  rootId: Scalars['String']['output']
}

export type GdprCookieDetails = {
  __typename?: 'GDPRCookieDetails'
  description: Scalars['String']['output']
  expiration: Scalars['String']['output']
  id: Scalars['String']['output']
  name: Scalars['String']['output']
  type: Scalars['String']['output']
}

export type GdprCookieVendorConfig = {
  __typename?: 'GDPRCookieVendorConfig'
  cookieCount: Scalars['Int']['output']
  cookieDetails: Array<Maybe<GdprCookieDetails>>
  id: Scalars['String']['output']
  name: Scalars['String']['output']
  url: Scalars['String']['output']
}

export type GdprCookieVendors = {
  __typename?: 'GDPRCookieVendors'
  config: Array<GdprCookieVendorConfig>
  keys: Array<CookieVendorKey>
}

export enum GdprGrants {
  AdPersonalization = 'ad_personalization',
  AdStorage = 'ad_storage',
  AdUserData = 'ad_user_data',
  AnalyticsStorage = 'analytics_storage',
}

export type GdprModalConfig = {
  __typename?: 'GDPRModalConfig'
  content: Scalars['String']['output']
  cookieGroups: Array<CookieGroup>
  title: Scalars['String']['output']
}

export type GalleryImage = {
  __typename?: 'GalleryImage'
  image: Image
  position: Scalars['Int']['output']
}

export type HeaderData = {
  __typename?: 'HeaderData'
  scripts: Array<Scalars['String']['output']>
}

export type HomePage = {
  __typename?: 'HomePage'
  heroSlider: CarouselWidget
  widgets: Array<Widget>
}

export type HtmlWidget = {
  __typename?: 'HtmlWidget'
  html?: Maybe<Scalars['String']['output']>
}

export type Image = {
  __typename?: 'Image'
  alt?: Maybe<Scalars['String']['output']>
  mobileSrc?: Maybe<Scalars['String']['output']>
  src: Scalars['String']['output']
  title?: Maybe<Scalars['String']['output']>
}

export type ImageTile = {
  __typename?: 'ImageTile'
  bgColor: Scalars['String']['output']
  content: TileContent
  image: Image
  position: TileContentPosition
}

export type IndividualInvoice = {
  __typename?: 'IndividualInvoice'
  egn: Scalars['String']['output']
  name: Scalars['String']['output']
  vat: Scalars['String']['output']
}

export type IndividualInvoiceInput = {
  egn: Scalars['String']['input']
  name: Scalars['String']['input']
  vat: Scalars['String']['input']
}

export enum InquiryType {
  Company = 'Company',
  Personal = 'Personal',
}

export type InvoiceInput = {
  address: Scalars['String']['input']
  city: Scalars['String']['input']
  company?: InputMaybe<CompanyInvoiceInput>
  individual?: InputMaybe<IndividualInvoiceInput>
  type: InvoiceType
}

export enum InvoiceType {
  Company = 'company',
  Personal = 'personal',
}

export type KeyVal = {
  key: Scalars['String']['input']
  value: Scalars['String']['input']
}

export type Link = {
  __typename?: 'Link'
  href: Scalars['String']['output']
  text: Scalars['String']['output']
  title?: Maybe<Scalars['String']['output']>
}

export type LoanCalculation = {
  __typename?: 'LoanCalculation'
  apr: Scalars['String']['output']
  correctDownpaymentAmount: Scalars['String']['output']
  installmentAmount: Scalars['String']['output']
  maturity: Scalars['String']['output']
  nir: Scalars['String']['output']
  pricingSchemeId: Scalars['String']['output']
  pricingSchemeName: Scalars['String']['output']
  pricingVariantId: Scalars['String']['output']
  processingFeeAmount: Scalars['String']['output']
  totalRepaymentAmount: Scalars['String']['output']
}

export enum LoginProvider {
  Facebook = 'Facebook',
  Google = 'Google',
}

export type MagentoBlog = {
  __typename?: 'MagentoBlog'
  currentPage: Scalars['Int']['output']
  posts: Array<BlogPost>
  totalPages: Scalars['Int']['output']
}

export type MagentoLabels = {
  __typename?: 'MagentoLabels'
  buyCheap?: Maybe<Scalars['Boolean']['output']>
  freeDelivery?: Maybe<Scalars['Boolean']['output']>
  freeDeliveryUntil?: Maybe<Scalars['String']['output']>
  fromBrochure?: Maybe<Scalars['Boolean']['output']>
  other?: Maybe<Array<Scalars['String']['output']>>
  warrantyMonths: Scalars['Int']['output']
}

export type MapLocation = {
  __typename?: 'MapLocation'
  lat: Scalars['Float']['output']
  lng: Scalars['Float']['output']
}

export type MapValue = {
  __typename?: 'MapValue'
  key: Scalars['String']['output']
  value: Scalars['String']['output']
}

export type Menu = {
  __typename?: 'Menu'
  categories: Array<MenuItem>
}

export type MenuItem = {
  __typename?: 'MenuItem'
  children?: Maybe<Array<MenuItem>>
  id: Scalars['ID']['output']
  name: Scalars['String']['output']
  sideSection?: Maybe<MenuSideSection>
  thumbnail: Scalars['String']['output']
  url: Scalars['String']['output']
}

export type MenuSideSection = {
  __typename?: 'MenuSideSection'
  image: Scalars['String']['output']
  rawHtml: Scalars['String']['output']
  url: Scalars['String']['output']
}

export type Mutation = {
  __typename?: 'Mutation'
  cartApplyCoupon: StoreCart
  cartAvailablePaymentMethods: Array<PaymentMethod>
  cartAvailableShippingMethods: Array<AvailableShippingMethod>
  cartCalculateTotals: StoreCart
  cartItemAdd: StoreCart
  cartItemRemove: StoreCart
  cartItemUpdate: StoreCart
  cartSaveBNPPayment: StoreCart
  cartSaveClient: StoreCart
  cartSavePayment: StoreCart
  cartSaveShipping: StoreCart
  customerAddressAdd: Array<CustomerAddress>
  customerAddressRemove: Array<CustomerAddress>
  customerAddressUpdate: Array<CustomerAddress>
  customerLogin: Customer
  customerLoginProvider: SocialLoginResponse
  customerLoginRefresh: Customer
  customerLogout: Scalars['Boolean']['output']
  customerPasswordForgot: Scalars['Boolean']['output']
  customerPasswordReset: Scalars['Boolean']['output']
  customerRegister: RegistrationResponse
  customerUpdateInfo: Customer
  customerUpdatePassword: Customer
  customerWishlistAdd: CustomerWishlist
  customerWishlistRemove: CustomerWishlist
  health?: Maybe<Scalars['String']['output']>
  placeOrder?: Maybe<PlaceOrderResponse>
  storeNewsletterSubscribe: Scalars['Boolean']['output']
  storeNewsletterUnsubscribe: Scalars['Boolean']['output']
  storeSendContactMessage: Scalars['Boolean']['output']
  storeSendInquiry: Scalars['Boolean']['output']
  submitBNPApplication: Scalars['Boolean']['output']
}

export type MutationCartApplyCouponArgs = {
  cartToken: Scalars['String']['input']
  couponCode: Scalars['String']['input']
}

export type MutationCartAvailablePaymentMethodsArgs = {
  cartToken: Scalars['String']['input']
}

export type MutationCartAvailableShippingMethodsArgs = {
  cartToken: Scalars['String']['input']
  data: ShippingInput
}

export type MutationCartCalculateTotalsArgs = {
  cartToken: Scalars['String']['input']
}

export type MutationCartItemAddArgs = {
  cartToken: Scalars['String']['input']
  item: NewCartItem
}

export type MutationCartItemRemoveArgs = {
  cartToken: Scalars['String']['input']
  sku: Scalars['String']['input']
}

export type MutationCartItemUpdateArgs = {
  cartToken: Scalars['String']['input']
  item: NewCartItem
}

export type MutationCartSaveBnpPaymentArgs = {
  cartToken: Scalars['String']['input']
  paymentData: BnpPaymentInput
}

export type MutationCartSaveClientArgs = {
  cartToken: Scalars['String']['input']
  data: ClientInput
}

export type MutationCartSavePaymentArgs = {
  cartToken: Scalars['String']['input']
  paymentMethodCode: Scalars['String']['input']
}

export type MutationCartSaveShippingArgs = {
  cartToken: Scalars['String']['input']
  data: ShippingInput
  shippingMethodCode: Scalars['String']['input']
}

export type MutationCustomerAddressAddArgs = {
  data: CustomerAddressInput
}

export type MutationCustomerAddressRemoveArgs = {
  addressID: Scalars['ID']['input']
}

export type MutationCustomerAddressUpdateArgs = {
  addressID: Scalars['ID']['input']
  data: CustomerAddressInput
}

export type MutationCustomerLoginArgs = {
  email: Scalars['String']['input']
  password: Scalars['String']['input']
}

export type MutationCustomerLoginProviderArgs = {
  providerToken: Scalars['String']['input']
  type: LoginProvider
}

export type MutationCustomerPasswordForgotArgs = {
  email: Scalars['String']['input']
}

export type MutationCustomerPasswordResetArgs = {
  customerId: Scalars['ID']['input']
  password: Scalars['String']['input']
  resetToken: Scalars['String']['input']
}

export type MutationCustomerRegisterArgs = {
  data: CustomerRegistrationData
}

export type MutationCustomerUpdateInfoArgs = {
  data?: InputMaybe<CustomerUpdateInput>
}

export type MutationCustomerUpdatePasswordArgs = {
  newPassword: Scalars['String']['input']
  oldPassword: Scalars['String']['input']
}

export type MutationCustomerWishlistAddArgs = {
  sku: Scalars['String']['input']
}

export type MutationCustomerWishlistRemoveArgs = {
  sku: Scalars['String']['input']
}

export type MutationPlaceOrderArgs = {
  cartToken: Scalars['String']['input']
  data: NewOrderInput
}

export type MutationStoreNewsletterSubscribeArgs = {
  email: Scalars['String']['input']
}

export type MutationStoreNewsletterUnsubscribeArgs = {
  code: Scalars['String']['input']
  id: Scalars['ID']['input']
}

export type MutationStoreSendContactMessageArgs = {
  input: ContactInput
}

export type MutationStoreSendInquiryArgs = {
  input: StoreInquiryInput
}

export type MutationSubmitBnpApplicationArgs = {
  orderNumber: Scalars['String']['input']
}

export type NewCartItem = {
  baseQty: Scalars['Float']['input']
  sku: Scalars['String']['input']
}

export type NewOrderInput = {
  client: ClientInput
  note?: InputMaybe<Scalars['String']['input']>
  paymentMethodCode: Scalars['String']['input']
  promoCode?: InputMaybe<Scalars['String']['input']>
  shipping: ShippingInput
  shippingMethodCode: Scalars['String']['input']
}

export type NewsWidget = {
  __typename?: 'NewsWidget'
  articles: Array<Article>
  identifier: Scalars['String']['output']
  link: Link
  subtitle: Scalars['String']['output']
  title: Scalars['String']['output']
}

export type NotificationMessage = {
  __typename?: 'NotificationMessage'
  message: Scalars['String']['output']
  title: Scalars['String']['output']
  type: NotificationType
}

export enum NotificationType {
  Error = 'ERROR',
  Info = 'INFO',
  Success = 'SUCCESS',
  Warning = 'WARNING',
}

export type OrderAddress = {
  __typename?: 'OrderAddress'
  city: Scalars['String']['output']
  email: Scalars['String']['output']
  firstName: Scalars['String']['output']
  id: Scalars['ID']['output']
  lastName: Scalars['String']['output']
  postcode: Scalars['String']['output']
  street: Scalars['String']['output']
  telephone: Scalars['String']['output']
}

export type OrderCustomer = {
  __typename?: 'OrderCustomer'
  email: Scalars['String']['output']
  firstName: Scalars['String']['output']
  invoice?: Maybe<CustomerInvoice>
  lastName: Scalars['String']['output']
  phone: Scalars['String']['output']
}

export type OrderItem = {
  __typename?: 'OrderItem'
  baseQty: Scalars['Float']['output']
  discountAmount: Price
  discountPercent: Scalars['Float']['output']
  id: Scalars['ID']['output']
  price: Price
  priceWithoutDiscount: Price
  product: Product
  rowTotal: Price
  rowTotalWithoutDiscount: Price
  sku: Scalars['String']['output']
}

export type OrderRedirect = {
  __typename?: 'OrderRedirect'
  data: Array<MapValue>
  url: Scalars['String']['output']
}

export type OrderStatus = {
  __typename?: 'OrderStatus'
  code: Scalars['String']['output']
  label: Scalars['String']['output']
}

export type Page = {
  __typename?: 'Page'
  breadcrumbs: Array<Breadcrumb>
  data?: Maybe<PageData>
  status: PageStatus
}

export type PageData = CmsPage | CatalogPage | ErrorData | ProductPage

export type PageMessages = {
  __typename?: 'PageMessages'
  newsletter: Scalars['String']['output']
  sendInquiryMessage: Scalars['String']['output']
}

export type PageMeta = {
  __typename?: 'PageMeta'
  canonicalUrl: Scalars['String']['output']
  description: Scalars['String']['output']
  image?: Maybe<Image>
  keywords: Scalars['String']['output']
  robots: Scalars['String']['output']
  title: Scalars['String']['output']
}

export type PageStatus = {
  __typename?: 'PageStatus'
  error?: Maybe<Scalars['String']['output']>
  redirectUrl?: Maybe<Scalars['String']['output']>
  statusCode: Scalars['Int']['output']
}

export type Pager = {
  __typename?: 'Pager'
  page: Scalars['Int']['output']
  pageSize: Scalars['Int']['output']
  totalItems: Scalars['Int']['output']
  totalPages: Scalars['Int']['output']
}

export type PaymentMethod = {
  __typename?: 'PaymentMethod'
  code: Scalars['String']['output']
  extraContent: Scalars['String']['output']
  name: Scalars['String']['output']
}

export type PlaceOrderResponse = {
  __typename?: 'PlaceOrderResponse'
  order: StoreOrder
  orderNumber: Scalars['String']['output']
  redirect: OrderRedirect
  status: OrderStatus
}

export enum Positions {
  LeftBottom = 'LEFT_BOTTOM',
  LeftTop = 'LEFT_TOP',
  RightBottom = 'RIGHT_BOTTOM',
  RightTop = 'RIGHT_TOP',
}

export type PraktisStore = {
  __typename?: 'PraktisStore'
  ID: Scalars['ID']['output']
  acceptOrders: Scalars['Boolean']['output']
  address: Scalars['String']['output']
  businessHours: Array<StoreSchedule>
  businessHoursData: Scalars['String']['output']
  city: Scalars['String']['output']
  descriptionArea?: Maybe<DescriptionArea>
  descriptionAreaData: Scalars['String']['output']
  displayOrder: Scalars['Int']['output']
  email: Scalars['String']['output']
  gallery: Array<StoreImage>
  identity: Scalars['String']['output']
  location: MapLocation
  locationData: Scalars['String']['output']
  metaDescription: Scalars['String']['output']
  metaKeywords: Scalars['String']['output']
  metaTitle: Scalars['String']['output']
  name: Scalars['String']['output']
  phone: Scalars['String']['output']
  services: Array<Service>
  transportInformation: Scalars['String']['output']
  virtualTour: Scalars['String']['output']
  warehouseCode: Scalars['String']['output']
  warehouseID: Scalars['ID']['output']
}

export type Price = {
  __typename?: 'Price'
  currency: Scalars['String']['output']
  value: Scalars['Float']['output']
}

export type PricingVariant = {
  __typename?: 'PricingVariant'
  apr: Scalars['String']['output']
  correctDownpaymentAmount: Scalars['String']['output']
  id: Scalars['String']['output']
  installment: Scalars['String']['output']
  installmentAmount: Scalars['String']['output']
  maturity: Scalars['String']['output']
  nir: Scalars['String']['output']
  pricingSchemeId: Scalars['String']['output']
  pricingSchemeName: Scalars['String']['output']
  processingFeeAmount: Scalars['String']['output']
  totalRepaymentAmount: Scalars['String']['output']
  total_repayment: Scalars['String']['output']
}

export type PrivacyData = {
  __typename?: 'PrivacyData'
  popupAgreement?: Maybe<Scalars['String']['output']>
}

export type Product = BundleProduct | SimpleProduct

export type ProductMeasures = {
  __typename?: 'ProductMeasures'
  base: Scalars['String']['output']
  secondary: Scalars['String']['output']
  secondaryMeasureUsed: Scalars['Boolean']['output']
  secondaryQty: Scalars['Float']['output']
}

export type ProductPage = {
  __typename?: 'ProductPage'
  boughtTogether?: Maybe<Array<SimpleProduct>>
  product: Product
  staticBlocks?: Maybe<Array<CmsBlock>>
  widgets?: Maybe<Array<ProductsSliderWidget>>
}

export type ProductPrice = {
  __typename?: 'ProductPrice'
  price: Price
  special?: Maybe<Price>
  specialFrom?: Maybe<Scalars['String']['output']>
  specialTo?: Maybe<Scalars['String']['output']>
}

export type ProductStock = {
  __typename?: 'ProductStock'
  blockForSale: Scalars['Boolean']['output']
  hasImages: Scalars['Boolean']['output']
  inStock: Scalars['Boolean']['output']
  manageStock: Scalars['Boolean']['output']
  minQty: Scalars['Float']['output']
  qty: Scalars['Float']['output']
  showOutOfStock: Scalars['Boolean']['output']
  zeronBlockedDelivery: Scalars['Boolean']['output']
  zeronSiteStatus: Scalars['String']['output']
}

export type ProductVideo = {
  __typename?: 'ProductVideo'
  description?: Maybe<Scalars['String']['output']>
  id: Scalars['ID']['output']
  position: Scalars['Int']['output']
  thumbnail?: Maybe<Scalars['String']['output']>
  title: Scalars['String']['output']
  url: Scalars['String']['output']
  videoType: Scalars['String']['output']
}

export type ProductsSliderWidget = {
  __typename?: 'ProductsSliderWidget'
  identifier: Scalars['String']['output']
  subtitle: Scalars['String']['output']
  tabs: Array<ProductsTab>
  title: Scalars['String']['output']
}

export type ProductsTab = {
  __typename?: 'ProductsTab'
  products: Array<Product>
  title: Scalars['String']['output']
}

export type Query = {
  __typename?: 'Query'
  availableServices: Array<Service>
  availableStores: Array<PraktisStore>
  calculateBNPLoan: LoanCalculation
  customerData: Customer
  customerWishlist: CustomerWishlist
  getBNPGoodCategories: Array<BnpGoodCategory>
  getBNPGoodTypes: Array<BnpGoodType>
  getBNPPricingSchemes: Array<BnpPricingScheme>
  getBlogPost: BlogPost
  getBlogPosts: MagentoBlog
  getBrands: Array<Brand>
  getCart: StoreCart
  getCreditCalculatorBNPParibas: Array<BnpVariantGroup>
  getCreditCalculatorBNPParibasForQuote: Array<BnpVariantGroup>
  getCreditCalculatorTBIBank: TbiConfiguration
  getEcontCity: Array<Maybe<EcontCity>>
  getEcontOffice: Array<Maybe<EcontOffice>>
  getFeaturedBlogPost: BlogPost
  getHomepage?: Maybe<HomePage>
  getNewCart: StoreCart
  getNotifications: Array<NotificationMessage>
  getStaticBlock: CmsBlock
  getStaticContent: StaticContent
  getStaticPage: CmsPage
  getStore?: Maybe<StorePageData>
  getStoreLogo: StoreLogo
  getWidget?: Maybe<Widget>
  health?: Maybe<Scalars['String']['output']>
  route: Page
  routeMeta: PageMeta
  search: SearchResults
  searchPage: SearchPage
}

export type QueryCalculateBnpLoanArgs = {
  cartToken: Scalars['String']['input']
  downPayment: Scalars['Float']['input']
  pricingVariantId: Scalars['Int']['input']
}

export type QueryGetBnpGoodTypesArgs = {
  categoryId?: InputMaybe<Scalars['String']['input']>
}

export type QueryGetBnpPricingSchemesArgs = {
  downPayment: Scalars['Float']['input']
  goodTypeIds: Scalars['String']['input']
  principal: Scalars['Float']['input']
}

export type QueryGetBlogPostArgs = {
  identifier: Scalars['String']['input']
}

export type QueryGetBlogPostsArgs = {
  page: Scalars['Int']['input']
  size: Scalars['Int']['input']
}

export type QueryGetBrandsArgs = {
  featured?: InputMaybe<Scalars['Boolean']['input']>
}

export type QueryGetCartArgs = {
  cartToken: Scalars['String']['input']
}

export type QueryGetCreditCalculatorBnpParibasArgs = {
  downPayment: Scalars['Float']['input']
  qty: Scalars['Int']['input']
  sku: Scalars['String']['input']
}

export type QueryGetCreditCalculatorBnpParibasForQuoteArgs = {
  cartToken: Scalars['String']['input']
  downPayment: Scalars['Float']['input']
}

export type QueryGetCreditCalculatorTbiBankArgs = {
  sku: Scalars['String']['input']
}

export type QueryGetEcontCityArgs = {
  country: Scalars['String']['input']
}

export type QueryGetEcontOfficeArgs = {
  cityID: Scalars['String']['input']
}

export type QueryGetStaticBlockArgs = {
  identifier: Scalars['String']['input']
}

export type QueryGetStaticPageArgs = {
  pageId: Scalars['ID']['input']
}

export type QueryGetStoreArgs = {
  identity: Scalars['String']['input']
}

export type QueryGetWidgetArgs = {
  identity: Scalars['String']['input']
}

export type QueryRouteArgs = {
  query?: InputMaybe<Array<InputMaybe<QueryParam>>>
  url: Scalars['String']['input']
}

export type QueryRouteMetaArgs = {
  query?: InputMaybe<Array<InputMaybe<QueryParam>>>
  url: Scalars['String']['input']
}

export type QuerySearchArgs = {
  searchQuery: Scalars['String']['input']
}

export type QuerySearchPageArgs = {
  query?: InputMaybe<Array<InputMaybe<QueryParam>>>
  searchQuery: Scalars['String']['input']
}

export type QueryParam = {
  name: Scalars['String']['input']
  value: Scalars['String']['input']
}

export type RegistrationResponse = {
  __typename?: 'RegistrationResponse'
  create: Scalars['Boolean']['output']
  requireConfirmation: Scalars['Boolean']['output']
}

export type ResultsPager = {
  page?: InputMaybe<Scalars['Int']['input']>
  pageSize?: InputMaybe<Scalars['Int']['input']>
  sortField: Scalars['String']['input']
  sortOrder?: EntityFilterSort
}

export type SearchCategory = {
  __typename?: 'SearchCategory'
  name: Scalars['String']['output']
  url: Scalars['String']['output']
}

export type SearchPage = {
  __typename?: 'SearchPage'
  data: SearchResults
  state: CatalogState
  status: PageStatus
  title: Scalars['String']['output']
}

export type SearchResults = {
  __typename?: 'SearchResults'
  block?: Maybe<CmsBlock>
  categories: Array<SearchCategory>
  popularTerms: Array<Scalars['String']['output']>
  products: Array<Product>
  totalItems: Scalars['Int']['output']
}

export type Service = {
  __typename?: 'Service'
  iconUrl: Scalars['String']['output']
  id: Scalars['ID']['output']
  image: Image
  name: Scalars['String']['output']
  url: Scalars['String']['output']
}

export type ServiceWidget = {
  __typename?: 'ServiceWidget'
  availableStores: Array<PraktisStore>
  form: FormData
  link: Link
  services: Array<Service>
  subtitle: Scalars['String']['output']
  title: Scalars['String']['output']
}

export type ShippingAddress = {
  __typename?: 'ShippingAddress'
  city: Scalars['String']['output']
  cityId: Scalars['String']['output']
  id: Scalars['ID']['output']
  method: ShippingMethodType
  officeCode: Scalars['String']['output']
  postCode: Scalars['String']['output']
  skus: Array<Scalars['String']['output']>
  storeCode: Scalars['String']['output']
  street: Scalars['String']['output']
}

export type ShippingInput = {
  address: Scalars['String']['input']
  city: Scalars['String']['input']
  cityId?: InputMaybe<Scalars['String']['input']>
  officeCode?: InputMaybe<Scalars['String']['input']>
  postCode: Scalars['String']['input']
  storeCode?: InputMaybe<Scalars['String']['input']>
  type: ShippingMethodType
}

export type ShippingMethod = {
  __typename?: 'ShippingMethod'
  code: Scalars['String']['output']
  name: Scalars['String']['output']
}

export enum ShippingMethodType {
  EcontToAddress = 'ECONT_TO_ADDRESS',
  EcontToOffice = 'ECONT_TO_OFFICE',
  None = 'NONE',
  ToStore = 'TO_STORE',
}

export type SimpleProduct = {
  __typename?: 'SimpleProduct'
  brand?: Maybe<Brand>
  description: Scalars['String']['output']
  energyLabel?: Maybe<EnergyLabel>
  gallery: Array<GalleryImage>
  id: Scalars['ID']['output']
  image: Image
  labels: MagentoLabels
  measures: ProductMeasures
  name: Scalars['String']['output']
  price: ProductPrice
  shortDescription: Scalars['String']['output']
  sku: Scalars['String']['output']
  skuAvailability: Array<StoreAvailabilityItem>
  stock: ProductStock
  urlKey: Scalars['String']['output']
  videos?: Maybe<Array<ProductVideo>>
}

export type SocialAccountInfo = {
  __typename?: 'SocialAccountInfo'
  ID: Scalars['String']['output']
  email: Scalars['String']['output']
  name: Scalars['String']['output']
  pictureURL: Scalars['String']['output']
  verifiedEmail: Scalars['Boolean']['output']
}

export type SocialLinks = {
  __typename?: 'SocialLinks'
  facebook?: Maybe<Scalars['String']['output']>
  viber?: Maybe<Scalars['String']['output']>
  youtube?: Maybe<Scalars['String']['output']>
}

export type SocialLoginResponse = {
  __typename?: 'SocialLoginResponse'
  customer: Customer
  newRegistration: Scalars['Boolean']['output']
}

export type SocialLogins = {
  __typename?: 'SocialLogins'
  facebook?: Maybe<SocialAccountInfo>
  google?: Maybe<SocialAccountInfo>
}

export enum SortDirection {
  Asc = 'ASC',
  Desc = 'DESC',
}

export type SortField = {
  __typename?: 'SortField'
  dir: SortDirection
  label: Scalars['String']['output']
  value: Scalars['String']['output']
}

export type SplashPage = {
  __typename?: 'SplashPage'
  description: Scalars['String']['output']
  id: Scalars['ID']['output']
  image: Image
  name: Scalars['String']['output']
  title: Scalars['String']['output']
  url: Scalars['String']['output']
}

export type StaticContent = {
  __typename?: 'StaticContent'
  apiKeys: ApiKeys
  brochureLink?: Maybe<Scalars['String']['output']>
  footer: Footer
  gdpr?: Maybe<GdprConfig>
  header: HeaderData
  logo?: Maybe<StoreLogo>
  menu: Menu
  messages: PageMessages
  store: StoreInfo
}

export type StoreAvailabilityItem = {
  __typename?: 'StoreAvailabilityItem'
  available: AvailabilityStatus
  sample: Scalars['Boolean']['output']
  store: PraktisStore
}

export type StoreCart = {
  __typename?: 'StoreCart'
  couponCode: Scalars['String']['output']
  currency: Scalars['String']['output']
  customer: OrderCustomer
  id: Scalars['ID']['output']
  items: Array<StoreCartItem>
  note: Scalars['String']['output']
  paymentMethod: Scalars['String']['output']
  shipping: CartShipping
  storeCode: Scalars['String']['output']
  token: Scalars['String']['output']
  totals: Array<CartTotal>
}

export type StoreCartItem = {
  __typename?: 'StoreCartItem'
  baseQty: Scalars['Float']['output']
  discountAmount: Price
  discountPercent: Scalars['Float']['output']
  id: Scalars['ID']['output']
  labels: StoreCartItemLabels
  price: Price
  priceWithoutDiscount: Price
  product: Product
  rowTotal: Price
  rowTotalWithoutDiscount: Price
  sku: Scalars['String']['output']
}

export type StoreCartItemLabels = {
  __typename?: 'StoreCartItemLabels'
  freeShipping: Scalars['Boolean']['output']
  usePallet: Scalars['Boolean']['output']
}

export type StoreContacts = {
  __typename?: 'StoreContacts'
  general?: Maybe<ContactInfo>
  onlineStore?: Maybe<ContactInfo>
}

export type StoreImage = {
  __typename?: 'StoreImage'
  ID: Scalars['ID']['output']
  alt?: Maybe<Scalars['String']['output']>
  isPrimary: Scalars['Boolean']['output']
  mobileSrc?: Maybe<Scalars['String']['output']>
  position: Scalars['Int']['output']
  praktisStoreID: Scalars['ID']['output']
  src: Scalars['String']['output']
  title?: Maybe<Scalars['String']['output']>
}

export type StoreInfo = {
  __typename?: 'StoreInfo'
  baseUrl: Scalars['String']['output']
  contacts: StoreContacts
  location: MapLocation
}

export type StoreInquiryInput = {
  email: Scalars['String']['input']
  message: Scalars['String']['input']
  name: Scalars['String']['input']
  phone: Scalars['String']['input']
  store: Scalars['String']['input']
  type: InquiryType
}

export type StoreLogo = {
  __typename?: 'StoreLogo'
  alt?: Maybe<Scalars['String']['output']>
  height?: Maybe<Scalars['Int']['output']>
  url: Scalars['String']['output']
  width?: Maybe<Scalars['Int']['output']>
}

export type StoreOrder = {
  __typename?: 'StoreOrder'
  couponCode: Scalars['String']['output']
  createAt: Scalars['String']['output']
  currency: Scalars['String']['output']
  id: Scalars['ID']['output']
  incrementId: Scalars['String']['output']
  invoice?: Maybe<CustomerInvoice>
  items: Array<OrderItem>
  note: Scalars['String']['output']
  paymentMethod: PaymentMethod
  protectCode: Scalars['String']['output']
  shippingAddress: OrderAddress
  shippingMethod: ShippingMethod
  state: Scalars['String']['output']
  status: OrderStatus
  totals: Array<CartTotal>
}

export type StorePageData = {
  __typename?: 'StorePageData'
  messageBlock: Scalars['String']['output']
  store: PraktisStore
}

export type StoreSchedule = {
  __typename?: 'StoreSchedule'
  close: Scalars['String']['output']
  day: Scalars['String']['output']
  open: Scalars['String']['output']
}

export type TbiConfiguration = {
  __typename?: 'TbiConfiguration'
  reklama: Scalars['String']['output']
  tbi_3m_purcent: Scalars['String']['output']
  tbi_4m: Scalars['String']['output']
  tbi_4m_categories: Scalars['String']['output']
  tbi_4m_manufacturers: Scalars['String']['output']
  tbi_4m_max: Scalars['String']['output']
  tbi_4m_min: Scalars['String']['output']
  tbi_4m_purcent: Scalars['String']['output']
  tbi_4m_pv: Scalars['String']['output']
  tbi_5m: Scalars['String']['output']
  tbi_5m_categories: Scalars['String']['output']
  tbi_5m_manufacturers: Scalars['String']['output']
  tbi_5m_max: Scalars['String']['output']
  tbi_5m_min: Scalars['String']['output']
  tbi_5m_purcent_default: Scalars['String']['output']
  tbi_5m_pv: Scalars['String']['output']
  tbi_6m: Scalars['String']['output']
  tbi_6m_categories: Scalars['String']['output']
  tbi_6m_manufacturers: Scalars['String']['output']
  tbi_6m_max: Scalars['String']['output']
  tbi_6m_min: Scalars['String']['output']
  tbi_6m_purcent: Scalars['String']['output']
  tbi_6m_pv: Scalars['String']['output']
  tbi_7m_purcent: Scalars['String']['output']
  tbi_9m: Scalars['String']['output']
  tbi_9m_categories: Scalars['String']['output']
  tbi_9m_manufacturers: Scalars['String']['output']
  tbi_9m_max: Scalars['String']['output']
  tbi_9m_min: Scalars['String']['output']
  tbi_9m_purcent: Scalars['String']['output']
  tbi_9m_pv: Scalars['String']['output']
  tbi_11m_purcent: Scalars['String']['output']
  tbi_12m: Scalars['String']['output']
  tbi_12m_categories: Scalars['String']['output']
  tbi_12m_manufacturers: Scalars['String']['output']
  tbi_12m_max: Scalars['String']['output']
  tbi_12m_min: Scalars['String']['output']
  tbi_12m_purcent: Scalars['String']['output']
  tbi_12m_pv: Scalars['String']['output']
  tbi_15m_purcent: Scalars['String']['output']
  tbi_18m_purcent: Scalars['String']['output']
  tbi_24m_purcent: Scalars['String']['output']
  tbi_30m_purcent: Scalars['String']['output']
  tbi_36m_purcent: Scalars['String']['output']
  tbi_42m_purcent: Scalars['String']['output']
  tbi_48m_purcent: Scalars['String']['output']
  tbi_54m_purcent: Scalars['String']['output']
  tbi_60m_purcent: Scalars['String']['output']
  tbi_bnpl: Scalars['String']['output']
  tbi_button_kvadrat: Scalars['String']['output']
  tbi_button_status: Scalars['String']['output']
  tbi_button_text_visible: Scalars['String']['output']
  tbi_custom_button_status: Scalars['String']['output']
  tbi_eur: Scalars['String']['output']
  tbi_is_cart: Scalars['String']['output']
  tbi_is_direct: Scalars['String']['output']
  tbi_maxstojnost: Scalars['String']['output']
  tbi_maxstojnost_bnpl: Scalars['String']['output']
  tbi_minstojnost: Scalars['String']['output']
  tbi_minstojnost_bnpl: Scalars['String']['output']
  tbi_over_5000: Scalars['String']['output']
  tbi_purcent_default: Scalars['String']['output']
  tbi_status: Scalars['String']['output']
  tbi_zaglavie: Scalars['String']['output']
}

export type Tile = CategoryTile | DoubleImageTile | ImageTile

export type TileContent = {
  __typename?: 'TileContent'
  icon?: Maybe<Image>
  link: Link
  text?: Maybe<Scalars['String']['output']>
  title: Scalars['String']['output']
}

export enum TileContentPosition {
  Left = 'LEFT',
  LeftBottom = 'LEFT_BOTTOM',
  LeftTop = 'LEFT_TOP',
  Right = 'RIGHT',
  RightBottom = 'RIGHT_BOTTOM',
  RightTop = 'RIGHT_TOP',
}

export type TilesRow = {
  __typename?: 'TilesRow'
  cols: Array<Tile>
}

export type TilesWidget = {
  __typename?: 'TilesWidget'
  identifier: Scalars['String']['output']
  rows: Array<TilesRow>
  subtitle: Scalars['String']['output']
  title: Scalars['String']['output']
  viewMore?: Maybe<Link>
}

export type ValidationCheck = {
  check: Constraint
  value?: InputMaybe<Scalars['String']['input']>
}

export type Warehouse = {
  __typename?: 'Warehouse'
  Address: Scalars['String']['output']
  Name: Scalars['String']['output']
  PostalCode: Scalars['String']['output']
  WarehouseCode: Scalars['Int']['output']
  WarehouseID: Scalars['ID']['output']
}

export type Widget =
  | CarouselWidget
  | CategoryLinkWidget
  | HtmlWidget
  | NewsWidget
  | ProductsSliderWidget
  | ServiceWidget
  | TilesWidget

export type WidgetCategory = {
  __typename?: 'WidgetCategory'
  image?: Maybe<Image>
  link: Link
  title: Scalars['String']['output']
}

export type SignUpResponseFragment = {
  __typename?: 'RegistrationResponse'
  create: boolean
  requireConfirmation: boolean
}

export type CustomerAuthDataFragment = { __typename?: 'Customer'; id: string; token: string }

export type AppImageFragment = {
  __typename?: 'Image'
  src: string
  mobileSrc?: string | null
  alt?: string | null
  title?: string | null
}

export type AppLinkFragment = { __typename?: 'Link'; href: string; text: string; title?: string | null }

export type AppPriceFragment = { __typename?: 'Price'; value: number; currency: string }

export type StaticBlockFragment = { __typename?: 'CMSBlock'; title: string; identifier: string; content: string }

export type AppBlogPostFragment = {
  __typename?: 'BlogPost'
  urlKey: string
  title: string
  summary: string
  previewImageUrl: string
  content: string
  mainImageUrl: string
  publishedAt: string
}

export type BnpPricingSchemeFragment = { __typename?: 'BNPPricingScheme'; id: string; name: string }

export type PricingVariantFragment = {
  __typename?: 'PricingVariant'
  id: string
  apr: string
  correctDownpaymentAmount: string
  installmentAmount: string
  maturity: string
  nir: string
  pricingSchemeId: string
  pricingSchemeName: string
  totalRepaymentAmount: string
  processingFeeAmount: string
}

export type BnpVariantGroupFragment = {
  __typename?: 'BNPVariantGroup'
  schemeId: string
  variants: Array<{
    __typename?: 'PricingVariant'
    id: string
    apr: string
    correctDownpaymentAmount: string
    installmentAmount: string
    maturity: string
    nir: string
    pricingSchemeId: string
    pricingSchemeName: string
    totalRepaymentAmount: string
    processingFeeAmount: string
  }>
}

export type LoanCalculationFragment = {
  __typename?: 'LoanCalculation'
  apr: string
  correctDownpaymentAmount: string
  installmentAmount: string
  maturity: string
  nir: string
  pricingSchemeId: string
  pricingSchemeName: string
  pricingVariantId: string
  processingFeeAmount: string
  totalRepaymentAmount: string
}

export type AppBrandFragment = {
  __typename?: 'Brand'
  name: string
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
}

export type AvailablePaymentMethodFragment = {
  __typename?: 'PaymentMethod'
  name: string
  code: string
  extraContent: string
}

export type StoreCartItemLabelsFragment = {
  __typename?: 'StoreCartItemLabels'
  freeShipping: boolean
  usePallet: boolean
}

export type CartItemFragment = {
  __typename?: 'StoreCartItem'
  id: string
  sku: string
  baseQty: number
  discountPercent: number
  labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
  product:
    | {
        __typename: 'BundleProduct'
        id: string
        sku: string
        name: string
        description: string
        shortDescription: string
        urlKey: string
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
        bundled: Array<{
          __typename?: 'SimpleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }>
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
      }
    | {
        __typename: 'SimpleProduct'
        id: string
        sku: string
        name: string
        urlKey: string
        description: string
        shortDescription: string
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        videos?: Array<{
          __typename?: 'ProductVideo'
          id: string
          title: string
          description?: string | null
          url: string
          videoType: string
          thumbnail?: string | null
          position: number
        }> | null
        measures: {
          __typename?: 'ProductMeasures'
          base: string
          secondary: string
          secondaryQty: number
          secondaryMeasureUsed: boolean
        }
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
        stock: {
          __typename?: 'ProductStock'
          blockForSale: boolean
          hasImages: boolean
          inStock: boolean
          manageStock: boolean
          qty: number
          minQty: number
          zeronBlockedDelivery: boolean
          zeronSiteStatus: string
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        energyLabel?: {
          __typename?: 'EnergyLabel'
          infoUrl?: string | null
          labelUrl?: string | null
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
      }
  discountAmount: { __typename?: 'Price'; value: number; currency: string }
  price: { __typename?: 'Price'; value: number; currency: string }
  rowTotal: { __typename?: 'Price'; value: number; currency: string }
}

export type CartShippingFullFragment = {
  __typename?: 'CartShipping'
  availableMethods: Array<ShippingMethodType>
  hasFreeShipping: boolean
  freeShippingAfter?: number | null
  minAmountForFreeShippingMessage?: number | null
  selectedMethod: string
  availableIn: Array<{
    __typename?: 'PraktisStore'
    ID: string
    identity: string
    name: string
    city: string
    address: string
    phone: string
    warehouseCode: string
  }>
  address?: {
    __typename?: 'ShippingAddress'
    id: string
    skus: Array<string>
    method: ShippingMethodType
    officeCode: string
    storeCode: string
    city: string
    cityId: string
    postCode: string
    street: string
  } | null
}

export type CartItemAvailableInFragment = {
  __typename?: 'PraktisStore'
  ID: string
  identity: string
  name: string
  city: string
  address: string
  phone: string
  warehouseCode: string
}

export type CartTotalFragment = {
  __typename?: 'CartTotal'
  code: CartTotalCode
  label: string
  order: number
  amount: { __typename?: 'Price'; value: number; currency: string }
}

export type CartFullFragment = {
  __typename?: 'StoreCart'
  id: string
  token: string
  storeCode: string
  currency: string
  couponCode: string
  note: string
  paymentMethod: string
  customer: {
    __typename?: 'OrderCustomer'
    firstName: string
    lastName: string
    email: string
    phone: string
    invoice?: {
      __typename?: 'CustomerInvoice'
      id: string
      type: InvoiceType
      city: string
      address: string
      company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
      individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
    } | null
  }
  items: Array<{
    __typename?: 'StoreCartItem'
    id: string
    sku: string
    baseQty: number
    discountPercent: number
    labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
    product:
      | {
          __typename: 'BundleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          brand?: {
            __typename?: 'Brand'
            name: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          bundled: Array<{
            __typename?: 'SimpleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }>
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }
      | {
          __typename: 'SimpleProduct'
          id: string
          sku: string
          name: string
          urlKey: string
          description: string
          shortDescription: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          videos?: Array<{
            __typename?: 'ProductVideo'
            id: string
            title: string
            description?: string | null
            url: string
            videoType: string
            thumbnail?: string | null
            position: number
          }> | null
          measures: {
            __typename?: 'ProductMeasures'
            base: string
            secondary: string
            secondaryQty: number
            secondaryMeasureUsed: boolean
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
          stock: {
            __typename?: 'ProductStock'
            blockForSale: boolean
            hasImages: boolean
            inStock: boolean
            manageStock: boolean
            qty: number
            minQty: number
            zeronBlockedDelivery: boolean
            zeronSiteStatus: string
          }
          brand?: {
            __typename?: 'Brand'
            name: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          } | null
          energyLabel?: {
            __typename?: 'EnergyLabel'
            infoUrl?: string | null
            labelUrl?: string | null
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
        }
    discountAmount: { __typename?: 'Price'; value: number; currency: string }
    price: { __typename?: 'Price'; value: number; currency: string }
    rowTotal: { __typename?: 'Price'; value: number; currency: string }
  }>
  shipping: {
    __typename?: 'CartShipping'
    availableMethods: Array<ShippingMethodType>
    hasFreeShipping: boolean
    freeShippingAfter?: number | null
    minAmountForFreeShippingMessage?: number | null
    selectedMethod: string
    availableIn: Array<{
      __typename?: 'PraktisStore'
      ID: string
      identity: string
      name: string
      city: string
      address: string
      phone: string
      warehouseCode: string
    }>
    address?: {
      __typename?: 'ShippingAddress'
      id: string
      skus: Array<string>
      method: ShippingMethodType
      officeCode: string
      storeCode: string
      city: string
      cityId: string
      postCode: string
      street: string
    } | null
  }
  totals: Array<{
    __typename?: 'CartTotal'
    code: CartTotalCode
    label: string
    order: number
    amount: { __typename?: 'Price'; value: number; currency: string }
  }>
}

export type CartViewFragment = {
  __typename?: 'StoreCart'
  id: string
  token: string
  storeCode: string
  couponCode: string
  currency: string
  totals: Array<{
    __typename?: 'CartTotal'
    code: CartTotalCode
    label: string
    amount: { __typename?: 'Price'; value: number; currency: string }
  }>
  items: Array<{
    __typename?: 'StoreCartItem'
    id: string
    sku: string
    baseQty: number
    discountPercent: number
    product:
      | {
          __typename: 'BundleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          brand?: {
            __typename?: 'Brand'
            name: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          bundled: Array<{
            __typename?: 'SimpleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }>
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }
      | {
          __typename: 'SimpleProduct'
          id: string
          sku: string
          name: string
          urlKey: string
          description: string
          shortDescription: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          videos?: Array<{
            __typename?: 'ProductVideo'
            id: string
            title: string
            description?: string | null
            url: string
            videoType: string
            thumbnail?: string | null
            position: number
          }> | null
          measures: {
            __typename?: 'ProductMeasures'
            base: string
            secondary: string
            secondaryQty: number
            secondaryMeasureUsed: boolean
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
          stock: {
            __typename?: 'ProductStock'
            blockForSale: boolean
            hasImages: boolean
            inStock: boolean
            manageStock: boolean
            qty: number
            minQty: number
            zeronBlockedDelivery: boolean
            zeronSiteStatus: string
          }
          brand?: {
            __typename?: 'Brand'
            name: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          } | null
          energyLabel?: {
            __typename?: 'EnergyLabel'
            infoUrl?: string | null
            labelUrl?: string | null
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
        }
    labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
    price: { __typename?: 'Price'; value: number; currency: string }
    rowTotal: { __typename?: 'Price'; value: number; currency: string }
    discountAmount: { __typename?: 'Price'; value: number; currency: string }
  }>
}

export type CompanyInvoiceFragment = {
  __typename?: 'CompanyInvoice'
  name: string
  mol: string
  eik: string
  vat: string
}

export type CustomerInvoiceFragment = {
  __typename?: 'CustomerInvoice'
  id: string
  type: InvoiceType
  city: string
  address: string
  company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
  individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
}

export type IndividualInvoiceFragment = { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string }

export type OrderCustomerFragment = {
  __typename?: 'OrderCustomer'
  firstName: string
  lastName: string
  email: string
  phone: string
  invoice?: {
    __typename?: 'CustomerInvoice'
    id: string
    type: InvoiceType
    city: string
    address: string
    company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
    individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
  } | null
}

export type PaymentMethodFragment = { __typename?: 'PaymentMethod'; name: string; code: string; extraContent: string }

export type ShippingAddressFragment = {
  __typename?: 'ShippingAddress'
  id: string
  skus: Array<string>
  method: ShippingMethodType
  officeCode: string
  storeCode: string
  city: string
  cityId: string
  postCode: string
  street: string
}

export type AppAppliedFilterFragment = {
  __typename?: 'AppliedFilter'
  label: string
  value: string
  requestVar: string
  attributeCode: string
}

export type AppFilterOptionFragment = { __typename?: 'AttributeOption'; label: string; value: string; order: number }

export type AppAvailableFilterFragment = {
  __typename?: 'AvailableFilter'
  position: number
  type: FilterRenderType
  requestVar: string
  attributeCode: string
  label: string
  options: Array<{ __typename?: 'AttributeOption'; label: string; value: string; order: number }>
}

export type AppFilterStateFragment = {
  __typename?: 'CatalogState'
  filters: {
    __typename?: 'Filters'
    applied: Array<{
      __typename?: 'AppliedFilter'
      label: string
      value: string
      requestVar: string
      attributeCode: string
    }>
    available: Array<{
      __typename?: 'AvailableFilter'
      position: number
      type: FilterRenderType
      requestVar: string
      attributeCode: string
      label: string
      options: Array<{ __typename?: 'AttributeOption'; label: string; value: string; order: number }>
    }>
  }
  pager: { __typename?: 'Pager'; page: number; pageSize: number; totalPages: number; totalItems: number }
  sort: { __typename?: 'CollectionSort'; value: string; dir: SortDirection }
}

export type AppCategoryViewFragment = {
  __typename: 'Category'
  id: string
  name: string
  url: string
  description?: string | null
  image?: {
    __typename?: 'Image'
    src: string
    mobileSrc?: string | null
    alt?: string | null
    title?: string | null
  } | null
  icon?: {
    __typename?: 'Image'
    src: string
    mobileSrc?: string | null
    alt?: string | null
    title?: string | null
  } | null
  banner?: {
    __typename?: 'CategoryBanner'
    url: string
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  } | null
  widgets?: Array<{
    __typename: 'CategoryLinkWidget'
    title: string
    url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
    image: { __typename?: 'Image'; alt?: string | null; src: string; title?: string | null; mobileSrc?: string | null }
  }> | null
}

export type AppSearchCategoryFragment = { __typename: 'SearchCategory'; name: string; url: string }

export type AppSplashViewFragment = { __typename: 'SplashPage'; title: string; url: string; splashDescription: string }

export type AppCategoryViewWidgetFragment = {
  __typename: 'CategoryLinkWidget'
  title: string
  url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  image: { __typename?: 'Image'; alt?: string | null; src: string; title?: string | null; mobileSrc?: string | null }
}

export type ShippingMethodFragment = { __typename?: 'ShippingMethod'; name: string; code: string }

export type AvailableShippingMethodFragment = {
  __typename?: 'AvailableShippingMethod'
  method: { __typename?: 'ShippingMethod'; name: string; code: string }
  price: { __typename?: 'Price'; value: number; currency: string }
}

export type CustomerAddressFullFragment = {
  __typename?: 'CustomerAddress'
  id: string
  firstName: string
  lastName: string
  phone: string
  companyName?: string | null
  country: string
  city: string
  cityID: string
  postCode: string
  street: string
}

export type CustomerFullFragment = {
  __typename?: 'Customer'
  id: string
  token: string
  isSubscribed: boolean
  firstName: string
  lastName: string
  email: string
  defaultBillingAddressID?: string | null
  defaultShippingAddressID?: string | null
  addresses: Array<{
    __typename?: 'CustomerAddress'
    id: string
    firstName: string
    lastName: string
    phone: string
    companyName?: string | null
    country: string
    city: string
    cityID: string
    postCode: string
    street: string
  }>
}

export type EcontCountryFragment = {
  __typename?: 'EcontCountry'
  code2: string
  code3: string
  id: string
  isEU: boolean
  name: string
  nameEn: string
}

export type EcontOfficeLocationFragment = { __typename?: 'EcontOfficeLocation'; latitude: number; longitude: number }

export type EcontOfficeAddressFragment = {
  __typename?: 'EcontOfficeAddress'
  fullAddress: string
  fullAddressEn: string
  city: {
    __typename?: 'EcontCity'
    id: string
    name: string
    nameEn: string
    postCode: string
    regionCode: string
    regionName: string
    regionNameEn: string
    type: string
    country: {
      __typename?: 'EcontCountry'
      code2: string
      code3: string
      id: string
      isEU: boolean
      name: string
      nameEn: string
    }
  }
  location: { __typename?: 'EcontOfficeLocation'; latitude: number; longitude: number }
}

export type EcontOfficeFragment = {
  __typename?: 'EcontOffice'
  id: string
  code: string
  name: string
  nameEn: string
  phones: Array<string>
  isAPS: boolean
  isMPS: boolean
  adderess: {
    __typename?: 'EcontOfficeAddress'
    fullAddress: string
    fullAddressEn: string
    city: {
      __typename?: 'EcontCity'
      id: string
      name: string
      nameEn: string
      postCode: string
      regionCode: string
      regionName: string
      regionNameEn: string
      type: string
      country: {
        __typename?: 'EcontCountry'
        code2: string
        code3: string
        id: string
        isEU: boolean
        name: string
        nameEn: string
      }
    }
    location: { __typename?: 'EcontOfficeLocation'; latitude: number; longitude: number }
  }
}

export type EcontCityFragment = {
  __typename?: 'EcontCity'
  id: string
  name: string
  nameEn: string
  postCode: string
  regionCode: string
  regionName: string
  regionNameEn: string
  type: string
  country: {
    __typename?: 'EcontCountry'
    code2: string
    code3: string
    id: string
    isEU: boolean
    name: string
    nameEn: string
  }
}

export type AppMenuFragment = {
  __typename?: 'Menu'
  categories: Array<{
    __typename?: 'MenuItem'
    url: string
    thumbnail: string
    name: string
    children?: Array<{ __typename?: 'MenuItem'; name: string; url: string }> | null
    sideSection?: { __typename?: 'MenuSideSection'; image: string; url: string; rawHtml: string } | null
  }>
}

export type AppContactsFragment = {
  __typename?: 'StoreContacts'
  general?: { __typename?: 'ContactInfo'; phone?: string | null } | null
  onlineStore?: { __typename?: 'ContactInfo'; phone?: string | null } | null
}

export type AppFooterFragment = {
  __typename?: 'Footer'
  columns?: Array<{
    __typename?: 'FooterColumn'
    title?: string | null
    links?: Array<{ __typename?: 'Link'; href: string; text: string; title?: string | null } | null> | null
  } | null> | null
  appLinks?: { __typename?: 'ApplicationLinks'; android?: string | null; ios?: string | null } | null
  contacts?: {
    __typename?: 'StoreContacts'
    general?: { __typename?: 'ContactInfo'; phone?: string | null } | null
    onlineStore?: { __typename?: 'ContactInfo'; phone?: string | null } | null
  } | null
  social?: {
    __typename?: 'SocialLinks'
    facebook?: string | null
    viber?: string | null
    youtube?: string | null
  } | null
}

export type AppLogoFragment = {
  __typename?: 'StoreLogo'
  url: string
  width?: number | null
  height?: number | null
  alt?: string | null
}

export type OrderFullFragment = {
  __typename?: 'StoreOrder'
  incrementId: string
  state: string
  couponCode: string
  protectCode: string
  createAt: string
  note: string
  status: { __typename?: 'OrderStatus'; label: string; code: string }
  items: Array<{
    __typename?: 'OrderItem'
    id: string
    sku: string
    baseQty: number
    discountPercent: number
    product:
      | {
          __typename: 'BundleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          brand?: {
            __typename?: 'Brand'
            name: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          bundled: Array<{
            __typename?: 'SimpleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }>
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }
      | {
          __typename: 'SimpleProduct'
          id: string
          sku: string
          name: string
          urlKey: string
          description: string
          shortDescription: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          videos?: Array<{
            __typename?: 'ProductVideo'
            id: string
            title: string
            description?: string | null
            url: string
            videoType: string
            thumbnail?: string | null
            position: number
          }> | null
          measures: {
            __typename?: 'ProductMeasures'
            base: string
            secondary: string
            secondaryQty: number
            secondaryMeasureUsed: boolean
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
          stock: {
            __typename?: 'ProductStock'
            blockForSale: boolean
            hasImages: boolean
            inStock: boolean
            manageStock: boolean
            qty: number
            minQty: number
            zeronBlockedDelivery: boolean
            zeronSiteStatus: string
          }
          brand?: {
            __typename?: 'Brand'
            name: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          } | null
          energyLabel?: {
            __typename?: 'EnergyLabel'
            infoUrl?: string | null
            labelUrl?: string | null
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
        }
    discountAmount: { __typename?: 'Price'; value: number; currency: string }
    price: { __typename?: 'Price'; value: number; currency: string }
    priceWithoutDiscount: { __typename?: 'Price'; value: number; currency: string }
    rowTotal: { __typename?: 'Price'; value: number; currency: string }
    rowTotalWithoutDiscount: { __typename?: 'Price'; value: number; currency: string }
  }>
  totals: Array<{
    __typename?: 'CartTotal'
    code: CartTotalCode
    label: string
    order: number
    amount: { __typename?: 'Price'; value: number; currency: string }
  }>
  shippingMethod: { __typename?: 'ShippingMethod'; name: string; code: string }
  paymentMethod: { __typename?: 'PaymentMethod'; name: string; code: string; extraContent: string }
  shippingAddress: {
    __typename?: 'OrderAddress'
    firstName: string
    lastName: string
    email: string
    telephone: string
    city: string
    postcode: string
    street: string
  }
  invoice?: {
    __typename?: 'CustomerInvoice'
    id: string
    type: InvoiceType
    city: string
    address: string
    company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
    individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
  } | null
}

export type OrderAddressFullFragment = {
  __typename?: 'OrderAddress'
  firstName: string
  lastName: string
  email: string
  telephone: string
  city: string
  postcode: string
  street: string
}

export type OrderItemFullFragment = {
  __typename?: 'OrderItem'
  id: string
  sku: string
  baseQty: number
  discountPercent: number
  product:
    | {
        __typename: 'BundleProduct'
        id: string
        sku: string
        name: string
        description: string
        shortDescription: string
        urlKey: string
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
        bundled: Array<{
          __typename?: 'SimpleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }>
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
      }
    | {
        __typename: 'SimpleProduct'
        id: string
        sku: string
        name: string
        urlKey: string
        description: string
        shortDescription: string
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        videos?: Array<{
          __typename?: 'ProductVideo'
          id: string
          title: string
          description?: string | null
          url: string
          videoType: string
          thumbnail?: string | null
          position: number
        }> | null
        measures: {
          __typename?: 'ProductMeasures'
          base: string
          secondary: string
          secondaryQty: number
          secondaryMeasureUsed: boolean
        }
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
        stock: {
          __typename?: 'ProductStock'
          blockForSale: boolean
          hasImages: boolean
          inStock: boolean
          manageStock: boolean
          qty: number
          minQty: number
          zeronBlockedDelivery: boolean
          zeronSiteStatus: string
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        energyLabel?: {
          __typename?: 'EnergyLabel'
          infoUrl?: string | null
          labelUrl?: string | null
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
      }
  discountAmount: { __typename?: 'Price'; value: number; currency: string }
  price: { __typename?: 'Price'; value: number; currency: string }
  priceWithoutDiscount: { __typename?: 'Price'; value: number; currency: string }
  rowTotal: { __typename?: 'Price'; value: number; currency: string }
  rowTotalWithoutDiscount: { __typename?: 'Price'; value: number; currency: string }
}

export type MapValueFullFragment = { __typename?: 'MapValue'; key: string; value: string }

export type OrderRedirectFullFragment = {
  __typename?: 'OrderRedirect'
  url: string
  data: Array<{ __typename?: 'MapValue'; key: string; value: string }>
}

export type OrderStatusFullFragment = { __typename?: 'OrderStatus'; label: string; code: string }

export type PlaceOrderFragment = {
  __typename?: 'PlaceOrderResponse'
  orderNumber: string
  status: { __typename?: 'OrderStatus'; label: string; code: string }
  redirect: {
    __typename?: 'OrderRedirect'
    url: string
    data: Array<{ __typename?: 'MapValue'; key: string; value: string }>
  }
}

export type AppProductPriceFragment = {
  __typename?: 'ProductPrice'
  price: { __typename?: 'Price'; value: number; currency: string }
  special?: { __typename?: 'Price'; value: number; currency: string } | null
}

export type AppEnergyLabelFragment = {
  __typename?: 'EnergyLabel'
  infoUrl?: string | null
  labelUrl?: string | null
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
}

export type AppLabelsFragment = {
  __typename?: 'MagentoLabels'
  warrantyMonths: number
  buyCheap?: boolean | null
  freeDelivery?: boolean | null
  fromBrochure?: boolean | null
  other?: Array<string> | null
}

type AppProductCard_BundleProduct_Fragment = {
  __typename: 'BundleProduct'
  id: string
  sku: string
  name: string
  urlKey: string
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  price: {
    __typename?: 'ProductPrice'
    price: { __typename?: 'Price'; value: number; currency: string }
    special?: { __typename?: 'Price'; value: number; currency: string } | null
  }
  labels: {
    __typename?: 'MagentoLabels'
    warrantyMonths: number
    buyCheap?: boolean | null
    freeDelivery?: boolean | null
    fromBrochure?: boolean | null
    other?: Array<string> | null
  }
}

type AppProductCard_SimpleProduct_Fragment = {
  __typename: 'SimpleProduct'
  id: string
  sku: string
  name: string
  urlKey: string
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  price: {
    __typename?: 'ProductPrice'
    price: { __typename?: 'Price'; value: number; currency: string }
    special?: { __typename?: 'Price'; value: number; currency: string } | null
  }
  energyLabel?: {
    __typename?: 'EnergyLabel'
    infoUrl?: string | null
    labelUrl?: string | null
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  } | null
  labels: {
    __typename?: 'MagentoLabels'
    warrantyMonths: number
    buyCheap?: boolean | null
    freeDelivery?: boolean | null
    fromBrochure?: boolean | null
    other?: Array<string> | null
  }
  stock: {
    __typename?: 'ProductStock'
    blockForSale: boolean
    hasImages: boolean
    inStock: boolean
    manageStock: boolean
    qty: number
    minQty: number
    zeronBlockedDelivery: boolean
    zeronSiteStatus: string
  }
}

export type AppProductCardFragment = AppProductCard_BundleProduct_Fragment | AppProductCard_SimpleProduct_Fragment

export type ProductStockFullFragment = {
  __typename?: 'ProductStock'
  blockForSale: boolean
  hasImages: boolean
  inStock: boolean
  manageStock: boolean
  qty: number
  minQty: number
  zeronBlockedDelivery: boolean
  zeronSiteStatus: string
}

export type StoreAvailabilityFragment = {
  __typename?: 'StoreAvailabilityItem'
  available: AvailabilityStatus
  sample: boolean
  store: {
    __typename?: 'PraktisStore'
    identity: string
    warehouseCode: string
    name: string
    displayOrder: number
    acceptOrders: boolean
    location: { __typename?: 'MapLocation'; lat: number; lng: number }
  }
}

export type SimpleProductViewFragment = {
  __typename: 'SimpleProduct'
  id: string
  sku: string
  name: string
  urlKey: string
  description: string
  shortDescription: string
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  gallery: Array<{
    __typename?: 'GalleryImage'
    position: number
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  }>
  videos?: Array<{
    __typename?: 'ProductVideo'
    id: string
    title: string
    description?: string | null
    url: string
    videoType: string
    thumbnail?: string | null
    position: number
  }> | null
  measures: {
    __typename?: 'ProductMeasures'
    base: string
    secondary: string
    secondaryQty: number
    secondaryMeasureUsed: boolean
  }
  price: {
    __typename?: 'ProductPrice'
    price: { __typename?: 'Price'; value: number; currency: string }
    special?: { __typename?: 'Price'; value: number; currency: string } | null
  }
  skuAvailability: Array<{
    __typename?: 'StoreAvailabilityItem'
    available: AvailabilityStatus
    sample: boolean
    store: {
      __typename?: 'PraktisStore'
      identity: string
      warehouseCode: string
      name: string
      displayOrder: number
      acceptOrders: boolean
      location: { __typename?: 'MapLocation'; lat: number; lng: number }
    }
  }>
  stock: {
    __typename?: 'ProductStock'
    blockForSale: boolean
    hasImages: boolean
    inStock: boolean
    manageStock: boolean
    qty: number
    minQty: number
    zeronBlockedDelivery: boolean
    zeronSiteStatus: string
  }
  brand?: {
    __typename?: 'Brand'
    name: string
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
    url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  } | null
  energyLabel?: {
    __typename?: 'EnergyLabel'
    infoUrl?: string | null
    labelUrl?: string | null
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  } | null
  labels: {
    __typename?: 'MagentoLabels'
    warrantyMonths: number
    buyCheap?: boolean | null
    freeDelivery?: boolean | null
    fromBrochure?: boolean | null
    other?: Array<string> | null
  }
}

export type AppBundleItemFragment = {
  __typename?: 'SimpleProduct'
  id: string
  sku: string
  name: string
  description: string
  shortDescription: string
  urlKey: string
  measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  price: {
    __typename?: 'ProductPrice'
    price: { __typename?: 'Price'; value: number; currency: string }
    special?: { __typename?: 'Price'; value: number; currency: string } | null
  }
  gallery: Array<{
    __typename?: 'GalleryImage'
    position: number
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  }>
  labels: {
    __typename?: 'MagentoLabels'
    warrantyMonths: number
    buyCheap?: boolean | null
    freeDelivery?: boolean | null
    fromBrochure?: boolean | null
    other?: Array<string> | null
  }
  skuAvailability: Array<{
    __typename?: 'StoreAvailabilityItem'
    available: AvailabilityStatus
    sample: boolean
    store: {
      __typename?: 'PraktisStore'
      identity: string
      warehouseCode: string
      name: string
      displayOrder: number
      acceptOrders: boolean
      location: { __typename?: 'MapLocation'; lat: number; lng: number }
    }
  }>
}

export type BundleProductViewFragment = {
  __typename: 'BundleProduct'
  id: string
  sku: string
  name: string
  description: string
  shortDescription: string
  urlKey: string
  gallery: Array<{
    __typename?: 'GalleryImage'
    position: number
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  }>
  price: {
    __typename?: 'ProductPrice'
    price: { __typename?: 'Price'; value: number; currency: string }
    special?: { __typename?: 'Price'; value: number; currency: string } | null
  }
  brand?: {
    __typename?: 'Brand'
    name: string
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
    url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  } | null
  labels: {
    __typename?: 'MagentoLabels'
    warrantyMonths: number
    buyCheap?: boolean | null
    freeDelivery?: boolean | null
    fromBrochure?: boolean | null
    other?: Array<string> | null
  }
  bundled: Array<{
    __typename?: 'SimpleProduct'
    id: string
    sku: string
    name: string
    description: string
    shortDescription: string
    urlKey: string
    measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
    price: {
      __typename?: 'ProductPrice'
      price: { __typename?: 'Price'; value: number; currency: string }
      special?: { __typename?: 'Price'; value: number; currency: string } | null
    }
    gallery: Array<{
      __typename?: 'GalleryImage'
      position: number
      image: {
        __typename?: 'Image'
        src: string
        mobileSrc?: string | null
        alt?: string | null
        title?: string | null
      }
    }>
    labels: {
      __typename?: 'MagentoLabels'
      warrantyMonths: number
      buyCheap?: boolean | null
      freeDelivery?: boolean | null
      fromBrochure?: boolean | null
      other?: Array<string> | null
    }
    skuAvailability: Array<{
      __typename?: 'StoreAvailabilityItem'
      available: AvailabilityStatus
      sample: boolean
      store: {
        __typename?: 'PraktisStore'
        identity: string
        warehouseCode: string
        name: string
        displayOrder: number
        acceptOrders: boolean
        location: { __typename?: 'MapLocation'; lat: number; lng: number }
      }
    }>
  }>
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  skuAvailability: Array<{
    __typename?: 'StoreAvailabilityItem'
    available: AvailabilityStatus
    sample: boolean
    store: {
      __typename?: 'PraktisStore'
      identity: string
      warehouseCode: string
      name: string
      displayOrder: number
      acceptOrders: boolean
      location: { __typename?: 'MapLocation'; lat: number; lng: number }
    }
  }>
}

export type ServiceFragment = { __typename?: 'Service'; id: string; name: string; url: string; iconUrl: string }

export type StoreGalleryImageFragment = {
  __typename?: 'StoreImage'
  ID: string
  praktisStoreID: string
  position: number
  alt?: string | null
  src: string
  title?: string | null
  mobileSrc?: string | null
  isPrimary: boolean
}

export type AppStorePreviewFragment = {
  __typename?: 'PraktisStore'
  name: string
  identity: string
  address: string
  email: string
  phone: string
  city: string
  displayOrder: number
  gallery: Array<{
    __typename?: 'StoreImage'
    ID: string
    praktisStoreID: string
    position: number
    alt?: string | null
    src: string
    title?: string | null
    mobileSrc?: string | null
    isPrimary: boolean
  }>
  businessHours: Array<{ __typename?: 'StoreSchedule'; open: string; close: string; day: string }>
}

export type StorePageDataFragment = {
  __typename?: 'StorePageData'
  messageBlock: string
  store: {
    __typename?: 'PraktisStore'
    name: string
    address: string
    city: string
    email: string
    phone: string
    virtualTour: string
    transportInformation: string
    metaDescription: string
    metaKeywords: string
    metaTitle: string
    gallery: Array<{
      __typename?: 'StoreImage'
      ID: string
      praktisStoreID: string
      position: number
      alt?: string | null
      src: string
      title?: string | null
      mobileSrc?: string | null
      isPrimary: boolean
    }>
    location: { __typename?: 'MapLocation'; lat: number; lng: number }
    businessHours: Array<{ __typename?: 'StoreSchedule'; open: string; close: string; day: string }>
    services: Array<{ __typename?: 'Service'; id: string; name: string; url: string; iconUrl: string }>
    descriptionArea?: {
      __typename?: 'DescriptionArea'
      backgroundImage: string
      content: string
      formID?: string | null
      title: string
    } | null
  }
}

export type AppStoreFragment = {
  __typename?: 'PraktisStore'
  name: string
  address: string
  city: string
  email: string
  phone: string
  virtualTour: string
  transportInformation: string
  metaDescription: string
  metaKeywords: string
  metaTitle: string
  gallery: Array<{
    __typename?: 'StoreImage'
    ID: string
    praktisStoreID: string
    position: number
    alt?: string | null
    src: string
    title?: string | null
    mobileSrc?: string | null
    isPrimary: boolean
  }>
  location: { __typename?: 'MapLocation'; lat: number; lng: number }
  businessHours: Array<{ __typename?: 'StoreSchedule'; open: string; close: string; day: string }>
  services: Array<{ __typename?: 'Service'; id: string; name: string; url: string; iconUrl: string }>
  descriptionArea?: {
    __typename?: 'DescriptionArea'
    backgroundImage: string
    content: string
    formID?: string | null
    title: string
  } | null
}

export type VideoFragment = {
  __typename?: 'ProductVideo'
  id: string
  title: string
  description?: string | null
  url: string
  videoType: string
  thumbnail?: string | null
  position: number
}

export type ServiceWidgetFragmentFragment = {
  __typename?: 'ServiceWidget'
  title: string
  subtitle: string
  link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  services: Array<{ __typename?: 'Service'; id: string; name: string; url: string; iconUrl: string }>
  availableStores: Array<{
    __typename?: 'PraktisStore'
    name: string
    identity: string
    address: string
    email: string
    phone: string
    city: string
    displayOrder: number
    gallery: Array<{
      __typename?: 'StoreImage'
      ID: string
      praktisStoreID: string
      position: number
      alt?: string | null
      src: string
      title?: string | null
      mobileSrc?: string | null
      isPrimary: boolean
    }>
    businessHours: Array<{ __typename?: 'StoreSchedule'; open: string; close: string; day: string }>
  }>
  form: { __typename?: 'FormData'; formId: string; text: string; title: string }
}

export type NewsWidgetFragmentFragment = {
  __typename?: 'NewsWidget'
  title: string
  subtitle: string
  identifier: string
  link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  articles: Array<{
    __typename?: 'Article'
    title: string
    description: string
    image: { __typename?: 'Image'; alt?: string | null; mobileSrc?: string | null; src: string; title?: string | null }
    link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  }>
}

export type CarouselWidgetFragmentFragment = {
  __typename?: 'CarouselWidget'
  identifier: string
  slides: Array<{
    __typename?: 'CarouselSlide'
    title: string
    description: string
    brand?: {
      __typename?: 'Brand'
      name: string
      url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
      image: {
        __typename?: 'Image'
        src: string
        mobileSrc?: string | null
        alt?: string | null
        title?: string | null
      }
    } | null
    features?: Array<{
      __typename?: 'Image'
      src: string
      mobileSrc?: string | null
      alt?: string | null
      title?: string | null
    }> | null
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
    link?: { __typename?: 'Link'; href: string; text: string; title?: string | null } | null
    price?: { __typename?: 'Price'; value: number; currency: string } | null
    priceLabels?: {
      __typename?: 'MagentoLabels'
      warrantyMonths: number
      buyCheap?: boolean | null
      freeDelivery?: boolean | null
      fromBrochure?: boolean | null
      other?: Array<string> | null
    } | null
  }>
}

export type WidgetSlideFragment = {
  __typename?: 'CarouselSlide'
  title: string
  description: string
  brand?: {
    __typename?: 'Brand'
    name: string
    url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  } | null
  features?: Array<{
    __typename?: 'Image'
    src: string
    mobileSrc?: string | null
    alt?: string | null
    title?: string | null
  }> | null
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  link?: { __typename?: 'Link'; href: string; text: string; title?: string | null } | null
  price?: { __typename?: 'Price'; value: number; currency: string } | null
  priceLabels?: {
    __typename?: 'MagentoLabels'
    warrantyMonths: number
    buyCheap?: boolean | null
    freeDelivery?: boolean | null
    fromBrochure?: boolean | null
    other?: Array<string> | null
  } | null
}

export type AppProductsSliderWidgetFragment = {
  __typename?: 'ProductsSliderWidget'
  identifier: string
  title: string
  subtitle: string
  tabs: Array<{
    __typename?: 'ProductsTab'
    title: string
    products: Array<
      | {
          __typename: 'BundleProduct'
          id: string
          sku: string
          name: string
          urlKey: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
        }
      | {
          __typename: 'SimpleProduct'
          id: string
          sku: string
          name: string
          urlKey: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          energyLabel?: {
            __typename?: 'EnergyLabel'
            infoUrl?: string | null
            labelUrl?: string | null
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          stock: {
            __typename?: 'ProductStock'
            blockForSale: boolean
            hasImages: boolean
            inStock: boolean
            manageStock: boolean
            qty: number
            minQty: number
            zeronBlockedDelivery: boolean
            zeronSiteStatus: string
          }
        }
    >
  }>
}

export type TilesWidgetFragmentFragment = {
  __typename?: 'TilesWidget'
  identifier: string
  title: string
  subtitle: string
  viewMore?: { __typename?: 'Link'; href: string; text: string; title?: string | null } | null
  rows: Array<{
    __typename?: 'TilesRow'
    cols: Array<
      | {
          __typename: 'CategoryTile'
          title: string
          bgColor: string
          textColor: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          categories: Array<{ __typename?: 'Link'; href: string; text: string; title?: string | null }>
        }
      | {
          __typename: 'DoubleImageTile'
          imageOnePosition: DoubleImageTilePosition
          imageTwoPosition: DoubleImageTilePosition
          imageOne: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          contentOne: {
            __typename?: 'TileContent'
            title: string
            text?: string | null
            icon?: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            } | null
            link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          }
          imageTwo: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          contentTwo: {
            __typename?: 'TileContent'
            title: string
            text?: string | null
            icon?: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            } | null
            link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          }
        }
      | {
          __typename: 'ImageTile'
          bgColor: string
          position: TileContentPosition
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          content: {
            __typename?: 'TileContent'
            title: string
            text?: string | null
            icon?: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            } | null
            link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          }
        }
    >
  }>
}

export type AppImageTileFragment = {
  __typename?: 'ImageTile'
  bgColor: string
  position: TileContentPosition
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  content: {
    __typename?: 'TileContent'
    title: string
    text?: string | null
    icon?: {
      __typename?: 'Image'
      src: string
      mobileSrc?: string | null
      alt?: string | null
      title?: string | null
    } | null
    link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  }
}

export type AppDoubleImageTileFragment = {
  __typename?: 'DoubleImageTile'
  imageOnePosition: DoubleImageTilePosition
  imageTwoPosition: DoubleImageTilePosition
  imageOne: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  contentOne: {
    __typename?: 'TileContent'
    title: string
    text?: string | null
    icon?: {
      __typename?: 'Image'
      src: string
      mobileSrc?: string | null
      alt?: string | null
      title?: string | null
    } | null
    link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  }
  imageTwo: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  contentTwo: {
    __typename?: 'TileContent'
    title: string
    text?: string | null
    icon?: {
      __typename?: 'Image'
      src: string
      mobileSrc?: string | null
      alt?: string | null
      title?: string | null
    } | null
    link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  }
}

export type AppCategoryTileFragment = {
  __typename?: 'CategoryTile'
  title: string
  bgColor: string
  textColor: string
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  categories: Array<{ __typename?: 'Link'; href: string; text: string; title?: string | null }>
}

export type AppTileContentFragment = {
  __typename?: 'TileContent'
  title: string
  text?: string | null
  icon?: {
    __typename?: 'Image'
    src: string
    mobileSrc?: string | null
    alt?: string | null
    title?: string | null
  } | null
  link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
}

export type CustomerLoginMutationVariables = Exact<{
  email: Scalars['String']['input']
  password: Scalars['String']['input']
}>

export type CustomerLoginMutation = {
  __typename?: 'Mutation'
  customerLogin: { __typename?: 'Customer'; id: string; token: string }
}

export type CustomerRegisterMutationVariables = Exact<{
  data: CustomerRegistrationData
}>

export type CustomerRegisterMutation = {
  __typename?: 'Mutation'
  customerRegister: { __typename?: 'RegistrationResponse'; create: boolean; requireConfirmation: boolean }
}

export type CustomerAuthMutationVariables = Exact<{ [key: string]: never }>

export type CustomerAuthMutation = {
  __typename?: 'Mutation'
  customerLoginRefresh: { __typename?: 'Customer'; id: string; token: string }
}

export type CustomerLogoutMutationVariables = Exact<{ [key: string]: never }>

export type CustomerLogoutMutation = { __typename?: 'Mutation'; customerLogout: boolean }

export type CartSaveBnpPaymentMutationVariables = Exact<{
  cartToken: Scalars['String']['input']
  paymentData: BnpPaymentInput
}>

export type CartSaveBnpPaymentMutation = {
  __typename?: 'Mutation'
  cartSaveBNPPayment: {
    __typename?: 'StoreCart'
    id: string
    token: string
    storeCode: string
    currency: string
    couponCode: string
    note: string
    paymentMethod: string
    customer: {
      __typename?: 'OrderCustomer'
      firstName: string
      lastName: string
      email: string
      phone: string
      invoice?: {
        __typename?: 'CustomerInvoice'
        id: string
        type: InvoiceType
        city: string
        address: string
        company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
        individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
      } | null
    }
    items: Array<{
      __typename?: 'StoreCartItem'
      id: string
      sku: string
      baseQty: number
      discountPercent: number
      labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
      product:
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            bundled: Array<{
              __typename?: 'SimpleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }>
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            description: string
            shortDescription: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            videos?: Array<{
              __typename?: 'ProductVideo'
              id: string
              title: string
              description?: string | null
              url: string
              videoType: string
              thumbnail?: string | null
              position: number
            }> | null
            measures: {
              __typename?: 'ProductMeasures'
              base: string
              secondary: string
              secondaryQty: number
              secondaryMeasureUsed: boolean
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
      discountAmount: { __typename?: 'Price'; value: number; currency: string }
      price: { __typename?: 'Price'; value: number; currency: string }
      rowTotal: { __typename?: 'Price'; value: number; currency: string }
    }>
    shipping: {
      __typename?: 'CartShipping'
      availableMethods: Array<ShippingMethodType>
      hasFreeShipping: boolean
      freeShippingAfter?: number | null
      minAmountForFreeShippingMessage?: number | null
      selectedMethod: string
      availableIn: Array<{
        __typename?: 'PraktisStore'
        ID: string
        identity: string
        name: string
        city: string
        address: string
        phone: string
        warehouseCode: string
      }>
      address?: {
        __typename?: 'ShippingAddress'
        id: string
        skus: Array<string>
        method: ShippingMethodType
        officeCode: string
        storeCode: string
        city: string
        cityId: string
        postCode: string
        street: string
      } | null
    }
    totals: Array<{
      __typename?: 'CartTotal'
      code: CartTotalCode
      label: string
      order: number
      amount: { __typename?: 'Price'; value: number; currency: string }
    }>
  }
}

export type SubmitBnpApplicationMutationVariables = Exact<{
  orderNumber: Scalars['String']['input']
}>

export type SubmitBnpApplicationMutation = { __typename?: 'Mutation'; submitBNPApplication: boolean }

export type CartAddMutationVariables = Exact<{
  cartToken: Scalars['String']['input']
  sku: Scalars['String']['input']
  quantity: Scalars['Float']['input']
}>

export type CartAddMutation = {
  __typename?: 'Mutation'
  cartItemAdd: {
    __typename?: 'StoreCart'
    id: string
    token: string
    storeCode: string
    currency: string
    couponCode: string
    note: string
    paymentMethod: string
    customer: {
      __typename?: 'OrderCustomer'
      firstName: string
      lastName: string
      email: string
      phone: string
      invoice?: {
        __typename?: 'CustomerInvoice'
        id: string
        type: InvoiceType
        city: string
        address: string
        company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
        individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
      } | null
    }
    items: Array<{
      __typename?: 'StoreCartItem'
      id: string
      sku: string
      baseQty: number
      discountPercent: number
      labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
      product:
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            bundled: Array<{
              __typename?: 'SimpleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }>
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            description: string
            shortDescription: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            videos?: Array<{
              __typename?: 'ProductVideo'
              id: string
              title: string
              description?: string | null
              url: string
              videoType: string
              thumbnail?: string | null
              position: number
            }> | null
            measures: {
              __typename?: 'ProductMeasures'
              base: string
              secondary: string
              secondaryQty: number
              secondaryMeasureUsed: boolean
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
      discountAmount: { __typename?: 'Price'; value: number; currency: string }
      price: { __typename?: 'Price'; value: number; currency: string }
      rowTotal: { __typename?: 'Price'; value: number; currency: string }
    }>
    shipping: {
      __typename?: 'CartShipping'
      availableMethods: Array<ShippingMethodType>
      hasFreeShipping: boolean
      freeShippingAfter?: number | null
      minAmountForFreeShippingMessage?: number | null
      selectedMethod: string
      availableIn: Array<{
        __typename?: 'PraktisStore'
        ID: string
        identity: string
        name: string
        city: string
        address: string
        phone: string
        warehouseCode: string
      }>
      address?: {
        __typename?: 'ShippingAddress'
        id: string
        skus: Array<string>
        method: ShippingMethodType
        officeCode: string
        storeCode: string
        city: string
        cityId: string
        postCode: string
        street: string
      } | null
    }
    totals: Array<{
      __typename?: 'CartTotal'
      code: CartTotalCode
      label: string
      order: number
      amount: { __typename?: 'Price'; value: number; currency: string }
    }>
  }
}

export type CartUpdateMutationVariables = Exact<{
  cartToken: Scalars['String']['input']
  sku: Scalars['String']['input']
  quantity: Scalars['Float']['input']
}>

export type CartUpdateMutation = {
  __typename?: 'Mutation'
  cartItemUpdate: {
    __typename?: 'StoreCart'
    id: string
    token: string
    storeCode: string
    currency: string
    couponCode: string
    note: string
    paymentMethod: string
    customer: {
      __typename?: 'OrderCustomer'
      firstName: string
      lastName: string
      email: string
      phone: string
      invoice?: {
        __typename?: 'CustomerInvoice'
        id: string
        type: InvoiceType
        city: string
        address: string
        company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
        individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
      } | null
    }
    items: Array<{
      __typename?: 'StoreCartItem'
      id: string
      sku: string
      baseQty: number
      discountPercent: number
      labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
      product:
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            bundled: Array<{
              __typename?: 'SimpleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }>
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            description: string
            shortDescription: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            videos?: Array<{
              __typename?: 'ProductVideo'
              id: string
              title: string
              description?: string | null
              url: string
              videoType: string
              thumbnail?: string | null
              position: number
            }> | null
            measures: {
              __typename?: 'ProductMeasures'
              base: string
              secondary: string
              secondaryQty: number
              secondaryMeasureUsed: boolean
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
      discountAmount: { __typename?: 'Price'; value: number; currency: string }
      price: { __typename?: 'Price'; value: number; currency: string }
      rowTotal: { __typename?: 'Price'; value: number; currency: string }
    }>
    shipping: {
      __typename?: 'CartShipping'
      availableMethods: Array<ShippingMethodType>
      hasFreeShipping: boolean
      freeShippingAfter?: number | null
      minAmountForFreeShippingMessage?: number | null
      selectedMethod: string
      availableIn: Array<{
        __typename?: 'PraktisStore'
        ID: string
        identity: string
        name: string
        city: string
        address: string
        phone: string
        warehouseCode: string
      }>
      address?: {
        __typename?: 'ShippingAddress'
        id: string
        skus: Array<string>
        method: ShippingMethodType
        officeCode: string
        storeCode: string
        city: string
        cityId: string
        postCode: string
        street: string
      } | null
    }
    totals: Array<{
      __typename?: 'CartTotal'
      code: CartTotalCode
      label: string
      order: number
      amount: { __typename?: 'Price'; value: number; currency: string }
    }>
  }
}

export type CartRemoveMutationVariables = Exact<{
  cartToken: Scalars['String']['input']
  sku: Scalars['String']['input']
}>

export type CartRemoveMutation = {
  __typename?: 'Mutation'
  cartItemRemove: {
    __typename?: 'StoreCart'
    id: string
    token: string
    storeCode: string
    currency: string
    couponCode: string
    note: string
    paymentMethod: string
    customer: {
      __typename?: 'OrderCustomer'
      firstName: string
      lastName: string
      email: string
      phone: string
      invoice?: {
        __typename?: 'CustomerInvoice'
        id: string
        type: InvoiceType
        city: string
        address: string
        company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
        individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
      } | null
    }
    items: Array<{
      __typename?: 'StoreCartItem'
      id: string
      sku: string
      baseQty: number
      discountPercent: number
      labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
      product:
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            bundled: Array<{
              __typename?: 'SimpleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }>
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            description: string
            shortDescription: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            videos?: Array<{
              __typename?: 'ProductVideo'
              id: string
              title: string
              description?: string | null
              url: string
              videoType: string
              thumbnail?: string | null
              position: number
            }> | null
            measures: {
              __typename?: 'ProductMeasures'
              base: string
              secondary: string
              secondaryQty: number
              secondaryMeasureUsed: boolean
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
      discountAmount: { __typename?: 'Price'; value: number; currency: string }
      price: { __typename?: 'Price'; value: number; currency: string }
      rowTotal: { __typename?: 'Price'; value: number; currency: string }
    }>
    shipping: {
      __typename?: 'CartShipping'
      availableMethods: Array<ShippingMethodType>
      hasFreeShipping: boolean
      freeShippingAfter?: number | null
      minAmountForFreeShippingMessage?: number | null
      selectedMethod: string
      availableIn: Array<{
        __typename?: 'PraktisStore'
        ID: string
        identity: string
        name: string
        city: string
        address: string
        phone: string
        warehouseCode: string
      }>
      address?: {
        __typename?: 'ShippingAddress'
        id: string
        skus: Array<string>
        method: ShippingMethodType
        officeCode: string
        storeCode: string
        city: string
        cityId: string
        postCode: string
        street: string
      } | null
    }
    totals: Array<{
      __typename?: 'CartTotal'
      code: CartTotalCode
      label: string
      order: number
      amount: { __typename?: 'Price'; value: number; currency: string }
    }>
  }
}

export type ApplyCouponCodeMutationVariables = Exact<{
  cartToken: Scalars['String']['input']
  couponCode: Scalars['String']['input']
}>

export type ApplyCouponCodeMutation = {
  __typename?: 'Mutation'
  cartApplyCoupon: {
    __typename?: 'StoreCart'
    id: string
    token: string
    storeCode: string
    currency: string
    couponCode: string
    note: string
    paymentMethod: string
    customer: {
      __typename?: 'OrderCustomer'
      firstName: string
      lastName: string
      email: string
      phone: string
      invoice?: {
        __typename?: 'CustomerInvoice'
        id: string
        type: InvoiceType
        city: string
        address: string
        company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
        individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
      } | null
    }
    items: Array<{
      __typename?: 'StoreCartItem'
      id: string
      sku: string
      baseQty: number
      discountPercent: number
      labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
      product:
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            bundled: Array<{
              __typename?: 'SimpleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }>
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            description: string
            shortDescription: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            videos?: Array<{
              __typename?: 'ProductVideo'
              id: string
              title: string
              description?: string | null
              url: string
              videoType: string
              thumbnail?: string | null
              position: number
            }> | null
            measures: {
              __typename?: 'ProductMeasures'
              base: string
              secondary: string
              secondaryQty: number
              secondaryMeasureUsed: boolean
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
      discountAmount: { __typename?: 'Price'; value: number; currency: string }
      price: { __typename?: 'Price'; value: number; currency: string }
      rowTotal: { __typename?: 'Price'; value: number; currency: string }
    }>
    shipping: {
      __typename?: 'CartShipping'
      availableMethods: Array<ShippingMethodType>
      hasFreeShipping: boolean
      freeShippingAfter?: number | null
      minAmountForFreeShippingMessage?: number | null
      selectedMethod: string
      availableIn: Array<{
        __typename?: 'PraktisStore'
        ID: string
        identity: string
        name: string
        city: string
        address: string
        phone: string
        warehouseCode: string
      }>
      address?: {
        __typename?: 'ShippingAddress'
        id: string
        skus: Array<string>
        method: ShippingMethodType
        officeCode: string
        storeCode: string
        city: string
        cityId: string
        postCode: string
        street: string
      } | null
    }
    totals: Array<{
      __typename?: 'CartTotal'
      code: CartTotalCode
      label: string
      order: number
      amount: { __typename?: 'Price'; value: number; currency: string }
    }>
  }
}

export type SubscribeMutationVariables = Exact<{
  email: Scalars['String']['input']
}>

export type SubscribeMutation = { __typename?: 'Mutation'; storeNewsletterSubscribe: boolean }

export type CartSaveClientMutationVariables = Exact<{
  cartToken: Scalars['String']['input']
  data: ClientInput
}>

export type CartSaveClientMutation = {
  __typename?: 'Mutation'
  cartSaveClient: {
    __typename?: 'StoreCart'
    id: string
    token: string
    storeCode: string
    currency: string
    couponCode: string
    note: string
    paymentMethod: string
    customer: {
      __typename?: 'OrderCustomer'
      firstName: string
      lastName: string
      email: string
      phone: string
      invoice?: {
        __typename?: 'CustomerInvoice'
        id: string
        type: InvoiceType
        city: string
        address: string
        company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
        individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
      } | null
    }
    items: Array<{
      __typename?: 'StoreCartItem'
      id: string
      sku: string
      baseQty: number
      discountPercent: number
      labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
      product:
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            bundled: Array<{
              __typename?: 'SimpleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }>
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            description: string
            shortDescription: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            videos?: Array<{
              __typename?: 'ProductVideo'
              id: string
              title: string
              description?: string | null
              url: string
              videoType: string
              thumbnail?: string | null
              position: number
            }> | null
            measures: {
              __typename?: 'ProductMeasures'
              base: string
              secondary: string
              secondaryQty: number
              secondaryMeasureUsed: boolean
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
      discountAmount: { __typename?: 'Price'; value: number; currency: string }
      price: { __typename?: 'Price'; value: number; currency: string }
      rowTotal: { __typename?: 'Price'; value: number; currency: string }
    }>
    shipping: {
      __typename?: 'CartShipping'
      availableMethods: Array<ShippingMethodType>
      hasFreeShipping: boolean
      freeShippingAfter?: number | null
      minAmountForFreeShippingMessage?: number | null
      selectedMethod: string
      availableIn: Array<{
        __typename?: 'PraktisStore'
        ID: string
        identity: string
        name: string
        city: string
        address: string
        phone: string
        warehouseCode: string
      }>
      address?: {
        __typename?: 'ShippingAddress'
        id: string
        skus: Array<string>
        method: ShippingMethodType
        officeCode: string
        storeCode: string
        city: string
        cityId: string
        postCode: string
        street: string
      } | null
    }
    totals: Array<{
      __typename?: 'CartTotal'
      code: CartTotalCode
      label: string
      order: number
      amount: { __typename?: 'Price'; value: number; currency: string }
    }>
  }
}

export type GetShippingMethodsMutationVariables = Exact<{
  cartToken: Scalars['String']['input']
  data: ShippingInput
}>

export type GetShippingMethodsMutation = {
  __typename?: 'Mutation'
  cartAvailableShippingMethods: Array<{
    __typename?: 'AvailableShippingMethod'
    method: { __typename?: 'ShippingMethod'; name: string; code: string }
    price: { __typename?: 'Price'; value: number; currency: string }
  }>
}

export type CartSaveShippingMethodMutationVariables = Exact<{
  cartToken: Scalars['String']['input']
  data: ShippingInput
  code: Scalars['String']['input']
}>

export type CartSaveShippingMethodMutation = {
  __typename?: 'Mutation'
  cartSaveShipping: {
    __typename?: 'StoreCart'
    id: string
    token: string
    storeCode: string
    currency: string
    couponCode: string
    note: string
    paymentMethod: string
    customer: {
      __typename?: 'OrderCustomer'
      firstName: string
      lastName: string
      email: string
      phone: string
      invoice?: {
        __typename?: 'CustomerInvoice'
        id: string
        type: InvoiceType
        city: string
        address: string
        company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
        individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
      } | null
    }
    items: Array<{
      __typename?: 'StoreCartItem'
      id: string
      sku: string
      baseQty: number
      discountPercent: number
      labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
      product:
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            bundled: Array<{
              __typename?: 'SimpleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }>
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            description: string
            shortDescription: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            videos?: Array<{
              __typename?: 'ProductVideo'
              id: string
              title: string
              description?: string | null
              url: string
              videoType: string
              thumbnail?: string | null
              position: number
            }> | null
            measures: {
              __typename?: 'ProductMeasures'
              base: string
              secondary: string
              secondaryQty: number
              secondaryMeasureUsed: boolean
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
      discountAmount: { __typename?: 'Price'; value: number; currency: string }
      price: { __typename?: 'Price'; value: number; currency: string }
      rowTotal: { __typename?: 'Price'; value: number; currency: string }
    }>
    shipping: {
      __typename?: 'CartShipping'
      availableMethods: Array<ShippingMethodType>
      hasFreeShipping: boolean
      freeShippingAfter?: number | null
      minAmountForFreeShippingMessage?: number | null
      selectedMethod: string
      availableIn: Array<{
        __typename?: 'PraktisStore'
        ID: string
        identity: string
        name: string
        city: string
        address: string
        phone: string
        warehouseCode: string
      }>
      address?: {
        __typename?: 'ShippingAddress'
        id: string
        skus: Array<string>
        method: ShippingMethodType
        officeCode: string
        storeCode: string
        city: string
        cityId: string
        postCode: string
        street: string
      } | null
    }
    totals: Array<{
      __typename?: 'CartTotal'
      code: CartTotalCode
      label: string
      order: number
      amount: { __typename?: 'Price'; value: number; currency: string }
    }>
  }
}

export type CartSavePaymentMethodMutationVariables = Exact<{
  cartToken: Scalars['String']['input']
  code: Scalars['String']['input']
}>

export type CartSavePaymentMethodMutation = {
  __typename?: 'Mutation'
  cartSavePayment: {
    __typename?: 'StoreCart'
    id: string
    token: string
    storeCode: string
    currency: string
    couponCode: string
    note: string
    paymentMethod: string
    customer: {
      __typename?: 'OrderCustomer'
      firstName: string
      lastName: string
      email: string
      phone: string
      invoice?: {
        __typename?: 'CustomerInvoice'
        id: string
        type: InvoiceType
        city: string
        address: string
        company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
        individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
      } | null
    }
    items: Array<{
      __typename?: 'StoreCartItem'
      id: string
      sku: string
      baseQty: number
      discountPercent: number
      labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
      product:
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            bundled: Array<{
              __typename?: 'SimpleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }>
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            description: string
            shortDescription: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            videos?: Array<{
              __typename?: 'ProductVideo'
              id: string
              title: string
              description?: string | null
              url: string
              videoType: string
              thumbnail?: string | null
              position: number
            }> | null
            measures: {
              __typename?: 'ProductMeasures'
              base: string
              secondary: string
              secondaryQty: number
              secondaryMeasureUsed: boolean
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
      discountAmount: { __typename?: 'Price'; value: number; currency: string }
      price: { __typename?: 'Price'; value: number; currency: string }
      rowTotal: { __typename?: 'Price'; value: number; currency: string }
    }>
    shipping: {
      __typename?: 'CartShipping'
      availableMethods: Array<ShippingMethodType>
      hasFreeShipping: boolean
      freeShippingAfter?: number | null
      minAmountForFreeShippingMessage?: number | null
      selectedMethod: string
      availableIn: Array<{
        __typename?: 'PraktisStore'
        ID: string
        identity: string
        name: string
        city: string
        address: string
        phone: string
        warehouseCode: string
      }>
      address?: {
        __typename?: 'ShippingAddress'
        id: string
        skus: Array<string>
        method: ShippingMethodType
        officeCode: string
        storeCode: string
        city: string
        cityId: string
        postCode: string
        street: string
      } | null
    }
    totals: Array<{
      __typename?: 'CartTotal'
      code: CartTotalCode
      label: string
      order: number
      amount: { __typename?: 'Price'; value: number; currency: string }
    }>
  }
}

export type GetPaymentMethodsMutationVariables = Exact<{
  cartToken: Scalars['String']['input']
}>

export type GetPaymentMethodsMutation = {
  __typename?: 'Mutation'
  cartAvailablePaymentMethods: Array<{ __typename?: 'PaymentMethod'; name: string; code: string; extraContent: string }>
}

export type CreateOrderMutationVariables = Exact<{
  cartToken: Scalars['String']['input']
  data: NewOrderInput
}>

export type CreateOrderMutation = {
  __typename?: 'Mutation'
  placeOrder?: {
    __typename?: 'PlaceOrderResponse'
    orderNumber: string
    status: { __typename?: 'OrderStatus'; label: string; code: string }
    redirect: {
      __typename?: 'OrderRedirect'
      url: string
      data: Array<{ __typename?: 'MapValue'; key: string; value: string }>
    }
  } | null
}

export type SendContactMessageMutationVariables = Exact<{
  input: ContactInput
}>

export type SendContactMessageMutation = { __typename?: 'Mutation'; storeSendContactMessage: boolean }

export type UpdateCustomerMutationVariables = Exact<{
  data: CustomerUpdateInput
}>

export type UpdateCustomerMutation = {
  __typename?: 'Mutation'
  customerUpdateInfo: {
    __typename?: 'Customer'
    id: string
    token: string
    isSubscribed: boolean
    firstName: string
    lastName: string
    email: string
    defaultBillingAddressID?: string | null
    defaultShippingAddressID?: string | null
    addresses: Array<{
      __typename?: 'CustomerAddress'
      id: string
      firstName: string
      lastName: string
      phone: string
      companyName?: string | null
      country: string
      city: string
      cityID: string
      postCode: string
      street: string
    }>
  }
}

export type UpdateCustomerPasswordMutationVariables = Exact<{
  oldPassword: Scalars['String']['input']
  newPassword: Scalars['String']['input']
}>

export type UpdateCustomerPasswordMutation = {
  __typename?: 'Mutation'
  customerUpdatePassword: { __typename?: 'Customer'; id: string }
}

export type CreateCustomerAddressMutationVariables = Exact<{
  data: CustomerAddressInput
}>

export type CreateCustomerAddressMutation = {
  __typename?: 'Mutation'
  customerAddressAdd: Array<{
    __typename?: 'CustomerAddress'
    id: string
    firstName: string
    lastName: string
    phone: string
    companyName?: string | null
    country: string
    city: string
    cityID: string
    postCode: string
    street: string
  }>
}

export type UpdateCustomerAddressMutationVariables = Exact<{
  data: CustomerAddressInput
  addressID: Scalars['ID']['input']
}>

export type UpdateCustomerAddressMutation = {
  __typename?: 'Mutation'
  customerAddressUpdate: Array<{
    __typename?: 'CustomerAddress'
    id: string
    firstName: string
    lastName: string
    phone: string
    companyName?: string | null
    country: string
    city: string
    cityID: string
    postCode: string
    street: string
  }>
}

export type DeleteCustomerAddressMutationVariables = Exact<{
  addressID: Scalars['ID']['input']
}>

export type DeleteCustomerAddressMutation = {
  __typename?: 'Mutation'
  customerAddressRemove: Array<{
    __typename?: 'CustomerAddress'
    id: string
    firstName: string
    lastName: string
    phone: string
    companyName?: string | null
    country: string
    city: string
    cityID: string
    postCode: string
    street: string
  }>
}

export type ForgotPasswordMutationVariables = Exact<{
  email: Scalars['String']['input']
}>

export type ForgotPasswordMutation = { __typename?: 'Mutation'; customerPasswordForgot: boolean }

export type CustomerPasswordResetMutationVariables = Exact<{
  customerId: Scalars['ID']['input']
  password: Scalars['String']['input']
  resetToken: Scalars['String']['input']
}>

export type CustomerPasswordResetMutation = { __typename?: 'Mutation'; customerPasswordReset: boolean }

export type SendInquiryMutationVariables = Exact<{
  input: StoreInquiryInput
}>

export type SendInquiryMutation = { __typename?: 'Mutation'; storeSendInquiry: boolean }

export type UnsubscribeMutationVariables = Exact<{
  id: Scalars['ID']['input']
  code: Scalars['String']['input']
}>

export type UnsubscribeMutation = { __typename?: 'Mutation'; storeNewsletterUnsubscribe: boolean }

export type WishlistAddMutationVariables = Exact<{
  sku: Scalars['String']['input']
}>

export type WishlistAddMutation = {
  __typename?: 'Mutation'
  customerWishlistAdd: { __typename?: 'CustomerWishlist'; skus: Array<string> }
}

export type WishlistRemoveMutationVariables = Exact<{
  sku: Scalars['String']['input']
}>

export type WishlistRemoveMutation = {
  __typename?: 'Mutation'
  customerWishlistRemove: { __typename?: 'CustomerWishlist'; skus: Array<string> }
}

export type GetAvailableServicesQueryVariables = Exact<{ [key: string]: never }>

export type GetAvailableServicesQuery = {
  __typename?: 'Query'
  availableServices: Array<{ __typename?: 'Service'; id: string; name: string; url: string; iconUrl: string }>
}

export type GetBlogPostsQueryVariables = Exact<{
  page: Scalars['Int']['input']
  size: Scalars['Int']['input']
}>

export type GetBlogPostsQuery = {
  __typename?: 'Query'
  getBlogPosts: {
    __typename?: 'MagentoBlog'
    currentPage: number
    totalPages: number
    posts: Array<{
      __typename?: 'BlogPost'
      urlKey: string
      title: string
      summary: string
      previewImageUrl: string
      content: string
      mainImageUrl: string
      publishedAt: string
    }>
  }
}

export type GetBlogPostQueryVariables = Exact<{
  identifier: Scalars['String']['input']
}>

export type GetBlogPostQuery = {
  __typename?: 'Query'
  getBlogPost: {
    __typename?: 'BlogPost'
    urlKey: string
    title: string
    summary: string
    previewImageUrl: string
    content: string
    mainImageUrl: string
    publishedAt: string
  }
}

export type GetFeaturedBlogPostQueryVariables = Exact<{ [key: string]: never }>

export type GetFeaturedBlogPostQuery = {
  __typename?: 'Query'
  getFeaturedBlogPost: {
    __typename?: 'BlogPost'
    urlKey: string
    title: string
    summary: string
    previewImageUrl: string
    content: string
    mainImageUrl: string
    publishedAt: string
  }
}

export type GetBnpPricingSchemesQueryVariables = Exact<{
  goodTypeIds: Scalars['String']['input']
  principal: Scalars['Float']['input']
  downPayment: Scalars['Float']['input']
}>

export type GetBnpPricingSchemesQuery = {
  __typename?: 'Query'
  getBNPPricingSchemes: Array<{ __typename?: 'BNPPricingScheme'; id: string; name: string }>
}

export type GetCreditCalculatorBnpParibasQueryVariables = Exact<{
  sku: Scalars['String']['input']
  downPayment: Scalars['Float']['input']
  qty: Scalars['Int']['input']
}>

export type GetCreditCalculatorBnpParibasQuery = {
  __typename?: 'Query'
  getCreditCalculatorBNPParibas: Array<{
    __typename?: 'BNPVariantGroup'
    schemeId: string
    variants: Array<{
      __typename?: 'PricingVariant'
      id: string
      apr: string
      correctDownpaymentAmount: string
      installmentAmount: string
      maturity: string
      nir: string
      pricingSchemeId: string
      pricingSchemeName: string
      totalRepaymentAmount: string
      processingFeeAmount: string
    }>
  }>
}

export type GetCreditCalculatorQuoteBnpParibasQueryVariables = Exact<{
  cartToken: Scalars['String']['input']
  downPayment: Scalars['Float']['input']
}>

export type GetCreditCalculatorQuoteBnpParibasQuery = {
  __typename?: 'Query'
  getCreditCalculatorBNPParibasForQuote: Array<{
    __typename?: 'BNPVariantGroup'
    schemeId: string
    variants: Array<{
      __typename?: 'PricingVariant'
      id: string
      apr: string
      correctDownpaymentAmount: string
      installmentAmount: string
      maturity: string
      nir: string
      pricingSchemeId: string
      pricingSchemeName: string
      totalRepaymentAmount: string
      processingFeeAmount: string
    }>
  }>
}

export type CalculateBnpLoanQueryVariables = Exact<{
  cartToken: Scalars['String']['input']
  downPayment: Scalars['Float']['input']
  pricingVariantId: Scalars['Int']['input']
}>

export type CalculateBnpLoanQuery = {
  __typename?: 'Query'
  calculateBNPLoan: {
    __typename?: 'LoanCalculation'
    apr: string
    correctDownpaymentAmount: string
    installmentAmount: string
    maturity: string
    nir: string
    pricingSchemeId: string
    pricingSchemeName: string
    pricingVariantId: string
    processingFeeAmount: string
    totalRepaymentAmount: string
  }
}

export type GetBrandsQueryVariables = Exact<{
  featured: Scalars['Boolean']['input']
}>

export type GetBrandsQuery = {
  __typename?: 'Query'
  getBrands: Array<{
    __typename?: 'Brand'
    name: string
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
    url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  }>
}

export type GetEmptyCartQueryVariables = Exact<{ [key: string]: never }>

export type GetEmptyCartQuery = { __typename?: 'Query'; getNewCart: { __typename?: 'StoreCart'; token: string } }

export type GetCartByIdQueryVariables = Exact<{
  cart: Scalars['String']['input']
}>

export type GetCartByIdQuery = {
  __typename?: 'Query'
  getCart: {
    __typename?: 'StoreCart'
    id: string
    token: string
    storeCode: string
    currency: string
    couponCode: string
    note: string
    paymentMethod: string
    customer: {
      __typename?: 'OrderCustomer'
      firstName: string
      lastName: string
      email: string
      phone: string
      invoice?: {
        __typename?: 'CustomerInvoice'
        id: string
        type: InvoiceType
        city: string
        address: string
        company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
        individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
      } | null
    }
    items: Array<{
      __typename?: 'StoreCartItem'
      id: string
      sku: string
      baseQty: number
      discountPercent: number
      labels: { __typename?: 'StoreCartItemLabels'; freeShipping: boolean; usePallet: boolean }
      product:
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            bundled: Array<{
              __typename?: 'SimpleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }>
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            description: string
            shortDescription: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            videos?: Array<{
              __typename?: 'ProductVideo'
              id: string
              title: string
              description?: string | null
              url: string
              videoType: string
              thumbnail?: string | null
              position: number
            }> | null
            measures: {
              __typename?: 'ProductMeasures'
              base: string
              secondary: string
              secondaryQty: number
              secondaryMeasureUsed: boolean
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
      discountAmount: { __typename?: 'Price'; value: number; currency: string }
      price: { __typename?: 'Price'; value: number; currency: string }
      rowTotal: { __typename?: 'Price'; value: number; currency: string }
    }>
    shipping: {
      __typename?: 'CartShipping'
      availableMethods: Array<ShippingMethodType>
      hasFreeShipping: boolean
      freeShippingAfter?: number | null
      minAmountForFreeShippingMessage?: number | null
      selectedMethod: string
      availableIn: Array<{
        __typename?: 'PraktisStore'
        ID: string
        identity: string
        name: string
        city: string
        address: string
        phone: string
        warehouseCode: string
      }>
      address?: {
        __typename?: 'ShippingAddress'
        id: string
        skus: Array<string>
        method: ShippingMethodType
        officeCode: string
        storeCode: string
        city: string
        cityId: string
        postCode: string
        street: string
      } | null
    }
    totals: Array<{
      __typename?: 'CartTotal'
      code: CartTotalCode
      label: string
      order: number
      amount: { __typename?: 'Price'; value: number; currency: string }
    }>
  }
}

export type GetTbiCreditQueryVariables = Exact<{
  sku: Scalars['String']['input']
}>

export type GetTbiCreditQuery = {
  __typename?: 'Query'
  getCreditCalculatorTBIBank: {
    __typename?: 'TbiConfiguration'
    tbi_minstojnost: string
    tbi_minstojnost_bnpl: string
    tbi_maxstojnost: string
    tbi_maxstojnost_bnpl: string
    tbi_zaglavie: string
    tbi_custom_button_status: string
    tbi_purcent_default: string
    reklama: string
    tbi_5m_purcent_default: string
    tbi_5m: string
    tbi_5m_categories: string
    tbi_5m_manufacturers: string
    tbi_5m_min: string
    tbi_5m_max: string
    tbi_5m_pv: string
    tbi_4m: string
    tbi_4m_categories: string
    tbi_4m_manufacturers: string
    tbi_4m_min: string
    tbi_4m_max: string
    tbi_4m_pv: string
    tbi_6m: string
    tbi_6m_categories: string
    tbi_6m_manufacturers: string
    tbi_6m_min: string
    tbi_6m_max: string
    tbi_6m_pv: string
    tbi_9m: string
    tbi_9m_categories: string
    tbi_9m_manufacturers: string
    tbi_9m_min: string
    tbi_9m_max: string
    tbi_9m_pv: string
    tbi_12m: string
    tbi_12m_categories: string
    tbi_12m_manufacturers: string
    tbi_12m_min: string
    tbi_12m_max: string
    tbi_12m_pv: string
    tbi_3m_purcent: string
    tbi_4m_purcent: string
    tbi_6m_purcent: string
    tbi_7m_purcent: string
    tbi_9m_purcent: string
    tbi_11m_purcent: string
    tbi_12m_purcent: string
    tbi_15m_purcent: string
    tbi_18m_purcent: string
    tbi_24m_purcent: string
    tbi_30m_purcent: string
    tbi_36m_purcent: string
    tbi_42m_purcent: string
    tbi_48m_purcent: string
    tbi_54m_purcent: string
    tbi_60m_purcent: string
    tbi_over_5000: string
    tbi_status: string
    tbi_bnpl: string
    tbi_button_status: string
    tbi_button_text_visible: string
    tbi_button_kvadrat: string
    tbi_is_direct: string
    tbi_is_cart: string
    tbi_eur: string
  }
}

export type GetBnpCreditQueryVariables = Exact<{
  sku: Scalars['String']['input']
  downPayment: Scalars['Float']['input']
  qty: Scalars['Int']['input']
}>

export type GetBnpCreditQuery = {
  __typename?: 'Query'
  getCreditCalculatorBNPParibas: Array<{
    __typename?: 'BNPVariantGroup'
    schemeId: string
    variants: Array<{
      __typename?: 'PricingVariant'
      id: string
      apr: string
      correctDownpaymentAmount: string
      installmentAmount: string
      maturity: string
      nir: string
      pricingSchemeId: string
      pricingSchemeName: string
      totalRepaymentAmount: string
      processingFeeAmount: string
    }>
  }>
}

export type TbiCreditDataFragment = {
  __typename?: 'TbiConfiguration'
  tbi_minstojnost: string
  tbi_minstojnost_bnpl: string
  tbi_maxstojnost: string
  tbi_maxstojnost_bnpl: string
  tbi_zaglavie: string
  tbi_custom_button_status: string
  tbi_purcent_default: string
  reklama: string
  tbi_5m_purcent_default: string
  tbi_5m: string
  tbi_5m_categories: string
  tbi_5m_manufacturers: string
  tbi_5m_min: string
  tbi_5m_max: string
  tbi_5m_pv: string
  tbi_4m: string
  tbi_4m_categories: string
  tbi_4m_manufacturers: string
  tbi_4m_min: string
  tbi_4m_max: string
  tbi_4m_pv: string
  tbi_6m: string
  tbi_6m_categories: string
  tbi_6m_manufacturers: string
  tbi_6m_min: string
  tbi_6m_max: string
  tbi_6m_pv: string
  tbi_9m: string
  tbi_9m_categories: string
  tbi_9m_manufacturers: string
  tbi_9m_min: string
  tbi_9m_max: string
  tbi_9m_pv: string
  tbi_12m: string
  tbi_12m_categories: string
  tbi_12m_manufacturers: string
  tbi_12m_min: string
  tbi_12m_max: string
  tbi_12m_pv: string
  tbi_3m_purcent: string
  tbi_4m_purcent: string
  tbi_6m_purcent: string
  tbi_7m_purcent: string
  tbi_9m_purcent: string
  tbi_11m_purcent: string
  tbi_12m_purcent: string
  tbi_15m_purcent: string
  tbi_18m_purcent: string
  tbi_24m_purcent: string
  tbi_30m_purcent: string
  tbi_36m_purcent: string
  tbi_42m_purcent: string
  tbi_48m_purcent: string
  tbi_54m_purcent: string
  tbi_60m_purcent: string
  tbi_over_5000: string
  tbi_status: string
  tbi_bnpl: string
  tbi_button_status: string
  tbi_button_text_visible: string
  tbi_button_kvadrat: string
  tbi_is_direct: string
  tbi_is_cart: string
  tbi_eur: string
}

export type CustomerDataFullFragment = {
  __typename?: 'Customer'
  defaultBillingAddressID?: string | null
  defaultShippingAddressID?: string | null
  firstName: string
  lastName: string
  isSubscribed: boolean
  email: string
  addresses: Array<{
    __typename?: 'CustomerAddress'
    id: string
    firstName: string
    lastName: string
    phone: string
    companyName?: string | null
    country: string
    city: string
    cityID: string
    postCode: string
    street: string
  }>
}

export type CustomerDataQueryVariables = Exact<{ [key: string]: never }>

export type CustomerDataQuery = {
  __typename?: 'Query'
  customerData: {
    __typename?: 'Customer'
    defaultBillingAddressID?: string | null
    defaultShippingAddressID?: string | null
    firstName: string
    lastName: string
    isSubscribed: boolean
    email: string
    addresses: Array<{
      __typename?: 'CustomerAddress'
      id: string
      firstName: string
      lastName: string
      phone: string
      companyName?: string | null
      country: string
      city: string
      cityID: string
      postCode: string
      street: string
    }>
  }
}

export type GetEcontOfficesQueryVariables = Exact<{
  cityId: Scalars['String']['input']
}>

export type GetEcontOfficesQuery = {
  __typename?: 'Query'
  getEcontOffice: Array<{
    __typename?: 'EcontOffice'
    id: string
    code: string
    name: string
    nameEn: string
    phones: Array<string>
    isAPS: boolean
    isMPS: boolean
    adderess: {
      __typename?: 'EcontOfficeAddress'
      fullAddress: string
      fullAddressEn: string
      city: {
        __typename?: 'EcontCity'
        id: string
        name: string
        nameEn: string
        postCode: string
        regionCode: string
        regionName: string
        regionNameEn: string
        type: string
        country: {
          __typename?: 'EcontCountry'
          code2: string
          code3: string
          id: string
          isEU: boolean
          name: string
          nameEn: string
        }
      }
      location: { __typename?: 'EcontOfficeLocation'; latitude: number; longitude: number }
    }
  } | null>
}

export type GetEcontCitiesQueryVariables = Exact<{
  country: Scalars['String']['input']
}>

export type GetEcontCitiesQuery = {
  __typename?: 'Query'
  getEcontCity: Array<{
    __typename?: 'EcontCity'
    id: string
    name: string
    nameEn: string
    postCode: string
    regionCode: string
    regionName: string
    regionNameEn: string
    type: string
    country: {
      __typename?: 'EcontCountry'
      code2: string
      code3: string
      id: string
      isEU: boolean
      name: string
      nameEn: string
    }
  } | null>
}

export type GetHomePageDataQueryVariables = Exact<{ [key: string]: never }>

export type GetHomePageDataQuery = {
  __typename?: 'Query'
  getHomepage?: {
    __typename?: 'HomePage'
    heroSlider: {
      __typename?: 'CarouselWidget'
      identifier: string
      slides: Array<{
        __typename?: 'CarouselSlide'
        title: string
        description: string
        brand?: {
          __typename?: 'Brand'
          name: string
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        } | null
        features?: Array<{
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }> | null
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        link?: { __typename?: 'Link'; href: string; text: string; title?: string | null } | null
        price?: { __typename?: 'Price'; value: number; currency: string } | null
        priceLabels?: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        } | null
      }>
    }
    widgets: Array<
      | {
          __typename: 'CarouselWidget'
          identifier: string
          slides: Array<{
            __typename?: 'CarouselSlide'
            title: string
            description: string
            brand?: {
              __typename?: 'Brand'
              name: string
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            features?: Array<{
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }> | null
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            link?: { __typename?: 'Link'; href: string; text: string; title?: string | null } | null
            price?: { __typename?: 'Price'; value: number; currency: string } | null
            priceLabels?: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            } | null
          }>
        }
      | {
          __typename: 'CategoryLinkWidget'
          title: string
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          image: {
            __typename?: 'Image'
            alt?: string | null
            src: string
            title?: string | null
            mobileSrc?: string | null
          }
        }
      | { __typename: 'HtmlWidget'; html?: string | null }
      | {
          __typename: 'NewsWidget'
          title: string
          subtitle: string
          identifier: string
          link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          articles: Array<{
            __typename?: 'Article'
            title: string
            description: string
            image: {
              __typename?: 'Image'
              alt?: string | null
              mobileSrc?: string | null
              src: string
              title?: string | null
            }
            link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          }>
        }
      | {
          __typename: 'ProductsSliderWidget'
          identifier: string
          title: string
          subtitle: string
          tabs: Array<{
            __typename?: 'ProductsTab'
            title: string
            products: Array<
              | {
                  __typename: 'BundleProduct'
                  id: string
                  sku: string
                  name: string
                  urlKey: string
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  price: {
                    __typename?: 'ProductPrice'
                    price: { __typename?: 'Price'; value: number; currency: string }
                    special?: { __typename?: 'Price'; value: number; currency: string } | null
                  }
                  labels: {
                    __typename?: 'MagentoLabels'
                    warrantyMonths: number
                    buyCheap?: boolean | null
                    freeDelivery?: boolean | null
                    fromBrochure?: boolean | null
                    other?: Array<string> | null
                  }
                }
              | {
                  __typename: 'SimpleProduct'
                  id: string
                  sku: string
                  name: string
                  urlKey: string
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  price: {
                    __typename?: 'ProductPrice'
                    price: { __typename?: 'Price'; value: number; currency: string }
                    special?: { __typename?: 'Price'; value: number; currency: string } | null
                  }
                  energyLabel?: {
                    __typename?: 'EnergyLabel'
                    infoUrl?: string | null
                    labelUrl?: string | null
                    image: {
                      __typename?: 'Image'
                      src: string
                      mobileSrc?: string | null
                      alt?: string | null
                      title?: string | null
                    }
                  } | null
                  labels: {
                    __typename?: 'MagentoLabels'
                    warrantyMonths: number
                    buyCheap?: boolean | null
                    freeDelivery?: boolean | null
                    fromBrochure?: boolean | null
                    other?: Array<string> | null
                  }
                  stock: {
                    __typename?: 'ProductStock'
                    blockForSale: boolean
                    hasImages: boolean
                    inStock: boolean
                    manageStock: boolean
                    qty: number
                    minQty: number
                    zeronBlockedDelivery: boolean
                    zeronSiteStatus: string
                  }
                }
            >
          }>
        }
      | {
          __typename: 'ServiceWidget'
          title: string
          subtitle: string
          link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          services: Array<{ __typename?: 'Service'; id: string; name: string; url: string; iconUrl: string }>
          availableStores: Array<{
            __typename?: 'PraktisStore'
            name: string
            identity: string
            address: string
            email: string
            phone: string
            city: string
            displayOrder: number
            gallery: Array<{
              __typename?: 'StoreImage'
              ID: string
              praktisStoreID: string
              position: number
              alt?: string | null
              src: string
              title?: string | null
              mobileSrc?: string | null
              isPrimary: boolean
            }>
            businessHours: Array<{ __typename?: 'StoreSchedule'; open: string; close: string; day: string }>
          }>
          form: { __typename?: 'FormData'; formId: string; text: string; title: string }
        }
      | {
          __typename: 'TilesWidget'
          identifier: string
          title: string
          subtitle: string
          viewMore?: { __typename?: 'Link'; href: string; text: string; title?: string | null } | null
          rows: Array<{
            __typename?: 'TilesRow'
            cols: Array<
              | {
                  __typename: 'CategoryTile'
                  title: string
                  bgColor: string
                  textColor: string
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  categories: Array<{ __typename?: 'Link'; href: string; text: string; title?: string | null }>
                }
              | {
                  __typename: 'DoubleImageTile'
                  imageOnePosition: DoubleImageTilePosition
                  imageTwoPosition: DoubleImageTilePosition
                  imageOne: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  contentOne: {
                    __typename?: 'TileContent'
                    title: string
                    text?: string | null
                    icon?: {
                      __typename?: 'Image'
                      src: string
                      mobileSrc?: string | null
                      alt?: string | null
                      title?: string | null
                    } | null
                    link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
                  }
                  imageTwo: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  contentTwo: {
                    __typename?: 'TileContent'
                    title: string
                    text?: string | null
                    icon?: {
                      __typename?: 'Image'
                      src: string
                      mobileSrc?: string | null
                      alt?: string | null
                      title?: string | null
                    } | null
                    link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
                  }
                }
              | {
                  __typename: 'ImageTile'
                  bgColor: string
                  position: TileContentPosition
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  content: {
                    __typename?: 'TileContent'
                    title: string
                    text?: string | null
                    icon?: {
                      __typename?: 'Image'
                      src: string
                      mobileSrc?: string | null
                      alt?: string | null
                      title?: string | null
                    } | null
                    link: { __typename?: 'Link'; href: string; text: string; title?: string | null }
                  }
                }
            >
          }>
        }
    >
  } | null
  getBrands: Array<{
    __typename?: 'Brand'
    name: string
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
    url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  }>
}

export type AppCategoryLinkWidgetFragment = {
  __typename?: 'CategoryLinkWidget'
  title: string
  url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  image: { __typename?: 'Image'; alt?: string | null; src: string; title?: string | null; mobileSrc?: string | null }
}

export type AppHtmlWidgetFragment = { __typename?: 'HtmlWidget'; html?: string | null }

export type GetCustomerOrdersQueryVariables = Exact<{ [key: string]: never }>

export type GetCustomerOrdersQuery = {
  __typename?: 'Query'
  customerData: {
    __typename?: 'Customer'
    id: string
    orders: Array<{
      __typename?: 'StoreOrder'
      incrementId: string
      state: string
      couponCode: string
      protectCode: string
      createAt: string
      note: string
      status: { __typename?: 'OrderStatus'; label: string; code: string }
      items: Array<{
        __typename?: 'OrderItem'
        id: string
        sku: string
        baseQty: number
        discountPercent: number
        product:
          | {
              __typename: 'BundleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              brand?: {
                __typename?: 'Brand'
                name: string
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
              } | null
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              bundled: Array<{
                __typename?: 'SimpleProduct'
                id: string
                sku: string
                name: string
                description: string
                shortDescription: string
                urlKey: string
                measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                price: {
                  __typename?: 'ProductPrice'
                  price: { __typename?: 'Price'; value: number; currency: string }
                  special?: { __typename?: 'Price'; value: number; currency: string } | null
                }
                gallery: Array<{
                  __typename?: 'GalleryImage'
                  position: number
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                }>
                labels: {
                  __typename?: 'MagentoLabels'
                  warrantyMonths: number
                  buyCheap?: boolean | null
                  freeDelivery?: boolean | null
                  fromBrochure?: boolean | null
                  other?: Array<string> | null
                }
                skuAvailability: Array<{
                  __typename?: 'StoreAvailabilityItem'
                  available: AvailabilityStatus
                  sample: boolean
                  store: {
                    __typename?: 'PraktisStore'
                    identity: string
                    warehouseCode: string
                    name: string
                    displayOrder: number
                    acceptOrders: boolean
                    location: { __typename?: 'MapLocation'; lat: number; lng: number }
                  }
                }>
              }>
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }
          | {
              __typename: 'SimpleProduct'
              id: string
              sku: string
              name: string
              urlKey: string
              description: string
              shortDescription: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              videos?: Array<{
                __typename?: 'ProductVideo'
                id: string
                title: string
                description?: string | null
                url: string
                videoType: string
                thumbnail?: string | null
                position: number
              }> | null
              measures: {
                __typename?: 'ProductMeasures'
                base: string
                secondary: string
                secondaryQty: number
                secondaryMeasureUsed: boolean
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
              stock: {
                __typename?: 'ProductStock'
                blockForSale: boolean
                hasImages: boolean
                inStock: boolean
                manageStock: boolean
                qty: number
                minQty: number
                zeronBlockedDelivery: boolean
                zeronSiteStatus: string
              }
              brand?: {
                __typename?: 'Brand'
                name: string
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
              } | null
              energyLabel?: {
                __typename?: 'EnergyLabel'
                infoUrl?: string | null
                labelUrl?: string | null
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              } | null
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
            }
        discountAmount: { __typename?: 'Price'; value: number; currency: string }
        price: { __typename?: 'Price'; value: number; currency: string }
        priceWithoutDiscount: { __typename?: 'Price'; value: number; currency: string }
        rowTotal: { __typename?: 'Price'; value: number; currency: string }
        rowTotalWithoutDiscount: { __typename?: 'Price'; value: number; currency: string }
      }>
      totals: Array<{
        __typename?: 'CartTotal'
        code: CartTotalCode
        label: string
        order: number
        amount: { __typename?: 'Price'; value: number; currency: string }
      }>
      shippingMethod: { __typename?: 'ShippingMethod'; name: string; code: string }
      paymentMethod: { __typename?: 'PaymentMethod'; name: string; code: string; extraContent: string }
      shippingAddress: {
        __typename?: 'OrderAddress'
        firstName: string
        lastName: string
        email: string
        telephone: string
        city: string
        postcode: string
        street: string
      }
      invoice?: {
        __typename?: 'CustomerInvoice'
        id: string
        type: InvoiceType
        city: string
        address: string
        company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
        individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
      } | null
    }>
  }
}

export type DataFragment = {
  __typename?: 'Customer'
  id: string
  orders: Array<{
    __typename?: 'StoreOrder'
    incrementId: string
    state: string
    couponCode: string
    protectCode: string
    createAt: string
    note: string
    status: { __typename?: 'OrderStatus'; label: string; code: string }
    items: Array<{
      __typename?: 'OrderItem'
      id: string
      sku: string
      baseQty: number
      discountPercent: number
      product:
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            bundled: Array<{
              __typename?: 'SimpleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }>
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            description: string
            shortDescription: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            videos?: Array<{
              __typename?: 'ProductVideo'
              id: string
              title: string
              description?: string | null
              url: string
              videoType: string
              thumbnail?: string | null
              position: number
            }> | null
            measures: {
              __typename?: 'ProductMeasures'
              base: string
              secondary: string
              secondaryQty: number
              secondaryMeasureUsed: boolean
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
      discountAmount: { __typename?: 'Price'; value: number; currency: string }
      price: { __typename?: 'Price'; value: number; currency: string }
      priceWithoutDiscount: { __typename?: 'Price'; value: number; currency: string }
      rowTotal: { __typename?: 'Price'; value: number; currency: string }
      rowTotalWithoutDiscount: { __typename?: 'Price'; value: number; currency: string }
    }>
    totals: Array<{
      __typename?: 'CartTotal'
      code: CartTotalCode
      label: string
      order: number
      amount: { __typename?: 'Price'; value: number; currency: string }
    }>
    shippingMethod: { __typename?: 'ShippingMethod'; name: string; code: string }
    paymentMethod: { __typename?: 'PaymentMethod'; name: string; code: string; extraContent: string }
    shippingAddress: {
      __typename?: 'OrderAddress'
      firstName: string
      lastName: string
      email: string
      telephone: string
      city: string
      postcode: string
      street: string
    }
    invoice?: {
      __typename?: 'CustomerInvoice'
      id: string
      type: InvoiceType
      city: string
      address: string
      company?: { __typename?: 'CompanyInvoice'; name: string; mol: string; eik: string; vat: string } | null
      individual?: { __typename?: 'IndividualInvoice'; name: string; egn: string; vat: string } | null
    } | null
  }>
}

type SearchResultProduct_BundleProduct_Fragment = {
  __typename?: 'BundleProduct'
  sku: string
  shortDescription: string
  name: string
  urlKey: string
  image: { __typename?: 'Image'; src: string; title?: string | null }
  price: {
    __typename?: 'ProductPrice'
    specialFrom?: string | null
    specialTo?: string | null
    price: { __typename?: 'Price'; value: number; currency: string }
    special?: { __typename?: 'Price'; value: number; currency: string } | null
  }
}

type SearchResultProduct_SimpleProduct_Fragment = {
  __typename?: 'SimpleProduct'
  sku: string
  shortDescription: string
  name: string
  urlKey: string
  image: { __typename?: 'Image'; src: string; title?: string | null }
  price: {
    __typename?: 'ProductPrice'
    specialFrom?: string | null
    specialTo?: string | null
    price: { __typename?: 'Price'; value: number; currency: string }
    special?: { __typename?: 'Price'; value: number; currency: string } | null
  }
}

export type SearchResultProductFragment =
  | SearchResultProduct_BundleProduct_Fragment
  | SearchResultProduct_SimpleProduct_Fragment

export type SearchFieldQueryVariables = Exact<{
  text: Scalars['String']['input']
}>

export type SearchFieldQuery = {
  __typename?: 'Query'
  search: {
    __typename?: 'SearchResults'
    popularTerms: Array<string>
    totalItems: number
    categories: Array<{ __typename?: 'SearchCategory'; name: string; url: string }>
    products: Array<
      | {
          __typename: 'BundleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          brand?: {
            __typename?: 'Brand'
            name: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          bundled: Array<{
            __typename?: 'SimpleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }>
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }
      | {
          __typename: 'SimpleProduct'
          id: string
          sku: string
          name: string
          urlKey: string
          description: string
          shortDescription: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          videos?: Array<{
            __typename?: 'ProductVideo'
            id: string
            title: string
            description?: string | null
            url: string
            videoType: string
            thumbnail?: string | null
            position: number
          }> | null
          measures: {
            __typename?: 'ProductMeasures'
            base: string
            secondary: string
            secondaryQty: number
            secondaryMeasureUsed: boolean
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
          stock: {
            __typename?: 'ProductStock'
            blockForSale: boolean
            hasImages: boolean
            inStock: boolean
            manageStock: boolean
            qty: number
            minQty: number
            zeronBlockedDelivery: boolean
            zeronSiteStatus: string
          }
          brand?: {
            __typename?: 'Brand'
            name: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          } | null
          energyLabel?: {
            __typename?: 'EnergyLabel'
            infoUrl?: string | null
            labelUrl?: string | null
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
        }
    >
  }
}

export type SearchPageResultsFragment = {
  __typename?: 'SearchResults'
  totalItems: number
  popularTerms: Array<string>
  block?: { __typename?: 'CMSBlock'; title: string; content: string } | null
  categories: Array<{ __typename?: 'SearchCategory'; name: string; url: string }>
  products: Array<
    | {
        __typename: 'BundleProduct'
        id: string
        sku: string
        name: string
        description: string
        shortDescription: string
        urlKey: string
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
        bundled: Array<{
          __typename?: 'SimpleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }>
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
      }
    | {
        __typename: 'SimpleProduct'
        id: string
        sku: string
        name: string
        urlKey: string
        description: string
        shortDescription: string
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        videos?: Array<{
          __typename?: 'ProductVideo'
          id: string
          title: string
          description?: string | null
          url: string
          videoType: string
          thumbnail?: string | null
          position: number
        }> | null
        measures: {
          __typename?: 'ProductMeasures'
          base: string
          secondary: string
          secondaryQty: number
          secondaryMeasureUsed: boolean
        }
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
        stock: {
          __typename?: 'ProductStock'
          blockForSale: boolean
          hasImages: boolean
          inStock: boolean
          manageStock: boolean
          qty: number
          minQty: number
          zeronBlockedDelivery: boolean
          zeronSiteStatus: string
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        energyLabel?: {
          __typename?: 'EnergyLabel'
          infoUrl?: string | null
          labelUrl?: string | null
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
      }
  >
}

export type SearchPageQueryVariables = Exact<{
  searchQuery: Scalars['String']['input']
  query?: InputMaybe<Array<InputMaybe<QueryParam>> | InputMaybe<QueryParam>>
}>

export type SearchPageQuery = {
  __typename?: 'Query'
  searchPage: {
    __typename?: 'SearchPage'
    title: string
    status: { __typename?: 'PageStatus'; statusCode: number }
    state: {
      __typename?: 'CatalogState'
      filters: {
        __typename?: 'Filters'
        applied: Array<{
          __typename?: 'AppliedFilter'
          label: string
          value: string
          requestVar: string
          attributeCode: string
        }>
        available: Array<{
          __typename?: 'AvailableFilter'
          position: number
          type: FilterRenderType
          requestVar: string
          attributeCode: string
          label: string
          options: Array<{ __typename?: 'AttributeOption'; label: string; value: string; order: number }>
        }>
      }
      pager: { __typename?: 'Pager'; page: number; pageSize: number; totalPages: number; totalItems: number }
      sort: { __typename?: 'CollectionSort'; value: string; dir: SortDirection }
    }
    data: {
      __typename?: 'SearchResults'
      totalItems: number
      popularTerms: Array<string>
      block?: { __typename?: 'CMSBlock'; title: string; content: string } | null
      categories: Array<{ __typename?: 'SearchCategory'; name: string; url: string }>
      products: Array<
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            bundled: Array<{
              __typename?: 'SimpleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }>
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            description: string
            shortDescription: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            videos?: Array<{
              __typename?: 'ProductVideo'
              id: string
              title: string
              description?: string | null
              url: string
              videoType: string
              thumbnail?: string | null
              position: number
            }> | null
            measures: {
              __typename?: 'ProductMeasures'
              base: string
              secondary: string
              secondaryQty: number
              secondaryMeasureUsed: boolean
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
            brand?: {
              __typename?: 'Brand'
              name: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
            } | null
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
      >
    }
  }
}

export type AppMetaFragment = {
  __typename?: 'PageMeta'
  title: string
  description: string
  keywords: string
  robots: string
  canonicalUrl: string
  image?: { __typename?: 'Image'; src: string } | null
}

export type GetRouteMetaQueryVariables = Exact<{
  url: Scalars['String']['input']
  query?: InputMaybe<Array<InputMaybe<QueryParam>> | InputMaybe<QueryParam>>
}>

export type GetRouteMetaQuery = {
  __typename?: 'Query'
  routeMeta: {
    __typename?: 'PageMeta'
    title: string
    description: string
    keywords: string
    robots: string
    canonicalUrl: string
    image?: { __typename?: 'Image'; src: string } | null
  }
}

export type GetRouteDataQueryVariables = Exact<{
  url: Scalars['String']['input']
  query?: InputMaybe<Array<InputMaybe<QueryParam>> | InputMaybe<QueryParam>>
}>

export type GetRouteDataQuery = {
  __typename?: 'Query'
  route: {
    __typename?: 'Page'
    breadcrumbs: Array<{
      __typename?: 'Breadcrumb'
      label: string
      url: string
      children?: Array<{ __typename?: 'Breadcrumb'; label: string; url: string }> | null
      siblings?: Array<{ __typename?: 'Breadcrumb'; label: string; url: string }> | null
    }>
    status: { __typename?: 'PageStatus'; redirectUrl?: string | null; statusCode: number }
    data?:
      | {
          __typename: 'CMSPage'
          identifier: string
          title: string
          content: string
          links: Array<{ __typename?: 'Link'; href: string; text: string; title?: string | null }>
        }
      | {
          __typename: 'CatalogPage'
          layout: CatalogLayout
          state: {
            __typename?: 'CatalogState'
            filters: {
              __typename?: 'Filters'
              applied: Array<{
                __typename?: 'AppliedFilter'
                label: string
                value: string
                requestVar: string
                attributeCode: string
              }>
              available: Array<{
                __typename?: 'AvailableFilter'
                position: number
                type: FilterRenderType
                requestVar: string
                attributeCode: string
                label: string
                options: Array<{ __typename?: 'AttributeOption'; label: string; value: string; order: number }>
              }>
            }
            pager: { __typename?: 'Pager'; page: number; pageSize: number; totalPages: number; totalItems: number }
            sort: { __typename?: 'CollectionSort'; value: string; dir: SortDirection }
          }
          category:
            | {
                __typename: 'Category'
                id: string
                name: string
                url: string
                description?: string | null
                image?: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                } | null
                icon?: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                } | null
                banner?: {
                  __typename?: 'CategoryBanner'
                  url: string
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                } | null
                widgets?: Array<{
                  __typename: 'CategoryLinkWidget'
                  title: string
                  url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
                  image: {
                    __typename?: 'Image'
                    alt?: string | null
                    src: string
                    title?: string | null
                    mobileSrc?: string | null
                  }
                }> | null
              }
            | { __typename: 'SearchCategory'; name: string; url: string }
            | { __typename: 'SplashPage'; title: string; url: string; splashDescription: string }
          products: Array<
            | {
                __typename: 'BundleProduct'
                id: string
                sku: string
                name: string
                description: string
                shortDescription: string
                urlKey: string
                gallery: Array<{
                  __typename?: 'GalleryImage'
                  position: number
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                }>
                price: {
                  __typename?: 'ProductPrice'
                  price: { __typename?: 'Price'; value: number; currency: string }
                  special?: { __typename?: 'Price'; value: number; currency: string } | null
                }
                brand?: {
                  __typename?: 'Brand'
                  name: string
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
                } | null
                labels: {
                  __typename?: 'MagentoLabels'
                  warrantyMonths: number
                  buyCheap?: boolean | null
                  freeDelivery?: boolean | null
                  fromBrochure?: boolean | null
                  other?: Array<string> | null
                }
                bundled: Array<{
                  __typename?: 'SimpleProduct'
                  id: string
                  sku: string
                  name: string
                  description: string
                  shortDescription: string
                  urlKey: string
                  measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  price: {
                    __typename?: 'ProductPrice'
                    price: { __typename?: 'Price'; value: number; currency: string }
                    special?: { __typename?: 'Price'; value: number; currency: string } | null
                  }
                  gallery: Array<{
                    __typename?: 'GalleryImage'
                    position: number
                    image: {
                      __typename?: 'Image'
                      src: string
                      mobileSrc?: string | null
                      alt?: string | null
                      title?: string | null
                    }
                  }>
                  labels: {
                    __typename?: 'MagentoLabels'
                    warrantyMonths: number
                    buyCheap?: boolean | null
                    freeDelivery?: boolean | null
                    fromBrochure?: boolean | null
                    other?: Array<string> | null
                  }
                  skuAvailability: Array<{
                    __typename?: 'StoreAvailabilityItem'
                    available: AvailabilityStatus
                    sample: boolean
                    store: {
                      __typename?: 'PraktisStore'
                      identity: string
                      warehouseCode: string
                      name: string
                      displayOrder: number
                      acceptOrders: boolean
                      location: { __typename?: 'MapLocation'; lat: number; lng: number }
                    }
                  }>
                }>
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                skuAvailability: Array<{
                  __typename?: 'StoreAvailabilityItem'
                  available: AvailabilityStatus
                  sample: boolean
                  store: {
                    __typename?: 'PraktisStore'
                    identity: string
                    warehouseCode: string
                    name: string
                    displayOrder: number
                    acceptOrders: boolean
                    location: { __typename?: 'MapLocation'; lat: number; lng: number }
                  }
                }>
              }
            | {
                __typename: 'SimpleProduct'
                id: string
                sku: string
                name: string
                urlKey: string
                description: string
                shortDescription: string
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                gallery: Array<{
                  __typename?: 'GalleryImage'
                  position: number
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                }>
                videos?: Array<{
                  __typename?: 'ProductVideo'
                  id: string
                  title: string
                  description?: string | null
                  url: string
                  videoType: string
                  thumbnail?: string | null
                  position: number
                }> | null
                measures: {
                  __typename?: 'ProductMeasures'
                  base: string
                  secondary: string
                  secondaryQty: number
                  secondaryMeasureUsed: boolean
                }
                price: {
                  __typename?: 'ProductPrice'
                  price: { __typename?: 'Price'; value: number; currency: string }
                  special?: { __typename?: 'Price'; value: number; currency: string } | null
                }
                skuAvailability: Array<{
                  __typename?: 'StoreAvailabilityItem'
                  available: AvailabilityStatus
                  sample: boolean
                  store: {
                    __typename?: 'PraktisStore'
                    identity: string
                    warehouseCode: string
                    name: string
                    displayOrder: number
                    acceptOrders: boolean
                    location: { __typename?: 'MapLocation'; lat: number; lng: number }
                  }
                }>
                stock: {
                  __typename?: 'ProductStock'
                  blockForSale: boolean
                  hasImages: boolean
                  inStock: boolean
                  manageStock: boolean
                  qty: number
                  minQty: number
                  zeronBlockedDelivery: boolean
                  zeronSiteStatus: string
                }
                brand?: {
                  __typename?: 'Brand'
                  name: string
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
                } | null
                energyLabel?: {
                  __typename?: 'EnergyLabel'
                  infoUrl?: string | null
                  labelUrl?: string | null
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                } | null
                labels: {
                  __typename?: 'MagentoLabels'
                  warrantyMonths: number
                  buyCheap?: boolean | null
                  freeDelivery?: boolean | null
                  fromBrochure?: boolean | null
                  other?: Array<string> | null
                }
              }
          >
        }
      | { __typename: 'ErrorData'; code: number; message: string }
      | {
          __typename: 'ProductPage'
          product:
            | {
                __typename: 'BundleProduct'
                id: string
                sku: string
                name: string
                description: string
                shortDescription: string
                urlKey: string
                gallery: Array<{
                  __typename?: 'GalleryImage'
                  position: number
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                }>
                price: {
                  __typename?: 'ProductPrice'
                  price: { __typename?: 'Price'; value: number; currency: string }
                  special?: { __typename?: 'Price'; value: number; currency: string } | null
                }
                brand?: {
                  __typename?: 'Brand'
                  name: string
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
                } | null
                labels: {
                  __typename?: 'MagentoLabels'
                  warrantyMonths: number
                  buyCheap?: boolean | null
                  freeDelivery?: boolean | null
                  fromBrochure?: boolean | null
                  other?: Array<string> | null
                }
                bundled: Array<{
                  __typename?: 'SimpleProduct'
                  id: string
                  sku: string
                  name: string
                  description: string
                  shortDescription: string
                  urlKey: string
                  measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  price: {
                    __typename?: 'ProductPrice'
                    price: { __typename?: 'Price'; value: number; currency: string }
                    special?: { __typename?: 'Price'; value: number; currency: string } | null
                  }
                  gallery: Array<{
                    __typename?: 'GalleryImage'
                    position: number
                    image: {
                      __typename?: 'Image'
                      src: string
                      mobileSrc?: string | null
                      alt?: string | null
                      title?: string | null
                    }
                  }>
                  labels: {
                    __typename?: 'MagentoLabels'
                    warrantyMonths: number
                    buyCheap?: boolean | null
                    freeDelivery?: boolean | null
                    fromBrochure?: boolean | null
                    other?: Array<string> | null
                  }
                  skuAvailability: Array<{
                    __typename?: 'StoreAvailabilityItem'
                    available: AvailabilityStatus
                    sample: boolean
                    store: {
                      __typename?: 'PraktisStore'
                      identity: string
                      warehouseCode: string
                      name: string
                      displayOrder: number
                      acceptOrders: boolean
                      location: { __typename?: 'MapLocation'; lat: number; lng: number }
                    }
                  }>
                }>
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                skuAvailability: Array<{
                  __typename?: 'StoreAvailabilityItem'
                  available: AvailabilityStatus
                  sample: boolean
                  store: {
                    __typename?: 'PraktisStore'
                    identity: string
                    warehouseCode: string
                    name: string
                    displayOrder: number
                    acceptOrders: boolean
                    location: { __typename?: 'MapLocation'; lat: number; lng: number }
                  }
                }>
              }
            | {
                __typename: 'SimpleProduct'
                id: string
                sku: string
                name: string
                urlKey: string
                description: string
                shortDescription: string
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                gallery: Array<{
                  __typename?: 'GalleryImage'
                  position: number
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                }>
                videos?: Array<{
                  __typename?: 'ProductVideo'
                  id: string
                  title: string
                  description?: string | null
                  url: string
                  videoType: string
                  thumbnail?: string | null
                  position: number
                }> | null
                measures: {
                  __typename?: 'ProductMeasures'
                  base: string
                  secondary: string
                  secondaryQty: number
                  secondaryMeasureUsed: boolean
                }
                price: {
                  __typename?: 'ProductPrice'
                  price: { __typename?: 'Price'; value: number; currency: string }
                  special?: { __typename?: 'Price'; value: number; currency: string } | null
                }
                skuAvailability: Array<{
                  __typename?: 'StoreAvailabilityItem'
                  available: AvailabilityStatus
                  sample: boolean
                  store: {
                    __typename?: 'PraktisStore'
                    identity: string
                    warehouseCode: string
                    name: string
                    displayOrder: number
                    acceptOrders: boolean
                    location: { __typename?: 'MapLocation'; lat: number; lng: number }
                  }
                }>
                stock: {
                  __typename?: 'ProductStock'
                  blockForSale: boolean
                  hasImages: boolean
                  inStock: boolean
                  manageStock: boolean
                  qty: number
                  minQty: number
                  zeronBlockedDelivery: boolean
                  zeronSiteStatus: string
                }
                brand?: {
                  __typename?: 'Brand'
                  name: string
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
                } | null
                energyLabel?: {
                  __typename?: 'EnergyLabel'
                  infoUrl?: string | null
                  labelUrl?: string | null
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                } | null
                labels: {
                  __typename?: 'MagentoLabels'
                  warrantyMonths: number
                  buyCheap?: boolean | null
                  freeDelivery?: boolean | null
                  fromBrochure?: boolean | null
                  other?: Array<string> | null
                }
              }
          widgets?: Array<{
            __typename: 'ProductsSliderWidget'
            identifier: string
            title: string
            subtitle: string
            tabs: Array<{
              __typename?: 'ProductsTab'
              title: string
              products: Array<
                | {
                    __typename: 'BundleProduct'
                    id: string
                    sku: string
                    name: string
                    urlKey: string
                    image: {
                      __typename?: 'Image'
                      src: string
                      mobileSrc?: string | null
                      alt?: string | null
                      title?: string | null
                    }
                    price: {
                      __typename?: 'ProductPrice'
                      price: { __typename?: 'Price'; value: number; currency: string }
                      special?: { __typename?: 'Price'; value: number; currency: string } | null
                    }
                    labels: {
                      __typename?: 'MagentoLabels'
                      warrantyMonths: number
                      buyCheap?: boolean | null
                      freeDelivery?: boolean | null
                      fromBrochure?: boolean | null
                      other?: Array<string> | null
                    }
                  }
                | {
                    __typename: 'SimpleProduct'
                    id: string
                    sku: string
                    name: string
                    urlKey: string
                    image: {
                      __typename?: 'Image'
                      src: string
                      mobileSrc?: string | null
                      alt?: string | null
                      title?: string | null
                    }
                    price: {
                      __typename?: 'ProductPrice'
                      price: { __typename?: 'Price'; value: number; currency: string }
                      special?: { __typename?: 'Price'; value: number; currency: string } | null
                    }
                    energyLabel?: {
                      __typename?: 'EnergyLabel'
                      infoUrl?: string | null
                      labelUrl?: string | null
                      image: {
                        __typename?: 'Image'
                        src: string
                        mobileSrc?: string | null
                        alt?: string | null
                        title?: string | null
                      }
                    } | null
                    labels: {
                      __typename?: 'MagentoLabels'
                      warrantyMonths: number
                      buyCheap?: boolean | null
                      freeDelivery?: boolean | null
                      fromBrochure?: boolean | null
                      other?: Array<string> | null
                    }
                    stock: {
                      __typename?: 'ProductStock'
                      blockForSale: boolean
                      hasImages: boolean
                      inStock: boolean
                      manageStock: boolean
                      qty: number
                      minQty: number
                      zeronBlockedDelivery: boolean
                      zeronSiteStatus: string
                    }
                  }
              >
            }>
          }> | null
          boughtTogether?: Array<{
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
          }> | null
        }
      | null
  }
}

export type DynamicRouteDataFragment = {
  __typename?: 'Page'
  breadcrumbs: Array<{
    __typename?: 'Breadcrumb'
    label: string
    url: string
    children?: Array<{ __typename?: 'Breadcrumb'; label: string; url: string }> | null
    siblings?: Array<{ __typename?: 'Breadcrumb'; label: string; url: string }> | null
  }>
  status: { __typename?: 'PageStatus'; redirectUrl?: string | null; statusCode: number }
  data?:
    | {
        __typename: 'CMSPage'
        identifier: string
        title: string
        content: string
        links: Array<{ __typename?: 'Link'; href: string; text: string; title?: string | null }>
      }
    | {
        __typename: 'CatalogPage'
        layout: CatalogLayout
        state: {
          __typename?: 'CatalogState'
          filters: {
            __typename?: 'Filters'
            applied: Array<{
              __typename?: 'AppliedFilter'
              label: string
              value: string
              requestVar: string
              attributeCode: string
            }>
            available: Array<{
              __typename?: 'AvailableFilter'
              position: number
              type: FilterRenderType
              requestVar: string
              attributeCode: string
              label: string
              options: Array<{ __typename?: 'AttributeOption'; label: string; value: string; order: number }>
            }>
          }
          pager: { __typename?: 'Pager'; page: number; pageSize: number; totalPages: number; totalItems: number }
          sort: { __typename?: 'CollectionSort'; value: string; dir: SortDirection }
        }
        category:
          | {
              __typename: 'Category'
              id: string
              name: string
              url: string
              description?: string | null
              image?: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              } | null
              icon?: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              } | null
              banner?: {
                __typename?: 'CategoryBanner'
                url: string
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              } | null
              widgets?: Array<{
                __typename: 'CategoryLinkWidget'
                title: string
                url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
                image: {
                  __typename?: 'Image'
                  alt?: string | null
                  src: string
                  title?: string | null
                  mobileSrc?: string | null
                }
              }> | null
            }
          | { __typename: 'SearchCategory'; name: string; url: string }
          | { __typename: 'SplashPage'; title: string; url: string; splashDescription: string }
        products: Array<
          | {
              __typename: 'BundleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              brand?: {
                __typename?: 'Brand'
                name: string
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
              } | null
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              bundled: Array<{
                __typename?: 'SimpleProduct'
                id: string
                sku: string
                name: string
                description: string
                shortDescription: string
                urlKey: string
                measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                price: {
                  __typename?: 'ProductPrice'
                  price: { __typename?: 'Price'; value: number; currency: string }
                  special?: { __typename?: 'Price'; value: number; currency: string } | null
                }
                gallery: Array<{
                  __typename?: 'GalleryImage'
                  position: number
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                }>
                labels: {
                  __typename?: 'MagentoLabels'
                  warrantyMonths: number
                  buyCheap?: boolean | null
                  freeDelivery?: boolean | null
                  fromBrochure?: boolean | null
                  other?: Array<string> | null
                }
                skuAvailability: Array<{
                  __typename?: 'StoreAvailabilityItem'
                  available: AvailabilityStatus
                  sample: boolean
                  store: {
                    __typename?: 'PraktisStore'
                    identity: string
                    warehouseCode: string
                    name: string
                    displayOrder: number
                    acceptOrders: boolean
                    location: { __typename?: 'MapLocation'; lat: number; lng: number }
                  }
                }>
              }>
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }
          | {
              __typename: 'SimpleProduct'
              id: string
              sku: string
              name: string
              urlKey: string
              description: string
              shortDescription: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              videos?: Array<{
                __typename?: 'ProductVideo'
                id: string
                title: string
                description?: string | null
                url: string
                videoType: string
                thumbnail?: string | null
                position: number
              }> | null
              measures: {
                __typename?: 'ProductMeasures'
                base: string
                secondary: string
                secondaryQty: number
                secondaryMeasureUsed: boolean
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
              stock: {
                __typename?: 'ProductStock'
                blockForSale: boolean
                hasImages: boolean
                inStock: boolean
                manageStock: boolean
                qty: number
                minQty: number
                zeronBlockedDelivery: boolean
                zeronSiteStatus: string
              }
              brand?: {
                __typename?: 'Brand'
                name: string
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
              } | null
              energyLabel?: {
                __typename?: 'EnergyLabel'
                infoUrl?: string | null
                labelUrl?: string | null
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              } | null
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
            }
        >
      }
    | { __typename: 'ErrorData'; code: number; message: string }
    | {
        __typename: 'ProductPage'
        product:
          | {
              __typename: 'BundleProduct'
              id: string
              sku: string
              name: string
              description: string
              shortDescription: string
              urlKey: string
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              brand?: {
                __typename?: 'Brand'
                name: string
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
              } | null
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
              bundled: Array<{
                __typename?: 'SimpleProduct'
                id: string
                sku: string
                name: string
                description: string
                shortDescription: string
                urlKey: string
                measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                price: {
                  __typename?: 'ProductPrice'
                  price: { __typename?: 'Price'; value: number; currency: string }
                  special?: { __typename?: 'Price'; value: number; currency: string } | null
                }
                gallery: Array<{
                  __typename?: 'GalleryImage'
                  position: number
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                }>
                labels: {
                  __typename?: 'MagentoLabels'
                  warrantyMonths: number
                  buyCheap?: boolean | null
                  freeDelivery?: boolean | null
                  fromBrochure?: boolean | null
                  other?: Array<string> | null
                }
                skuAvailability: Array<{
                  __typename?: 'StoreAvailabilityItem'
                  available: AvailabilityStatus
                  sample: boolean
                  store: {
                    __typename?: 'PraktisStore'
                    identity: string
                    warehouseCode: string
                    name: string
                    displayOrder: number
                    acceptOrders: boolean
                    location: { __typename?: 'MapLocation'; lat: number; lng: number }
                  }
                }>
              }>
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
            }
          | {
              __typename: 'SimpleProduct'
              id: string
              sku: string
              name: string
              urlKey: string
              description: string
              shortDescription: string
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
              gallery: Array<{
                __typename?: 'GalleryImage'
                position: number
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              }>
              videos?: Array<{
                __typename?: 'ProductVideo'
                id: string
                title: string
                description?: string | null
                url: string
                videoType: string
                thumbnail?: string | null
                position: number
              }> | null
              measures: {
                __typename?: 'ProductMeasures'
                base: string
                secondary: string
                secondaryQty: number
                secondaryMeasureUsed: boolean
              }
              price: {
                __typename?: 'ProductPrice'
                price: { __typename?: 'Price'; value: number; currency: string }
                special?: { __typename?: 'Price'; value: number; currency: string } | null
              }
              skuAvailability: Array<{
                __typename?: 'StoreAvailabilityItem'
                available: AvailabilityStatus
                sample: boolean
                store: {
                  __typename?: 'PraktisStore'
                  identity: string
                  warehouseCode: string
                  name: string
                  displayOrder: number
                  acceptOrders: boolean
                  location: { __typename?: 'MapLocation'; lat: number; lng: number }
                }
              }>
              stock: {
                __typename?: 'ProductStock'
                blockForSale: boolean
                hasImages: boolean
                inStock: boolean
                manageStock: boolean
                qty: number
                minQty: number
                zeronBlockedDelivery: boolean
                zeronSiteStatus: string
              }
              brand?: {
                __typename?: 'Brand'
                name: string
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
                url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
              } | null
              energyLabel?: {
                __typename?: 'EnergyLabel'
                infoUrl?: string | null
                labelUrl?: string | null
                image: {
                  __typename?: 'Image'
                  src: string
                  mobileSrc?: string | null
                  alt?: string | null
                  title?: string | null
                }
              } | null
              labels: {
                __typename?: 'MagentoLabels'
                warrantyMonths: number
                buyCheap?: boolean | null
                freeDelivery?: boolean | null
                fromBrochure?: boolean | null
                other?: Array<string> | null
              }
            }
        widgets?: Array<{
          __typename: 'ProductsSliderWidget'
          identifier: string
          title: string
          subtitle: string
          tabs: Array<{
            __typename?: 'ProductsTab'
            title: string
            products: Array<
              | {
                  __typename: 'BundleProduct'
                  id: string
                  sku: string
                  name: string
                  urlKey: string
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  price: {
                    __typename?: 'ProductPrice'
                    price: { __typename?: 'Price'; value: number; currency: string }
                    special?: { __typename?: 'Price'; value: number; currency: string } | null
                  }
                  labels: {
                    __typename?: 'MagentoLabels'
                    warrantyMonths: number
                    buyCheap?: boolean | null
                    freeDelivery?: boolean | null
                    fromBrochure?: boolean | null
                    other?: Array<string> | null
                  }
                }
              | {
                  __typename: 'SimpleProduct'
                  id: string
                  sku: string
                  name: string
                  urlKey: string
                  image: {
                    __typename?: 'Image'
                    src: string
                    mobileSrc?: string | null
                    alt?: string | null
                    title?: string | null
                  }
                  price: {
                    __typename?: 'ProductPrice'
                    price: { __typename?: 'Price'; value: number; currency: string }
                    special?: { __typename?: 'Price'; value: number; currency: string } | null
                  }
                  energyLabel?: {
                    __typename?: 'EnergyLabel'
                    infoUrl?: string | null
                    labelUrl?: string | null
                    image: {
                      __typename?: 'Image'
                      src: string
                      mobileSrc?: string | null
                      alt?: string | null
                      title?: string | null
                    }
                  } | null
                  labels: {
                    __typename?: 'MagentoLabels'
                    warrantyMonths: number
                    buyCheap?: boolean | null
                    freeDelivery?: boolean | null
                    fromBrochure?: boolean | null
                    other?: Array<string> | null
                  }
                  stock: {
                    __typename?: 'ProductStock'
                    blockForSale: boolean
                    hasImages: boolean
                    inStock: boolean
                    manageStock: boolean
                    qty: number
                    minQty: number
                    zeronBlockedDelivery: boolean
                    zeronSiteStatus: string
                  }
                }
            >
          }>
        }> | null
        boughtTogether?: Array<{
          __typename: 'SimpleProduct'
          id: string
          sku: string
          name: string
          urlKey: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          energyLabel?: {
            __typename?: 'EnergyLabel'
            infoUrl?: string | null
            labelUrl?: string | null
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          stock: {
            __typename?: 'ProductStock'
            blockForSale: boolean
            hasImages: boolean
            inStock: boolean
            manageStock: boolean
            qty: number
            minQty: number
            zeronBlockedDelivery: boolean
            zeronSiteStatus: string
          }
        }> | null
      }
    | null
}

type DynamicPageData_CmsPage_Fragment = {
  __typename: 'CMSPage'
  identifier: string
  title: string
  content: string
  links: Array<{ __typename?: 'Link'; href: string; text: string; title?: string | null }>
}

type DynamicPageData_CatalogPage_Fragment = {
  __typename: 'CatalogPage'
  layout: CatalogLayout
  state: {
    __typename?: 'CatalogState'
    filters: {
      __typename?: 'Filters'
      applied: Array<{
        __typename?: 'AppliedFilter'
        label: string
        value: string
        requestVar: string
        attributeCode: string
      }>
      available: Array<{
        __typename?: 'AvailableFilter'
        position: number
        type: FilterRenderType
        requestVar: string
        attributeCode: string
        label: string
        options: Array<{ __typename?: 'AttributeOption'; label: string; value: string; order: number }>
      }>
    }
    pager: { __typename?: 'Pager'; page: number; pageSize: number; totalPages: number; totalItems: number }
    sort: { __typename?: 'CollectionSort'; value: string; dir: SortDirection }
  }
  category:
    | {
        __typename: 'Category'
        id: string
        name: string
        url: string
        description?: string | null
        image?: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        } | null
        icon?: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        } | null
        banner?: {
          __typename?: 'CategoryBanner'
          url: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        } | null
        widgets?: Array<{
          __typename: 'CategoryLinkWidget'
          title: string
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          image: {
            __typename?: 'Image'
            alt?: string | null
            src: string
            title?: string | null
            mobileSrc?: string | null
          }
        }> | null
      }
    | { __typename: 'SearchCategory'; name: string; url: string }
    | { __typename: 'SplashPage'; title: string; url: string; splashDescription: string }
  products: Array<
    | {
        __typename: 'BundleProduct'
        id: string
        sku: string
        name: string
        description: string
        shortDescription: string
        urlKey: string
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
        bundled: Array<{
          __typename?: 'SimpleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }>
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
      }
    | {
        __typename: 'SimpleProduct'
        id: string
        sku: string
        name: string
        urlKey: string
        description: string
        shortDescription: string
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        videos?: Array<{
          __typename?: 'ProductVideo'
          id: string
          title: string
          description?: string | null
          url: string
          videoType: string
          thumbnail?: string | null
          position: number
        }> | null
        measures: {
          __typename?: 'ProductMeasures'
          base: string
          secondary: string
          secondaryQty: number
          secondaryMeasureUsed: boolean
        }
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
        stock: {
          __typename?: 'ProductStock'
          blockForSale: boolean
          hasImages: boolean
          inStock: boolean
          manageStock: boolean
          qty: number
          minQty: number
          zeronBlockedDelivery: boolean
          zeronSiteStatus: string
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        energyLabel?: {
          __typename?: 'EnergyLabel'
          infoUrl?: string | null
          labelUrl?: string | null
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
      }
  >
}

type DynamicPageData_ErrorData_Fragment = { __typename: 'ErrorData'; code: number; message: string }

type DynamicPageData_ProductPage_Fragment = {
  __typename: 'ProductPage'
  product:
    | {
        __typename: 'BundleProduct'
        id: string
        sku: string
        name: string
        description: string
        shortDescription: string
        urlKey: string
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
        bundled: Array<{
          __typename?: 'SimpleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }>
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
      }
    | {
        __typename: 'SimpleProduct'
        id: string
        sku: string
        name: string
        urlKey: string
        description: string
        shortDescription: string
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        videos?: Array<{
          __typename?: 'ProductVideo'
          id: string
          title: string
          description?: string | null
          url: string
          videoType: string
          thumbnail?: string | null
          position: number
        }> | null
        measures: {
          __typename?: 'ProductMeasures'
          base: string
          secondary: string
          secondaryQty: number
          secondaryMeasureUsed: boolean
        }
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
        stock: {
          __typename?: 'ProductStock'
          blockForSale: boolean
          hasImages: boolean
          inStock: boolean
          manageStock: boolean
          qty: number
          minQty: number
          zeronBlockedDelivery: boolean
          zeronSiteStatus: string
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        energyLabel?: {
          __typename?: 'EnergyLabel'
          infoUrl?: string | null
          labelUrl?: string | null
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
      }
  widgets?: Array<{
    __typename: 'ProductsSliderWidget'
    identifier: string
    title: string
    subtitle: string
    tabs: Array<{
      __typename?: 'ProductsTab'
      title: string
      products: Array<
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
          }
      >
    }>
  }> | null
  boughtTogether?: Array<{
    __typename: 'SimpleProduct'
    id: string
    sku: string
    name: string
    urlKey: string
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
    price: {
      __typename?: 'ProductPrice'
      price: { __typename?: 'Price'; value: number; currency: string }
      special?: { __typename?: 'Price'; value: number; currency: string } | null
    }
    energyLabel?: {
      __typename?: 'EnergyLabel'
      infoUrl?: string | null
      labelUrl?: string | null
      image: {
        __typename?: 'Image'
        src: string
        mobileSrc?: string | null
        alt?: string | null
        title?: string | null
      }
    } | null
    labels: {
      __typename?: 'MagentoLabels'
      warrantyMonths: number
      buyCheap?: boolean | null
      freeDelivery?: boolean | null
      fromBrochure?: boolean | null
      other?: Array<string> | null
    }
    stock: {
      __typename?: 'ProductStock'
      blockForSale: boolean
      hasImages: boolean
      inStock: boolean
      manageStock: boolean
      qty: number
      minQty: number
      zeronBlockedDelivery: boolean
      zeronSiteStatus: string
    }
  }> | null
}

export type DynamicPageDataFragment =
  | DynamicPageData_CmsPage_Fragment
  | DynamicPageData_CatalogPage_Fragment
  | DynamicPageData_ErrorData_Fragment
  | DynamicPageData_ProductPage_Fragment

export type CatalogPageDataFragment = {
  __typename: 'CatalogPage'
  layout: CatalogLayout
  state: {
    __typename?: 'CatalogState'
    filters: {
      __typename?: 'Filters'
      applied: Array<{
        __typename?: 'AppliedFilter'
        label: string
        value: string
        requestVar: string
        attributeCode: string
      }>
      available: Array<{
        __typename?: 'AvailableFilter'
        position: number
        type: FilterRenderType
        requestVar: string
        attributeCode: string
        label: string
        options: Array<{ __typename?: 'AttributeOption'; label: string; value: string; order: number }>
      }>
    }
    pager: { __typename?: 'Pager'; page: number; pageSize: number; totalPages: number; totalItems: number }
    sort: { __typename?: 'CollectionSort'; value: string; dir: SortDirection }
  }
  category:
    | {
        __typename: 'Category'
        id: string
        name: string
        url: string
        description?: string | null
        image?: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        } | null
        icon?: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        } | null
        banner?: {
          __typename?: 'CategoryBanner'
          url: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        } | null
        widgets?: Array<{
          __typename: 'CategoryLinkWidget'
          title: string
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          image: {
            __typename?: 'Image'
            alt?: string | null
            src: string
            title?: string | null
            mobileSrc?: string | null
          }
        }> | null
      }
    | { __typename: 'SearchCategory'; name: string; url: string }
    | { __typename: 'SplashPage'; title: string; url: string; splashDescription: string }
  products: Array<
    | {
        __typename: 'BundleProduct'
        id: string
        sku: string
        name: string
        description: string
        shortDescription: string
        urlKey: string
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
        bundled: Array<{
          __typename?: 'SimpleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }>
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
      }
    | {
        __typename: 'SimpleProduct'
        id: string
        sku: string
        name: string
        urlKey: string
        description: string
        shortDescription: string
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        videos?: Array<{
          __typename?: 'ProductVideo'
          id: string
          title: string
          description?: string | null
          url: string
          videoType: string
          thumbnail?: string | null
          position: number
        }> | null
        measures: {
          __typename?: 'ProductMeasures'
          base: string
          secondary: string
          secondaryQty: number
          secondaryMeasureUsed: boolean
        }
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
        stock: {
          __typename?: 'ProductStock'
          blockForSale: boolean
          hasImages: boolean
          inStock: boolean
          manageStock: boolean
          qty: number
          minQty: number
          zeronBlockedDelivery: boolean
          zeronSiteStatus: string
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        energyLabel?: {
          __typename?: 'EnergyLabel'
          infoUrl?: string | null
          labelUrl?: string | null
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
      }
  >
}

export type CmsPageDataFragment = {
  __typename: 'CMSPage'
  identifier: string
  title: string
  content: string
  links: Array<{ __typename?: 'Link'; href: string; text: string; title?: string | null }>
}

type ProductView_BundleProduct_Fragment = {
  __typename: 'BundleProduct'
  id: string
  sku: string
  name: string
  description: string
  shortDescription: string
  urlKey: string
  gallery: Array<{
    __typename?: 'GalleryImage'
    position: number
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  }>
  price: {
    __typename?: 'ProductPrice'
    price: { __typename?: 'Price'; value: number; currency: string }
    special?: { __typename?: 'Price'; value: number; currency: string } | null
  }
  brand?: {
    __typename?: 'Brand'
    name: string
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
    url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  } | null
  labels: {
    __typename?: 'MagentoLabels'
    warrantyMonths: number
    buyCheap?: boolean | null
    freeDelivery?: boolean | null
    fromBrochure?: boolean | null
    other?: Array<string> | null
  }
  bundled: Array<{
    __typename?: 'SimpleProduct'
    id: string
    sku: string
    name: string
    description: string
    shortDescription: string
    urlKey: string
    measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
    price: {
      __typename?: 'ProductPrice'
      price: { __typename?: 'Price'; value: number; currency: string }
      special?: { __typename?: 'Price'; value: number; currency: string } | null
    }
    gallery: Array<{
      __typename?: 'GalleryImage'
      position: number
      image: {
        __typename?: 'Image'
        src: string
        mobileSrc?: string | null
        alt?: string | null
        title?: string | null
      }
    }>
    labels: {
      __typename?: 'MagentoLabels'
      warrantyMonths: number
      buyCheap?: boolean | null
      freeDelivery?: boolean | null
      fromBrochure?: boolean | null
      other?: Array<string> | null
    }
    skuAvailability: Array<{
      __typename?: 'StoreAvailabilityItem'
      available: AvailabilityStatus
      sample: boolean
      store: {
        __typename?: 'PraktisStore'
        identity: string
        warehouseCode: string
        name: string
        displayOrder: number
        acceptOrders: boolean
        location: { __typename?: 'MapLocation'; lat: number; lng: number }
      }
    }>
  }>
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  skuAvailability: Array<{
    __typename?: 'StoreAvailabilityItem'
    available: AvailabilityStatus
    sample: boolean
    store: {
      __typename?: 'PraktisStore'
      identity: string
      warehouseCode: string
      name: string
      displayOrder: number
      acceptOrders: boolean
      location: { __typename?: 'MapLocation'; lat: number; lng: number }
    }
  }>
}

type ProductView_SimpleProduct_Fragment = {
  __typename: 'SimpleProduct'
  id: string
  sku: string
  name: string
  urlKey: string
  description: string
  shortDescription: string
  image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  gallery: Array<{
    __typename?: 'GalleryImage'
    position: number
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  }>
  videos?: Array<{
    __typename?: 'ProductVideo'
    id: string
    title: string
    description?: string | null
    url: string
    videoType: string
    thumbnail?: string | null
    position: number
  }> | null
  measures: {
    __typename?: 'ProductMeasures'
    base: string
    secondary: string
    secondaryQty: number
    secondaryMeasureUsed: boolean
  }
  price: {
    __typename?: 'ProductPrice'
    price: { __typename?: 'Price'; value: number; currency: string }
    special?: { __typename?: 'Price'; value: number; currency: string } | null
  }
  skuAvailability: Array<{
    __typename?: 'StoreAvailabilityItem'
    available: AvailabilityStatus
    sample: boolean
    store: {
      __typename?: 'PraktisStore'
      identity: string
      warehouseCode: string
      name: string
      displayOrder: number
      acceptOrders: boolean
      location: { __typename?: 'MapLocation'; lat: number; lng: number }
    }
  }>
  stock: {
    __typename?: 'ProductStock'
    blockForSale: boolean
    hasImages: boolean
    inStock: boolean
    manageStock: boolean
    qty: number
    minQty: number
    zeronBlockedDelivery: boolean
    zeronSiteStatus: string
  }
  brand?: {
    __typename?: 'Brand'
    name: string
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
    url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
  } | null
  energyLabel?: {
    __typename?: 'EnergyLabel'
    infoUrl?: string | null
    labelUrl?: string | null
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
  } | null
  labels: {
    __typename?: 'MagentoLabels'
    warrantyMonths: number
    buyCheap?: boolean | null
    freeDelivery?: boolean | null
    fromBrochure?: boolean | null
    other?: Array<string> | null
  }
}

export type ProductViewFragment = ProductView_BundleProduct_Fragment | ProductView_SimpleProduct_Fragment

export type ProductPageDataFragment = {
  __typename: 'ProductPage'
  product:
    | {
        __typename: 'BundleProduct'
        id: string
        sku: string
        name: string
        description: string
        shortDescription: string
        urlKey: string
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
        bundled: Array<{
          __typename?: 'SimpleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }>
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
      }
    | {
        __typename: 'SimpleProduct'
        id: string
        sku: string
        name: string
        urlKey: string
        description: string
        shortDescription: string
        image: {
          __typename?: 'Image'
          src: string
          mobileSrc?: string | null
          alt?: string | null
          title?: string | null
        }
        gallery: Array<{
          __typename?: 'GalleryImage'
          position: number
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        }>
        videos?: Array<{
          __typename?: 'ProductVideo'
          id: string
          title: string
          description?: string | null
          url: string
          videoType: string
          thumbnail?: string | null
          position: number
        }> | null
        measures: {
          __typename?: 'ProductMeasures'
          base: string
          secondary: string
          secondaryQty: number
          secondaryMeasureUsed: boolean
        }
        price: {
          __typename?: 'ProductPrice'
          price: { __typename?: 'Price'; value: number; currency: string }
          special?: { __typename?: 'Price'; value: number; currency: string } | null
        }
        skuAvailability: Array<{
          __typename?: 'StoreAvailabilityItem'
          available: AvailabilityStatus
          sample: boolean
          store: {
            __typename?: 'PraktisStore'
            identity: string
            warehouseCode: string
            name: string
            displayOrder: number
            acceptOrders: boolean
            location: { __typename?: 'MapLocation'; lat: number; lng: number }
          }
        }>
        stock: {
          __typename?: 'ProductStock'
          blockForSale: boolean
          hasImages: boolean
          inStock: boolean
          manageStock: boolean
          qty: number
          minQty: number
          zeronBlockedDelivery: boolean
          zeronSiteStatus: string
        }
        brand?: {
          __typename?: 'Brand'
          name: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
        } | null
        energyLabel?: {
          __typename?: 'EnergyLabel'
          infoUrl?: string | null
          labelUrl?: string | null
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
        } | null
        labels: {
          __typename?: 'MagentoLabels'
          warrantyMonths: number
          buyCheap?: boolean | null
          freeDelivery?: boolean | null
          fromBrochure?: boolean | null
          other?: Array<string> | null
        }
      }
  widgets?: Array<{
    __typename: 'ProductsSliderWidget'
    identifier: string
    title: string
    subtitle: string
    tabs: Array<{
      __typename?: 'ProductsTab'
      title: string
      products: Array<
        | {
            __typename: 'BundleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
          }
        | {
            __typename: 'SimpleProduct'
            id: string
            sku: string
            name: string
            urlKey: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            energyLabel?: {
              __typename?: 'EnergyLabel'
              infoUrl?: string | null
              labelUrl?: string | null
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            } | null
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            stock: {
              __typename?: 'ProductStock'
              blockForSale: boolean
              hasImages: boolean
              inStock: boolean
              manageStock: boolean
              qty: number
              minQty: number
              zeronBlockedDelivery: boolean
              zeronSiteStatus: string
            }
          }
      >
    }>
  }> | null
  boughtTogether?: Array<{
    __typename: 'SimpleProduct'
    id: string
    sku: string
    name: string
    urlKey: string
    image: { __typename?: 'Image'; src: string; mobileSrc?: string | null; alt?: string | null; title?: string | null }
    price: {
      __typename?: 'ProductPrice'
      price: { __typename?: 'Price'; value: number; currency: string }
      special?: { __typename?: 'Price'; value: number; currency: string } | null
    }
    energyLabel?: {
      __typename?: 'EnergyLabel'
      infoUrl?: string | null
      labelUrl?: string | null
      image: {
        __typename?: 'Image'
        src: string
        mobileSrc?: string | null
        alt?: string | null
        title?: string | null
      }
    } | null
    labels: {
      __typename?: 'MagentoLabels'
      warrantyMonths: number
      buyCheap?: boolean | null
      freeDelivery?: boolean | null
      fromBrochure?: boolean | null
      other?: Array<string> | null
    }
    stock: {
      __typename?: 'ProductStock'
      blockForSale: boolean
      hasImages: boolean
      inStock: boolean
      manageStock: boolean
      qty: number
      minQty: number
      zeronBlockedDelivery: boolean
      zeronSiteStatus: string
    }
  }> | null
}

export type GetStaticBlockQueryVariables = Exact<{
  id: Scalars['String']['input']
}>

export type GetStaticBlockQuery = {
  __typename?: 'Query'
  getStaticBlock: { __typename?: 'CMSBlock'; title: string; content: string }
}

export type GetStoreStaticDataQueryVariables = Exact<{ [key: string]: never }>

export type GetStoreStaticDataQuery = {
  __typename?: 'Query'
  getStaticContent: {
    __typename?: 'StaticContent'
    brochureLink?: string | null
    logo?: {
      __typename?: 'StoreLogo'
      url: string
      width?: number | null
      height?: number | null
      alt?: string | null
    } | null
    apiKeys: {
      __typename?: 'ApiKeys'
      facebookLoginAppId: string
      googleLoginClientId: string
      googleMaps: string
      googleRecaptchaKey: string
    }
    gdpr?: {
      __typename: 'GDPRConfig'
      rootId: string
      clarityId: string
      pixelId: string
      gtagId: string
      extraGTagsIds: Array<string>
      modal: {
        __typename: 'GDPRModalConfig'
        title: string
        content: string
        cookieGroups: Array<{
          __typename: 'CookieGroup'
          id: string
          title: string
          content: string
          cookiePatterns: Array<string>
          grants: Array<GdprGrants>
          vendors: {
            __typename: 'GDPRCookieVendors'
            keys: Array<CookieVendorKey>
            config: Array<{
              __typename: 'GDPRCookieVendorConfig'
              id: string
              name: string
              url: string
              cookieCount: number
              cookieDetails: Array<{
                __typename: 'GDPRCookieDetails'
                id: string
                name: string
                type: string
                description: string
                expiration: string
              } | null>
            }>
          }
        }>
      }
    } | null
    menu: {
      __typename?: 'Menu'
      categories: Array<{
        __typename?: 'MenuItem'
        url: string
        thumbnail: string
        name: string
        children?: Array<{ __typename?: 'MenuItem'; name: string; url: string }> | null
        sideSection?: { __typename?: 'MenuSideSection'; image: string; url: string; rawHtml: string } | null
      }>
    }
    footer: {
      __typename?: 'Footer'
      columns?: Array<{
        __typename?: 'FooterColumn'
        title?: string | null
        links?: Array<{ __typename?: 'Link'; href: string; text: string; title?: string | null } | null> | null
      } | null> | null
      appLinks?: { __typename?: 'ApplicationLinks'; android?: string | null; ios?: string | null } | null
      contacts?: {
        __typename?: 'StoreContacts'
        general?: { __typename?: 'ContactInfo'; phone?: string | null } | null
        onlineStore?: { __typename?: 'ContactInfo'; phone?: string | null } | null
      } | null
      social?: {
        __typename?: 'SocialLinks'
        facebook?: string | null
        viber?: string | null
        youtube?: string | null
      } | null
    }
    messages: { __typename?: 'PageMessages'; newsletter: string; sendInquiryMessage: string }
    store: {
      __typename?: 'StoreInfo'
      baseUrl: string
      contacts: {
        __typename?: 'StoreContacts'
        general?: { __typename?: 'ContactInfo'; phone?: string | null } | null
        onlineStore?: { __typename?: 'ContactInfo'; phone?: string | null } | null
      }
    }
  }
}

export type GetGoogleApiKeyQueryVariables = Exact<{ [key: string]: never }>

export type GetGoogleApiKeyQuery = {
  __typename?: 'Query'
  getStaticContent: { __typename?: 'StaticContent'; apiKeys: { __typename?: 'ApiKeys'; googleMaps: string } }
}

export type GetGoogleClientIdQueryVariables = Exact<{ [key: string]: never }>

export type GetGoogleClientIdQuery = {
  __typename?: 'Query'
  getStaticContent: { __typename?: 'StaticContent'; apiKeys: { __typename?: 'ApiKeys'; googleLoginClientId: string } }
}

export type GetAvailableStoresQueryVariables = Exact<{ [key: string]: never }>

export type GetAvailableStoresQuery = {
  __typename?: 'Query'
  availableStores: Array<{
    __typename?: 'PraktisStore'
    name: string
    identity: string
    address: string
    email: string
    phone: string
    city: string
    displayOrder: number
    gallery: Array<{
      __typename?: 'StoreImage'
      ID: string
      praktisStoreID: string
      position: number
      alt?: string | null
      src: string
      title?: string | null
      mobileSrc?: string | null
      isPrimary: boolean
    }>
    businessHours: Array<{ __typename?: 'StoreSchedule'; open: string; close: string; day: string }>
  }>
}

export type GetStoreQueryVariables = Exact<{
  uri: Scalars['String']['input']
}>

export type GetStoreQuery = {
  __typename?: 'Query'
  getStore?: {
    __typename?: 'StorePageData'
    messageBlock: string
    store: {
      __typename?: 'PraktisStore'
      name: string
      address: string
      city: string
      email: string
      phone: string
      virtualTour: string
      transportInformation: string
      metaDescription: string
      metaKeywords: string
      metaTitle: string
      gallery: Array<{
        __typename?: 'StoreImage'
        ID: string
        praktisStoreID: string
        position: number
        alt?: string | null
        src: string
        title?: string | null
        mobileSrc?: string | null
        isPrimary: boolean
      }>
      location: { __typename?: 'MapLocation'; lat: number; lng: number }
      businessHours: Array<{ __typename?: 'StoreSchedule'; open: string; close: string; day: string }>
      services: Array<{ __typename?: 'Service'; id: string; name: string; url: string; iconUrl: string }>
      descriptionArea?: {
        __typename?: 'DescriptionArea'
        backgroundImage: string
        content: string
        formID?: string | null
        title: string
      } | null
    }
  } | null
}

export type GetWishlistSkusQueryVariables = Exact<{ [key: string]: never }>

export type GetWishlistSkusQuery = {
  __typename?: 'Query'
  customerWishlist: { __typename?: 'CustomerWishlist'; skus: Array<string> }
}

export type GetWishlistProductsQueryVariables = Exact<{ [key: string]: never }>

export type GetWishlistProductsQuery = {
  __typename?: 'Query'
  customerWishlist: {
    __typename?: 'CustomerWishlist'
    products: Array<
      | {
          __typename: 'BundleProduct'
          id: string
          sku: string
          name: string
          description: string
          shortDescription: string
          urlKey: string
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          brand?: {
            __typename?: 'Brand'
            name: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
          bundled: Array<{
            __typename?: 'SimpleProduct'
            id: string
            sku: string
            name: string
            description: string
            shortDescription: string
            urlKey: string
            measures: { __typename?: 'ProductMeasures'; base: string; secondary: string; secondaryQty: number }
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            price: {
              __typename?: 'ProductPrice'
              price: { __typename?: 'Price'; value: number; currency: string }
              special?: { __typename?: 'Price'; value: number; currency: string } | null
            }
            gallery: Array<{
              __typename?: 'GalleryImage'
              position: number
              image: {
                __typename?: 'Image'
                src: string
                mobileSrc?: string | null
                alt?: string | null
                title?: string | null
              }
            }>
            labels: {
              __typename?: 'MagentoLabels'
              warrantyMonths: number
              buyCheap?: boolean | null
              freeDelivery?: boolean | null
              fromBrochure?: boolean | null
              other?: Array<string> | null
            }
            skuAvailability: Array<{
              __typename?: 'StoreAvailabilityItem'
              available: AvailabilityStatus
              sample: boolean
              store: {
                __typename?: 'PraktisStore'
                identity: string
                warehouseCode: string
                name: string
                displayOrder: number
                acceptOrders: boolean
                location: { __typename?: 'MapLocation'; lat: number; lng: number }
              }
            }>
          }>
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
        }
      | {
          __typename: 'SimpleProduct'
          id: string
          sku: string
          name: string
          urlKey: string
          description: string
          shortDescription: string
          image: {
            __typename?: 'Image'
            src: string
            mobileSrc?: string | null
            alt?: string | null
            title?: string | null
          }
          gallery: Array<{
            __typename?: 'GalleryImage'
            position: number
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          }>
          videos?: Array<{
            __typename?: 'ProductVideo'
            id: string
            title: string
            description?: string | null
            url: string
            videoType: string
            thumbnail?: string | null
            position: number
          }> | null
          measures: {
            __typename?: 'ProductMeasures'
            base: string
            secondary: string
            secondaryQty: number
            secondaryMeasureUsed: boolean
          }
          price: {
            __typename?: 'ProductPrice'
            price: { __typename?: 'Price'; value: number; currency: string }
            special?: { __typename?: 'Price'; value: number; currency: string } | null
          }
          skuAvailability: Array<{
            __typename?: 'StoreAvailabilityItem'
            available: AvailabilityStatus
            sample: boolean
            store: {
              __typename?: 'PraktisStore'
              identity: string
              warehouseCode: string
              name: string
              displayOrder: number
              acceptOrders: boolean
              location: { __typename?: 'MapLocation'; lat: number; lng: number }
            }
          }>
          stock: {
            __typename?: 'ProductStock'
            blockForSale: boolean
            hasImages: boolean
            inStock: boolean
            manageStock: boolean
            qty: number
            minQty: number
            zeronBlockedDelivery: boolean
            zeronSiteStatus: string
          }
          brand?: {
            __typename?: 'Brand'
            name: string
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
            url: { __typename?: 'Link'; href: string; text: string; title?: string | null }
          } | null
          energyLabel?: {
            __typename?: 'EnergyLabel'
            infoUrl?: string | null
            labelUrl?: string | null
            image: {
              __typename?: 'Image'
              src: string
              mobileSrc?: string | null
              alt?: string | null
              title?: string | null
            }
          } | null
          labels: {
            __typename?: 'MagentoLabels'
            warrantyMonths: number
            buyCheap?: boolean | null
            freeDelivery?: boolean | null
            fromBrochure?: boolean | null
            other?: Array<string> | null
          }
        }
    >
  }
}

export const SignUpResponseFragmentDoc = gql`
  fragment SignUpResponse on RegistrationResponse {
    create
    requireConfirmation
  }
`
export const CustomerAuthDataFragmentDoc = gql`
  fragment CustomerAuthData on Customer {
    id
    token
  }
`
export const StaticBlockFragmentDoc = gql`
  fragment StaticBlock on CMSBlock {
    title
    identifier
    content
  }
`
export const AppBlogPostFragmentDoc = gql`
  fragment AppBlogPost on BlogPost {
    urlKey
    title
    summary
    previewImageUrl
    content
    mainImageUrl
    publishedAt
  }
`
export const BnpPricingSchemeFragmentDoc = gql`
  fragment BNPPricingScheme on BNPPricingScheme {
    id
    name
  }
`
export const PricingVariantFragmentDoc = gql`
  fragment PricingVariant on PricingVariant {
    id
    apr
    correctDownpaymentAmount
    installmentAmount
    maturity
    nir
    pricingSchemeId
    pricingSchemeName
    totalRepaymentAmount
    processingFeeAmount
  }
`
export const BnpVariantGroupFragmentDoc = gql`
  fragment BNPVariantGroup on BNPVariantGroup {
    schemeId
    variants {
      ...PricingVariant
    }
  }
  ${PricingVariantFragmentDoc}
`
export const LoanCalculationFragmentDoc = gql`
  fragment LoanCalculation on LoanCalculation {
    apr
    correctDownpaymentAmount
    installmentAmount
    maturity
    nir
    pricingSchemeId
    pricingSchemeName
    pricingVariantId
    processingFeeAmount
    totalRepaymentAmount
  }
`
export const AvailablePaymentMethodFragmentDoc = gql`
  fragment AvailablePaymentMethod on PaymentMethod {
    name
    code
    extraContent
  }
`
export const CompanyInvoiceFragmentDoc = gql`
  fragment CompanyInvoice on CompanyInvoice {
    name
    mol
    eik
    vat
  }
`
export const IndividualInvoiceFragmentDoc = gql`
  fragment IndividualInvoice on IndividualInvoice {
    name
    egn
    vat
  }
`
export const CustomerInvoiceFragmentDoc = gql`
  fragment CustomerInvoice on CustomerInvoice {
    id
    type
    city
    address
    company {
      ...CompanyInvoice
    }
    individual {
      ...IndividualInvoice
    }
  }
  ${CompanyInvoiceFragmentDoc}
  ${IndividualInvoiceFragmentDoc}
`
export const OrderCustomerFragmentDoc = gql`
  fragment OrderCustomer on OrderCustomer {
    firstName
    lastName
    email
    phone
    invoice {
      ...CustomerInvoice
    }
  }
  ${CustomerInvoiceFragmentDoc}
`
export const StoreCartItemLabelsFragmentDoc = gql`
  fragment StoreCartItemLabels on StoreCartItemLabels {
    freeShipping
    usePallet
  }
`
export const AppImageFragmentDoc = gql`
  fragment AppImage on Image {
    src
    mobileSrc
    alt
    title
  }
`
export const VideoFragmentDoc = gql`
  fragment Video on ProductVideo {
    id
    title
    description
    url
    videoType
    thumbnail
    position
  }
`
export const AppPriceFragmentDoc = gql`
  fragment AppPrice on Price {
    value
    currency
  }
`
export const AppProductPriceFragmentDoc = gql`
  fragment AppProductPrice on ProductPrice {
    price {
      ...AppPrice
    }
    special {
      ...AppPrice
    }
  }
  ${AppPriceFragmentDoc}
`
export const StoreAvailabilityFragmentDoc = gql`
  fragment StoreAvailability on StoreAvailabilityItem {
    available
    sample
    store {
      identity
      warehouseCode
      name
      displayOrder
      acceptOrders
      location {
        lat
        lng
      }
    }
  }
`
export const ProductStockFullFragmentDoc = gql`
  fragment ProductStockFull on ProductStock {
    blockForSale
    hasImages
    inStock
    manageStock
    qty
    minQty
    zeronBlockedDelivery
    zeronSiteStatus
  }
`
export const AppLinkFragmentDoc = gql`
  fragment AppLink on Link {
    href
    text
    title
  }
`
export const AppBrandFragmentDoc = gql`
  fragment AppBrand on Brand {
    name
    image {
      ...AppImage
    }
    url {
      ...AppLink
    }
  }
  ${AppImageFragmentDoc}
  ${AppLinkFragmentDoc}
`
export const AppEnergyLabelFragmentDoc = gql`
  fragment AppEnergyLabel on EnergyLabel {
    image {
      ...AppImage
    }
    infoUrl
    labelUrl
  }
  ${AppImageFragmentDoc}
`
export const AppLabelsFragmentDoc = gql`
  fragment AppLabels on MagentoLabels {
    warrantyMonths
    buyCheap
    freeDelivery
    fromBrochure
    other
  }
`
export const SimpleProductViewFragmentDoc = gql`
  fragment SimpleProductView on SimpleProduct {
    __typename
    id
    sku
    name
    urlKey
    image {
      ...AppImage
    }
    gallery {
      image {
        ...AppImage
      }
      position
    }
    videos {
      ...Video
    }
    measures {
      base
      secondary
      secondaryQty
      secondaryMeasureUsed
    }
    price {
      ...AppProductPrice
    }
    description
    shortDescription
    skuAvailability {
      ...StoreAvailability
    }
    stock {
      ...ProductStockFull
    }
    brand {
      ...AppBrand
    }
    energyLabel {
      ...AppEnergyLabel
    }
    labels {
      ...AppLabels
    }
  }
  ${AppImageFragmentDoc}
  ${VideoFragmentDoc}
  ${AppProductPriceFragmentDoc}
  ${StoreAvailabilityFragmentDoc}
  ${ProductStockFullFragmentDoc}
  ${AppBrandFragmentDoc}
  ${AppEnergyLabelFragmentDoc}
  ${AppLabelsFragmentDoc}
`
export const AppBundleItemFragmentDoc = gql`
  fragment AppBundleItem on SimpleProduct {
    id
    sku
    name
    measures {
      base
      secondary
      secondaryQty
    }
    image {
      ...AppImage
    }
    price {
      ...AppProductPrice
    }
    description
    gallery {
      image {
        ...AppImage
      }
      position
    }
    labels {
      ...AppLabels
    }
    shortDescription
    skuAvailability {
      ...StoreAvailability
    }
    urlKey
  }
  ${AppImageFragmentDoc}
  ${AppProductPriceFragmentDoc}
  ${AppLabelsFragmentDoc}
  ${StoreAvailabilityFragmentDoc}
`
export const BundleProductViewFragmentDoc = gql`
  fragment BundleProductView on BundleProduct {
    __typename
    id
    sku
    name
    gallery {
      image {
        ...AppImage
      }
      position
    }
    price {
      ...AppProductPrice
    }
    description
    shortDescription
    brand {
      ...AppBrand
    }
    labels {
      ...AppLabels
    }
    bundled {
      ...AppBundleItem
    }
    urlKey
    image {
      ...AppImage
    }
    skuAvailability {
      ...StoreAvailability
    }
  }
  ${AppImageFragmentDoc}
  ${AppProductPriceFragmentDoc}
  ${AppBrandFragmentDoc}
  ${AppLabelsFragmentDoc}
  ${AppBundleItemFragmentDoc}
  ${StoreAvailabilityFragmentDoc}
`
export const ProductViewFragmentDoc = gql`
  fragment ProductView on Product {
    __typename
    ... on SimpleProduct {
      ...SimpleProductView
    }
    ... on BundleProduct {
      ...BundleProductView
    }
  }
  ${SimpleProductViewFragmentDoc}
  ${BundleProductViewFragmentDoc}
`
export const CartItemFragmentDoc = gql`
  fragment CartItem on StoreCartItem {
    id
    sku
    labels {
      ...StoreCartItemLabels
    }
    product {
      ...ProductView
    }
    baseQty
    discountAmount {
      ...AppPrice
    }
    discountPercent
    price {
      ...AppPrice
    }
    rowTotal {
      ...AppPrice
    }
  }
  ${StoreCartItemLabelsFragmentDoc}
  ${ProductViewFragmentDoc}
  ${AppPriceFragmentDoc}
`
export const CartItemAvailableInFragmentDoc = gql`
  fragment CartItemAvailableIn on PraktisStore {
    ID
    identity
    name
    city
    address
    phone
    warehouseCode
  }
`
export const ShippingAddressFragmentDoc = gql`
  fragment ShippingAddress on ShippingAddress {
    id
    skus
    method
    officeCode
    storeCode
    city
    cityId
    postCode
    street
  }
`
export const CartShippingFullFragmentDoc = gql`
  fragment CartShippingFull on CartShipping {
    availableMethods
    availableIn {
      ...CartItemAvailableIn
    }
    hasFreeShipping
    freeShippingAfter
    minAmountForFreeShippingMessage
    selectedMethod
    address {
      ...ShippingAddress
    }
  }
  ${CartItemAvailableInFragmentDoc}
  ${ShippingAddressFragmentDoc}
`
export const CartTotalFragmentDoc = gql`
  fragment CartTotal on CartTotal {
    code
    label
    amount {
      ...AppPrice
    }
    order
  }
  ${AppPriceFragmentDoc}
`
export const CartFullFragmentDoc = gql`
  fragment CartFull on StoreCart {
    id
    token
    storeCode
    currency
    couponCode
    note
    customer {
      ...OrderCustomer
    }
    items {
      ...CartItem
    }
    shipping {
      ...CartShippingFull
    }
    paymentMethod
    totals {
      ...CartTotal
    }
  }
  ${OrderCustomerFragmentDoc}
  ${CartItemFragmentDoc}
  ${CartShippingFullFragmentDoc}
  ${CartTotalFragmentDoc}
`
export const CartViewFragmentDoc = gql`
  fragment CartView on StoreCart {
    id
    token
    totals {
      code
      label
      amount {
        value
        currency
      }
    }
    items {
      id
      sku
      product {
        ...ProductView
      }
      labels {
        freeShipping
        usePallet
      }
      baseQty
      price {
        value
        currency
      }
      rowTotal {
        value
        currency
      }
      discountAmount {
        value
        currency
      }
      discountPercent
    }
    storeCode
    couponCode
    currency
  }
  ${ProductViewFragmentDoc}
`
export const ShippingMethodFragmentDoc = gql`
  fragment ShippingMethod on ShippingMethod {
    name
    code
  }
`
export const AvailableShippingMethodFragmentDoc = gql`
  fragment AvailableShippingMethod on AvailableShippingMethod {
    method {
      ...ShippingMethod
    }
    price {
      ...AppPrice
    }
  }
  ${ShippingMethodFragmentDoc}
  ${AppPriceFragmentDoc}
`
export const CustomerAddressFullFragmentDoc = gql`
  fragment CustomerAddressFull on CustomerAddress {
    id
    firstName
    lastName
    phone
    companyName
    country
    city
    cityID
    postCode
    street
  }
`
export const CustomerFullFragmentDoc = gql`
  fragment CustomerFull on Customer {
    id
    token
    isSubscribed
    firstName
    lastName
    email
    defaultBillingAddressID
    defaultShippingAddressID
    addresses {
      ...CustomerAddressFull
    }
  }
  ${CustomerAddressFullFragmentDoc}
`
export const EcontCountryFragmentDoc = gql`
  fragment EcontCountry on EcontCountry {
    code2
    code3
    id
    isEU
    name
    nameEn
  }
`
export const EcontCityFragmentDoc = gql`
  fragment EcontCity on EcontCity {
    country {
      ...EcontCountry
    }
    id
    name
    nameEn
    postCode
    regionCode
    regionName
    regionNameEn
    type
  }
  ${EcontCountryFragmentDoc}
`
export const EcontOfficeLocationFragmentDoc = gql`
  fragment EcontOfficeLocation on EcontOfficeLocation {
    latitude
    longitude
  }
`
export const EcontOfficeAddressFragmentDoc = gql`
  fragment EcontOfficeAddress on EcontOfficeAddress {
    city {
      ...EcontCity
    }
    fullAddress
    fullAddressEn
    location {
      ...EcontOfficeLocation
    }
  }
  ${EcontCityFragmentDoc}
  ${EcontOfficeLocationFragmentDoc}
`
export const EcontOfficeFragmentDoc = gql`
  fragment EcontOffice on EcontOffice {
    id
    code
    name
    nameEn
    phones
    isAPS
    isMPS
    adderess {
      ...EcontOfficeAddress
    }
  }
  ${EcontOfficeAddressFragmentDoc}
`
export const AppMenuFragmentDoc = gql`
  fragment AppMenu on Menu {
    categories {
      url
      thumbnail
      name
      children {
        name
        url
      }
      sideSection {
        image
        url
        rawHtml
      }
    }
  }
`
export const AppContactsFragmentDoc = gql`
  fragment AppContacts on StoreContacts {
    general {
      phone
    }
    onlineStore {
      phone
    }
  }
`
export const AppFooterFragmentDoc = gql`
  fragment AppFooter on Footer {
    columns {
      links {
        ...AppLink
      }
      title
    }
    appLinks {
      android
      ios
    }
    contacts {
      ...AppContacts
    }
    social {
      facebook
      viber
      youtube
    }
  }
  ${AppLinkFragmentDoc}
  ${AppContactsFragmentDoc}
`
export const AppLogoFragmentDoc = gql`
  fragment AppLogo on StoreLogo {
    url
    width
    height
    alt
  }
`
export const OrderStatusFullFragmentDoc = gql`
  fragment OrderStatusFull on OrderStatus {
    label
    code
  }
`
export const MapValueFullFragmentDoc = gql`
  fragment MapValueFull on MapValue {
    key
    value
  }
`
export const OrderRedirectFullFragmentDoc = gql`
  fragment OrderRedirectFull on OrderRedirect {
    url
    data {
      ...MapValueFull
    }
  }
  ${MapValueFullFragmentDoc}
`
export const PlaceOrderFragmentDoc = gql`
  fragment PlaceOrder on PlaceOrderResponse {
    orderNumber
    status {
      ...OrderStatusFull
    }
    redirect {
      ...OrderRedirectFull
    }
  }
  ${OrderStatusFullFragmentDoc}
  ${OrderRedirectFullFragmentDoc}
`
export const StoreGalleryImageFragmentDoc = gql`
  fragment StoreGalleryImage on StoreImage {
    ID
    praktisStoreID
    position
    alt
    src
    title
    mobileSrc
    isPrimary
  }
`
export const ServiceFragmentDoc = gql`
  fragment Service on Service {
    id
    name
    url
    iconUrl
  }
`
export const AppStoreFragmentDoc = gql`
  fragment AppStore on PraktisStore {
    name
    address
    city
    email
    phone
    virtualTour
    transportInformation
    gallery {
      ...StoreGalleryImage
    }
    location {
      lat
      lng
    }
    businessHours {
      open
      close
      day
    }
    services {
      ...Service
    }
    descriptionArea {
      backgroundImage
      content
      formID
      title
    }
    metaDescription
    metaKeywords
    metaTitle
  }
  ${StoreGalleryImageFragmentDoc}
  ${ServiceFragmentDoc}
`
export const StorePageDataFragmentDoc = gql`
  fragment StorePageData on StorePageData {
    messageBlock
    store {
      ...AppStore
    }
  }
  ${AppStoreFragmentDoc}
`
export const AppStorePreviewFragmentDoc = gql`
  fragment AppStorePreview on PraktisStore {
    name
    identity
    address
    email
    phone
    city
    displayOrder
    gallery {
      ...StoreGalleryImage
    }
    businessHours {
      open
      close
      day
    }
  }
  ${StoreGalleryImageFragmentDoc}
`
export const ServiceWidgetFragmentFragmentDoc = gql`
  fragment ServiceWidgetFragment on ServiceWidget {
    link {
      ...AppLink
    }
    title
    subtitle
    services {
      ...Service
    }
    availableStores {
      ...AppStorePreview
    }
    form {
      formId
      text
      title
    }
  }
  ${AppLinkFragmentDoc}
  ${ServiceFragmentDoc}
  ${AppStorePreviewFragmentDoc}
`
export const NewsWidgetFragmentFragmentDoc = gql`
  fragment NewsWidgetFragment on NewsWidget {
    title
    subtitle
    identifier
    link {
      href
      text
      title
    }
    articles {
      title
      description
      image {
        alt
        mobileSrc
        src
        title
      }
      link {
        href
        text
        title
      }
    }
  }
`
export const WidgetSlideFragmentDoc = gql`
  fragment WidgetSlide on CarouselSlide {
    brand {
      name
      url {
        ...AppLink
      }
      image {
        ...AppImage
      }
    }
    title
    description
    features {
      ...AppImage
    }
    image {
      ...AppImage
    }
    link {
      ...AppLink
    }
    price {
      ...AppPrice
    }
    priceLabels {
      ...AppLabels
    }
  }
  ${AppLinkFragmentDoc}
  ${AppImageFragmentDoc}
  ${AppPriceFragmentDoc}
  ${AppLabelsFragmentDoc}
`
export const CarouselWidgetFragmentFragmentDoc = gql`
  fragment CarouselWidgetFragment on CarouselWidget {
    identifier
    slides {
      ...WidgetSlide
    }
  }
  ${WidgetSlideFragmentDoc}
`
export const AppTileContentFragmentDoc = gql`
  fragment AppTileContent on TileContent {
    icon {
      ...AppImage
    }
    link {
      ...AppLink
    }
    title
    text
  }
  ${AppImageFragmentDoc}
  ${AppLinkFragmentDoc}
`
export const AppImageTileFragmentDoc = gql`
  fragment AppImageTile on ImageTile {
    bgColor
    image {
      ...AppImage
    }
    position
    content {
      ...AppTileContent
    }
  }
  ${AppImageFragmentDoc}
  ${AppTileContentFragmentDoc}
`
export const AppDoubleImageTileFragmentDoc = gql`
  fragment AppDoubleImageTile on DoubleImageTile {
    imageOnePosition
    imageOne {
      ...AppImage
    }
    contentOne {
      ...AppTileContent
    }
    imageTwoPosition
    imageTwo {
      ...AppImage
    }
    contentTwo {
      ...AppTileContent
    }
  }
  ${AppImageFragmentDoc}
  ${AppTileContentFragmentDoc}
`
export const AppCategoryTileFragmentDoc = gql`
  fragment AppCategoryTile on CategoryTile {
    image {
      ...AppImage
    }
    title
    bgColor
    textColor
    categories {
      ...AppLink
    }
  }
  ${AppImageFragmentDoc}
  ${AppLinkFragmentDoc}
`
export const TilesWidgetFragmentFragmentDoc = gql`
  fragment TilesWidgetFragment on TilesWidget {
    identifier
    viewMore {
      href
      text
      title
    }
    title
    subtitle
    rows {
      cols {
        __typename
        ... on ImageTile {
          ...AppImageTile
        }
        ... on DoubleImageTile {
          ...AppDoubleImageTile
        }
        ... on CategoryTile {
          ...AppCategoryTile
        }
      }
    }
  }
  ${AppImageTileFragmentDoc}
  ${AppDoubleImageTileFragmentDoc}
  ${AppCategoryTileFragmentDoc}
`
export const TbiCreditDataFragmentDoc = gql`
  fragment TBICreditData on TbiConfiguration {
    tbi_minstojnost
    tbi_minstojnost_bnpl
    tbi_maxstojnost
    tbi_maxstojnost_bnpl
    tbi_zaglavie
    tbi_custom_button_status
    tbi_purcent_default
    reklama
    tbi_5m_purcent_default
    tbi_5m
    tbi_5m_categories
    tbi_5m_manufacturers
    tbi_5m_min
    tbi_5m_max
    tbi_5m_pv
    tbi_4m
    tbi_4m_categories
    tbi_4m_manufacturers
    tbi_4m_min
    tbi_4m_max
    tbi_4m_pv
    tbi_6m
    tbi_6m_categories
    tbi_6m_manufacturers
    tbi_6m_min
    tbi_6m_max
    tbi_6m_pv
    tbi_9m
    tbi_9m_categories
    tbi_9m_manufacturers
    tbi_9m_min
    tbi_9m_max
    tbi_9m_pv
    tbi_12m
    tbi_12m_categories
    tbi_12m_manufacturers
    tbi_12m_min
    tbi_12m_max
    tbi_12m_pv
    tbi_3m_purcent
    tbi_4m_purcent
    tbi_6m_purcent
    tbi_7m_purcent
    tbi_9m_purcent
    tbi_11m_purcent
    tbi_12m_purcent
    tbi_15m_purcent
    tbi_18m_purcent
    tbi_24m_purcent
    tbi_30m_purcent
    tbi_36m_purcent
    tbi_42m_purcent
    tbi_48m_purcent
    tbi_54m_purcent
    tbi_60m_purcent
    tbi_over_5000
    tbi_status
    tbi_bnpl
    tbi_button_status
    tbi_button_text_visible
    tbi_button_kvadrat
    tbi_is_direct
    tbi_is_cart
    tbi_eur
  }
`
export const CustomerDataFullFragmentDoc = gql`
  fragment CustomerDataFull on Customer {
    defaultBillingAddressID
    defaultShippingAddressID
    firstName
    lastName
    isSubscribed
    email
    addresses {
      ...CustomerAddressFull
    }
  }
  ${CustomerAddressFullFragmentDoc}
`
export const AppCategoryLinkWidgetFragmentDoc = gql`
  fragment AppCategoryLinkWidget on CategoryLinkWidget {
    title
    url {
      href
      text
      title
    }
    image {
      alt
      src
      title
      mobileSrc
    }
  }
`
export const AppHtmlWidgetFragmentDoc = gql`
  fragment AppHtmlWidget on HtmlWidget {
    html
  }
`
export const OrderItemFullFragmentDoc = gql`
  fragment OrderItemFull on OrderItem {
    id
    sku
    product {
      ...ProductView
    }
    baseQty
    discountAmount {
      ...AppPrice
    }
    discountPercent
    price {
      ...AppPrice
    }
    priceWithoutDiscount {
      ...AppPrice
    }
    rowTotal {
      ...AppPrice
    }
    rowTotalWithoutDiscount {
      ...AppPrice
    }
  }
  ${ProductViewFragmentDoc}
  ${AppPriceFragmentDoc}
`
export const PaymentMethodFragmentDoc = gql`
  fragment PaymentMethod on PaymentMethod {
    name
    code
    extraContent
  }
`
export const OrderAddressFullFragmentDoc = gql`
  fragment OrderAddressFull on OrderAddress {
    firstName
    lastName
    email
    telephone
    city
    postcode
    street
  }
`
export const OrderFullFragmentDoc = gql`
  fragment OrderFull on StoreOrder {
    incrementId
    state
    status {
      ...OrderStatusFull
    }
    couponCode
    protectCode
    createAt
    items {
      ...OrderItemFull
    }
    totals {
      ...CartTotal
    }
    shippingMethod {
      ...ShippingMethod
    }
    paymentMethod {
      ...PaymentMethod
    }
    note
    shippingAddress {
      ...OrderAddressFull
    }
    invoice {
      ...CustomerInvoice
    }
  }
  ${OrderStatusFullFragmentDoc}
  ${OrderItemFullFragmentDoc}
  ${CartTotalFragmentDoc}
  ${ShippingMethodFragmentDoc}
  ${PaymentMethodFragmentDoc}
  ${OrderAddressFullFragmentDoc}
  ${CustomerInvoiceFragmentDoc}
`
export const DataFragmentDoc = gql`
  fragment Data on Customer {
    id
    orders {
      ...OrderFull
    }
  }
  ${OrderFullFragmentDoc}
`
export const SearchResultProductFragmentDoc = gql`
  fragment SearchResultProduct on Product {
    ... on SimpleProduct {
      sku
      shortDescription
      name
      urlKey
      image {
        src
        title
      }
      price {
        price {
          value
          currency
        }
        special {
          value
          currency
        }
        specialFrom
        specialTo
      }
    }
    ... on BundleProduct {
      sku
      shortDescription
      name
      urlKey
      image {
        src
        title
      }
      price {
        price {
          value
          currency
        }
        special {
          value
          currency
        }
        specialFrom
        specialTo
      }
    }
  }
`
export const SearchPageResultsFragmentDoc = gql`
  fragment SearchPageResults on SearchResults {
    totalItems
    block {
      title
      content
    }
    categories {
      name
      url
    }
    popularTerms
    products {
      ...ProductView
    }
  }
  ${ProductViewFragmentDoc}
`
export const AppMetaFragmentDoc = gql`
  fragment AppMeta on PageMeta {
    title
    description
    keywords
    image {
      src
    }
    keywords
    robots
    canonicalUrl
  }
`
export const AppAppliedFilterFragmentDoc = gql`
  fragment AppAppliedFilter on AppliedFilter {
    label
    value
    requestVar
    attributeCode
  }
`
export const AppFilterOptionFragmentDoc = gql`
  fragment AppFilterOption on AttributeOption {
    label
    value
    order
  }
`
export const AppAvailableFilterFragmentDoc = gql`
  fragment AppAvailableFilter on AvailableFilter {
    position
    type
    requestVar
    attributeCode
    label
    options {
      ...AppFilterOption
    }
  }
  ${AppFilterOptionFragmentDoc}
`
export const AppFilterStateFragmentDoc = gql`
  fragment AppFilterState on CatalogState {
    filters {
      applied {
        ...AppAppliedFilter
      }
      available {
        ...AppAvailableFilter
      }
    }
    pager {
      page
      pageSize
      totalPages
      totalItems
    }
    sort {
      value
      dir
    }
  }
  ${AppAppliedFilterFragmentDoc}
  ${AppAvailableFilterFragmentDoc}
`
export const AppSplashViewFragmentDoc = gql`
  fragment AppSplashView on SplashPage {
    __typename
    ... on SplashPage {
      splashDescription: description
      title
      url
    }
  }
`
export const AppCategoryViewWidgetFragmentDoc = gql`
  fragment AppCategoryViewWidget on CategoryWidget {
    __typename
    ... on CategoryLinkWidget {
      title
      url {
        href
        text
        title
      }
      image {
        alt
        src
        title
        mobileSrc
      }
    }
  }
`
export const AppCategoryViewFragmentDoc = gql`
  fragment AppCategoryView on Category {
    __typename
    id
    name
    url
    description
    image {
      ...AppImage
    }
    icon {
      ...AppImage
    }
    banner {
      image {
        ...AppImage
      }
      url
    }
    widgets {
      ...AppCategoryViewWidget
    }
  }
  ${AppImageFragmentDoc}
  ${AppCategoryViewWidgetFragmentDoc}
`
export const AppSearchCategoryFragmentDoc = gql`
  fragment AppSearchCategory on SearchCategory {
    __typename
    ... on SearchCategory {
      name
      url
    }
  }
`
export const CatalogPageDataFragmentDoc = gql`
  fragment CatalogPageData on CatalogPage {
    __typename
    layout
    state {
      ...AppFilterState
    }
    category {
      ... on SplashPage {
        ...AppSplashView
      }
      ... on Category {
        ...AppCategoryView
      }
      ... on SearchCategory {
        ...AppSearchCategory
      }
    }
    products {
      ...ProductView
    }
  }
  ${AppFilterStateFragmentDoc}
  ${AppSplashViewFragmentDoc}
  ${AppCategoryViewFragmentDoc}
  ${AppSearchCategoryFragmentDoc}
  ${ProductViewFragmentDoc}
`
export const CmsPageDataFragmentDoc = gql`
  fragment CMSPageData on CMSPage {
    __typename
    links {
      ...AppLink
    }
    identifier
    title
    content
  }
  ${AppLinkFragmentDoc}
`
export const AppProductCardFragmentDoc = gql`
  fragment AppProductCard on Product {
    __typename
    ... on SimpleProduct {
      id
      sku
      name
      urlKey
      image {
        ...AppImage
      }
      price {
        ...AppProductPrice
      }
      energyLabel {
        ...AppEnergyLabel
      }
      labels {
        ...AppLabels
      }
      stock {
        ...ProductStockFull
      }
    }
    ... on BundleProduct {
      id
      sku
      name
      urlKey
      image {
        ...AppImage
      }
      price {
        ...AppProductPrice
      }
      labels {
        ...AppLabels
      }
    }
  }
  ${AppImageFragmentDoc}
  ${AppProductPriceFragmentDoc}
  ${AppEnergyLabelFragmentDoc}
  ${AppLabelsFragmentDoc}
  ${ProductStockFullFragmentDoc}
`
export const AppProductsSliderWidgetFragmentDoc = gql`
  fragment AppProductsSliderWidget on ProductsSliderWidget {
    identifier
    title
    subtitle
    tabs {
      title
      products {
        ...AppProductCard
      }
    }
  }
  ${AppProductCardFragmentDoc}
`
export const ProductPageDataFragmentDoc = gql`
  fragment ProductPageData on ProductPage {
    __typename
    product {
      ...ProductView
    }
    widgets {
      __typename
      ...AppProductsSliderWidget
    }
    boughtTogether {
      ...AppProductCard
    }
  }
  ${ProductViewFragmentDoc}
  ${AppProductsSliderWidgetFragmentDoc}
  ${AppProductCardFragmentDoc}
`
export const DynamicPageDataFragmentDoc = gql`
  fragment DynamicPageData on PageData {
    __typename
    ... on CatalogPage {
      ...CatalogPageData
    }
    ... on CMSPage {
      ...CMSPageData
    }
    ... on ProductPage {
      ...ProductPageData
    }
    ... on ErrorData {
      code
      message
    }
  }
  ${CatalogPageDataFragmentDoc}
  ${CmsPageDataFragmentDoc}
  ${ProductPageDataFragmentDoc}
`
export const DynamicRouteDataFragmentDoc = gql`
  fragment DynamicRouteData on Page {
    breadcrumbs {
      label
      url
      children {
        label
        url
      }
      siblings {
        label
        url
      }
    }
    status {
      redirectUrl
      statusCode
    }
    data {
      ...DynamicPageData
    }
  }
  ${DynamicPageDataFragmentDoc}
`
export const CustomerLoginDocument = gql`
  mutation CustomerLogin($email: String!, $password: String!) {
    customerLogin(email: $email, password: $password) {
      ...CustomerAuthData
    }
  }
  ${CustomerAuthDataFragmentDoc}
`
export const CustomerRegisterDocument = gql`
  mutation CustomerRegister($data: CustomerRegistrationData!) {
    customerRegister(data: $data) {
      ...SignUpResponse
    }
  }
  ${SignUpResponseFragmentDoc}
`
export const CustomerAuthDocument = gql`
  mutation CustomerAuth {
    customerLoginRefresh {
      ...CustomerAuthData
    }
  }
  ${CustomerAuthDataFragmentDoc}
`
export const CustomerLogoutDocument = gql`
  mutation CustomerLogout {
    customerLogout
  }
`
export const CartSaveBnpPaymentDocument = gql`
  mutation CartSaveBNPPayment($cartToken: String!, $paymentData: BNPPaymentInput!) {
    cartSaveBNPPayment(cartToken: $cartToken, paymentData: $paymentData) {
      ...CartFull
    }
  }
  ${CartFullFragmentDoc}
`
export const SubmitBnpApplicationDocument = gql`
  mutation SubmitBNPApplication($orderNumber: String!) {
    submitBNPApplication(orderNumber: $orderNumber)
  }
`
export const CartAddDocument = gql`
  mutation CartAdd($cartToken: String!, $sku: String!, $quantity: Float!) {
    cartItemAdd(cartToken: $cartToken, item: { baseQty: $quantity, sku: $sku }) {
      ...CartFull
    }
  }
  ${CartFullFragmentDoc}
`
export const CartUpdateDocument = gql`
  mutation CartUpdate($cartToken: String!, $sku: String!, $quantity: Float!) {
    cartItemUpdate(cartToken: $cartToken, item: { baseQty: $quantity, sku: $sku }) {
      ...CartFull
    }
  }
  ${CartFullFragmentDoc}
`
export const CartRemoveDocument = gql`
  mutation CartRemove($cartToken: String!, $sku: String!) {
    cartItemRemove(cartToken: $cartToken, sku: $sku) {
      ...CartFull
    }
  }
  ${CartFullFragmentDoc}
`
export const ApplyCouponCodeDocument = gql`
  mutation ApplyCouponCode($cartToken: String!, $couponCode: String!) {
    cartApplyCoupon(cartToken: $cartToken, couponCode: $couponCode) {
      ...CartFull
    }
  }
  ${CartFullFragmentDoc}
`
export const SubscribeDocument = gql`
  mutation Subscribe($email: String!) {
    storeNewsletterSubscribe(email: $email)
  }
`
export const CartSaveClientDocument = gql`
  mutation CartSaveClient($cartToken: String!, $data: ClientInput!) {
    cartSaveClient(cartToken: $cartToken, data: $data) {
      ...CartFull
    }
  }
  ${CartFullFragmentDoc}
`
export const GetShippingMethodsDocument = gql`
  mutation GetShippingMethods($cartToken: String!, $data: ShippingInput!) {
    cartAvailableShippingMethods(cartToken: $cartToken, data: $data) {
      ...AvailableShippingMethod
    }
  }
  ${AvailableShippingMethodFragmentDoc}
`
export const CartSaveShippingMethodDocument = gql`
  mutation CartSaveShippingMethod($cartToken: String!, $data: ShippingInput!, $code: String!) {
    cartSaveShipping(cartToken: $cartToken, shippingMethodCode: $code, data: $data) {
      ...CartFull
    }
  }
  ${CartFullFragmentDoc}
`
export const CartSavePaymentMethodDocument = gql`
  mutation CartSavePaymentMethod($cartToken: String!, $code: String!) {
    cartSavePayment(cartToken: $cartToken, paymentMethodCode: $code) {
      ...CartFull
    }
  }
  ${CartFullFragmentDoc}
`
export const GetPaymentMethodsDocument = gql`
  mutation GetPaymentMethods($cartToken: String!) {
    cartAvailablePaymentMethods(cartToken: $cartToken) {
      ...AvailablePaymentMethod
    }
  }
  ${AvailablePaymentMethodFragmentDoc}
`
export const CreateOrderDocument = gql`
  mutation CreateOrder($cartToken: String!, $data: NewOrderInput!) {
    placeOrder(cartToken: $cartToken, data: $data) {
      ...PlaceOrder
    }
  }
  ${PlaceOrderFragmentDoc}
`
export const SendContactMessageDocument = gql`
  mutation SendContactMessage($input: ContactInput!) {
    storeSendContactMessage(input: $input)
  }
`
export const UpdateCustomerDocument = gql`
  mutation UpdateCustomer($data: CustomerUpdateInput!) {
    customerUpdateInfo(data: $data) {
      ...CustomerFull
    }
  }
  ${CustomerFullFragmentDoc}
`
export const UpdateCustomerPasswordDocument = gql`
  mutation UpdateCustomerPassword($oldPassword: String!, $newPassword: String!) {
    customerUpdatePassword(oldPassword: $oldPassword, newPassword: $newPassword) {
      id
    }
  }
`
export const CreateCustomerAddressDocument = gql`
  mutation CreateCustomerAddress($data: CustomerAddressInput!) {
    customerAddressAdd(data: $data) {
      ...CustomerAddressFull
    }
  }
  ${CustomerAddressFullFragmentDoc}
`
export const UpdateCustomerAddressDocument = gql`
  mutation UpdateCustomerAddress($data: CustomerAddressInput!, $addressID: ID!) {
    customerAddressUpdate(data: $data, addressID: $addressID) {
      ...CustomerAddressFull
    }
  }
  ${CustomerAddressFullFragmentDoc}
`
export const DeleteCustomerAddressDocument = gql`
  mutation DeleteCustomerAddress($addressID: ID!) {
    customerAddressRemove(addressID: $addressID) {
      ...CustomerAddressFull
    }
  }
  ${CustomerAddressFullFragmentDoc}
`
export const ForgotPasswordDocument = gql`
  mutation ForgotPassword($email: String!) {
    customerPasswordForgot(email: $email)
  }
`
export const CustomerPasswordResetDocument = gql`
  mutation CustomerPasswordReset($customerId: ID!, $password: String!, $resetToken: String!) {
    customerPasswordReset(customerId: $customerId, password: $password, resetToken: $resetToken)
  }
`
export const SendInquiryDocument = gql`
  mutation SendInquiry($input: StoreInquiryInput!) {
    storeSendInquiry(input: $input)
  }
`
export const UnsubscribeDocument = gql`
  mutation Unsubscribe($id: ID!, $code: String!) {
    storeNewsletterUnsubscribe(id: $id, code: $code)
  }
`
export const WishlistAddDocument = gql`
  mutation WishlistAdd($sku: String!) {
    customerWishlistAdd(sku: $sku) {
      skus
    }
  }
`
export const WishlistRemoveDocument = gql`
  mutation WishlistRemove($sku: String!) {
    customerWishlistRemove(sku: $sku) {
      skus
    }
  }
`
export const GetAvailableServicesDocument = gql`
  query GetAvailableServices {
    availableServices {
      ...Service
    }
  }
  ${ServiceFragmentDoc}
`
export const GetBlogPostsDocument = gql`
  query GetBlogPosts($page: Int!, $size: Int!) {
    getBlogPosts(page: $page, size: $size) {
      posts {
        ...AppBlogPost
      }
      currentPage
      totalPages
    }
  }
  ${AppBlogPostFragmentDoc}
`
export const GetBlogPostDocument = gql`
  query GetBlogPost($identifier: String!) {
    getBlogPost(identifier: $identifier) {
      ...AppBlogPost
    }
  }
  ${AppBlogPostFragmentDoc}
`
export const GetFeaturedBlogPostDocument = gql`
  query GetFeaturedBlogPost {
    getFeaturedBlogPost {
      ...AppBlogPost
    }
  }
  ${AppBlogPostFragmentDoc}
`
export const GetBnpPricingSchemesDocument = gql`
  query GetBNPPricingSchemes($goodTypeIds: String!, $principal: Float!, $downPayment: Float!) {
    getBNPPricingSchemes(goodTypeIds: $goodTypeIds, principal: $principal, downPayment: $downPayment) {
      ...BNPPricingScheme
    }
  }
  ${BnpPricingSchemeFragmentDoc}
`
export const GetCreditCalculatorBnpParibasDocument = gql`
  query GetCreditCalculatorBNPParibas($sku: String!, $downPayment: Float!, $qty: Int!) {
    getCreditCalculatorBNPParibas(sku: $sku, downPayment: $downPayment, qty: $qty) {
      ...BNPVariantGroup
    }
  }
  ${BnpVariantGroupFragmentDoc}
`
export const GetCreditCalculatorQuoteBnpParibasDocument = gql`
  query GetCreditCalculatorQuoteBNPParibas($cartToken: String!, $downPayment: Float!) {
    getCreditCalculatorBNPParibasForQuote(cartToken: $cartToken, downPayment: $downPayment) {
      ...BNPVariantGroup
    }
  }
  ${BnpVariantGroupFragmentDoc}
`
export const CalculateBnpLoanDocument = gql`
  query CalculateBNPLoan($cartToken: String!, $downPayment: Float!, $pricingVariantId: Int!) {
    calculateBNPLoan(cartToken: $cartToken, downPayment: $downPayment, pricingVariantId: $pricingVariantId) {
      ...LoanCalculation
    }
  }
  ${LoanCalculationFragmentDoc}
`
export const GetBrandsDocument = gql`
  query GetBrands($featured: Boolean!) {
    getBrands(featured: $featured) {
      ...AppBrand
    }
  }
  ${AppBrandFragmentDoc}
`
export const GetEmptyCartDocument = gql`
  query GetEmptyCart {
    getNewCart {
      token
    }
  }
`
export const GetCartByIdDocument = gql`
  query GetCartById($cart: String!) {
    getCart(cartToken: $cart) {
      ...CartFull
    }
  }
  ${CartFullFragmentDoc}
`
export const GetTbiCreditDocument = gql`
  query GetTBICredit($sku: String!) {
    getCreditCalculatorTBIBank(sku: $sku) {
      ...TBICreditData
    }
  }
  ${TbiCreditDataFragmentDoc}
`
export const GetBnpCreditDocument = gql`
  query GetBNPCredit($sku: String!, $downPayment: Float!, $qty: Int!) {
    getCreditCalculatorBNPParibas(sku: $sku, downPayment: $downPayment, qty: $qty) {
      ...BNPVariantGroup
    }
  }
  ${BnpVariantGroupFragmentDoc}
`
export const CustomerDataDocument = gql`
  query CustomerData {
    customerData {
      ...CustomerDataFull
    }
  }
  ${CustomerDataFullFragmentDoc}
`
export const GetEcontOfficesDocument = gql`
  query GetEcontOffices($cityId: String!) {
    getEcontOffice(cityID: $cityId) {
      ...EcontOffice
    }
  }
  ${EcontOfficeFragmentDoc}
`
export const GetEcontCitiesDocument = gql`
  query GetEcontCities($country: String!) {
    getEcontCity(country: $country) {
      ...EcontCity
    }
  }
  ${EcontCityFragmentDoc}
`
export const GetHomePageDataDocument = gql`
  query GetHomePageData {
    getHomepage {
      heroSlider {
        ...CarouselWidgetFragment
      }
      widgets {
        __typename
        ... on CarouselWidget {
          ...CarouselWidgetFragment
        }
        ... on ProductsSliderWidget {
          ...AppProductsSliderWidget
        }
        ... on TilesWidget {
          ...TilesWidgetFragment
        }
        ... on CategoryLinkWidget {
          ...AppCategoryLinkWidget
        }
        ... on ServiceWidget {
          ...ServiceWidgetFragment
        }
        ... on NewsWidget {
          ...NewsWidgetFragment
        }
        ... on HtmlWidget {
          ...AppHtmlWidget
        }
      }
    }
    getBrands(featured: true) {
      ...AppBrand
    }
  }
  ${CarouselWidgetFragmentFragmentDoc}
  ${AppProductsSliderWidgetFragmentDoc}
  ${TilesWidgetFragmentFragmentDoc}
  ${AppCategoryLinkWidgetFragmentDoc}
  ${ServiceWidgetFragmentFragmentDoc}
  ${NewsWidgetFragmentFragmentDoc}
  ${AppHtmlWidgetFragmentDoc}
  ${AppBrandFragmentDoc}
`
export const GetCustomerOrdersDocument = gql`
  query GetCustomerOrders {
    customerData {
      ...Data
    }
  }
  ${DataFragmentDoc}
`
export const SearchFieldDocument = gql`
  query SearchField($text: String!) {
    search(searchQuery: $text) {
      categories {
        name
        url
      }
      popularTerms
      totalItems
      products {
        ...ProductView
      }
    }
  }
  ${ProductViewFragmentDoc}
`
export const SearchPageDocument = gql`
  query SearchPage($searchQuery: String!, $query: [QueryParam]) {
    searchPage(query: $query, searchQuery: $searchQuery) {
      status {
        statusCode
      }
      state {
        ...AppFilterState
      }
      title
      data {
        ...SearchPageResults
      }
    }
  }
  ${AppFilterStateFragmentDoc}
  ${SearchPageResultsFragmentDoc}
`
export const GetRouteMetaDocument = gql`
  query GetRouteMeta($url: String!, $query: [QueryParam]) {
    routeMeta(url: $url, query: $query) {
      ...AppMeta
    }
  }
  ${AppMetaFragmentDoc}
`
export const GetRouteDataDocument = gql`
  query GetRouteData($url: String!, $query: [QueryParam]) {
    route(url: $url, query: $query) {
      ...DynamicRouteData
    }
  }
  ${DynamicRouteDataFragmentDoc}
`
export const GetStaticBlockDocument = gql`
  query getStaticBlock($id: String!) {
    getStaticBlock(identifier: $id) {
      title
      content
    }
  }
`
export const GetStoreStaticDataDocument = gql`
  query GetStoreStaticData {
    getStaticContent {
      brochureLink
      logo {
        ...AppLogo
      }
      apiKeys {
        facebookLoginAppId
        googleLoginClientId
        googleMaps
        googleRecaptchaKey
      }
      gdpr {
        __typename
        rootId
        clarityId
        pixelId
        gtagId
        extraGTagsIds
        modal {
          __typename
          title
          content
          cookieGroups {
            __typename
            id
            title
            content
            cookiePatterns
            grants
            vendors {
              __typename
              keys
              config {
                __typename
                id
                name
                url
                cookieCount
                cookieDetails {
                  __typename
                  id
                  name
                  type
                  description
                  expiration
                }
              }
            }
          }
        }
      }
      menu {
        ...AppMenu
      }
      footer {
        ...AppFooter
      }
      messages {
        newsletter
        sendInquiryMessage
      }
      store {
        baseUrl
        contacts {
          ...AppContacts
        }
      }
    }
  }
  ${AppLogoFragmentDoc}
  ${AppMenuFragmentDoc}
  ${AppFooterFragmentDoc}
  ${AppContactsFragmentDoc}
`
export const GetGoogleApiKeyDocument = gql`
  query GetGoogleApiKey {
    getStaticContent {
      apiKeys {
        googleMaps
      }
    }
  }
`
export const GetGoogleClientIdDocument = gql`
  query GetGoogleClientId {
    getStaticContent {
      apiKeys {
        googleLoginClientId
      }
    }
  }
`
export const GetAvailableStoresDocument = gql`
  query GetAvailableStores {
    availableStores {
      ...AppStorePreview
    }
  }
  ${AppStorePreviewFragmentDoc}
`
export const GetStoreDocument = gql`
  query GetStore($uri: String!) {
    getStore(identity: $uri) {
      ...StorePageData
    }
  }
  ${StorePageDataFragmentDoc}
`
export const GetWishlistSkusDocument = gql`
  query GetWishlistSkus {
    customerWishlist {
      skus
    }
  }
`
export const GetWishlistProductsDocument = gql`
  query GetWishlistProducts {
    customerWishlist {
      products {
        ...ProductView
      }
    }
  }
  ${ProductViewFragmentDoc}
`

export type SdkFunctionWrapper = <T>(
  action: (requestHeaders?: Record<string, string>) => Promise<T>,
  operationName: string,
  operationType?: string,
  variables?: any
) => Promise<T>

const defaultWrapper: SdkFunctionWrapper = (action, _operationName, _operationType, _variables) => action()

export function getSdk(client: GraphQLClient, withWrapper: SdkFunctionWrapper = defaultWrapper) {
  return {
    CustomerLogin(
      variables: CustomerLoginMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CustomerLoginMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CustomerLoginMutation>(CustomerLoginDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CustomerLogin',
        'mutation',
        variables
      )
    },
    CustomerRegister(
      variables: CustomerRegisterMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CustomerRegisterMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CustomerRegisterMutation>(CustomerRegisterDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CustomerRegister',
        'mutation',
        variables
      )
    },
    CustomerAuth(
      variables?: CustomerAuthMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CustomerAuthMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CustomerAuthMutation>(CustomerAuthDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CustomerAuth',
        'mutation',
        variables
      )
    },
    CustomerLogout(
      variables?: CustomerLogoutMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CustomerLogoutMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CustomerLogoutMutation>(CustomerLogoutDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CustomerLogout',
        'mutation',
        variables
      )
    },
    CartSaveBNPPayment(
      variables: CartSaveBnpPaymentMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CartSaveBnpPaymentMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CartSaveBnpPaymentMutation>(CartSaveBnpPaymentDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CartSaveBNPPayment',
        'mutation',
        variables
      )
    },
    SubmitBNPApplication(
      variables: SubmitBnpApplicationMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<SubmitBnpApplicationMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<SubmitBnpApplicationMutation>(SubmitBnpApplicationDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'SubmitBNPApplication',
        'mutation',
        variables
      )
    },
    CartAdd(
      variables: CartAddMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CartAddMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CartAddMutation>(CartAddDocument, variables, { ...requestHeaders, ...wrappedRequestHeaders }),
        'CartAdd',
        'mutation',
        variables
      )
    },
    CartUpdate(
      variables: CartUpdateMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CartUpdateMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CartUpdateMutation>(CartUpdateDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CartUpdate',
        'mutation',
        variables
      )
    },
    CartRemove(
      variables: CartRemoveMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CartRemoveMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CartRemoveMutation>(CartRemoveDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CartRemove',
        'mutation',
        variables
      )
    },
    ApplyCouponCode(
      variables: ApplyCouponCodeMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<ApplyCouponCodeMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<ApplyCouponCodeMutation>(ApplyCouponCodeDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'ApplyCouponCode',
        'mutation',
        variables
      )
    },
    Subscribe(
      variables: SubscribeMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<SubscribeMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<SubscribeMutation>(SubscribeDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'Subscribe',
        'mutation',
        variables
      )
    },
    CartSaveClient(
      variables: CartSaveClientMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CartSaveClientMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CartSaveClientMutation>(CartSaveClientDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CartSaveClient',
        'mutation',
        variables
      )
    },
    GetShippingMethods(
      variables: GetShippingMethodsMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetShippingMethodsMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetShippingMethodsMutation>(GetShippingMethodsDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetShippingMethods',
        'mutation',
        variables
      )
    },
    CartSaveShippingMethod(
      variables: CartSaveShippingMethodMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CartSaveShippingMethodMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CartSaveShippingMethodMutation>(CartSaveShippingMethodDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CartSaveShippingMethod',
        'mutation',
        variables
      )
    },
    CartSavePaymentMethod(
      variables: CartSavePaymentMethodMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CartSavePaymentMethodMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CartSavePaymentMethodMutation>(CartSavePaymentMethodDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CartSavePaymentMethod',
        'mutation',
        variables
      )
    },
    GetPaymentMethods(
      variables: GetPaymentMethodsMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetPaymentMethodsMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetPaymentMethodsMutation>(GetPaymentMethodsDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetPaymentMethods',
        'mutation',
        variables
      )
    },
    CreateOrder(
      variables: CreateOrderMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CreateOrderMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CreateOrderMutation>(CreateOrderDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CreateOrder',
        'mutation',
        variables
      )
    },
    SendContactMessage(
      variables: SendContactMessageMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<SendContactMessageMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<SendContactMessageMutation>(SendContactMessageDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'SendContactMessage',
        'mutation',
        variables
      )
    },
    UpdateCustomer(
      variables: UpdateCustomerMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<UpdateCustomerMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<UpdateCustomerMutation>(UpdateCustomerDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'UpdateCustomer',
        'mutation',
        variables
      )
    },
    UpdateCustomerPassword(
      variables: UpdateCustomerPasswordMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<UpdateCustomerPasswordMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<UpdateCustomerPasswordMutation>(UpdateCustomerPasswordDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'UpdateCustomerPassword',
        'mutation',
        variables
      )
    },
    CreateCustomerAddress(
      variables: CreateCustomerAddressMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CreateCustomerAddressMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CreateCustomerAddressMutation>(CreateCustomerAddressDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CreateCustomerAddress',
        'mutation',
        variables
      )
    },
    UpdateCustomerAddress(
      variables: UpdateCustomerAddressMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<UpdateCustomerAddressMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<UpdateCustomerAddressMutation>(UpdateCustomerAddressDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'UpdateCustomerAddress',
        'mutation',
        variables
      )
    },
    DeleteCustomerAddress(
      variables: DeleteCustomerAddressMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<DeleteCustomerAddressMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<DeleteCustomerAddressMutation>(DeleteCustomerAddressDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'DeleteCustomerAddress',
        'mutation',
        variables
      )
    },
    ForgotPassword(
      variables: ForgotPasswordMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<ForgotPasswordMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<ForgotPasswordMutation>(ForgotPasswordDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'ForgotPassword',
        'mutation',
        variables
      )
    },
    CustomerPasswordReset(
      variables: CustomerPasswordResetMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CustomerPasswordResetMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CustomerPasswordResetMutation>(CustomerPasswordResetDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CustomerPasswordReset',
        'mutation',
        variables
      )
    },
    SendInquiry(
      variables: SendInquiryMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<SendInquiryMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<SendInquiryMutation>(SendInquiryDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'SendInquiry',
        'mutation',
        variables
      )
    },
    Unsubscribe(
      variables: UnsubscribeMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<UnsubscribeMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<UnsubscribeMutation>(UnsubscribeDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'Unsubscribe',
        'mutation',
        variables
      )
    },
    WishlistAdd(
      variables: WishlistAddMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<WishlistAddMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<WishlistAddMutation>(WishlistAddDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'WishlistAdd',
        'mutation',
        variables
      )
    },
    WishlistRemove(
      variables: WishlistRemoveMutationVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<WishlistRemoveMutation> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<WishlistRemoveMutation>(WishlistRemoveDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'WishlistRemove',
        'mutation',
        variables
      )
    },
    GetAvailableServices(
      variables?: GetAvailableServicesQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetAvailableServicesQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetAvailableServicesQuery>(GetAvailableServicesDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetAvailableServices',
        'query',
        variables
      )
    },
    GetBlogPosts(
      variables: GetBlogPostsQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetBlogPostsQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetBlogPostsQuery>(GetBlogPostsDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetBlogPosts',
        'query',
        variables
      )
    },
    GetBlogPost(
      variables: GetBlogPostQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetBlogPostQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetBlogPostQuery>(GetBlogPostDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetBlogPost',
        'query',
        variables
      )
    },
    GetFeaturedBlogPost(
      variables?: GetFeaturedBlogPostQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetFeaturedBlogPostQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetFeaturedBlogPostQuery>(GetFeaturedBlogPostDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetFeaturedBlogPost',
        'query',
        variables
      )
    },
    GetBNPPricingSchemes(
      variables: GetBnpPricingSchemesQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetBnpPricingSchemesQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetBnpPricingSchemesQuery>(GetBnpPricingSchemesDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetBNPPricingSchemes',
        'query',
        variables
      )
    },
    GetCreditCalculatorBNPParibas(
      variables: GetCreditCalculatorBnpParibasQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCreditCalculatorBnpParibasQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCreditCalculatorBnpParibasQuery>(GetCreditCalculatorBnpParibasDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetCreditCalculatorBNPParibas',
        'query',
        variables
      )
    },
    GetCreditCalculatorQuoteBNPParibas(
      variables: GetCreditCalculatorQuoteBnpParibasQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCreditCalculatorQuoteBnpParibasQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCreditCalculatorQuoteBnpParibasQuery>(
            GetCreditCalculatorQuoteBnpParibasDocument,
            variables,
            { ...requestHeaders, ...wrappedRequestHeaders }
          ),
        'GetCreditCalculatorQuoteBNPParibas',
        'query',
        variables
      )
    },
    CalculateBNPLoan(
      variables: CalculateBnpLoanQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CalculateBnpLoanQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CalculateBnpLoanQuery>(CalculateBnpLoanDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CalculateBNPLoan',
        'query',
        variables
      )
    },
    GetBrands(
      variables: GetBrandsQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetBrandsQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetBrandsQuery>(GetBrandsDocument, variables, { ...requestHeaders, ...wrappedRequestHeaders }),
        'GetBrands',
        'query',
        variables
      )
    },
    GetEmptyCart(
      variables?: GetEmptyCartQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetEmptyCartQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetEmptyCartQuery>(GetEmptyCartDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetEmptyCart',
        'query',
        variables
      )
    },
    GetCartById(
      variables: GetCartByIdQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCartByIdQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCartByIdQuery>(GetCartByIdDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetCartById',
        'query',
        variables
      )
    },
    GetTBICredit(
      variables: GetTbiCreditQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetTbiCreditQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetTbiCreditQuery>(GetTbiCreditDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetTBICredit',
        'query',
        variables
      )
    },
    GetBNPCredit(
      variables: GetBnpCreditQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetBnpCreditQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetBnpCreditQuery>(GetBnpCreditDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetBNPCredit',
        'query',
        variables
      )
    },
    CustomerData(
      variables?: CustomerDataQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<CustomerDataQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<CustomerDataQuery>(CustomerDataDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'CustomerData',
        'query',
        variables
      )
    },
    GetEcontOffices(
      variables: GetEcontOfficesQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetEcontOfficesQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetEcontOfficesQuery>(GetEcontOfficesDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetEcontOffices',
        'query',
        variables
      )
    },
    GetEcontCities(
      variables: GetEcontCitiesQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetEcontCitiesQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetEcontCitiesQuery>(GetEcontCitiesDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetEcontCities',
        'query',
        variables
      )
    },
    GetHomePageData(
      variables?: GetHomePageDataQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetHomePageDataQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetHomePageDataQuery>(GetHomePageDataDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetHomePageData',
        'query',
        variables
      )
    },
    GetCustomerOrders(
      variables?: GetCustomerOrdersQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetCustomerOrdersQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetCustomerOrdersQuery>(GetCustomerOrdersDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetCustomerOrders',
        'query',
        variables
      )
    },
    SearchField(
      variables: SearchFieldQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<SearchFieldQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<SearchFieldQuery>(SearchFieldDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'SearchField',
        'query',
        variables
      )
    },
    SearchPage(
      variables: SearchPageQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<SearchPageQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<SearchPageQuery>(SearchPageDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'SearchPage',
        'query',
        variables
      )
    },
    GetRouteMeta(
      variables: GetRouteMetaQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetRouteMetaQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetRouteMetaQuery>(GetRouteMetaDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetRouteMeta',
        'query',
        variables
      )
    },
    GetRouteData(
      variables: GetRouteDataQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetRouteDataQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetRouteDataQuery>(GetRouteDataDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetRouteData',
        'query',
        variables
      )
    },
    getStaticBlock(
      variables: GetStaticBlockQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetStaticBlockQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetStaticBlockQuery>(GetStaticBlockDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'getStaticBlock',
        'query',
        variables
      )
    },
    GetStoreStaticData(
      variables?: GetStoreStaticDataQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetStoreStaticDataQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetStoreStaticDataQuery>(GetStoreStaticDataDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetStoreStaticData',
        'query',
        variables
      )
    },
    GetGoogleApiKey(
      variables?: GetGoogleApiKeyQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetGoogleApiKeyQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetGoogleApiKeyQuery>(GetGoogleApiKeyDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetGoogleApiKey',
        'query',
        variables
      )
    },
    GetGoogleClientId(
      variables?: GetGoogleClientIdQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetGoogleClientIdQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetGoogleClientIdQuery>(GetGoogleClientIdDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetGoogleClientId',
        'query',
        variables
      )
    },
    GetAvailableStores(
      variables?: GetAvailableStoresQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetAvailableStoresQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetAvailableStoresQuery>(GetAvailableStoresDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetAvailableStores',
        'query',
        variables
      )
    },
    GetStore(variables: GetStoreQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetStoreQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetStoreQuery>(GetStoreDocument, variables, { ...requestHeaders, ...wrappedRequestHeaders }),
        'GetStore',
        'query',
        variables
      )
    },
    GetWishlistSkus(
      variables?: GetWishlistSkusQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetWishlistSkusQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetWishlistSkusQuery>(GetWishlistSkusDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetWishlistSkus',
        'query',
        variables
      )
    },
    GetWishlistProducts(
      variables?: GetWishlistProductsQueryVariables,
      requestHeaders?: GraphQLClientRequestHeaders
    ): Promise<GetWishlistProductsQuery> {
      return withWrapper(
        (wrappedRequestHeaders) =>
          client.request<GetWishlistProductsQuery>(GetWishlistProductsDocument, variables, {
            ...requestHeaders,
            ...wrappedRequestHeaders,
          }),
        'GetWishlistProducts',
        'query',
        variables
      )
    },
  }
}
export type Sdk = ReturnType<typeof getSdk>
