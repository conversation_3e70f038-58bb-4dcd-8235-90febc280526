/**
 * Facebook Pixel Core Module
 *
 * This module provides the core functionality for Facebook Pixel tracking,
 * serving as a single source of truth for all Facebook Pixel related code.
 *
 * @module fbqCore
 */
import { trackPageView } from '@lib/fbp/faceBookPixelHelper'

// Enable/disable debug logging based on environment
const DEBUG = process.env.NODE_ENV !== 'production'

// Import Facebook Pixel type definitions
import { BaseEventParams } from './facebookPixelTypes'

/**
 * Check if Facebook Pixel is available in the current environment
 */
export function isFbqAvailable(): boolean {
  return typeof window !== 'undefined' && !!window.fbq
}

/**
 * Log debug information if in development mode
 */
function debugLog(message: string, ...args: any[]): void {
  if (DEBUG) {
    console.log(`[FB Pixel] ${message}`, ...args)
  }
}

/**
 * Track a standard Facebook Pixel event
 *
 * @param eventName - The name of the event to track
 * @param parameters - Event parameters
 * @param eventID - Optional event ID for deduplication
 */
export function trackEvent(eventName: string, parameters: BaseEventParams = {}, eventID?: string): void {
  if (!isFbqAvailable()) return

  debugLog(`Tracking event: ${eventName}`, parameters, eventID ? { eventID } : '')

  if (eventID) {
    fbq('track', eventName, parameters, { eventID })
  } else {
    fbq('track', eventName, parameters)
  }
}

/**
 * Track a custom Facebook Pixel event
 *
 * @param eventName - The name of the custom event
 * @param parameters - Event parameters
 */
export function trackCustomEvent(eventName: string, parameters: BaseEventParams = {}): void {
  if (!isFbqAvailable()) return

  debugLog(`Tracking custom event: ${eventName}`, parameters)

  fbq('trackCustom', eventName, parameters)
}

/**
 * Initialize Facebook Pixel with the given pixel ID
 *
 * @param pixelId - The Facebook Pixel ID
 * @param consentGranted - Whether the user has granted consent for tracking
 */
export function initializePixel(pixelId: string, consentGranted: boolean = false): void {
  // Set up Facebook Pixel
  if (!pixelId || typeof window === 'undefined') return
  // @ts-ignore
  // prettier-ignore
  !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en_US/fbevents.js');
  window.fbq('consent', consentGranted ? 'grant' : 'revoke')
  window.fbq('init', pixelId)
  debugLog(`Initialized Facebook Pixel with ID: ${pixelId}, consent: ${consentGranted}`)
  trackPageView({ page: window.location.pathname })
}

/**
 * Track multiple items in a single event (e.g., AddToCart with multiple products)
 *
 * @param eventName - The name of the event to track
 * @param items - Array of items to track
 * @param currency - Currency code (default: USD)
 * @param eventID - Optional event ID for deduplication
 */
export function trackMultipleItems(
  eventName: string,
  items: Array<{ id: string; value: number; quantity?: number }>,
  currency: string = 'USD',
  eventID?: string
): void {
  if (items.length === 0) return

  const content_ids = items.map((item) => item.id)
  const totalValue = items.reduce((sum, item) => sum + item.value, 0)
  const contents = items.map((item) => ({
    id: item.id,
    quantity: item.quantity || 1,
  }))

  // Create parameters based on event type
  let parameters: BaseEventParams = {
    content_ids,
    content_type: 'product',
    value: totalValue,
    currency,
    contents,
    num_items: items.length,
  }

  trackEvent(eventName, parameters, eventID)
}

/**
 * Track multiple events in a batch
 *
 * @param events - Array of events to track
 */
export function trackBatchEvents(
  events: Array<{
    eventName: string
    parameters?: BaseEventParams
    eventID?: string
  }>
): void {
  events.forEach(({ eventName, parameters = {}, eventID }) => {
    trackEvent(eventName, parameters, eventID)
  })
}

/**
 * Core Facebook Pixel functionality
 */
export const fbqCore = {
  isAvailable: isFbqAvailable,
  trackEvent,
  trackCustomEvent,
  trackMultipleItems,
  trackBatchEvents,
  initialize: initializePixel,
}

export default fbqCore
