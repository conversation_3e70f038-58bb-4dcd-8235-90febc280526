'use client'
import { useFacebookPixel } from '@lib/fbp/useFacebookPixel'
import { ViewContentParams } from '@lib/fbp/facebookPixelTypes'
import { useEffect } from 'react'
import { AppCategoryViewFragment, ProductViewFragment, SimpleProductViewFragment } from '@lib/_generated/graphql_sdk'

interface PixelTrackViewContentProps {
  data: AppCategoryViewFragment
  category?: string
  products: ProductViewFragment[]
}

export const PixelTrackViewCategoryProducts: React.FC<PixelTrackViewContentProps> = ({ data, category, products }) => {
  const { trackViewContent } = useFacebookPixel()

  const { name } = data

  useEffect(() => {
    trackViewContent({
      content_type: 'product_group',
      content_category: category,
      content_name: name,
      content_ids: products.map((product) => String(product.id)),
    })
  }, [category, name, products, trackViewContent])
  return null
}
