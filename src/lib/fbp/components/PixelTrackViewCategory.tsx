'use client'
import { useFacebookPixel } from '@lib/fbp/useFacebookPixel'
import { ViewContentParams } from '@lib/fbp/facebookPixelTypes'
import { useEffect } from 'react'
import { AppCategoryViewFragment, ProductViewFragment } from '@lib/_generated/graphql_sdk'

interface PixelTrackViewContentProps {
  data: AppCategoryViewFragment
  category?: string
}

export const PixelTrackViewCategory: React.FC<PixelTrackViewContentProps> = ({ data, category }) => {
  const { trackViewContent } = useFacebookPixel()

  const { name } = data
  const categoryIds = data.widgets?.map((widget) => widget.title)

  useEffect(() => {
    trackViewContent({
      content_type: 'product_group',
      content_category: category,
      content_name: name,
      content_ids: categoryIds || [],
    })
  }, [category, categoryIds, name, trackViewContent])
  return null
}
