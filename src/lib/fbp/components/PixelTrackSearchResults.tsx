'use client'
import { useFacebookPixel } from '@lib/fbp/useFacebookPixel'
import { useEffect } from 'react'
import { ProductViewFragment } from '@lib/_generated/graphql_sdk'

interface PixelTrackViewContentProps {
  data: ProductViewFragment[]
  query: string
}

export const PixelTrackSearchResults: React.FC<PixelTrackViewContentProps> = ({ data, query }) => {
  const { trackSearch } = useFacebookPixel()

  useEffect(() => {
    const productIds = data.map((product) => String(product.id))
    const productCurrency = data[0]?.price?.price.currency || 'BGN'

    trackSearch({
      content_category: 'Search Results',
      content_ids: productIds,
      content_type: 'product',
      currency: productCurrency,
      search_string: query,
      value: 0,
    })
  }, [data, query, trackSearch])
  return null
}
