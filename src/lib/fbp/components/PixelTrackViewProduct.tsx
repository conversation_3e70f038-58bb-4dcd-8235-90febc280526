'use client'
import { useFacebookPixel } from '@lib/fbp/useFacebookPixel'
import { useEffect } from 'react'
import { ProductViewFragment } from '@lib/_generated/graphql_sdk'

interface PixelTrackViewContentProps {
  data: ProductViewFragment
  category?: string
}

export const PixelTrackViewProduct: React.FC<PixelTrackViewContentProps> = ({ data, category }) => {
  const { trackViewContent } = useFacebookPixel()

  const {
    price: {
      price: { currency, value },
      special,
    },
    name,
    id,
  } = data
  const productPrice = special?.value || value

  useEffect(() => {
    trackViewContent({
      content_type: 'product',
      content_ids: [String(id)],
      content_name: name,
      content_category: category,
      currency: currency,
      value: productPrice,
    })
  }, [category, currency, id, name, trackViewContent, productPrice])
  return null
}
