'use client'
import { useFacebookPixel } from '@lib/fbp/useFacebookPixel'
import { ViewContentParams } from '@lib/fbp/facebookPixelTypes'
import { useEffect } from 'react'
import { AppCategoryViewFragment, ProductViewFragment } from '@lib/_generated/graphql_sdk'
import { trackViewContent } from '@lib/fbp/faceBookPixelHelper'

interface PixelTrackViewContentProps {
  data: ProductViewFragment[]
  query: string
}

export const PixelTrackSearchPage: React.FC<PixelTrackViewContentProps> = ({ data, query }) => {
  const { trackViewContent } = useFacebookPixel()

  useEffect(() => {
    const productIds = data.map((product) => String(product.id))

    trackViewContent({
      content_type: 'product',
      content_category: `Search Results for "${query}"`,
      content_ids: productIds,
      search_string: query,
    })
  }, [data, query, trackViewContent])
  return null
}
