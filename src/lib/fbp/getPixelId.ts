import { useStaticContentStore } from '@features/static/static.store'

/**
 * Gets the Facebook Pixel ID from the static content store
 * @returns The Facebook Pixel ID or null if not available
 */
export function getPixelId(): string | null {
  if (typeof window === 'undefined') return null
  
  // Get the pixel ID from the static content store
  const staticContent = useStaticContentStore.getState().staticContent
  const pixelId = staticContent?.gdpr?.pixelId
  
  if (!pixelId) {
    console.warn('Facebook Pixel ID not found in static content')
    return null
  }
  
  return pixelId
}
