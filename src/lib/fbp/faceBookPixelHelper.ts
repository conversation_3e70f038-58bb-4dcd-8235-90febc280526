/**
 * Facebook Pixel Helper
 *
 * This module provides a standardized way to interact with the Facebook Pixel
 * for tracking events in a web application.
 *
 * @module FacebookPixelHelper
 */

import {
  ViewContentParams,
  AddToCartParams,
  AddToWishlistParams,
  InitiateCheckoutParams,
  AddPaymentInfoParams,
  PurchaseParams,
  CompleteRegistrationParams,
  LeadParams,
  ContactParams,
  SubmitApplicationParams,
  SearchParams,
  FindLocationParams,
  SubscribeParams,
  StartTrialParams,
  ScheduleParams,
  CustomizeProductParams,
  DonateParams,
  ContentParams,
  BaseEventParams,
  CustomParams,
  ViewPageViewParams,
} from '@lib/fbp/facebookPixelTypes'

import fbqCore from './fbqCore'

/**
 * Core Facebook Pixel tracking functionality
 */
class FacebookPixelHelper {
  /**
   * Checks if Facebook Pixel is available in the current environment
   */
  private static isAvailable(): boolean {
    return fbqCore.isAvailable()
  }

  /**
   * Generic event tracking method
   * @param eventName - The name of the event to track
   * @param parameters - Event parameters
   * @param eventID - Optional event ID for deduplication
   */
  static trackEvent(eventName: string, parameters?: any, eventID?: string): void {
    fbqCore.trackEvent(eventName, parameters, eventID)
  }

  /**
   * Track custom events not predefined by Facebook
   * @param eventName - The name of the custom event
   * @param parameters - Event parameters
   */
  static trackCustomEvent(eventName: string, parameters: CustomParams): void {
    fbqCore.trackCustomEvent(eventName, parameters)
  }

  // ===== STANDARD EVENTS =====

  /**
   * Track page view (automatically tracked by pixel in most cases)
   */
  static trackPageView(params: ViewPageViewParams): void {
    this.trackEvent('PageView', params)
  }

  // ----- E-commerce Events -----

  /**
   * Track when a user views content (product, landing page, etc.)
   */
  static trackViewContent(params: ViewContentParams): void {
    this.trackEvent('ViewContent', params)
  }

  /**
   * Track when a user adds a product to their shopping cart
   */
  static trackAddToCart(params: AddToCartParams, eventID?: string): void {
    this.trackEvent('AddToCart', params, eventID)
  }

  /**
   * Track when a user adds a product to their wishlist
   */
  static trackAddToWishlist(params: AddToWishlistParams): void {
    this.trackEvent('AddToWishlist', params)
  }

  /**
   * Track when a user initiates the checkout process
   */
  static trackInitiateCheckout(params: InitiateCheckoutParams): void {
    this.trackEvent('InitiateCheckout', params)
  }

  /**
   * Track when a user adds payment information during checkout
   */
  static trackAddPaymentInfo(params: AddPaymentInfoParams = {}): void {
    this.trackEvent('AddPaymentInfo', params)
  }

  /**
   * Track when a user completes a purchase
   */
  static trackPurchase(params: PurchaseParams): void {
    this.trackEvent('Purchase', params)
  }

  // ----- Registration & Lead Generation Events -----

  /**
   * Track when a user completes a registration form
   */
  static trackCompleteRegistration(params: CompleteRegistrationParams = {}): void {
    this.trackEvent('CompleteRegistration', params)
  }

  /**
   * Track when a user expresses interest in an offer
   */
  static trackLead(params: LeadParams = {}): void {
    this.trackEvent('Lead', params)
  }

  /**
   * Track when a user initiates contact with the business
   */
  static trackContact(params: ContactParams = {}): void {
    this.trackEvent('Contact')
  }

  /**
   * Track when a user submits an application
   */
  static trackSubmitApplication(params: SubmitApplicationParams = {}): void {
    this.trackEvent('SubmitApplication', params)
  }

  // ----- Search & Discovery Events -----

  /**
   * Track when a user performs a search
   */
  static trackSearch(params: SearchParams): void {
    this.trackEvent('Search', params)
  }

  /**
   * Track when a user searches for a location
   */
  static trackFindLocation(params: FindLocationParams = {}): void {
    this.trackEvent('FindLocation', params)
  }

  // ----- Subscription & Services Events -----

  /**
   * Track when a user subscribes to a service
   */
  static trackSubscribe(params: SubscribeParams = {}): void {
    this.trackEvent('Subscribe', params)
  }

  /**
   * Track when a user starts a free trial
   */
  static trackStartTrial(params: StartTrialParams = {}): void {
    this.trackEvent('StartTrial', params)
  }

  /**
   * Track when a user schedules an appointment
   */
  static trackSchedule(params: ScheduleParams = {}): void {
    this.trackEvent('Schedule', params)
  }

  // ----- Other Standard Events -----

  /**
   * Track when a user customizes a product
   */
  static trackCustomizeProduct(params: CustomizeProductParams = {}): void {
    this.trackEvent('CustomizeProduct', params)
  }

  /**
   * Track when a user makes a donation
   */
  static trackDonate(params: DonateParams = {}): void {
    this.trackEvent('Donate', params)
  }

  // ===== ADVANCED TRACKING HELPERS =====

  /**
   * Track events with multiple items (e.g., AddToCart with multiple products)
   */
  static trackMultipleItems(
    eventName: string,
    items: Array<{ id: string; value: number; quantity?: number }>,
    eventID?: string
  ): void {
    fbqCore.trackMultipleItems(eventName, items, 'USD', eventID)
  }

  /**
   * Advanced purchase tracking with comprehensive parameters
   */
  static trackAdvancedPurchase(params: {
    content_ids: string[]
    value: number
    currency?: string
    content_type?: string
    content_name?: string
    content_category?: string
    num_items?: number
    contents?: Array<{
      id: string
      quantity: number
      item_price?: number
    }>
    custom_data?: Record<string, any>
    eventID?: string
    order_id?: string
  }): void {
    const { eventID, ...parameters } = params
    this.trackEvent(
      'Purchase',
      {
        currency: 'USD', // Default currency, should be overridden
        content_type: 'product',
        ...parameters,
      },
      eventID
    )
  }

  /**
   * Track multiple events in a batch
   */
  static trackBatchEvents(
    events: Array<{ eventName: string; parameters?: Record<string, any>; eventID?: string }>
  ): void {
    fbqCore.trackBatchEvents(events)
  }

  /**
   * Track an event with explicit event ID for deduplication
   */
  static trackWithEventID(eventName: string, parameters: Record<string, any> = {}, eventID: string): void {
    this.trackEvent(eventName, parameters, eventID)
  }

  /**
   * Initialize Facebook Pixel with the given pixel ID
   */
  static initialize(pixelId: string, consentGranted: boolean = false): void {
    fbqCore.initialize(pixelId, consentGranted)
  }
}

// Export the class as the default export
export default FacebookPixelHelper

// Export individual functions for direct use
type HelperFunctions = {
  trackEvent: typeof FacebookPixelHelper.trackEvent
  trackCustomEvent: typeof FacebookPixelHelper.trackCustomEvent
  trackPageView: typeof FacebookPixelHelper.trackPageView
  trackViewContent: typeof FacebookPixelHelper.trackViewContent
  trackAddToCart: typeof FacebookPixelHelper.trackAddToCart
  trackAddToWishlist: typeof FacebookPixelHelper.trackAddToWishlist
  trackInitiateCheckout: typeof FacebookPixelHelper.trackInitiateCheckout
  trackAddPaymentInfo: typeof FacebookPixelHelper.trackAddPaymentInfo
  trackPurchase: typeof FacebookPixelHelper.trackPurchase
  trackCompleteRegistration: typeof FacebookPixelHelper.trackCompleteRegistration
  trackLead: typeof FacebookPixelHelper.trackLead
  trackContact: typeof FacebookPixelHelper.trackContact
  trackSubmitApplication: typeof FacebookPixelHelper.trackSubmitApplication
  trackSearch: typeof FacebookPixelHelper.trackSearch
  trackFindLocation: typeof FacebookPixelHelper.trackFindLocation
  trackSubscribe: typeof FacebookPixelHelper.trackSubscribe
  trackStartTrial: typeof FacebookPixelHelper.trackStartTrial
  trackSchedule: typeof FacebookPixelHelper.trackSchedule
  trackCustomizeProduct: typeof FacebookPixelHelper.trackCustomizeProduct
  trackDonate: typeof FacebookPixelHelper.trackDonate
  trackMultipleItems: typeof FacebookPixelHelper.trackMultipleItems
  trackAdvancedPurchase: typeof FacebookPixelHelper.trackAdvancedPurchase
  trackBatchEvents: typeof FacebookPixelHelper.trackBatchEvents
  trackWithEventID: typeof FacebookPixelHelper.trackWithEventID
  initialize: typeof FacebookPixelHelper.initialize
}

export const trackEvent = FacebookPixelHelper.trackEvent.bind(FacebookPixelHelper)
export const trackCustomEvent = FacebookPixelHelper.trackCustomEvent.bind(FacebookPixelHelper)
export const trackPageView = FacebookPixelHelper.trackPageView.bind(FacebookPixelHelper)

// E-commerce events
export const trackViewContent = FacebookPixelHelper.trackViewContent.bind(FacebookPixelHelper)
export const trackAddToCart = FacebookPixelHelper.trackAddToCart.bind(FacebookPixelHelper)
export const trackAddToWishlist = FacebookPixelHelper.trackAddToWishlist.bind(FacebookPixelHelper)
export const trackInitiateCheckout = FacebookPixelHelper.trackInitiateCheckout.bind(FacebookPixelHelper)
export const trackAddPaymentInfo = FacebookPixelHelper.trackAddPaymentInfo.bind(FacebookPixelHelper)
export const trackPurchase = FacebookPixelHelper.trackPurchase.bind(FacebookPixelHelper)

// Registration & Lead Generation
export const trackCompleteRegistration = FacebookPixelHelper.trackCompleteRegistration.bind(FacebookPixelHelper)
export const trackLead = FacebookPixelHelper.trackLead.bind(FacebookPixelHelper)
export const trackContact = FacebookPixelHelper.trackContact.bind(FacebookPixelHelper)
export const trackSubmitApplication = FacebookPixelHelper.trackSubmitApplication.bind(FacebookPixelHelper)

// Search & Discovery
export const trackSearch = FacebookPixelHelper.trackSearch.bind(FacebookPixelHelper)
export const trackFindLocation = FacebookPixelHelper.trackFindLocation.bind(FacebookPixelHelper)

// Subscription & Services
export const trackSubscribe = FacebookPixelHelper.trackSubscribe.bind(FacebookPixelHelper)
export const trackStartTrial = FacebookPixelHelper.trackStartTrial.bind(FacebookPixelHelper)
export const trackSchedule = FacebookPixelHelper.trackSchedule.bind(FacebookPixelHelper)

// Other events
export const trackCustomizeProduct = FacebookPixelHelper.trackCustomizeProduct.bind(FacebookPixelHelper)
export const trackDonate = FacebookPixelHelper.trackDonate.bind(FacebookPixelHelper)

// Advanced helpers
export const trackMultipleItems = FacebookPixelHelper.trackMultipleItems.bind(FacebookPixelHelper)
export const trackAdvancedPurchase = FacebookPixelHelper.trackAdvancedPurchase.bind(FacebookPixelHelper)
export const trackBatchEvents = FacebookPixelHelper.trackBatchEvents.bind(FacebookPixelHelper)
export const trackWithEventID = FacebookPixelHelper.trackWithEventID.bind(FacebookPixelHelper)
export const initialize = FacebookPixelHelper.initialize.bind(FacebookPixelHelper)

// Re-export types for external use
export type {
  BaseEventParams,
  ContentParams,
  AddToCartParams,
  AddToWishlistParams,
  CompleteRegistrationParams,
  ContactParams,
  CustomizeProductParams,
  DonateParams,
  FindLocationParams,
  InitiateCheckoutParams,
  LeadParams,
  PurchaseParams,
  ScheduleParams,
  SearchParams,
  StartTrialParams,
  SubmitApplicationParams,
  SubscribeParams,
  ViewContentParams,
  AddPaymentInfoParams,
}
