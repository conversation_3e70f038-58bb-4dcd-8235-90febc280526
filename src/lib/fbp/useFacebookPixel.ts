/**
 * React hook for Facebook Pixel tracking
 *
 * This hook provides a React-friendly way to use Facebook Pixel tracking
 * in functional components with proper dependency management.
 */

import { useCallback } from 'react'
import FacebookPixelHelper from './faceBookPixelHelper'
import type {
  BaseEventParams,
  AddPaymentInfoParams,
  AddToCartParams,
  AddToWishlistParams,
  CompleteRegistrationParams,
  ContactParams,
  CustomizeProductParams,
  DonateParams,
  FindLocationParams,
  InitiateCheckoutParams,
  LeadParams,
  PurchaseParams,
  ScheduleParams,
  SearchParams,
  StartTrialParams,
  SubmitApplicationParams,
  SubscribeParams,
  ViewContentParams,
} from './facebookPixelTypes'

/**
 * React hook for Facebook Pixel tracking
 *
 * @returns Object containing tracking functions for various Facebook Pixel events
 */
export function useFacebookPixel() {
  // ===== CORE TRACKING FUNCTIONS =====

  /**
   * Track a standard Facebook Pixel event
   */
  const trackEvent = useCallback((eventName: string, parameters?: any, eventID?: string) => {
    FacebookPixelHelper.trackEvent(eventName, parameters, eventID)
  }, [])

  /**
   * Track a custom Facebook Pixel event
   */
  const trackCustomEvent = useCallback((eventName: string, parameters: any) => {
    FacebookPixelHelper.trackCustomEvent(eventName, parameters)
  }, [])

  /**
   * Track page view event
   */
  const trackPageView = useCallback(() => {
    trackEvent('PageView')
  }, [trackEvent])

  // ===== E-COMMERCE EVENTS =====

  /**
   * Track when a user views content (product, landing page, etc.)
   */
  const trackViewContent = useCallback(
    (params: ViewContentParams) => {
      trackEvent('ViewContent', params)
    },
    [trackEvent]
  )

  /**
   * Track when a user adds a product to their shopping cart
   */
  const trackAddToCart = useCallback(
    (params: AddToCartParams, eventID?: string) => {
      trackEvent('AddToCart', params, eventID)
    },
    [trackEvent]
  )

  /**
   * Track when a user adds a product to their wishlist
   */
  const trackAddToWishlist = useCallback(
    (params: AddToWishlistParams) => {
      trackEvent('AddToWishlist', params)
    },
    [trackEvent]
  )

  /**
   * Track when a user initiates the checkout process
   */
  const trackInitiateCheckout = useCallback(
    (params: InitiateCheckoutParams) => {
      trackEvent('InitiateCheckout', params)
    },
    [trackEvent]
  )

  /**
   * Track when a user adds payment information during checkout
   */
  const trackAddPaymentInfo = useCallback(
    (params: AddPaymentInfoParams = {}) => {
      trackEvent('AddPaymentInfo', params)
    },
    [trackEvent]
  )

  /**
   * Track when a user completes a purchase
   */
  const trackPurchase = useCallback(
    (params: PurchaseParams) => {
      trackEvent('Purchase', params)
    },
    [trackEvent]
  )

  // ===== REGISTRATION & LEAD GENERATION EVENTS =====

  /**
   * Track when a user completes a registration form
   */
  const trackCompleteRegistration = useCallback(
    (params: CompleteRegistrationParams = {}) => {
      trackEvent('CompleteRegistration', params)
    },
    [trackEvent]
  )

  /**
   * Track when a user expresses interest in an offer
   */
  const trackLead = useCallback(
    (params: LeadParams = {}) => {
      trackEvent('Lead', params)
    },
    [trackEvent]
  )

  /**
   * Track when a user initiates contact with the business
   */
  const trackContact = useCallback(
    (params: ContactParams = {}) => {
      trackEvent('Contact', params)
    },
    [trackEvent]
  )

  /**
   * Track when a user submits an application
   */
  const trackSubmitApplication = useCallback(
    (params: SubmitApplicationParams = {}) => {
      trackEvent('SubmitApplication', params)
    },
    [trackEvent]
  )

  // ===== SEARCH & DISCOVERY EVENTS =====

  /**
   * Track when a user performs a search
   */
  const trackSearch = useCallback(
    (params: SearchParams) => {
      trackEvent('Search', params)
    },
    [trackEvent]
  )

  /**
   * Track when a user searches for a location
   */
  const trackFindLocation = useCallback(
    (params: FindLocationParams = {}) => {
      trackEvent('FindLocation', params)
    },
    [trackEvent]
  )

  // ===== SUBSCRIPTION & SERVICES EVENTS =====

  /**
   * Track when a user subscribes to a service
   */
  const trackSubscribe = useCallback(
    (params: SubscribeParams = {}) => {
      trackEvent('Subscribe', params)
    },
    [trackEvent]
  )

  /**
   * Track when a user starts a free trial
   */
  const trackStartTrial = useCallback(
    (params: StartTrialParams = {}) => {
      trackEvent('StartTrial', params)
    },
    [trackEvent]
  )

  /**
   * Track when a user schedules an appointment
   */
  const trackSchedule = useCallback(
    (params: ScheduleParams = {}) => {
      trackEvent('Schedule', params)
    },
    [trackEvent]
  )

  // ===== OTHER STANDARD EVENTS =====

  /**
   * Track when a user customizes a product
   */
  const trackCustomizeProduct = useCallback(
    (params: CustomizeProductParams = {}) => {
      trackEvent('CustomizeProduct', params)
    },
    [trackEvent]
  )

  /**
   * Track when a user makes a donation
   */
  const trackDonate = useCallback(
    (params: DonateParams = {}) => {
      trackEvent('Donate', params)
    },
    [trackEvent]
  )

  // ===== ADVANCED TRACKING =====

  /**
   * Track events with multiple items (e.g., AddToCart with multiple products)
   */
  const trackMultipleItems = useCallback(
    (eventName: string, items: Array<{ id: string; value: number; quantity?: number }>, eventID?: string) => {
      FacebookPixelHelper.trackMultipleItems(eventName, items, eventID)
    },
    []
  )

  /**
   * Track an event with explicit event ID for deduplication
   */
  const trackWithEventID = useCallback((eventName: string, parameters: Record<string, any> = {}, eventID: string) => {
    FacebookPixelHelper.trackWithEventID(eventName, parameters, eventID)
  }, [])

  return {
    // Core tracking
    trackEvent,
    trackCustomEvent,
    trackPageView,

    // E-commerce events
    trackViewContent,
    trackAddToCart,
    trackAddToWishlist,
    trackInitiateCheckout,
    trackAddPaymentInfo,
    trackPurchase,

    // Registration & Lead Generation
    trackCompleteRegistration,
    trackLead,
    trackContact,
    trackSubmitApplication,

    // Search & Discovery
    trackSearch,
    trackFindLocation,

    // Subscription & Services
    trackSubscribe,
    trackStartTrial,
    trackSchedule,

    // Other events
    trackCustomizeProduct,
    trackDonate,

    // Advanced tracking
    trackMultipleItems,
    trackWithEventID,
  }
}
