/**
 * Type definitions for Facebook Pixel
 * Based on Meta Pixel documentation: https://developers.facebook.com/docs/meta-pixel/reference/
 */

declare global {
  interface Window {
    fbq: Fbq;
    _fbq?: any;
  }

  /**
   * Facebook Pixel tracking function
   */
  type Fbq = {
    (
      method: 'init',
      pixelId: string,
      params?: Record<string, any>
    ): void;
    (
      method: 'track',
      eventName: string,
      params?: Record<string, any>,
      options?: { eventID?: string }
    ): void;
    (
      method: 'trackCustom',
      eventName: string,
      params?: Record<string, any>,
      options?: { eventID?: string }
    ): void;
    (
      method: 'trackSingle',
      pixelId: string,
      eventName: string,
      params?: Record<string, any>,
      options?: { eventID?: string }
    ): void;
    (
      method: 'trackSingleCustom',
      pixelId: string,
      eventName: string,
      params?: Record<string, any>,
      options?: { eventID?: string }
    ): void;
    (
      method: 'consent',
      action: 'grant' | 'revoke',
      params?: Record<string, any>
    ): void;
    push: (...args: any[]) => void;
    loaded: boolean;
    version: string;
    queue: any[];
    callMethod?: (...args: any[]) => void;
  };

  // Make fbq available globally
  const fbq: Fbq;
}

// This empty export makes the file a module
export {};
