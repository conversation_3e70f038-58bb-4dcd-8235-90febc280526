/**
 * Meta Pixel Event Types
 * Based on Meta Pixel documentation: https://developers.facebook.com/docs/meta-pixel/reference/
 */

// Base parameters that can be included with any event
export interface BaseEventParams {
  // Common parameters
  value?: number
  currency?: string
  content_category?: string
  content_ids?: string[]
  content_name?: string
  content_type?: string
  contents?: Array<{
    id: string
    quantity: number
    item_price?: number
    delivery_category?: string
  }>
  predicted_ltv?: number
  num_items?: number
  search_string?: string
  status?: boolean
  event_id?: string
  external_id?: string
  data_processing_options?: string[]
  data_processing_options_country?: number
  data_processing_options_state?: number
  user_data?: {
    em?: string // email
    ph?: string // phone
    fn?: string // first name
    ln?: string // last name
    ge?: string // gender
    db?: string // date of birth
    ct?: string // city
    st?: string // state
    zp?: string // zip/postal code
    country?: string
    external_id?: string
    client_ip_address?: string
    client_user_agent?: string
    fbc?: string
    fbp?: string
    subscription_id?: string
    fb_login_id?: string
    lead_id?: string
  }
  custom_data?: Record<string, any>
}

export interface ViewPageViewParams extends BaseEventParams {
  page: string
}

// ViewContent event parameters
export interface ViewContentParams extends BaseEventParams {
  content_ids: string[]
  content_type: 'product' | 'product_group'
  content_name?: string
  content_category?: string
  value?: number
  currency?: string
}

// Search event parameters
export interface SearchParams extends BaseEventParams {
  search_string: string
  content_category?: string
  content_ids?: string[]
  value?: number
  currency?: string
}

// AddToCart event parameters
export interface AddToCartParams extends BaseEventParams {
  content_ids?: string[]
  content_name?: string
  content_type: 'product' | 'product_group'
  contents?: Array<{
    id: string
    quantity: number
    item_price?: number
  }>
  value?: number
  currency?: string
}

// AddToWishlist event parameters
export interface AddToWishlistParams extends BaseEventParams {
  content_ids: string[]
  content_name?: string
  content_category?: string
  content_type?: 'product' | 'product_group'
  value?: number
  currency?: string
}

// InitiateCheckout event parameters
export interface InitiateCheckoutParams extends BaseEventParams {
  content_ids?: string[]
  content_category?: string
  content_name?: string
  contents?: Array<{
    id: string
    quantity: number
    item_price?: number
  }>
  num_items?: number
  value?: number
  currency?: string
}

// AddPaymentInfo event parameters
export interface AddPaymentInfoParams extends BaseEventParams {
  content_category?: string
  content_ids?: string[]
  contents?: Array<{
    id: string
    quantity: number
    item_price?: number
  }>
  value?: number
  currency?: string
  payment_type?: string
}

// Purchase event parameters
export interface PurchaseParams extends BaseEventParams {
  content_ids?: string[]
  content_name?: string
  content_type?: 'product' | 'product_group'
  contents?: Array<{
    id: string
    quantity: number
    item_price?: number
  }>
  num_items?: number
  value: number
  currency: string
  transaction_id?: string
  tax?: number
  shipping?: number
}

// Lead event parameters
export interface LeadParams extends BaseEventParams {
  content_category?: string
  content_name?: string
  value?: number
  currency?: string
  lead_id?: string
}

// CompleteRegistration event parameters
export interface CompleteRegistrationParams extends BaseEventParams {
  content_name?: string
  currency?: string
  value?: number
  status?: boolean
}

// Custom event parameters
export type CustomParams = BaseEventParams

// For backward compatibility with existing code
export interface ContentParams extends ViewContentParams {
  content_ids: string[]
  content_type: 'product' | 'product_group'
}

// Additional types not directly provided by Meta Pixel documentation
export interface ContactParams extends BaseEventParams {
  // Contact event can include any base parameters
}

export interface CustomizeProductParams extends BaseEventParams {
  content_ids?: string[]
}

export interface DonateParams extends BaseEventParams {
  value?: number
  currency?: string
}

export interface FindLocationParams extends BaseEventParams {
  // Find location event can include any base parameters
}

export interface ScheduleParams extends BaseEventParams {
  // Schedule event can include any base parameters
}

export interface StartTrialParams extends BaseEventParams {
  value?: number
  currency?: string
  predicted_ltv?: number
}

export interface SubmitApplicationParams extends BaseEventParams {
  // Submit application can include any base parameters
}

export interface SubscribeParams extends BaseEventParams {
  predicted_ltv?: number
  value?: number
  currency?: string
  subscription_id?: string
}
