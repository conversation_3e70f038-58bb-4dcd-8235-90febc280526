import { Brand, SimpleProduct } from '@lib/_generated/graphql_sdk'

const brand: Brand = {
  __typename: 'Brand',
  image: {
    src: 'https://praktis.bg/media/attributesplash/cache/1b5d27_199x110/CAT-POWERTOOLS_1.png',
  },
  name: 'ATG',
  url: {
    href: '/shops',
    text: '',
  },
}

export const additionalImages = [
  {
    position: 1,
    image: {
      src: 'https://praktis.bg/media/catalog/product/3/4/3432388_id31146.jpg',
    },
  },
  {
    position: 2,
    image: {
      src: 'https://praktis.bg/media/catalog/product/3/5/35535728_id107912.jpg',
    },
  },
  {
    position: 3,
    image: {
      src: 'https://praktis.bg/media/catalog/product/3/5/3508230_id73018.jpg',
    },
  },
  {
    position: 4,
    image: {
      src: 'https://praktis.bg/media/catalog/product/3/5/3508230_id73018.jpg',
    },
  },
  {
    position: 5,
    image: {
      src: 'https://praktis.bg/media/catalog/product/3/5/3508230_id73018.jpg',
    },
  },
  {
    position: 6,
    image: {
      src: 'https://praktis.bg/media/catalog/product/3/5/3508230_id73018.jpg',
    },
  },
  {
    position: 7,
    image: {
      src: 'https://praktis.bg/media/catalog/product/3/5/3508230_id73018.jpg',
    },
  },
  {
    position: 8,
    image: {
      src: 'https://praktis.bg/media/catalog/product/3/5/3508230_id73018.jpg',
    },
  },
]

const product: SimpleProduct = {
  id: '1',
  shortDescription: 'short description',
  skuAvailability: [],
  labels: {
    buyCheap: true,
    freeDelivery: true,
    fromBrochure: true,
    warrantyMonths: 24,
  },
  measures: {
    base: '',
    secondary: '',
    secondaryQty: 0,
    secondaryMeasureUsed: false,
  },
  brand: brand,
  description: 'description',
  energyLabel: {
    image: {
      src: 'https://praktis.bg/skin/frontend/praktis/default/images/energy-label-a+.png',
    },
  },
  gallery: [
    {
      position: 0,
      image: {
        src: 'https://praktis.bg/media/catalog/product/3/5/35555463_id110071.jpg',
      },
    },
    ...additionalImages,
  ],
  image: {
    alt: '',
    src: 'https://praktis.bg/media/catalog/product/3/5/35555463_id110071.jpg',
  },
  name: 'Акумулаторен винтоверт марка цена дълго заглавие CAT DX14 12V 30Nm с батерия и зарядно',
  price: {
    price: {
      currency: 'лв.',
      value: 130,
    },
    special: {
      currency: 'лв.',
      value: 80,
    },
  },
  sku: '',
  urlKey: 'svesht-za-motorni-trioni-universal-rcj7y',
  stock: {
    showOutOfStock: true,
    blockForSale: false,
    hasImages: true,
    inStock: true,
    manageStock: true,
    qty: 10,
    minQty: 0,
    zeronBlockedDelivery: true,
    zeronSiteStatus: 'DEMO',
  },
}

const product2: SimpleProduct = {
  ...product,
  id: '2',
  name: 'Бензинова коса My Garden S-BC520 / 1500W',
  image: {
    alt: '',
    src: 'https://praktis.bg/media/catalog/product/3/5/35562071_id115597.jpg',
  },
  price: {
    price: {
      currency: 'лв.',
      value: 46,
    },
    special: {
      currency: 'лв.',
      value: 30,
    },
  },
  urlKey: 'benzinov-trimer-za-treva-my-garden-s-bc520-1-5kw',
}

const product3: SimpleProduct = {
  ...product,
  id: '6',
  name: 'Бензинова мотофреза Ziel BK-450 3kW',
  image: {
    alt: '',
    src: 'https://praktis.bg/media/catalog/product/3/5/35561913_.jpg',
  },
  price: {
    price: {
      currency: 'лв.',
      value: 589,
    },
  },
  urlKey: 'benzinova-motofreza-ziel-bk-450-3kw',
}

const product4: SimpleProduct = {
  ...product,
  id: '5',
  name: 'Четиритактово масло ZieL SAE 30 4T / 0.5L',
  image: {
    alt: '',
    src: 'https://praktis.bg/media/catalog/product/3/4/3493590_id67033_1.jpg',
  },
  price: {
    price: {
      currency: 'лв.',
      value: 589,
    },
  },
  urlKey: 'chetiritaktovo-maslo-ziel-sae-30-4t-0-5l',
}

export function getRandomProductsList(numberOfProducts: number): SimpleProduct[] {
  // all products
  const allProducts = [product, product2, product3, product4]

  return Array.from({ length: numberOfProducts }, (_, i) => {
    const randomProduct = allProducts[Math.floor(Math.random() * allProducts.length)]
    return {
      ...randomProduct,
      id: i.toString(),
    }
  })
}
