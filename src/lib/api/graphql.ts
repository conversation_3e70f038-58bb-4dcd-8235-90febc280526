import { GraphQLClient } from 'graphql-request'

import { getSdk, SdkFunctionWrapper } from '@lib/_generated/graphql_sdk'

export const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_FRONTEND_GRAPTHQL_ENDPOINT ?? ''
export const SERVER_GRAPHQL_ENDPOINT = process.env.SERVER_SIDE_GRAPTHQL_ENDPOINT ?? GRAPHQL_ENDPOINT

export const BACKEND_DATASOURCE = {
  endpoint: GRAPHQL_ENDPOINT,
  fetchParams: {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': '',
    },
  },
}

function isServerSide() {
  return typeof window === 'undefined'
}

const url = isServerSide() ? SERVER_GRAPHQL_ENDPOINT : GRAPHQL_ENDPOINT

const client = new GraphQLClient(url, {
  headers: { ...BACKEND_DATASOURCE.fetchParams.headers },
})

const authWrapper: SdkFunctionWrapper = (action, operationName, operationType) => {
  const authHeaders: Record<string, string> = {}
  if (!isServerSide()) {
    // authHeaders[AuthTokenHeader] = getAuthTokenFromCookie()
  }

  return action(authHeaders)
}

export const GraphQLBackend = getSdk(client, authWrapper)
