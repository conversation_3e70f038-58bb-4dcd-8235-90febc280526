import { toast } from 'sonner'

import { ResponseError } from '@/src/models/ResponseError'
import { GraphQLBackend } from '@lib/api/graphql'
import { showErrorToast, showInfoToast, showSuccessToast, showWarningToast } from '@lib/utils/toaster'

// Define the toast interface
interface ToastApi {
  success: (message: string) => void
  error: (message: string) => void
  info: (message: string) => void
  warn: (message: string) => void
}

const sonnerToast: ToastApi = {
  success: (message) => showSuccessToast({ description: message }),
  error: (message) => showErrorToast({ description: message }),
  info: (message) => showInfoToast({ description: message }),
  warn: (message) => showWarningToast({ description: message }),
}

// Configuration state
let toastApi: ToastApi = sonnerToast

// Function to check if an error matches the ResponseError structure
const isResponseError = (error: unknown): error is ResponseError => {
  return typeof error === 'object' && error !== null && 'response' in error && 'request' in error
}

// Process an error and show appropriate toast messages
const processError = (error: unknown): never => {
  if (isResponseError(error)) {
    const status = error.response.status

    if (status >= 500) {
      toastApi.error('Server error. Please try again later.')
    } else if (status === 401 || status === 403) {
      toastApi.error('Authentication error. Please login again.')
    } else if (status === 404) {
      toastApi.error('Resource not found.')
    } else {
      // Handle GraphQL specific errors
      if (error.response.errors?.length) {
        const messages = error.response.errors.map((err) => err.message)
        toastApi.error(messages.join('. '))
      } else {
        toastApi.error('An error occurred. Please try again.')
      }
    }
  } else if (error instanceof Error) {
    toastApi.error(`Network error: ${error.message}`)
  } else {
    toastApi.error('An unknown error occurred.')
  }

  throw error
}

// Create a type-safe wrapper around GraphQLBackend
type GraphQLBackendType = typeof GraphQLBackend

// Create a new object with the same shape as GraphQLBackend but with wrapped methods
export const GraphQLFrontend = Object.entries(GraphQLBackend).reduce((acc, [key, value]) => {
  if (typeof value === 'function') {
    // For each function property, create a wrapper that handles errors
    acc[key as keyof GraphQLBackendType] = function (this: any, ...args: any[]) {
      // Call the original method with the original context and arguments
      return (GraphQLBackend[key as keyof GraphQLBackendType] as Function)
        .apply(GraphQLBackend, args)
        .catch(processError)
    }
  } else {
    // For non-function properties, just copy them over
    acc[key as keyof GraphQLBackendType] = value
  }
  return acc
}, {} as GraphQLBackendType)
