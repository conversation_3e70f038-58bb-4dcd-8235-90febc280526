import { GraphQLClient } from 'graphql-request'
import { cookies } from 'next/headers'

import { AuthCookieName, AuthTokenHeader } from '@/src/features/cart/ClientCookie'
import { ServerCookie } from '@features/cart/ServerCookie'
import { getSdk, SdkFunctionWrapper } from '@lib/_generated/graphql_sdk'
import { SERVER_GRAPHQL_ENDPOINT } from '@lib/api/graphql'

const getServerClient = () => {
  return new GraphQLClient(SERVER_GRAPHQL_ENDPOINT, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': '',
    },
  })
}

const authWrapper: SdkFunctionWrapper = async (action, operationName, operationType): Promise<any> => {
  const authHeaders: Record<string, string> = {}

  try {
    const cookieStore = await cookies()
    const token = cookieStore.get(AuthCookieName)?.value

    if (token) {
      authHeaders[AuthTokenHeader] = token
      console.log(`[GraphQL Server] Using auth token for operation: ${operationName}`)
    }
  } catch (error) {
    console.error(`[GraphQL Server] Error getting auth token:`, error)
  }

  return action(authHeaders)
}

const getServerGraphQLBackend = () => {
  const client = getServerClient()
  return getSdk(client, authWrapper)
}

export const ServerGraphQLBackend = getServerGraphQLBackend()
