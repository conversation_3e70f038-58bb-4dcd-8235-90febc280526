import { APIError } from '@/api'
import { showErrorToast } from '@lib/utils/toaster'

/**
 * Type guard to check if an unknown error matches our APIError interface
 */
export function isAPIError(error: unknown): error is APIError {
  return (
    error !== null &&
    typeof error === 'object' &&
    'response' in error &&
    !!error.response &&
    typeof error.response === 'object' &&
    'errors' in error.response &&
    Array.isArray(error.response.errors)
  )
}

export function handleAPIError(err: unknown) {
  if (isAPIError(err) && err.response?.errors?.length) {
    for (const errorItem of err.response.errors) {
      showErrorToast({
        title: 'Грешка!',
        description: errorItem.message || 'Неизвестна грешка',
      })
    }
  } else {
    showErrorToast({
      title: 'Нещо се обърка!',
      description: err instanceof Error ? err.message : 'Моля, опитайте по-късно',
    })
  }
}
