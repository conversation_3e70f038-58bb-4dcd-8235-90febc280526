export enum SystemRoutes {
  CatalogSearch = '/catalogsearch/result',
}

export function getUrl(path: SystemRoutes, q: { [key: string]: string }): string {
  return path
}

export function getSearchQueryUrl(term: string): string {
  let qTerm = encodeURIComponent(term)
  return `${SystemRoutes.CatalogSearch}?q=${qTerm}`
}

export function getCurrentLocation() {
  console.log('getCurrentLocation')
  if (typeof window !== 'undefined') {
    const path = window.location.pathname
    // get query params
    const search = window.location.search
    if (search) {
      return `${path}${search}`
    }

    return path
  }

  console.error('window is undefined')
  return ''
}
