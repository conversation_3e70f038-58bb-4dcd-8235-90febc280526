import { Alert<PERSON>ircle, <PERSON>ertTriangle, CheckCircle, LucideInfo } from 'lucide-react'
import { toast } from 'sonner'

type toastProps = {
  title?: string
  description?: React.ReactNode
}

export const showInfoToast = ({ title, description }: toastProps) => {
  toast.info(title || 'Съобщение', {
    description: description || "Here's some additional details about this information.",
    icon: <LucideInfo className="h-5 w-5 text-blue-500" />,
    classNames: {
      toast: 'group border-blue-400 bg-blue-50 dark:border-blue-800 dark:bg-blue-950',
      title: 'text-blue-700 dark:text-blue-400',
      description: 'text-blue-600 dark:text-blue-300',
    },
  })
}

export const showSuccessToast = ({ title, description }: toastProps) => {
  toast.success(title || '', {
    description: description || 'The operation was completed successfully.',
    icon: <CheckCircle className="h-5 w-5 text-green-500" />,
    classNames: {
      toast: 'group border-green-400 bg-green-50 dark:border-green-800 dark:bg-green-950',
      title: 'text-green-700 dark:text-green-400',
      description: 'text-green-600 dark:text-green-300',
    },
  })
}

export const showErrorToast = ({ title, description }: toastProps) => {
  toast.error(title || 'Грешка', {
    duration: 10000,
    description: description || 'An error occurred while processing your request.',
    icon: <AlertCircle className="h-5 w-5 text-red-500" />,
    className: 'group border-red-400 bg-red-50 dark:border-red-800 dark:bg-red-950',
    classNames: {
      toast: '',
      title: 'text-red-700 dark:text-red-400',
      description: 'text-red-600 dark:text-red-300',
    },
  })
}

export const showWarningToast = ({ title, description }: toastProps) => {
  toast.warning(title || 'Внимание', {
    description: description || 'Please review this information before proceeding.',
    icon: <AlertTriangle className="h-5 w-5 text-amber-500" />,
    classNames: {
      toast: 'group border-amber-400 bg-amber-50 dark:border-amber-800 dark:bg-amber-950',
      title: 'text-amber-700 dark:text-amber-400',
      description: 'text-amber-600 dark:text-amber-300',
    },
  })
}
