import React from 'react'

export const DummyCallback = () => ({}) as any
export const PreventDefaultCallback = (e: React.SyntheticEvent): void => {
  e.preventDefault()
}
export const DummyStringCallback = () => ''

export function paginate<T>(array: T[], pageSize: number, pageNumber: number): T[] {
  // human-readable page numbers usually start with 1, so we reduce 1 in the first argument
  return array.slice((pageNumber - 1) * pageSize, pageNumber * pageSize)
}

export function getDebounceCaller(func: Function, timeoutMs: number) {
  let timeout: NodeJS.Timeout
  return function (...args: any) {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      func(args)
    }, timeoutMs)
  }
}

export function objDeepEqual(object1: { [k: string]: any }, object2: { [k: string]: any }) {
  if (object1 === object2) {
    return true
  }

  const areObjects = isObject(object1) && isObject(object2)
  if (!areObjects) {
    return false
  }

  const keys1 = Object.keys(object1)
  const keys2 = Object.keys(object2)
  if (keys1.length !== keys2.length) {
    return false
  }

  for (const key of keys1) {
    const val1 = object1[key]
    const val2 = object2[key]
    const areObjects = isObject(val1) && isObject(val2)
    if ((areObjects && !objDeepEqual(val1, val2)) || (!areObjects && val1 !== val2)) {
      return false
    }
  }
  return true
}

function isObject(_object: any): _object is { [k: string]: any } {
  return _object != null && typeof _object === 'object'
}
