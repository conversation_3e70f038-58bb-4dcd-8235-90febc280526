import { SimpleProductViewFragment, Maybe, ProductStock } from '@lib/_generated/graphql_sdk'

export class SiteStatusHelper {
  // Status constants
  // Default status when no status is determined (equivalent to not active)
  static readonly NO_STATUS = 0
  // Product is not active (e.g., missing images or not configured for sale)
  static readonly NOT_ACTIVE = 1
  // Product is out of stock or blocked for sales
  static readonly DEPLETED = 2
  // Product is available for online purchase and delivery
  static readonly AVAILABLE = 3
  // Product is available for online purchase with store pickup only
  static readonly AVAILABLE_PICKUP = 4
  // Product is temporarily out of stock but will be available for delivery soon
  static readonly AWAITING_DELIVERY = 5
  // Product is temporarily out of stock but will be available for store pickup soon
  static readonly AWAITING_DELIVERY_PICKUP = 6
  // Product can be ordered individually (special order) with delivery
  static readonly INDIVIDUAL_ORDER = 7
  // Product can be ordered individually (special order) with store pickup only
  static readonly INDIVIDUAL_ORDER_PICKUP = 8
  // Product is only available for purchase in physical stores
  static readonly ONLY_IN_STORE = 9
  // Product has limited visibility (listed but with restricted purchasing options)
  static readonly LIMITED_VISIBILITY = 10

  // Product available for online sale and delivery (when in stock) or for individual order (when not managing stock)
  static readonly ZERON_SITE_STATUS_1 = '1'
  // Product available for online sale with store pickup only (not for delivery)
  static readonly ZERON_SITE_STATUS_2 = '2'
  // Products available only in physical stores (not for online purchase)
  static readonly ZERON_SITE_STATUS_3 = '3'
  // Products with limited visibility (listed online but not available for online purchase)
  static readonly ZERON_SITE_STATUS_4 = '4'
  // Products with limited visibility (special case, similar to status 4)
  static readonly ZERON_SITE_STATUS_5 = '5'

  private static instance: SiteStatusHelper
  private cachedStatus: { [key: string]: number }

  private constructor() {
    this.cachedStatus = {}
  }

  public static getInstance(): SiteStatusHelper {
    if (!SiteStatusHelper.instance) {
      SiteStatusHelper.instance = new SiteStatusHelper()
    }
    return SiteStatusHelper.instance
  }

  isSalable(product: SimpleProductViewFragment): boolean {
    return (
      !this.isAwaitingDelivery(product) &&
      !this.isDepleted(product) &&
      !this.isNotActive(product) &&
      !this.isLimitedVisibility(product) &&
      !this.isOnlyInStore(product)
    )
  }

  isProductOnlyAvailableForStorePickup(product: SimpleProductViewFragment): boolean {
    return (
      this.isAvailableFromStore(product) ||
      this.isAwaitingDeliveryFromStore(product) ||
      this.isAllowedForIndividualOrderFromStore(product)
    )
  }

  isLimitedAvailability(product: SimpleProductViewFragment): boolean {
    if (product.__typename === 'SimpleProduct') {
      return product.stock.qty >= 1 && product.stock.qty <= 3
    }
    return false
  }

  isOnlyInStore(product: SimpleProductViewFragment): boolean {
    return this.getStatus(product) === SiteStatusHelper.ONLY_IN_STORE
  }

  isLimitedVisibility(product: SimpleProductViewFragment): boolean {
    return this.getStatus(product) === SiteStatusHelper.LIMITED_VISIBILITY
  }

  isAvailable(product: SimpleProductViewFragment): boolean {
    return this.getStatus(product) === SiteStatusHelper.AVAILABLE
  }

  isAvailableFromStore(product: SimpleProductViewFragment): boolean {
    return this.getStatus(product) === SiteStatusHelper.AVAILABLE_PICKUP
  }

  isAwaitingDelivery(product: SimpleProductViewFragment): boolean {
    return this.getStatus(product) === SiteStatusHelper.AWAITING_DELIVERY
  }

  isAwaitingDeliveryFromStore(product: SimpleProductViewFragment): boolean {
    return this.getStatus(product) === SiteStatusHelper.AWAITING_DELIVERY_PICKUP
  }

  isDepleted(product: SimpleProductViewFragment): boolean {
    return this.getStatus(product) === SiteStatusHelper.DEPLETED
  }

  isNotActive(product: SimpleProductViewFragment): boolean {
    return this.getStatus(product) === SiteStatusHelper.NOT_ACTIVE
  }

  isAllowedForIndividualOrder(product: SimpleProductViewFragment): boolean {
    return this.getStatus(product) === SiteStatusHelper.INDIVIDUAL_ORDER
  }

  isAllowedForIndividualOrderFromStore(product: SimpleProductViewFragment): boolean {
    return this.getStatus(product) === SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP
  }

  getStatus(product: SimpleProductViewFragment): number {
    if (this.cachedStatus[product.id]) {
      return this.cachedStatus[product.id]
    }

    const stock = product.__typename === 'SimpleProduct' ? product.stock : undefined
    if (!stock) {
      return SiteStatusHelper.DEPLETED
    }

    const inStock = stock.inStock ?? false
    const manageStock = stock.manageStock ?? false
    const blockedForSales = stock.blockForSale ?? false
    const zeronSiteStatus = stock.zeronSiteStatus ?? ''
    const zeronBlockedDelivery = stock.zeronBlockedDelivery ?? false
    const hasImages = this.hasImages(product)

    const value = this.getStatusValue(
      blockedForSales,
      zeronSiteStatus,
      zeronBlockedDelivery,
      manageStock,
      inStock,
      hasImages
    )

    this.setStatus(product, value)
    return value
  }

  setStatus(product: SimpleProductViewFragment, value: number): SiteStatusHelper {
    this.cachedStatus[product.id] = value
    return this
  }

  getStatusValue(
    blockedForSales: boolean,
    zeronSiteStatus: string,
    zeronBlockedDelivery: boolean,
    manageStock: boolean,
    inStock: boolean,
    hasImages: boolean
  ): number {
    const showOutOfStock = true
    let status: number

    if (!hasImages) {
      status = SiteStatusHelper.NOT_ACTIVE
    } else if (blockedForSales) {
      status = SiteStatusHelper.DEPLETED
    } else if (manageStock) {
      if (zeronSiteStatus === SiteStatusHelper.ZERON_SITE_STATUS_1) {
        if (inStock) {
          status = SiteStatusHelper.AVAILABLE
        } else if (showOutOfStock) {
          if (zeronBlockedDelivery) {
            status = SiteStatusHelper.DEPLETED
          } else {
            status = SiteStatusHelper.AWAITING_DELIVERY
          }
        } else {
          status = SiteStatusHelper.DEPLETED
        }
      } else if (zeronSiteStatus === SiteStatusHelper.ZERON_SITE_STATUS_3) {
        if (!inStock) {
          status = SiteStatusHelper.DEPLETED
        } else {
          status = SiteStatusHelper.ONLY_IN_STORE
        }
      } else if (
        zeronSiteStatus === SiteStatusHelper.ZERON_SITE_STATUS_4 ||
        zeronSiteStatus === SiteStatusHelper.ZERON_SITE_STATUS_5
      ) {
        status = SiteStatusHelper.LIMITED_VISIBILITY
      } else {
        if (inStock) {
          status = SiteStatusHelper.AVAILABLE_PICKUP
        } else if (showOutOfStock) {
          if (zeronBlockedDelivery) {
            status = SiteStatusHelper.DEPLETED
          } else {
            status = SiteStatusHelper.AWAITING_DELIVERY_PICKUP
          }
        } else {
          status = SiteStatusHelper.DEPLETED
        }
      }
    } else {
      if (zeronBlockedDelivery) {
        status = SiteStatusHelper.DEPLETED
      } else if (zeronSiteStatus === SiteStatusHelper.ZERON_SITE_STATUS_3) {
        status = SiteStatusHelper.ONLY_IN_STORE
      } else if (
        zeronSiteStatus === SiteStatusHelper.ZERON_SITE_STATUS_4 ||
        zeronSiteStatus === SiteStatusHelper.ZERON_SITE_STATUS_5
      ) {
        status = SiteStatusHelper.LIMITED_VISIBILITY
      } else {
        if (zeronSiteStatus === SiteStatusHelper.ZERON_SITE_STATUS_1) {
          status = SiteStatusHelper.INDIVIDUAL_ORDER
        } else {
          status = SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP
        }
      }
    }

    return status
  }

  hasImages(product: SimpleProductViewFragment): boolean {
    // @TBD: Temporary fix for demo env
    return true //product.gallery && product.gallery.length > 0
  }

  getStatusText(status: number): string {
    switch (status) {
      case SiteStatusHelper.AVAILABLE:
        return 'Available'
      case SiteStatusHelper.AVAILABLE_PICKUP:
        return 'Available for Pickup'
      case SiteStatusHelper.AWAITING_DELIVERY:
        return 'Awaiting Delivery'
      case SiteStatusHelper.AWAITING_DELIVERY_PICKUP:
        return 'Awaiting Delivery (Pickup)'
      case SiteStatusHelper.INDIVIDUAL_ORDER:
        return 'Available by Individual Order'
      case SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP:
        return 'Available by Individual Order (Pickup)'
      case SiteStatusHelper.ONLY_IN_STORE:
        return 'Available Only In Store'
      case SiteStatusHelper.DEPLETED:
        return 'Out of Stock'
      case SiteStatusHelper.NOT_ACTIVE:
        return 'Not Active'
      case SiteStatusHelper.LIMITED_VISIBILITY:
        return 'Limited Visibility'
      default:
        return 'Unknown Status'
    }
  }
}

export const siteStatusHelper = SiteStatusHelper.getInstance()
