import { IncomingMessage } from 'http'
import { ParsedUrlQuery } from 'querystring'

import { NextApiRequestCookies } from 'next/dist/server/api-utils'

export type SlugParam<T = string[]> = {
  dynamic: T
  identifier: string
}

export type SearchParams = { [key: string]: string | string[] | undefined }

export type NextPageProps<TParams = {}, TSearchParams = SearchParams> = {
  params: Promise<TParams>
  searchParams: Promise<TSearchParams>
}

export function getRequestedUrlFromSlug(
  req: IncomingMessage & {
    cookies: NextApiRequestCookies
  },
  params?: ParsedUrlQuery
): string {
  let url = req.url ?? ''
  if (params && params.slug) {
    if (Array.isArray(params.slug)) {
      url = params.slug.join('/')
    } else {
      url = params.slug
    }
  }

  return url.toLowerCase()
}

export function urlIncludes(path: string): boolean {
  return window && window.location && window.location.pathname.includes(path)
}
