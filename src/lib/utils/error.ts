export const NOT_AUTHENTICATED_CODE = '4002'

interface GraphQLError {
  message: string
  extensions: {
    field: string
  }
}

export function getErrorMessage(error: Error | string | any): string {
  if (typeof error === 'string') {
    return error
  } else if (error?.response?.errors?.length > 0) {
    for (const _err of error.response.errors) {
      if (!_err.extensions?.field) {
        return _err.message
      }
    }

    return error.response.errors[0].message
  } else if (error?.message) {
    return error.message
  } else {
    return 'Unknown error'
  }
}

export function getFormikErrorsFromResponse(error: Error | string | any): Record<string, string> | null {
  const errors: Record<string, string> = {}
  if (error?.response?.errors?.length > 0) {
    error.response.errors.forEach((error: GraphQLError, index: number) => {
      if (error.extensions?.field) {
        const fieldName = error.extensions.field.toString().toLocaleLowerCase()
        errors[fieldName] = error.message
      }
    })

    return errors
  }

  return null
}
