import Cookies from 'universal-cookie'

const cookies = new Cookies()

function getHoursInMs(hours: number): number {
  return hours * 60 * 60 * 1000
}

export function getExpirationTimeAfterHours(hours: number): number {
  return Date.now() + getHoursInMs(hours)
}

export function saveCookie(key: string, val: string, expiration?: number): void {
  // Set default expiration to 7 days from now if not provided
  const defaultExpiration = Date.now() + 7 * 24 * 60 * 60 * 1000
  const expirationDate = new Date(expiration ?? defaultExpiration)

  cookies.set(key, val, {
    path: '/', // Accessible site-wide
    expires: expirationDate, // Set expiration
  })
}

export function getCookieValue(key: string): any {
  return cookies.get(key) ?? ''
}
