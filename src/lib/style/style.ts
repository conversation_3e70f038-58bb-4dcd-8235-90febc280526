import { cn } from '@components/lib/utils'

export const addClasses = cn

export function removeElementClass(elementId: string, className: string): void {
  const element = document.getElementById(elementId)
  if (element) {
    element.classList.remove(className)
  }
}

export function addElementClass(elementId: string, className: string): void {
  const element = document.getElementById(elementId)
  if (element) {
    element.classList.add(className)
  }
}

export function elementHasClass(elementId: string, className: string): boolean {
  const element = document.getElementById(elementId)
  if (element) {
    return element.classList.contains(className)
  }

  return false
}
