import { ThemeColors } from '@/tailwind.config'
import { addClasses } from '@lib/style/style'

export type ComponentWidth = 'full' | string

export interface ThemeStyles {
  color?: ThemeColors
  position?: 'absolute' | 'relative' | 'fixed' | 'static'
  padding?: number | string
  margin?: number | string
  rounding?: string
  width?: ComponentWidth
  shadow?: string
  unstyled?: boolean
  className?: string
}

export type ThemeWidths = 'full' | 'fit'

export type JustifyTypes =
  | 'justify-center'
  | 'justify-start'
  | 'justify-end'
  | 'justify-between'

export function getStyleClassesFromProps(props: ThemeStyles): string {
  if (props.unstyled) {
    return ''
  }

  let classes = addClasses(props.position ?? '', props.rounding ?? 'rounded-md')

  if (props.width) {
    const _w = `${props.width}`
    classes = addClasses(classes, 'w-' + _w.replace('w-', ''))
  }

  if (props.padding) {
    if (typeof props.padding === 'number') {
      classes = addClasses(classes, `p-${props.padding}`)
    } else {
      classes = addClasses(classes, props.padding)
    }
  }

  if (props.margin) {
    if (typeof props.margin === 'number') {
      classes = addClasses(classes, `m-${props.padding}`)
    } else {
      classes = addClasses(classes, props.margin)
    }
  }

  return classes
}

export const MaxProductCardWidth = 218
