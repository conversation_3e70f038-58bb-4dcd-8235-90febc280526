import React from 'react'

import CatalogViewGrid from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/CatalogViewGrid'
import Sort from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/Sort/Sort'
import { Filters } from '@/src/features/filters/Filters'
import { AppFilterStateFragment, CatalogPageDataFragment, ProductViewFragment } from '@lib/_generated/graphql_sdk'

interface Props {
  state: AppFilterStateFragment
  products: ProductViewFragment[]
}

const CategoryProductsList: React.FC<Props> = ({ state, products }) => {
  return (
    <>
      <div className="flex lg:gap-4">
        <Filters applied={state.filters.applied} available={state.filters.available} />
        <div className="flex flex-col flex-1">
          <div className="flex justify-end mb-6">
            <Sort defaultSort={state.sort} pageSize={state.pager.pageSize} />
          </div>
          <CatalogViewGrid products={products} pagination={state.pager} />
        </div>
      </div>
    </>
  )
}

export default CategoryProductsList
