import { LucidePlug, LucidePlus } from 'lucide-react'
import React from 'react'

import { Dimmer } from '@/src/features/catalog/components/CategoryItem/components/Dimmer'
import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { Card } from '@components/theme/ui/card'
import type { CategoryLinkWidget } from '@lib/_generated/graphql_sdk'
import { addClasses } from '@lib/style/style'

interface Props {
  widget: CategoryLinkWidget
}

const CategoryItem: React.FC<Props> = ({ widget }) => {
  return (
    <Card className={cn('bg-transparent shadow-none border-none group relative', 'md:p-3', 'lg:p-5')}>
      <AppLink {...widget.url} dontAppendSlash className={addClasses('flex flex-col')}>
        <Dimmer className="md:group-hover:opacity-70" />
        <div className="w-full aspect-square relative">
          <div
            className={cn(
              'absolute left-0 right-0 bottom-0 z-20',
              'flex justify-between items-center gap-2',
              'bg-white group-hover:bg-primary group-hover:text-white transition-all duration-300',
              'min-h-[35%] p-1',
              'rounded-lg xl:rounded-xl',
              'sm:px-5'
            )}
          >
            <div className="max-w-full w-2/3 overflow-visible">
              <span
                className={cn(
                  'text-wrap break-normal overflow-visible',
                  'font-bold',
                  'text-sm',
                  'sm:text-lg',
                  'md:text-base',
                  'lg:text-lg',
                  'xl:text-xl',
                  '2xl:text-2xl'
                )}
              >
                {widget.title.replace(',', ', ')}
              </span>
            </div>
            <div>
              <Button size="icon" className="group-hover:text-primary group-hover:bg-white">
                <LucidePlus />
              </Button>
            </div>
          </div>
          <Img fill src={widget.image.src} alt={widget.title} className="rounded-xl" />
        </div>
      </AppLink>
    </Card>
  )
}

export default CategoryItem
