import React, { useMemo } from 'react'

import CategoryItem from '@/src/features/catalog/components/CategoryItem/CategoryItem'
import { cn } from '@components/lib/utils'
import { AppCategoryViewWidgetFragment } from '@lib/_generated/graphql_sdk'

interface CategoriesListProps {
  widgets?: AppCategoryViewWidgetFragment[] | null
}

const CategoriesList: React.FC<CategoriesListProps> = ({ widgets }) => {
  const fragments = useMemo(() => {
    return widgets?.filter((w) => w.__typename === 'CategoryLinkWidget') || []
  }, [widgets])

  return (
    <div
      className={cn(
        'grid gap-1 lg:gap-2',
        'grid-cols-2',
        'sm:grid-cols-2 sm:gap-2',
        'md:grid-cols-3 md:gap-2',
        'lg:grid-cols-3 lg:gap-2',
        'xl:grid-cols-4 xl:gap-2',
        '2xl:grid-cols-4 2xl:gap-4',
        '3xl:grid-cols-4 3xl:gap-4',
        '4xl:grid-cols-5 4xl:gap-4',
        '5xl:grid-cols-6 5xl:gap-4',
        '6xl:grid-cols-7 6xl:gap-4'
      )}
    >
      {fragments.map((w, i) => (
        <CategoryItem key={i} widget={w} />
      ))}
    </div>
  )
}

export default CategoriesList
