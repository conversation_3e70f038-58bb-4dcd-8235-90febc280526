import React from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { useCartStore } from '@features/cart/cart-state'
import { ProductPrice } from '@components/molecules/ProductPrice'

export const MiniCheckoutTotals: React.FC = () => {
  const { totals } = useCartStore()
  return (
    <div className="bg-background rounded-xl my-3 p-1.5 flex flex-col">
      {totals
        .sort((a, b) => a.order - b.order)
        .map((total, i) => (
          <div
            className={cn('flex justify-between text-sm p-2', {
              'border-b border-[#CDCDCD]': totals.length - 1 !== i,
            })}
            key={total.code}
          >
            <Text className={total.code === 'GRANT_TOTAL' ? 'text-primary font-bold' : 'text-black'}>
              {total.label}:
            </Text>
            <ProductPrice
              data={{
                price: total.amount,
              }}
              variant={total.code === 'GRANT_TOTAL' ? 'checkoutGrandTotal' : 'checkoutTotal'}
            />
          </div>
        ))}
    </div>
  )
}
