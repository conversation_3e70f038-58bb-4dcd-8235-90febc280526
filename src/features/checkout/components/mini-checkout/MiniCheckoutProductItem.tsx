import AppLink from '@atoms/AppLink'
import Img from '@atoms/Img'
import { Image, Price } from '@lib/_generated/graphql_sdk'
import { ProductPrice } from '@components/molecules/ProductPrice'
import React from 'react'

interface MiniCheckoutProductItemProps {
  image: Image
  count: string
  title: string
  url: string
  rowTotal: Price
}

export const MiniCheckoutProductItem: React.FC<MiniCheckoutProductItemProps> = ({
  image,
  count,
  title,
  url,
  rowTotal,
}) => {
  return (
    <div className="flex items-center gap-4">
      <div className="relative min-w-[80px] aspect-square">
        <div className="bg-secondary-foreground absolute z-10 text-white text-xs rounded-full aspect-square w-[25px] h-[25px] text-center flex items-center justify-center -right-2 -top-2 font-bold">
          {count}x
        </div>
        <Img
          src={image.src}
          alt={image.alt || title}
          fill
          className="object-contain border border-[#F3F3F3] rounded-xl p-2"
        />
      </div>
      <div className="flex flex-col gap-2">
        <AppLink href={url} className="text-sm">
          {title}
        </AppLink>
        <ProductPrice
          data={{
            price: rowTotal,
          }}
          variant="checkoutTotal"
        />
      </div>
    </div>
  )
}
