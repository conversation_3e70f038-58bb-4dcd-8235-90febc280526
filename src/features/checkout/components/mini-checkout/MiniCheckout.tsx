import { LucideLoaderCircle } from 'lucide-react'
import React, { useEffect, useState } from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@components/theme/ui/card'
import { useCartStore } from '@features/cart/cart-state'
import { MiniCheckoutDeliveryInfo } from '@features/checkout/components/DeliveryInfo'
import { MiniCheckoutProductItem } from '@features/checkout/components/mini-checkout/MiniCheckoutProductItem'
import { MiniCheckoutTotals } from '@features/checkout/components/mini-checkout/MiniCheckoutTotals'
import { getProductMainImage } from '@features/product/helpers'
import { FormSubmit } from '@components/molecules/FormControllers/FormSubmit'
import { Price } from '@components/molecules/Price'
import { ProductPrice } from '@components/molecules/ProductPrice'

interface MiniCheckoutProps {
  onPressPrev: () => void
  hasPreviousStep?: boolean
  onPressComplete: () => void
  createOrderLoading?: boolean
  isActive?: boolean
}
export const MiniCheckout: React.FC<MiniCheckoutProps> = ({
  onPressPrev,
  hasPreviousStep,
  onPressComplete,
  isActive,
  createOrderLoading,
}) => {
  const { initCart, items, loading, shipping, totals, currency } = useCartStore()

  const [mounted, setMounted] = useState(false)
  useEffect(() => {
    if (!mounted) {
      setMounted(true)
      return
    }
    console.log('HERE')
    initCart()
  }, [mounted, initCart])

  const subTotal = totals.find((total) => total.code === 'SUB_TOTAL')
  const shippingTotal = totals.find((total) => total.code === 'SHIPPING_TOTAL')
  const freeDeliveryLimit = (shipping?.freeShippingAfter || 0) - (subTotal?.amount.value || 0)

  return (
    <Card className="w-full rounded-3xl lg:p-4 sticky top-[120px]" id="finale">
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="hidden lg:block text-2xl font-bold">Моите продукти</div>
        <div className="lg:hidden text-2xl font-bold">Преглед</div>
        {loading && <LucideLoaderCircle className="animate-spin" />}
      </CardHeader>
      {!loading && (
        <CardContent>
          {(shipping?.address?.method === 'ECONT_TO_ADDRESS' || shipping?.address?.method === 'ECONT_TO_OFFICE') && (
            <div className="hidden lg:block">
              {/*<MiniCheckoutDeliveryInfo>*/}
              {/*  {freeDeliveryLimit > 0 && (*/}
              {/*    <div className="flex justify-between">*/}
              {/*      <div>*/}
              {/*        <Text className="text-sm font-bold">Продукт с доставка:</Text>*/}
              {/*      </div>*/}
              {/*      {shippingTotal?.amount.value && (*/}
              {/*        <div>*/}
              {/*          <Text className="text-sm font-bold">*/}
              {/*            {shippingTotal.amount.value.toFixed(2)} {shippingTotal?.amount.currency}*/}
              {/*          </Text>*/}
              {/*        </div>*/}
              {/*      )}*/}
              {/*    </div>*/}
              {/*  )}*/}
              {/*  {shipping && (shipping.minAmountForFreeShippingMessage || 0) < (subTotal?.amount.value || 0) && (*/}
              {/*    <div>*/}
              {/*      {freeDeliveryLimit > 0 ? (*/}
              {/*        <Text className="text-paragraph-muted text-sm">*/}
              {/*          Остават {freeDeliveryLimit.toFixed(2)} {currency} до безплатна доставка*/}
              {/*        </Text>*/}
              {/*      ) : (*/}
              {/*        <Text className="text-black font-bold text-sm">Безплатна доставка</Text>*/}
              {/*      )}*/}
              {/*    </div>*/}
              {/*  )}*/}
              {/*</MiniCheckoutDeliveryInfo>*/}
            </div>
          )}
          <div className="flex flex-col gap-4">
            {items.map((item) => {
              const productMainImage = getProductMainImage(item.product.image, item.product.gallery)
              return (
                <MiniCheckoutProductItem
                  key={item.id}
                  title={item.product.name}
                  url={item.product.urlKey}
                  rowTotal={item.rowTotal}
                  image={productMainImage}
                  count={item.baseQty.toString()}
                />
              )
            })}
          </div>
          <MiniCheckoutTotals />
        </CardContent>
      )}
      <CardFooter className="px-0">
        <div
          className={cn('flex flex-row-reverse flex-wrap 3xl:flex-row flex-1 gap-1 items-center justify-center py-2', {
            hidden: loading,
          })}
        >
          {hasPreviousStep && (
            <Button onClick={onPressPrev} variant="link" className="hidden lg:flex font-bold text-black">
              Предишна стъпка
            </Button>
          )}
          <div id="sidebar-button-container"></div>
          {isActive && (
            <Button type="submit" disabled={createOrderLoading} className="tracking-normal" onClick={onPressComplete}>
              {createOrderLoading && <LucideLoaderCircle className="animate-spin text-white" />}
              <Text>Завършване на поръчка</Text>
            </Button>
          )}
        </div>
      </CardFooter>
      {(createOrderLoading || loading) && (
        <CardContent className="flex justify-center items-center py-10">
          <Text>Моля, изчакайте...</Text>
        </CardContent>
      )}
    </Card>
  )
}
