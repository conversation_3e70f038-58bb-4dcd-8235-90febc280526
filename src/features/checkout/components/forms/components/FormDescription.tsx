import React, { PropsWithChildren } from 'react'
import { CardDescription, CardTitle } from '@components/theme/ui/card'
import { PropsWithClassName } from '@lib/types/ClassName'
import { cn } from '@components/lib/utils'

export const FormDescription: React.FC<PropsWithChildren<PropsWithClassName>> = ({ children, className }) => {
  return <CardDescription className={cn(className)}>{children}</CardDescription>
}
