import React, { PropsWithChildren } from 'react'

import { CardContent } from '@/src/components/theme/ui/card'
import { cn } from '@components/lib/utils'

interface FormContentProps {
  open: boolean
}

export const FormContent: React.FC<PropsWithChildren<FormContentProps>> = ({ children, open }) => {
  return (
    <CardContent
      data-state={open ? 'open' : 'closed'}
      className={cn('p-3 md:p-6 data-[state=open]:animate-accordion-down', !open && 'hidden')}
    >
      {children}
    </CardContent>
  )
}
