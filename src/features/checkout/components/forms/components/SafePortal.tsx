import React, { useEffect, useState } from 'react'
import { createPortal } from 'react-dom'

interface SafePortalProps {
  children: React.ReactNode
  containerSelector: string
}

// A portal component that safely handles null checking and client-side rendering
const SafePortal: React.FC<SafePortalProps> = ({ children, containerSelector }) => {
  const [container, setContainer] = useState<Element | null>(null)

  useEffect(() => {
    // Only run in browser environment
    if (typeof window !== 'undefined') {
      const containerElement = document.querySelector(containerSelector)
      setContainer(containerElement)
    }
  }, [containerSelector])

  // Only render the portal if we have a container and we're in the browser
  if (container && typeof window !== 'undefined') {
    return createPortal(children, container)
  }

  // Return null during SSR or if container not found
  return null
}

export default SafePortal
