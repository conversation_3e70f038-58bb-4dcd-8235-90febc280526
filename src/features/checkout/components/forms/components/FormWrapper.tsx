import { LucideLoaderCircle } from 'lucide-react'
import React, { PropsWithChildren } from 'react'

import { cn } from '@components/lib/utils'
import { CardDescription, CardHeader } from '@components/theme/ui/card'
import { FormTitle } from '@features/checkout/components/forms/components/FormTitle'
import { ValidFormSign } from '@features/checkout/components/ValidFormSign'

import { FormCard } from './FormCard'
import { FormState, FormType } from '@components/pages/Checkout/types'
import { FormDescription } from '@features/checkout/components/forms/components/FormDescription'

interface FormWrapperProps {
  title: string
  type: FormType
  formState: FormState
  currentStep: FormType
  onClickForm: (formType: FormType) => void
}

export function FormWrapper<T extends FormType>({
  children,
  type,
  title,
  formState,
  currentStep,
  onClickForm,
}: PropsWithChildren<FormWrapperProps>) {
  const isLoading = formState[type].status === 'loading'
  const isCurrentStep = currentStep === type
  const isAvailable = formState[type].isAvailable
  const isValid = formState[type].status === true

  return (
    <FormCard id={type}>
      <CardHeader
        className={cn('flex flex-row items-center justify-between cursor-default', {
          'lg:hover:bg-black/5 rounded-3xl cursor-pointer': isAvailable && !isCurrentStep,
        })}
        onClick={() => {
          if (isAvailable) {
            onClickForm(type)
          }
        }}
      >
        <div className="flex-col">
          <FormTitle>{title}</FormTitle>
          {isLoading && isCurrentStep && (
            <FormDescription className="lg:hidden">
              <span>Моля, изчакайте...</span>
            </FormDescription>
          )}
        </div>
        <div>
          {isValid && <ValidFormSign />}
          {isLoading && (
            <div className="flex items-center gap-2">
              {isCurrentStep && <span className="hidden lg:block">Моля, изчакайте...</span>}
              <LucideLoaderCircle className="animate-spin" />
            </div>
          )}
        </div>
      </CardHeader>
      {!isLoading && isAvailable ? children : null}
    </FormCard>
  )
}
