'use client'
import React, { useState, useEffect } from 'react'
import { formatPercentage } from '@components/jet-leasing/BNPPayment'

// TypeScript interfaces for the API response
interface CalculatorVariant {
  id: string
  apr: string
  correct_downpayment_amount: string
  installment_amount: string
  maturity: string
  nir: string
  pricing_scheme_id?: string
  pricing_scheme_name?: string
  total_repayment_amount: string
  is_promo: boolean
}

interface VariantsByScheme {
  scheme_id: string
  scheme_name: string
  variants: CalculatorVariant[]
}

interface CalculatorConfiguration {
  quote_id: string
  product_id: string | null
  product_qty: string | null
  can_add_choose_inputs: boolean
  can_add_application_form_inputs: boolean
  input_prefix: string
  input_suffix: string
  choose_input_name: string
  downpayment_input_name: string
  choose_input_form_id: string
  choose_input_id_prefix: string
  hide_down_payment: boolean
}

interface Calculator {
  principal: number
  downpayment: number
  formatted_principal: string
  formatted_downpayment: string
  configuration: CalculatorConfiguration
  variants: CalculatorVariant[]
  variants_by_scheme: VariantsByScheme[]
  recalc_url: string
  has_variants: boolean
}

interface CalculatorMeta {
  timestamp: number
  currency_code: string
  store_id: string
}

interface CalculatorApiResponse {
  success: boolean
  calculator: Calculator
  meta: CalculatorMeta
}

interface RenderCalculatorProps {
  json?: CalculatorApiResponse
  onVariantSelect?: (variantId: string) => void
  className?: string
}

// Default JSON data from the API
const defaultJson: CalculatorApiResponse = {
  success: true,
  calculator: {
    principal: 129.99,
    downpayment: 0,
    formatted_principal: '<span class="price">129,<sup>99</sup>&nbsp;лв.</span>',
    formatted_downpayment: '<span class="price">0,<sup>00</sup>&nbsp;лв.</span>',
    configuration: {
      quote_id: '1144929',
      product_id: null,
      product_qty: null,
      can_add_choose_inputs: true,
      can_add_application_form_inputs: true,
      input_prefix: 'payment[',
      input_suffix: ']',
      choose_input_name: 'variant_id',
      downpayment_input_name: 'downpayment',
      choose_input_form_id: 'co-payment-form',
      choose_input_id_prefix: 'payment_jetcredit_variant_',
      hide_down_payment: true,
    },
    variants: [
      {
        id: '792441',
        apr: '0.06',
        correct_downpayment_amount: '0',
        installment_amount: '65.00',
        maturity: '2',
        nir: '0',
        pricing_scheme_id: '2791',
        pricing_scheme_name: 'Вземи сега, плати после:0% за 2м. с кредитна карта',
        total_repayment_amount: '129.99',
        is_promo: false,
      },
      {
        id: '792443',
        apr: '0',
        correct_downpayment_amount: '0',
        installment_amount: '43.33',
        maturity: '3',
        nir: '0',
        pricing_scheme_id: '2792',
        pricing_scheme_name: 'Вземи сега, плати после:0% за 3м. с кредитна карта',
        total_repayment_amount: '129.99',
        is_promo: false,
      },
    ],
    variants_by_scheme: [
      {
        scheme_id: '2791',
        scheme_name: 'Вземи сега, плати после:0% за 2м. с кредитна карта',
        variants: [
          {
            id: '792441',
            apr: '0.06',
            correct_downpayment_amount: '0',
            installment_amount: '65.00',
            maturity: '2',
            nir: '0',
            total_repayment_amount: '129.99',
            is_promo: false,
          },
        ],
      },
      {
        scheme_id: '2792',
        scheme_name: 'Вземи сега, плати после:0% за 3м. с кредитна карта',
        variants: [
          {
            id: '792443',
            apr: '0',
            correct_downpayment_amount: '0',
            installment_amount: '43.33',
            maturity: '3',
            nir: '0',
            total_repayment_amount: '129.99',
            is_promo: false,
          },
        ],
      },
    ],
    recalc_url: 'https://praktis2-demo.pfgbulgaria.com/stenik_leasingjetcredit/calculator/recalc/quote/1144929/',
    has_variants: true,
  },
  meta: {
    timestamp: 1750286626,
    currency_code: 'BGN',
    store_id: '1',
  },
}

export const RenderCalculator: React.FC<RenderCalculatorProps> = ({ json, onVariantSelect, className = '' }) => {
  const [selectedVariantId, setSelectedVariantId] = useState<string>('')
  const [downpayment, setDownpayment] = useState<number>(0)
  const [isRecalculating, setIsRecalculating] = useState(false)

  const calculatorData = json || defaultJson

  useEffect(() => {
    // Auto-select first variant if available
    if (calculatorData.calculator.variants.length > 0 && !selectedVariantId) {
      setSelectedVariantId(calculatorData.calculator.variants[0].id)
    }
    // Initialize downpayment from calculator data
    setDownpayment(calculatorData.calculator.downpayment)
  }, [calculatorData, selectedVariantId])

  const handleVariantChange = (variantId: string) => {
    setSelectedVariantId(variantId)
    onVariantSelect?.(variantId)
  }

  const handleDownpaymentChange = async (newDownpayment: number) => {
    setDownpayment(newDownpayment)
    setIsRecalculating(true)

    try {
      // TODO: GraphQL query placeholder - recalculate with new downpayment
      // const response = await fetch(calculatorData.calculator.recalc_url, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ downpayment: newDownpayment })
      // })
      // const newData = await response.json()
      // Update calculator data with new variants
    } catch (error) {
      console.error('Error recalculating:', error)
    } finally {
      setIsRecalculating(false)
    }
  }

  const formatPrice = (amount: string | number): string => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount
    return `${num.toFixed(2).replace('.', ',')} лв.`
  }

  const selectedVariant = calculatorData.calculator.variants.find((v) => v.id === selectedVariantId)

  if (!calculatorData.success) {
    return (
      <div className={`p-4 border border-red-200 rounded-lg bg-red-50 ${className}`}>
        <p className="text-red-600 text-sm">Грешка при зареждане на калкулатора</p>
      </div>
    )
  }

  return (
    <div className={`p-6 border border-gray-200 rounded-lg bg-white ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-lg font-bold text-gray-900 mb-2">Калкулатор за разсрочено плащане</h3>
        <div className="flex items-center gap-4 flex-wrap">
          <div>
            <span className="text-sm text-gray-600">Обща сума: </span>
            <span
              className="text-lg font-semibold text-orange-600"
              dangerouslySetInnerHTML={{
                __html: calculatorData.calculator.formatted_principal,
              }}
            />
          </div>
          {!calculatorData.calculator.configuration.hide_down_payment && (
            <div>
              <span className="text-sm text-gray-600">Първоначална вноска: </span>
              <span
                className="text-lg font-semibold"
                dangerouslySetInnerHTML={{
                  __html: calculatorData.calculator.formatted_downpayment,
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Downpayment Input (if not hidden) */}
      {!calculatorData.calculator.configuration.hide_down_payment && (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Първоначална вноска</label>
          <div className="flex items-center gap-2">
            <input
              type="number"
              min="0"
              max={calculatorData.calculator.principal}
              step="0.01"
              value={downpayment}
              onChange={(e) => handleDownpaymentChange(parseFloat(e.target.value) || 0)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              disabled={isRecalculating}
            />
            <span className="text-sm text-gray-600">лв.</span>
            {isRecalculating && <span className="text-xs text-gray-500">Преизчисляване...</span>}
          </div>
        </div>
      )}

      {/* Payment Variants */}
      {calculatorData.calculator.has_variants && (
        <div className="mb-6">
          <h4 className="text-md font-semibold text-gray-900 mb-4">Изберете схема на плащане:</h4>

          <div className="space-y-3">
            {calculatorData.calculator.variants.map((variant) => (
              <div
                key={variant.id}
                className="border border-gray-200 rounded-lg p-4 hover:border-orange-300 transition-colors"
              >
                <label className="flex items-start gap-3 cursor-pointer">
                  <input
                    type="radio"
                    name={calculatorData.calculator.configuration.choose_input_name}
                    value={variant.id}
                    checked={selectedVariantId === variant.id}
                    onChange={() => handleVariantChange(variant.id)}
                    className="mt-1 text-orange-600 focus:ring-orange-500"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 mb-1">{variant.pricing_scheme_name}</div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Месечна вноска:</span>
                        <div className="font-semibold text-orange-600">{formatPrice(variant.installment_amount)}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Срок:</span>
                        <div className="font-semibold">{variant.maturity} месеца</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Обща сума:</span>
                        <div className="font-semibold">{formatPrice(variant.total_repayment_amount)}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">ГПР:</span>
                        <div className="font-semibold">{formatPercentage(variant.apr)}</div>
                      </div>
                    </div>
                    {variant.is_promo && (
                      <div className="mt-2">
                        <span className="inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                          Промоция
                        </span>
                      </div>
                    )}
                  </div>
                </label>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Selected Variant Summary */}
      {selectedVariant && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h5 className="font-semibold text-gray-900 mb-3">Избрана схема:</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="text-gray-600">Месечна вноска</div>
              <div className="text-xl font-bold text-orange-600">{formatPrice(selectedVariant.installment_amount)}</div>
            </div>
            <div className="text-center">
              <div className="text-gray-600">Срок на плащане</div>
              <div className="text-xl font-bold">{selectedVariant.maturity} месеца</div>
            </div>
            <div className="text-center">
              <div className="text-gray-600">Обща сума за плащане</div>
              <div className="text-xl font-bold">{formatPrice(selectedVariant.total_repayment_amount)}</div>
            </div>
          </div>

          {/* Payment Schedule Preview */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h6 className="font-medium text-gray-900 mb-2">График на плащанията:</h6>
            <div className="text-sm text-gray-600">
              {Array.from({ length: parseInt(selectedVariant.maturity) }, (_, i) => (
                <div key={i} className="flex justify-between py-1">
                  <span>Месец {i + 1}:</span>
                  <span className="font-medium">{formatPrice(selectedVariant.installment_amount)}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Hidden inputs for form submission */}
      {calculatorData.calculator.configuration.can_add_choose_inputs && selectedVariantId && (
        <input
          type="hidden"
          name={`${calculatorData.calculator.configuration.input_prefix}${calculatorData.calculator.configuration.choose_input_name}${calculatorData.calculator.configuration.input_suffix}`}
          value={selectedVariantId}
        />
      )}

      {!calculatorData.calculator.configuration.hide_down_payment && (
        <input
          type="hidden"
          name={`${calculatorData.calculator.configuration.input_prefix}${calculatorData.calculator.configuration.downpayment_input_name}${calculatorData.calculator.configuration.input_suffix}`}
          value={downpayment}
        />
      )}
    </div>
  )
}
