'use client'

import React from 'react'
import { useFormContext, useWatch } from 'react-hook-form'

import { FormCheckbox } from '@components/molecules/FormControllers/FormCheckbox'
import { FormTextField } from '@components/molecules/FormControllers/FormTextField'
import { PersonalSchema } from '@features/checkout/components/forms/personal/personal.schema'

export const InvoicePersonalFields: React.FC = () => {
  const { control } = useFormContext<PersonalSchema>()
  const invoiceVatRegistered = useWatch({ control, name: 'invoiceVatRegistered' })

  return (
    <>
      <div className="col-span-2 lg:col-span-1">
        <FormTextField name="invoiceName" label="Име" />
      </div>
      <div className="col-span-2 lg:col-span-1">
        <FormTextField name="invoiceCity" label="Град/Село" />
      </div>
      <div className="col-span-2 lg:col-span-1">
        <FormTextField name="invoiceEIK" label="ЕГН/ЕИК" />
      </div>
      <div className="col-span-2 lg:col-span-1 flex items-center">
        <FormCheckbox
          name="invoiceVatRegistered"
          label="Регистриран по ДДС"
        />
      </div>
      {invoiceVatRegistered && (
        <div className="col-span-2 lg:col-span-1">
          <FormTextField name="invoiceVatNumber" label="Номер на рег. по ДДС" />
        </div>
      )}
      <div className="col-span-full">
        <FormTextField name="invoiceAddress" label="Адрес" />
      </div>
    </>
  )
}

InvoicePersonalFields.displayName = 'InvoicePersonalFields'
