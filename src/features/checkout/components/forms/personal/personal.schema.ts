import { z } from 'zod'

// Define constants for validation parameters
const MIN_NAME_LENGTH = 2
const MIN_PHONE_LENGTH = 4

export const personalSchema = z
  .object({
    // Personal information
    firstName: z.string().min(MIN_NAME_LENGTH, 'Името е невалидно').trim(),
    lastName: z.string().min(MIN_NAME_LENGTH, 'Фамилията е невалидна').trim(),
    phone: z.string().min(MIN_PHONE_LENGTH, 'Невалиден телефон').trim(),
    email: z.string().email('Невалиден имейл адрес').trim().toLowerCase(),

    // Registration details
    registerMe: z.boolean(),
    password: z.string().optional(),
    passwordRepeat: z.string().optional(),

    // Invoice information
    invoice: z.boolean(),
    invoiceType: z.enum(['Company', 'Personal']).nullable(),
    invoiceName: z.string().optional(),
    invoiceVatRegistered: z.boolean(),
    invoiceVatNumber: z.string().optional(),
    invoiceAddress: z.string().optional(),
    invoiceMRP: z.string().optional(),
    invoiceCity: z.string().optional(),
    invoiceEIK: z.string().optional(),
  })
  // Password validation
  .refine(
    (data) => {
      if (data.registerMe) {
        return !!data.password // Check if password exists and is not empty
      }
      return true
    },
    {
      message: 'Моля, въведете парола',
      path: ['password'],
    }
  )
  .refine(
    (data) => {
      if (data.registerMe && data.password) {
        return data.password === data.passwordRepeat
      }
      return true
    },
    {
      message: 'Паролите не съвпадат',
      path: ['passwordRepeat'],
    }
  )
  // Invoice validation
  .refine(
    (data) => {
      if (data.invoice) {
        return !!data.invoiceType
      }
      return true
    },
    {
      message: 'Изберете опция за фактура',
      path: ['invoiceType'],
    }
  )
  .refine(
    (data) => {
      if (data.invoice) {
        return !!data.invoiceName
      }
      return true
    },
    {
      message: 'Моля, въведете име за фактура',
      path: ['invoiceName'],
    }
  )
  .refine(
    (data) => {
      if (data.invoice && data.invoiceVatRegistered) {
        return !!data.invoiceVatNumber
      }
      return true
    },
    {
      message: 'Моля, въведете номер на ДДС',
      path: ['invoiceVatNumber'],
    }
  )
  .refine(
    (data) => {
      if (data.invoice) {
        return !!data.invoiceAddress
      }
      return true
    },
    {
      message: 'Моля, въведете адрес за фактура',
      path: ['invoiceAddress'],
    }
  )

export type PersonalSchema = z.infer<typeof personalSchema>
