'use client'

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import React, { useRef } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHand<PERSON>, useForm, useWatch } from 'react-hook-form'

import Text from '@atoms/Text'
import { FormCheckbox } from '@components/molecules/FormControllers/FormCheckbox'
import { FormDropdown } from '@components/molecules/FormControllers/FormDropdown'
import { FormSubmit } from '@components/molecules/FormControllers/FormSubmit'
import { FormTextField } from '@components/molecules/FormControllers/FormTextField'
import { FormProps } from '@components/pages/Checkout/types'
import { CardFooter } from '@components/theme/ui/card'
import { FormContent } from '@features/checkout/components/forms/components/FormContent'
import { FormGrid } from '@features/checkout/components/forms/components/FormGrid'
import SafePortal from '@features/checkout/components/forms/components/SafePortal'
import { InvoiceCompanyFields } from '@features/checkout/components/forms/personal/InvoiceCompanyFields'
import { InvoicePersonalFields } from '@features/checkout/components/forms/personal/InvoicePersonalFields'
import { RegistrationFields } from '@features/checkout/components/forms/personal/RegistrationFields'
import { personalDefaults } from '@features/checkout/components/forms/personal/personal.defaults'
import { PersonalSchema, personalSchema } from '@features/checkout/components/forms/personal/personal.schema'
import { useCustomerStore } from '@features/customer/customer.store'
import { InvoiceType, OrderCustomer } from '@lib/_generated/graphql_sdk'
import { cn } from '@components/lib/utils'

const generatePersonalDataForm = (customer: OrderCustomer): PersonalSchema => ({
  firstName: customer.firstName,
  lastName: customer.lastName,
  phone: customer.phone,
  email: customer.email,
  registerMe: false,
  password: '',
  passwordRepeat: '',
  invoice: !!customer.invoice?.type,
  invoiceType:
    customer.invoice?.type === InvoiceType.Company
      ? 'Company'
      : customer.invoice?.type === InvoiceType.Personal
        ? 'Personal'
        : null,
  invoiceName: customer.invoice?.[customer.invoice?.type === InvoiceType.Company ? 'company' : 'individual']?.name,
  invoiceMRP: customer.invoice?.company?.mol,
  invoiceCity: customer.invoice?.city,
  invoiceEIK: customer.invoice?.company?.eik || customer.invoice?.individual?.egn,
  invoiceVatRegistered:
    !!customer.invoice?.[customer.invoice?.type === InvoiceType.Company ? 'company' : 'individual']?.vat,
  invoiceVatNumber: customer.invoice?.[customer.invoice?.type === InvoiceType.Company ? 'company' : 'individual']?.vat,
  invoiceAddress: customer.invoice?.address,
})

export const PersonalData = ({ data, isExpanded, formStatus, onSubmitAction }: FormProps<PersonalSchema>) => {
  const formRef = useRef<HTMLFormElement>(null)

  const { authenticated, customer } = useCustomerStore()
  const form = useForm<PersonalSchema>({
    resolver: zodResolver(personalSchema),
    mode: 'onBlur',
    defaultValues: data || personalDefaults,
    disabled: formStatus === 'loading',
  })

  const registerMe = useWatch({ control: form.control, name: 'registerMe' })
  const invoice = useWatch({ control: form.control, name: 'invoice' })
  const invoiceType = useWatch({ control: form.control, name: 'invoiceType' })

  const onSubmitHandler: SubmitHandler<PersonalSchema> = (data) => {
    onSubmitAction(data)
  }

  const onErrorHandler = (errors: Record<string, unknown>) => {
    // Handle form errors more gracefully - could integrate with a toast notification system
    // or scroll to the first error field
    const firstErrorKey = Object.keys(errors)[0]
    if (firstErrorKey) {
      // Could implement scrolling to the first error field
      // or showing a toast notification
    }
  }

  const handlePortalButtonClick = () => {
    if (formRef.current) {
      formRef.current.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }))
    }
  }

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler, onErrorHandler)} ref={formRef}>
        <FormContent open={isExpanded}>
          <FormGrid>
            <div className="col-span-2 lg:col-span-1">
              <FormTextField name="firstName" label="Име" />
            </div>
            <div className="col-span-2 lg:col-span-1">
              <FormTextField name="lastName" label="Фамилия" />
            </div>
            <div className="col-span-2 lg:col-span-1">
              <FormTextField name="phone" label="Телефон" />
            </div>
            <div className="col-span-2 lg:col-span-1">
              <FormTextField name="email" label="Имейл" />
            </div>

            {!authenticated && (
              <div className="col-span-2">
                <FormCheckbox name="registerMe" label="Желая да се регистрирам" divider />
              </div>
            )}
            {registerMe && <RegistrationFields />}
            <div className="col-span-2">
              <FormCheckbox name="invoice" label="Искам фактура" divider />
            </div>
            {invoice && (
              <>
                <FormDropdown
                  name="invoiceType"
                  label="Фактура за"
                  options={[
                    { value: 'Personal', label: 'Физическо лице' },
                    { value: 'Company', label: 'Юридическо лице' },
                  ]}
                />
                {invoiceType === 'Company' && <InvoiceCompanyFields />}
                {invoiceType === 'Personal' && <InvoicePersonalFields />}
              </>
            )}
          </FormGrid>
        </FormContent>
        <CardFooter className={cn('lg:hidden justify-center mt-4', !isExpanded && 'hidden')}>
          <FormSubmit loading={formStatus === 'loading'} className="tracking-normal">
            <Text>Продължете към доставка</Text>
          </FormSubmit>
        </CardFooter>
        {isExpanded && (
          <SafePortal containerSelector="#sidebar-button-container">
            <FormSubmit loading={formStatus === 'loading'} onClick={handlePortalButtonClick}>
              <Text className="tracking-normal sm:tracking-widest">Продължете към доставка</Text>
            </FormSubmit>
          </SafePortal>
        )}
      </form>
    </FormProvider>
  )
}

PersonalData.displayName = 'PersonalData'
