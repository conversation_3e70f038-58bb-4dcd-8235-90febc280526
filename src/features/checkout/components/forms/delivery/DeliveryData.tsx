'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { APIProvider } from '@vis.gl/react-google-maps'
import { LucideLoaderCircle } from 'lucide-react'
import React, { useCallback, useEffect, useMemo, useRef } from 'react'
import { FormProvider, useForm, useWatch } from 'react-hook-form'

import Text from '@atoms/Text'
import { Callout } from '@components/molecules/Callout'
import { FormComboBox } from '@components/molecules/FormControllers/FormComboBox'
import { FormDropdown } from '@components/molecules/FormControllers/FormDropdown'
import { FormRadioGroup } from '@components/molecules/FormControllers/FormRadioGroup'
import { FormSubmit } from '@components/molecules/FormControllers/FormSubmit'
import { FormTextField } from '@components/molecules/FormControllers/FormTextField'
import { FormProps } from '@components/pages/Checkout/types'
import { CardFooter } from '@components/theme/ui/card'
import { useCartStore } from '@features/cart/cart-state'
import { useEcontStore } from '@features/checkout/checkout.state'
import { FormContent } from '@features/checkout/components/forms/components/FormContent'
import { FormGrid } from '@features/checkout/components/forms/components/FormGrid'
import SafePortal from '@features/checkout/components/forms/components/SafePortal'
import { deliveryDefaults } from '@features/checkout/components/forms/delivery/delivery.defaults'
import { createDeliverySchema, DeliverySchema } from '@features/checkout/components/forms/delivery/delivery.schema'
import { deliveryTypeEnum } from '@features/checkout/components/forms/deliveryType.enum'
import { EcontCity, EcontOffice } from '@features/checkout/service-providers/econt/types'
import { Price } from '@lib/_generated/graphql_sdk'
import { cn } from '@components/lib/utils'
import { useStaticContentStore } from '@features/static/static.store'
import { formToPayload } from '@components/pages/Checkout/dataMapper'

const OfficeLocator = React.lazy(() => import('@features/checkout/components/OfficeLocator/OfficeLocator'))

let t: ReturnType<typeof setTimeout>

export const DeliveryData = ({
  data,
  isExpanded,
  formStatus,
  onSubmitAction,
  onFetchShippingPricesAction,
}: FormProps<DeliverySchema> & { onFetchShippingPricesAction: (data: DeliverySchema) => void }) => {
  const availableIn = useCartStore((state) => state.shipping.availableIn)
  const availableShippingMethods = useCartStore((state) => state.shipping.availableMethods)
  const clearShippingPrices = useCartStore((state) => state.clearShippingPrices)
  const shippingPrices = useCartStore((state) => state.shippingPrices.data)
  const shippingPricesLoading = useCartStore((state) => state.shippingPrices.loading)
  const shippingError = useCartStore((state) => state.shippingPrices.error)
  const saveShippingMethod = useCartStore((state) => state.saveShippingMethod)

  const validationSchema = React.useMemo(() => {
    return createDeliverySchema(shippingPrices || [])
  }, [shippingPrices])

  const form = useForm<DeliverySchema>({
    resolver: zodResolver(validationSchema),
    mode: 'onBlur',
    defaultValues: data || deliveryDefaults,
    disabled: formStatus === 'loading',
  })

  // Form methods
  const { setValue, handleSubmit, watch, formState, getValues, reset, resetField } = form

  // Touched fields
  const {
    touchedFields: {
      deliveryCity: touchedDeliveryCity,
      deliveryType: touchedDeliveryType,
      deliveryOffice: touchedDeliveryOffice,
      deliveryStore: touchedDeliveryStore,
      deliveryAddress: touchedDeliveryAddress,
      deliveryMethod: touchedDeliveryMethod,
    },
  } = formState

  // Watched fields
  const deliveryType = useWatch({ control: form.control, name: 'deliveryType' })
  const deliveryCity = useWatch({ control: form.control, name: 'deliveryCity' })
  const deliveryOffice = useWatch({ control: form.control, name: 'deliveryOffice' })
  const deliveryStore = useWatch({ control: form.control, name: 'deliveryStore' })
  const deliveryAddress = useWatch({ control: form.control, name: 'deliveryAddress' })
  const deliveryMethod = useWatch({ control: form.control, name: 'deliveryMethod' })

  // Load offices
  const getOffices = useEcontStore((state) => state.getOffices)
  const fetchOffices = useCallback(
    (cityId: number) => {
      getOffices({
        countryCode: 'BGR',
        cityID: cityId,
        servingReceptions: true,
        showCargoReceptions: true,
        showLC: true,
      }).then((response) => {
        if (!response.success) {
          return
        }
      })
    },
    [getOffices]
  )

  const cities = useEcontStore((state) => state.cities)
  const { list: cityList } = cities
  const { list: officesList, loading: officesLoading } = useEcontStore((state) => state.offices)

  const resetOptions = useMemo(
    () => ({
      keepDirty: false,
      keepTouched: false,
      keepError: false,
      defaultValue: null,
    }),
    []
  )

  // Delivery type change clears entire form
  useEffect(() => {
    if (deliveryType && touchedDeliveryType) {
      // console.log('> DeliveryType change:', deliveryType)
      resetField('deliveryStore', resetOptions)
      resetField('deliveryCity', resetOptions)
      resetField('deliveryPostalCode', resetOptions)
      resetField('deliveryOffice', resetOptions)
      resetField('deliveryAddress', resetOptions)
      resetField('deliveryMethod', resetOptions)
      clearShippingPrices()
    }
  }, [clearShippingPrices, deliveryType, resetField, resetOptions, touchedDeliveryType])

  // City change clears office, address, shipping prices and fetches offices
  useEffect(() => {
    if (deliveryCity && touchedDeliveryCity) {
      // console.log('> City change:', deliveryCity)
      clearShippingPrices()
      resetField('deliveryPostalCode', resetOptions)
      resetField('deliveryOffice', resetOptions)
      resetField('deliveryAddress', resetOptions)
      resetField('deliveryMethod', resetOptions)
      fetchOffices(deliveryCity)
    }
  }, [clearShippingPrices, deliveryCity, fetchOffices, resetField, resetOptions, touchedDeliveryCity])

  //
  useEffect(() => {
    if (deliveryOffice && touchedDeliveryOffice) {
      onFetchShippingPricesAction(getValues())
    }
  }, [deliveryOffice, getValues, onFetchShippingPricesAction, touchedDeliveryOffice])

  useEffect(() => {
    if (deliveryStore && touchedDeliveryStore && deliveryType === 'TO_STORE') {
      onFetchShippingPricesAction(getValues())
    }
  }, [deliveryStore, deliveryType, getValues, onFetchShippingPricesAction, touchedDeliveryStore])

  useEffect(() => {
    if (deliveryAddress && touchedDeliveryAddress && deliveryType === 'ECONT_TO_ADDRESS') {
      clearTimeout(t)
      resetField('deliveryMethod', resetOptions)
      clearShippingPrices()
      t = setTimeout(() => {
        onFetchShippingPricesAction(getValues())
      }, 1000)
    }
  }, [
    clearShippingPrices,
    deliveryAddress,
    deliveryType,
    getValues,
    onFetchShippingPricesAction,
    resetField,
    resetOptions,
    touchedDeliveryAddress,
  ])

  useEffect(() => {
    if (deliveryMethod && touchedDeliveryMethod) {
      // console.log('> deliveryMethod change:', deliveryMethod)
      saveShippingMethod(deliveryMethod, formToPayload.delivery({ data: getValues(), cityList, officesList }))
    }
  }, [cityList, deliveryMethod, getValues, officesList, saveShippingMethod, touchedDeliveryMethod])

  const selectedCity = cities.list.find((city) => city.id === deliveryCity)

  const {
    postCode: cityPostCode,
    nameEn: cityNameEn,
    id: cityId,
    country: { nameEn: cityCountryNameEn },
  } = selectedCity || { country: {} }

  const googleApiKey = useStaticContentStore((state) => state.staticContent?.apiKeys.googleMaps)

  const onErrorHandler = () => {}

  // Autofill postal code
  useEffect(() => {
    if (cityPostCode) {
      setValue('deliveryPostalCode', cityPostCode, {
        shouldValidate: true,
      })
    }
  }, [deliveryCity, cityPostCode, setValue])

  const ref = useRef<HTMLFormElement>(null)

  const handlePortalButtonClick = () => {
    if (ref && typeof ref === 'object' && ref.current) {
      const formElement = ref.current as HTMLFormElement
      formElement.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }))
    }
  }

  return (
    <FormProvider {...form}>
      <form onSubmit={handleSubmit(onSubmitAction, onErrorHandler)} ref={ref as React.Ref<HTMLFormElement>}>
        <FormContent open={isExpanded}>
          <FormGrid>
            {/* deliveryType / availableShippingMethods */}
            <div className="col-span-2 lg:col-span-1">
              <FormDropdown
                name="deliveryType"
                label="Опция за доставка"
                options={Array.from(new Set(availableShippingMethods)).map((item) => ({
                  value: item,
                  label: deliveryTypeEnum[item],
                }))}
              />
            </div>
            {/* deliveryStore */}
            <>
              {watch('deliveryType') === 'TO_STORE' && (
                <FormDropdown
                  name="deliveryStore"
                  label="Магазин"
                  options={availableIn.map((item) => ({
                    value: String(item.warehouseCode),
                    label: `[${item.warehouseCode}] ${item.name}`,
                  }))}
                />
              )}
            </>
            {(watch('deliveryType') === 'ECONT_TO_OFFICE' || watch('deliveryType') === 'ECONT_TO_ADDRESS') && (
              <div className="col-span-2 lg:col-span-1" />
            )}
            {/* deliveryOffice */}
            {(watch('deliveryType') === 'ECONT_TO_OFFICE' || watch('deliveryType') === 'ECONT_TO_ADDRESS') && (
              <div className="col-span-2 lg:col-span-1">
                <FormComboBox<EcontCity>
                  name="deliveryCity"
                  label="Град/Село"
                  data={cities.list}
                  minLetters={2}
                  labelProp="name"
                  valueProp="id"
                  placeholder="Изберете град"
                  renderLabel={(item) => `[${item.postCode}] ${item.name}`}
                  filterFn={(item, keyword) => {
                    return `${item.postCode} ${item.name} ${item.nameEn}`.toLowerCase().includes(keyword.toLowerCase())
                  }}
                  loading={cities.loading}
                  loadingLabel="Зареждане на градове..."
                />
              </div>
            )}
            {/* deliveryPostalCode */}
            {(watch('deliveryType') === 'ECONT_TO_OFFICE' || watch('deliveryType') === 'ECONT_TO_ADDRESS') && (
              <div className="col-span-2 lg:col-span-1">
                <FormTextField name="deliveryPostalCode" label="Пощенски код" />
              </div>
            )}
            {/* Office */}
            {watch('deliveryType') === 'ECONT_TO_OFFICE' && (
              <div className="col-span-2 lg:col-span-1">
                <FormComboBox<EcontOffice>
                  key={cityId}
                  showFullList
                  name="deliveryOffice"
                  label="Избор на офис на Еконт"
                  data={officesList}
                  minLetters={2}
                  labelProp="name"
                  valueProp="code"
                  placeholder="Изберете офис"
                  renderLabel={(item) => item.name}
                  filterFn={(item, keyword) => {
                    return `${item.name} ${item.nameEn}`.toLowerCase().includes(keyword.toLowerCase())
                  }}
                  disabled={!deliveryCity}
                  loading={officesLoading}
                  loadingLabel="Зареждане на офиси..."
                />
              </div>
            )}
            {/* Office Locator */}
            {watch('deliveryType') === 'ECONT_TO_OFFICE' && (
              <div className="col-span-1 flex justify-center items-center">
                {deliveryCity && googleApiKey && (
                  <APIProvider apiKey={googleApiKey}>
                    <OfficeLocator
                      onSelect={(office: EcontOffice) => {
                        setValue('deliveryOffice', office.code, { shouldValidate: true })
                      }}
                      cityId={deliveryCity}
                      cityName={`${cityCountryNameEn}, ${cityNameEn}, ${cityPostCode}`}
                    />
                  </APIProvider>
                )}
              </div>
            )}
            {/* Address */}
            {watch('deliveryType') === 'ECONT_TO_ADDRESS' && (
              <div className="col-span-2">
                <FormTextField name="deliveryAddress" label="Адрес" />
              </div>
            )}
            {shippingPricesLoading && (
              <div className="col-span-2">
                <Callout
                  variant="warning"
                  title="Моля, изчакайте..."
                  icon={<LucideLoaderCircle className="animate-spin" />}
                >
                  <Text>Зареждане на начини за доставка...</Text>
                </Callout>
              </div>
            )}
            {shippingError && (
              <div className="col-span-2">
                <Callout variant="error" title="Грешка">
                  {shippingError}
                </Callout>
              </div>
            )}
            {/* Shipping prices */}
            {shippingPrices && shippingPrices.length > 0 && (
              <div className="col-span-2">
                <FormRadioGroup
                  label="Начин на доставка"
                  validateOnChange
                  name="deliveryMethod"
                  options={shippingPrices?.map((item) => ({
                    value: item.method.code,
                    label: item.method.name,
                    description: formatShippingPrice(item.price),
                  }))}
                  className="flef flex-col md:grid md:grid-cols-3 md:gap-y-10 md:gap-x-8"
                  optionClassName="items-start border border-[#D9D9D9] rounded-2xl p-3 gap-3 rounded-xl"
                  optionSelectedClassName="border-[#F1B201] bg-[#F1B201] hover:border-[#F1B201] text-white"
                  radioItemClassName="border-gray-400 w-4 h-4"
                  radioItemSelectedClassName="border-white bg-white"
                  indicatorClassName="bg-[#F1B201] border-none w-2 h-2"
                  labelClassName="gap-2"
                  descriptionSelectedClassName="text-white"
                />
              </div>
            )}
          </FormGrid>
        </FormContent>
        <CardFooter className={cn('lg:hidden justify-center mt-4', !isExpanded && 'hidden')}>
          <FormSubmit loading={formStatus === 'loading'} className="tracking-normal">
            <Text>Продължете към плащане</Text>
          </FormSubmit>
        </CardFooter>
        {isExpanded && (
          <SafePortal containerSelector="#sidebar-button-container">
            <FormSubmit loading={formStatus === 'loading'} onClick={handlePortalButtonClick}>
              <Text className="tracking-normal sm:tracking-widest">Продължете към плащане</Text>
            </FormSubmit>
          </SafePortal>
        )}
      </form>
    </FormProvider>
  )
}

DeliveryData.displayName = 'DeliveryData'

function formatShippingPrice(price: Price) {
  if (price.value == 0) {
    return 'Безплатна доставка'
  }
  return `${price.value} ${price.currency}`
}
