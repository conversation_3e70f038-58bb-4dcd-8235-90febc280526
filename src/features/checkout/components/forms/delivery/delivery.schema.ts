import { z } from 'zod'

import { ShippingMethodType } from '@lib/_generated/graphql_sdk'

export const createDeliverySchema = (shippingMethods: any[] = []) => {
  return z
    .object({
      deliveryType: z.nativeEnum(ShippingMethodType).nullable().default(null),
      deliveryStore: z.string().nullable().default(null),
      deliveryCity: z.number().nullable().default(null),
      deliveryPostalCode: z.string().nullable().default(''),
      deliveryOffice: z.string().nullable().default(null),
      deliveryAddress: z.string().nullable().default(''),
      deliveryMethod: z.string().nullable().default(null),
    })
    .superRefine((data, ctx) => {
      // Validate delivery type
      if (!data.deliveryType) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Изберете начин на доставка',
          path: ['deliveryType'],
        })
      }

      // Validate store for store delivery
      if (data.deliveryType === 'TO_STORE') {
        if (!data.deliveryStore) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Изберете магазин за доставка',
            path: ['deliveryStore'],
          })
        }
      }

      // Validate city for delivery types that need it
      if (data.deliveryType === 'ECONT_TO_ADDRESS' || data.deliveryType === 'ECONT_TO_OFFICE') {
        if (!data.deliveryCity) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Изберете град за доставка',
            path: ['deliveryCity'],
          })
        }

        // Validate postal code format for ECONT deliveries
        if (!data.deliveryPostalCode || (!!data.deliveryCity && !/^\d{4,6}$/.test(data.deliveryPostalCode))) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Невалиден пощенски код',
            path: ['deliveryPostalCode'],
          })
        }
      }

      // Validate office for office delivery
      if (data.deliveryType === 'ECONT_TO_OFFICE' && !data.deliveryOffice) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Изберете офис за доставка',
          path: ['deliveryOffice'],
        })
      }

      // Validate address for address delivery
      if (data.deliveryType === 'ECONT_TO_ADDRESS' && !data.deliveryAddress) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Изберете адрес за доставка',
          path: ['deliveryAddress'],
        })
      }

      // Validate delivery method when shipping methods are available
      if (shippingMethods.length > 0 && !data.deliveryMethod) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Изберете предпочитан начин за доставка',
          path: ['deliveryMethod'],
        })
      }
    })
}

// Default schema with no shipping methods required
export const deliverySchema = createDeliverySchema([])

export type DeliverySchema = z.infer<ReturnType<typeof createDeliverySchema>>
