import { AdvancedMarkerAnchorPoint, InfoWindow, Map, Pin } from '@vis.gl/react-google-maps'
import { LucideLoaderCircle } from 'lucide-react'
import React, { useCallback, useEffect, useMemo, useState } from 'react'

import useGeocoder from '@/src/hooks/useGeocoder'
import Text from '@atoms/Text'
import { Modal } from '@components/organisms/Modal/Modal'
import { Button } from '@components/theme/ui/button'
import { mappers, useEcontStore } from '@features/checkout/checkout.state'
import { InfoWindowContents } from '@features/checkout/components/OfficeLocator/InfoWindowContents'
import { EcontOffice, OfficeResponse } from '@features/checkout/service-providers/econt/types'
import '@/src/app/(store)/contacts/google-map.css'
import { showErrorToast } from '@lib/utils/toaster'

import { AdvancedMarkerWithRef } from './AdvancedMarkerWithRef'

interface OfficeLocatorProps {
  onSelect: (office: EcontOffice) => void
  cityName: string
  cityId: number
}

export const OfficeLocator: React.FC<OfficeLocatorProps> = ({ onSelect, cityName, cityId }) => {
  const [open, setOpen] = useState(false)
  const [data, setData] = useState<EcontOffice[]>([])
  const [dataLoading, setDataLoading] = useState(false)
  const [geoLoading, setGeoLoading] = useState(false)
  const [defaultCenter, setDefaultCenter] = useState({ lat: 42.6584586313325, lng: 25.14654407575573 })
  const getOffices = useEcontStore((state) => state.getOffices)

  const Z_INDEX_SELECTED = data.length
  const Z_INDEX_HOVER = data.length + 1

  const { isLoaded: geocoderLoaded, getCoordinates } = useGeocoder()

  useEffect(() => {
    if (geocoderLoaded) {
      setGeoLoading(true)
      getCoordinates(cityName)
        .then((response) => {
          setDefaultCenter({
            lat: response.lat,
            lng: response.lng,
          })
        })
        .catch(() => {
          showErrorToast({ description: 'Грешка при зареждане на координати' })
        })
        .finally(() => {
          setGeoLoading(false)
        })
    }
  }, [cityName, getCoordinates, geocoderLoaded])

  useEffect(() => {
    if (open && data.length === 0) {
      setDataLoading(true)
      getOffices({
        showCargoReceptions: true,
        showLC: true,
        servingReceptions: true,
        cityID: cityId,
        countryCode: 'BGR',
      })
        .then(({ data }) => {
          console.log({ data })
          if (data && data.length > 0) {
            setData(
              data
                .filter((office) => {
                  return office.address.location.latitude
                })
                .sort((a, b) => b.address.location.latitude - a.address.location.latitude)
                .map((dataItem, index) => ({
                  ...dataItem,
                  zIndex: index,
                  position: {
                    lat: dataItem.address.location.latitude,
                    lng: dataItem.address.location.longitude,
                  },
                }))
            )
          } else {
            showErrorToast({ description: 'Няма офиси в тази градска област' })
          }
        })
        .catch((e) => {
          console.error(e)
          showErrorToast({ description: 'Грешка при зареждане на офисите (2)' })
        })
        .finally(() => {
          setDataLoading(false)
        })
    }
  }, [cityId, data.length, getOffices, open])

  const [hoverId, setHoverId] = useState<number | null>(null)
  const [selectedId, setSelectedId] = useState<number | null>(null)

  const onMouseEnter = useCallback((id: number | null) => setHoverId(id), [])
  const onMouseLeave = useCallback(() => setHoverId(null), [])

  const infoWindowShown = selectedId !== null

  const selectedOffice = useMemo(() => {
    if (selectedId) {
      return data.find((office) => office.id === selectedId)
    }
    return null
  }, [data, selectedId])

  return (
    <>
      <Button variant="link" type="button" onClick={() => setOpen(true)}>
        <Text>Офис локатор</Text>
      </Button>
      <Modal
        controlled={{
          open,
          onClose: () => {
            setOpen(false)
          },
        }}
        title={dataLoading ? <Text>Зареждане на списък с офиси.</Text> : <Text>Списък с офиси</Text>}
        description={dataLoading ? <Text>Моля, изчакайте...</Text> : <Text>Изберете офис</Text>}
        closeText="Затвори"
        className="xl:max-w-screen-lg"
      >
        <div style={{ width: '100%', height: '400px' }}>
          {dataLoading && (
            <div className="flex flex-1 justify-center items-center py-10">
              <LucideLoaderCircle size={26} className="animate-spin" />
            </div>
          )}
          {!dataLoading && (
            <Map
              mapId="OFFICE_LOCATOR"
              defaultZoom={13}
              defaultCenter={defaultCenter}
              gestureHandling={'greedy'}
              onClick={() => setSelectedId(null)}
              clickableIcons={false}
            >
              {data.map((office) => {
                const {
                  id,
                  address: { location },
                } = office
                let zIndex = id

                if (hoverId === id) {
                  zIndex = Z_INDEX_HOVER
                }

                if (selectedId === id) {
                  zIndex = Z_INDEX_SELECTED
                }

                return (
                  <AdvancedMarkerWithRef
                    onMarkerClick={() => setSelectedId(id)}
                    onMouseEnter={() => onMouseEnter(id)}
                    onMouseLeave={onMouseLeave}
                    key={id}
                    zIndex={zIndex}
                    className="custom-marker"
                    style={{
                      transform: `scale(${[hoverId, selectedId].includes(id) ? 1.3 : 1})`,
                      transformOrigin: AdvancedMarkerAnchorPoint['BOTTOM'].join(' '),
                    }}
                    position={{
                      lat: location.latitude,
                      lng: location.longitude,
                    }}
                  >
                    <Pin
                      background={selectedId === id ? '#ee7f00' : null}
                      borderColor={selectedId === id ? '#b05f00' : null}
                      glyphColor={selectedId === id ? '#653400' : null}
                    />
                  </AdvancedMarkerWithRef>
                )
              })}
              {infoWindowShown && selectedOffice && (
                <InfoWindow
                  position={{
                    lat: selectedOffice.address.location.latitude,
                    lng: selectedOffice.address.location.longitude,
                  }}
                  pixelOffset={[0, -2]}
                  onCloseClick={() => setSelectedId(null)}
                >
                  <InfoWindowContents
                    selectedOffice={selectedOffice}
                    onSelect={() => {
                      onSelect(selectedOffice)
                      setSelectedId(null)
                      setOpen(false)
                    }}
                  />
                </InfoWindow>
              )}
            </Map>
          )}
        </div>
      </Modal>
    </>
  )
}

export default OfficeLocator
