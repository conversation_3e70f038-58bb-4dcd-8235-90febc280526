import React from 'react'

import Text from '@atoms/Text'
import { Button } from '@components/theme/ui/button'
import { EcontOffice } from '@features/checkout/service-providers/econt/types'

interface InfoWindowContentsProps {
  selectedOffice: EcontOffice
  onSelect: () => void
}

export const InfoWindowContents: React.FC<InfoWindowContentsProps> = ({ selectedOffice, onSelect }) => {
  return (
    <div className="flex flex-col">
      {selectedOffice && (
        <div className="p-3 pt-0 flex flex-col gap-2">
          <div className="flex flex-col">
            <Text className="font-bold">Град/Село:</Text>
            <div>{selectedOffice.name}</div>
          </div>
          <div className="flex flex-col">
            <Text className="font-bold">Адрес:</Text>
            <div>{selectedOffice.address.fullAddress}</div>
          </div>
          <div className="flex flex-col">
            <Text className="font-bold">Телефон за контакт:</Text>
            <div>{selectedOffice.phones.join(', ')}</div>
          </div>
        </div>
      )}
      <Button onClick={onSelect}>Избери</Button>
    </div>
  )
}
