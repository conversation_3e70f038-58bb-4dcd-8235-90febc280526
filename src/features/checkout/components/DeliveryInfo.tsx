import React, { PropsWithChildren } from 'react'

import { cn } from '@components/lib/utils'
import Truck from '@icons/minimalistic/static/truck.inline.svg'

export const MiniCheckoutDeliveryInfo: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <div className={cn('p-4 bg-background rounded-xl my-3 flex items-center')}>
      <div className="flex items-center sm:items-start justify-center pr-4">
        <Truck className="h-10 w-fit" />
      </div>
      <div className="flex flex-col flex-1">{children}</div>
    </div>
  )
}
