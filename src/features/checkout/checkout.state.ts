import { create } from 'zustand'

import {
  CityRequest,
  CityResponse,
  EcontCity,
  EcontOffice,
  EcontResponse,
  OfficeRequest,
  OfficeResponse,
} from '@features/checkout/service-providers/econt/types'
import { GraphQLBackend } from '@lib/api/graphql'
import { map } from 'zod'

interface EcontState {
  cities: {
    list: EcontCity[]
    loading: boolean
  }
  offices: {
    list: EcontOffice[]
    loading: boolean
  }
  initCart: () => Promise<void>
  getCities: (request: CityRequest) => Promise<EcontResponse<EcontCity[]>>
  getOffices: (request: OfficeRequest) => Promise<EcontResponse<EcontOffice[]>>
}

export const mappers = {
  mapCity: (data: any): EcontCity => ({
    id: data.id,
    country: data.country,
    postCode: data.postCode,
    name: data.name,
    nameEn: data.nameEn,
    regionName: data.regionName,
    regionNameEn: data.regionNameEn,
    phoneCode: data.phoneCode,
  }),
  mapOffice: (data: any): EcontOffice => {
    return {
      id: data.id,
      code: data.code,
      name: data.name,
      nameEn: data.nameEn,
      phones: data.phones,
      isAPS: data.isAPS,
      isMPS: data.isMPS,
      address: {
        city: {
          country: data.adderess.city.country,
          postCode: data.adderess.city.postCode,
        },
        fullAddress: data.adderess.fullAddress,
        fullAddressEn: data.adderess.fullAddressEn,
        location: {
          latitude: data.adderess.location.latitude,
          longitude: data.adderess.location.longitude,
        },
      },
    }
  },
}

export const useEcontStore = create<EcontState>((set, get) => ({
  cities: {
    list: [],
    loading: false,
    error: null,
  },
  offices: {
    list: [],
    loading: false,
    error: null,
  },
  initCart: async () => {},
  getCities: async (request: CityRequest) => {
    set({
      cities: {
        list: [],
        loading: true,
      },
    })
    const response = await GraphQLBackend.GetEcontCities({ country: String(request.countryCode) })
    if (response.getEcontCity) {
      const mappedCities = response.getEcontCity.map(mappers.mapCity)
      set({
        cities: {
          list: mappedCities,
          loading: false,
        },
      })
      return {
        success: true,
        data: mappedCities,
      }
    } else {
      set({
        cities: {
          list: [],
          loading: false,
        },
      })
    }
    return {
      success: false,
      data: [],
    }
  },
  getOffices: async (request: OfficeRequest) => {
    const response = await GraphQLBackend.GetEcontOffices({ cityId: String(request.cityID) })
    set({
      offices: {
        list: [],
        loading: true,
      },
    })

    if (response.getEcontOffice) {
      // Exclude econtomats
      const mappedOffices = response.getEcontOffice.filter((o) => !o?.isAPS).map(mappers.mapOffice)
      set({
        offices: {
          list: mappedOffices,
          loading: false,
        },
      })
      return {
        success: true,
        error: null,
        data: mappedOffices,
      }
    } else {
      console.log(response)
      set({
        offices: {
          list: [],
          loading: false,
        },
      })
    }
    return {
      success: false,
      data: [],
    }
  },
}))
