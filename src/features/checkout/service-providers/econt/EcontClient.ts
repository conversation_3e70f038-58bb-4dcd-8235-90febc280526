import {
  EcontCity,
  CityRequest,
  EcontConfig,
  EcontResponse,
  LabelData,
  Office,
  OfficeRequest,
  PriceCalculationRequest,
  ShipmentTrackingRequest,
} from './types'

class EcontClient {
  private static instance: EcontClient
  private readonly username: string
  private readonly password: string
  private readonly baseUrl: string
  random = Math.random()

  cityList: EcontCity[] = []

  constructor({ username, password, environment = 'test' }: EcontConfig) {
    this.username = username
    this.password = password
    this.baseUrl = environment === 'prod' ? 'https://ee.econt.com/services' : 'https://demo.econt.com/ee/services'
  }

  static getInstance(config?: EcontConfig): EcontClient {
    if (!EcontClient.instance) {
      if (!config) {
        throw new Error('EcontClient must be initialized with credentials first!')
      }
      EcontClient.instance = new EcontClient(config)
    }
    return EcontClient.instance
  }

  private async request<T>(endpoint: string, method: 'POST' = 'POST', data?: object): Promise<EcontResponse<T>> {
    const url = `${this.baseUrl}/${endpoint}.json`

    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Basic ' + btoa(`${this.username}:${this.password}`),
        },
        body: JSON.stringify(data),
      })

      const responseData = await response.json()

      if (!response.ok) {
        return {
          success: false,
          data: null as unknown as T,
        }
      }

      return {
        success: true,
        data: responseData,
      }
    } catch (error) {
      return {
        success: false,
        data: null as unknown as T,
      }
    }
  }

  async createLabel(labelData: LabelData): Promise<EcontResponse<any>> {
    return this.request<any>('Shipments/LabelService.createLabel', 'POST', {
      mode: 'create',
      label: labelData,
    })
  }

  async trackShipment(request: ShipmentTrackingRequest): Promise<EcontResponse<any>> {
    return this.request<any>('Shipments/TrackService.getStatus', 'POST', request)
  }

  async calculatePrice(request: PriceCalculationRequest): Promise<EcontResponse<any>> {
    return this.request<any>('Shipments/PriceService.calculate', 'POST', request)
  }

  async getOffices(request?: OfficeRequest): Promise<EcontResponse<Office[]>> {
    return this.request<Office[]>('Shipments/OfficeService.getOffices', 'POST', request)
  }

  async getCities(request: CityRequest): Promise<EcontResponse<EcontCity[]>> {
    const response = await this.request<EcontCity[]>('Nomenclatures/NomenclaturesService.getCities', 'POST', request)
    this.cityList = response.data
    return response
  }
}

export { EcontClient }
