import { z } from 'zod'

export type Environment = 'test' | 'prod'

export interface EcontConfig {
  username: string
  password: string
  environment?: Environment
}

export const econtCountrySchema = z.object({
  id: z.nullable(z.number()),
  code2: z.string(),
  code3: z.string(),
  name: z.string(),
  nameEn: z.string(),
  isEU: z.boolean(),
})

export type EcontCountry = z.infer<typeof econtCountrySchema>

export const econtOfficeSchema = z.object({
  id: z.number(),
  code: z.string(),
  name: z.string(),
  nameEn: z.string(),
  phones: z.array(z.string()),
  isAPS: z.boolean(),
  isMPS: z.boolean(),
  address: z.object({
    city: z.object({
      country: econtCountrySchema,
      postCode: z.string(),
    }),
    fullAddress: z.string(),
    fullAddressEn: z.string(),
    location: z.object({
      latitude: z.number(),
      longitude: z.number(),
    }),
  }),
})

export type EcontOffice = z.infer<typeof econtOfficeSchema>

export const econtCitySchema = z.object({
  id: z.number(),
  country: econtCountrySchema,
  postCode: z.string(),
  name: z.string(),
  nameEn: z.string(),
  regionName: z.string(),
  regionNameEn: z.string(),
  phoneCode: z.string(),
})
export type EcontCity = z.infer<typeof econtCitySchema>

export interface Address {
  city: EcontCity
  street: string
  num: string
}

export interface Client {
  name: string
  phones: string[]
}

export interface LabelData {
  senderClient: Client
  senderAddress: Address
  receiverClient: Client
  receiverAddress: Address
  packCount: number
  shipmentType: 'pack' | 'document' | 'pallet'
  weight: number
  shipmentDescription?: string
}

export interface ShipmentTrackingRequest {
  shipmentNumbers: string[]
}

export interface PriceCalculationRequest {
  shipmentType: 'pack' | 'document' | 'pallet'
  senderCity: EcontCity
  receiverCity: EcontCity
  weight: number
  packCount?: number
  declaredValue?: number
  codAmount?: number
}

export interface EcontResponse<T> {
  success: boolean
  data: T
}

export interface OfficeRequest {
  countryCode: string
  cityID: number
  showCargoReceptions: boolean
  showLC: boolean
  servingReceptions: boolean
}

export interface OfficeResponse {
  offices: EcontOffice[]
}

export interface Office {
  code: string
  name: string
  address: {
    city: string
    street: string
    num: string
  }
  phones: string[]
  gps: {
    lat: number
    lng: number
  }
}

export interface CityRequest {
  countryCode: string
}

export interface CityResponse {
  cities: EcontCity[]
}
