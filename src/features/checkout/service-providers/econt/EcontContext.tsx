import React, { createContext, useContext, useState } from 'react'

import econt from '@features/checkout/service-providers/econt/econt-client'
import { EcontCity } from '@features/checkout/service-providers/econt/types'

type FormType = 'personal' | 'delivery' | 'payment'

interface EcontContextType {
  cityList: EcontCity[]
}

const EcontContext = createContext<EcontContextType>({
  cityList: [],
})

interface EcontProviderProps {
  children: React.ReactNode
}

export const EcontProvider: React.FC<EcontProviderProps> = ({ children }) => {
  const [cityList, setCityList] = useState<EcontCity[]>([])

  const getCities = async () => {
    try {
      const response = await econt.getCities({ countryCode: 'BGR' })
      setCityList(response.data)
    } catch (err) {
      console.error((err as Error).message)
    }
  }

  return <EcontContext.Provider value={{ cityList }}>{children}</EcontContext.Provider>
}

// Custom Hook
const useEcontContext = () => useContext(EcontContext)

export default useEcontContext
