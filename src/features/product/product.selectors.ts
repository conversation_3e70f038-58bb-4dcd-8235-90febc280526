'use client'

import { useMemo } from 'react'

import { useProductStore } from '@features/product/product.store'

export const useProductPrice = () => {
  const { price, promoPrice, multiplier, secondaryQty } = useProductStore()

  const secondaryUnitPrice = useMemo(() => {
    return Math.round((multiplier * (promoPrice || price) + Number.EPSILON) * 100) / 100
  }, [price, promoPrice, multiplier])

  const totalAmount = useMemo(() => {
    return secondaryUnitPrice * secondaryQty
  }, [secondaryUnitPrice, secondaryQty])

  const savedAmount = useMemo(() => {
    return multiplier * price * secondaryQty - totalAmount
  }, [multiplier, price, secondaryQty, totalAmount])

  return {
    secondaryUnitPrice,
    primaryUnitPrice: promoPrice || price,
    totalAmount,
    savedAmount,
  }
}
