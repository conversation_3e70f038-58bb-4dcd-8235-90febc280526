import { BundleProductViewFragment, SimpleProductViewFragment, ProductViewFragment } from '@lib/_generated/graphql_sdk'

export function isSimpleProduct(product?: ProductViewFragment): product is Omit<
  SimpleProductViewFragment,
  '__typename'
> & {
  __typename: 'SimpleProduct'
} {
  return product?.__typename === 'SimpleProduct'
}

export function isBundleProduct(product?: ProductViewFragment): product is Omit<
  BundleProductViewFragment,
  '__typename'
> & {
  __typename: 'BundleProduct'
} {
  return product?.__typename === 'BundleProduct'
}

export function getProductUrl(product: { urlKey?: string | null | undefined }): string {
  if (!product.urlKey) {
    console.error('Product has no urlKey', product)
    return '/'
  }

  return `/${product?.urlKey ?? ''}`
}
