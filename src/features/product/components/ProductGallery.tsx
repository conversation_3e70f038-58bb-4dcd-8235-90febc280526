'use client'

import React, { useCallback } from 'react'

import useElementVisibility from '@/src/hooks/useElementVisibility'
import { ButtonIcon } from '@components/molecules/ButtonIcon'
import { ProductPromoCard } from '@components/molecules/ProductPromoCard'
import { ProductPhotoGallery } from '@components/organisms/ProductPhotoGallery'
import ArrowLeft from '@icons/arrow-left.inline.svg'
import ArrowRight from '@icons/arrow-right.inline.svg'
import { SimpleProductViewFragment, Product, ProductMeasures } from '@lib/_generated/graphql_sdk'
import { cn } from '@components/lib/utils'
import useBreakpoint from '@/src/hooks/useBreakpoint'
import productCard from '@components/molecules/ProductCard/ProductCard'

interface ProductGalleryProps {
  product: SimpleProductViewFragment
}

export const ProductGallery: React.FC<ProductGalleryProps> = ({ product }) => {
  const [isOutOfView, setIsOutOfView] = React.useState(false)

  const { breakpoint } = useBreakpoint()
  const handleVisibilityChange = useCallback(
    (isOutOfView: boolean): void => {
      if (breakpoint && ['lg', 'xl', '2xl', '3xl', '4xl', '5xl', '6xl'].includes(breakpoint)) {
        setIsOutOfView(isOutOfView)
      }
    },
    [breakpoint]
  )

  // Hook usage with TypeScript
  useElementVisibility('product-buy-card', handleVisibilityChange)

  return (
    <div className="sticky top-[140px] w-full">
      <ProductPhotoGallery
        image={product.image}
        images={product.gallery}
        videos={product.videos}
        // images={[...data.product.gallery, ...additionalImages]}
        galleryID="custom-gallery"
        thumbnailSize={120}
        showThumbnails={!isOutOfView}
        imageWidth={1000}
        imageHeight={1000}
        mainImageContainerClass={cn('w-full flex justify-center mx-auto rounded-b-3xl xl:rounded-3xl bg-white')}
        thumbnailsContainerClass="hidden md:flex grid grid-cols-3 gap-2 mt-2 "
        thumbnailClass="hover:border-primary border-white border-4"
        cardContainerClass={cn('border-none shadow-none rounded-none rounded-b-3xl xl:rounded-3xl xl:shadow-sm')}
        prevButton={
          <ButtonIcon
            variant="ghost"
            icon={<ArrowLeft />}
            className="z-10 h-14 w-14"
            iconClassName="text-primary h-14 w-14 p-5 bg-white shadow rounded-full"
          />
        }
        nextButton={
          <ButtonIcon
            variant="ghost"
            icon={<ArrowRight />}
            className="z-10 h-14 w-14"
            iconClassName="text-primary h-14 w-14 p-5 bg-white shadow rounded-full"
          />
        }
      />
      {isOutOfView && <ProductPromoCard product={product} />}
    </div>
  )
}
