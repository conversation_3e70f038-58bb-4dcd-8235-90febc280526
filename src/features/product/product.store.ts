import { create } from 'zustand'

export interface ProductState {
  ready: boolean
  price: number
  promoPrice?: number
  currency: string
  multiplier: number
  primaryQty: number
  primaryUnit: string
  secondaryQty: number
  secondaryUnit: string
  initProduct: (product: Partial<ProductState>) => void
  setPrimaryQty: (qty: number) => void
  setSecondaryQty: (qty: number) => void
  reset: () => void
}

const initialState = {
  ready: false,
  price: 0,
  promoPrice: 0,
  currency: '',
  multiplier: 0,
  primaryQty: 0,
  primaryUnit: '',
  secondaryQty: 0,
  secondaryUnit: '',
}
export const useProductStore = create<ProductState>((set) => ({
  ...initialState,
  initProduct: (product) => set(() => ({ ...product, ready: true })),
  setPrimaryQty: (qty) => set((state) => ({ ...state, primaryQty: qty })),
  setSecondaryQty: (qty) => set((state) => ({ ...state, secondaryQty: qty })),
  reset: () => set(() => initialState),
}))
