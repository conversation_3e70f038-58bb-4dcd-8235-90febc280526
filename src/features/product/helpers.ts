import { ProductState } from '@features/product/product.store'
import { SimpleProductViewFragment, GalleryImage, Image, Product, SimpleProduct } from '@lib/_generated/graphql_sdk'

export const productToState = (product: SimpleProductViewFragment): Partial<ProductState> => {
  return {
    ready: true,
    price: product.price.price.value,
    promoPrice: product.price.special?.value,
    currency: product.price.price.currency,
    multiplier: product.measures.secondaryQty,
    primaryQty: 1,
    primaryUnit: product.measures.base,
    secondaryQty: 1,
    secondaryUnit: product.measures.secondary,
  }
}

export const getProductMainImage = (productImage: Product['image'], gallery?: GalleryImage[]): Image => {
  const placeholder: Image = {
    src: '/images/no-image-placeholder.png',
    mobileSrc: '/images/no-image-placeholder.png',
    alt: 'No image',
    title: 'No image',
  }
  if (productImage) {
    return productImage
  }

  if (!gallery || gallery.length === 0) {
    return placeholder
  }
  const sortedGallery = [...gallery].sort((a, b) => a.position - b.position)
  return sortedGallery[0]?.image || placeholder
}
