import {
  AppEnergyLabelFragment,
  AppLabelsFragment,
  AppProductCardFragment,
  Price,
  Product,
  ProductPrice,
  SimpleProduct,
} from '@lib/_generated/graphql_sdk'

export type PraktisProduct = {
  id: string
  name: string
  sku: string
  urlKey: string
  image: {
    src: string
    alt?: string | null
  }
  price: ProductPrice
  labels: AppLabelsFragment
  energyLabel: {
    image: {
      src: string
    }
    labelUrl?: string | null
    infoUrl?: string | null
  }
}

export function formatPrice(price: Price | number): string {
  let symbol = 'лв.'
  if (typeof price === 'object' && price.currency != 'BGN') {
    symbol = price.currency
  }

  let _price: number
  if (typeof price === 'number') {
    _price = price
  } else {
    _price = price?.value || 0
  }
  return `${_price.toFixed(2)} ${symbol}`
}

function hasEnergyLabel(product?: AppProductCardFragment | Product): product is SimpleProduct {
  return !!product && (product as SimpleProduct).energyLabel !== undefined
}

export function toProductModel(product?: AppProductCardFragment | Product): PraktisProduct {
  // TODO: Implement
  const image = product?.image ?? { src: '', alt: '' }
  if (!image.alt) {
    image.alt = product?.name ?? 'product'
  }

  let energyLabel: AppEnergyLabelFragment = {
    infoUrl: '',
    labelUrl: '',
    image: { src: '' },
  }

  if (hasEnergyLabel(product) && product.energyLabel) {
    energyLabel = product.energyLabel
  }

  return {
    id: product?.id ?? '0',
    name: product?.name ?? '',
    sku: product?.sku ?? '',
    urlKey: product?.urlKey ?? '',
    image: image,
    price: product?.price ?? {
      price: { value: 0, currency: 'BGN' },
      special: null,
    },
    labels: product?.labels ?? {
      warrantyMonths: 0,
      freeDelivery: false,
      fromBrochure: false,
      other: [],
    },
    energyLabel: energyLabel,
  }
}

export function calcDiscountPercent(price: number, special: number): number {
  return Math.abs(Math.round(((price - special) / price) * 100))
}

export function getPriceValue(product: PraktisProduct): number {
  return product.price?.price?.value
}

export function getSpecialPriceValue(product: PraktisProduct): number | undefined {
  return product.price?.special?.value ?? undefined
}

export interface ProductCardLabel {
  text: string
  color?: string
}

export function getProductLabels(product: PraktisProduct): ProductCardLabel[] {
  const labels: ProductCardLabel[] = []
  const specialPrice = getSpecialPriceValue(product)
  if (specialPrice) {
    const discountPercent = calcDiscountPercent(getPriceValue(product), specialPrice)
    labels.push({
      text: `Отстъпка - ${discountPercent}%`,
      color: 'bg-primary',
    })
  }

  if (product.labels?.fromBrochure) {
    labels.push({
      text: 'Продукт от брошурата',
      color: 'bg-background-dark',
    })
  }

  if (product.labels?.freeDelivery) {
    labels.push({
      text: 'Безплатна доставка',
      color: 'bg-background-dark',
    })
  }

  if (product.labels?.other) {
    for (const label of product.labels?.other) {
      if (!label) {
        continue
      }

      labels.push({
        text: label,
        color: 'bg-background-dark',
      })
    }
  }

  return labels
}
