import React from 'react'
import Static from './index'
import { StaticContentProps } from '@features/static/types'
import { getStaticContent } from '@features/static/api'
import { StaticContent } from '@lib/_generated/graphql_sdk'

export const withStaticData = <P extends {}>(
  Component: React.ComponentType<P & StaticContentProps>
): React.FC<Omit<P, keyof StaticContentProps>> => {
  return async function WrappedComponent(props: Omit<P, keyof StaticContentProps>) {
    let staticContent: StaticContent | null = null
    try {
      staticContent = await getStaticContent()
    } catch {}

    return <Component staticContent={staticContent} {...(props as P)} />
  }
}
