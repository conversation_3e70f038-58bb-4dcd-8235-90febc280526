import { CatalogPageDataFragment, CmsPageDataFragment, ProductPageDataFragment } from '@lib/_generated/graphql_sdk'

export function isCatalogPage(
  data?: {
    __typename?: string
  } | null
): data is CatalogPageDataFragment {
  return data?.__typename === 'CatalogPage'
}

export function isProductPage(
  data?: {
    __typename?: string
  } | null
): data is ProductPageDataFragment {
  return data?.__typename === 'ProductPage'
}

export function isCMSPage(
  data?: {
    __typename?: string
  } | null
): data is CmsPageDataFragment {
  return data?.__typename === 'CMSPage'
}
