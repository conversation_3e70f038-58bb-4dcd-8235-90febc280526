'use client'

import { StaticContentProps } from '@features/static/types'
import { useEffect } from 'react'
import { useStaticContentStore } from '@features/static/static.store'

export function StaticStoreInit({ staticContent }: StaticContentProps) {
  const { setStaticContent } = useStaticContentStore()

  useEffect(() => {
    if (staticContent) {
      setStaticContent(staticContent)
    }
  }, [staticContent, setStaticContent])

  return null
}
