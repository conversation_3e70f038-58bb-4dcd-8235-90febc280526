import { cache } from 'react'

import { StaticContent } from '@lib/_generated/graphql_sdk'
import { GraphQLBackend } from '@lib/api/graphql'
import { Default_StaticContent } from '@lib/data/layout'

// Next.js request scope cache
export const getStaticContent = cache(async (): Promise<StaticContent> => {
  try {
    const data = await GraphQLBackend.GetStoreStaticData()
    if (data.getStaticContent) {
      return {
        ...data.getStaticContent,
      } as StaticContent
    }
  } catch (e) {
    console.error('Failed to fetch static content:', e)
  }

  // Fallback to default content
  return Default_StaticContent
})
