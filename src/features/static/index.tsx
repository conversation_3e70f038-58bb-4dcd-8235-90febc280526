import React, { ComponentType, ReactElement } from 'react'

import { getStaticContent } from '@/src/features/static/api'
import { StaticContentProps } from '@/src/features/static/types'

export async function Static<T extends object>({
  component: Component,
  ...rest
}: {
  component: ComponentType<T & StaticContentProps> // Generic for additional props + staticContent
} & Omit<T, keyof StaticContentProps>): Promise<ReactElement> {
  const staticContent = await getStaticContent()

  return <Component staticContent={staticContent} {...(rest as T)} />
}

export default Static
