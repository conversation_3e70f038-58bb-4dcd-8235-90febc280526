'use client'

import { PropsWithChildren, useCallback, useEffect, useState } from 'react'

import { cn } from '@components/lib/utils'
import { ButtonArrow } from '@components/molecules/Buttons/ButtonArrow'
import { Card, CardContent, CardHeader } from '@components/theme/ui/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@components/theme/ui/collapsible'
import { saveCookie } from '@lib/utils/cookie'

interface FilterCardProps {
  id: string
  title?: string
  defaultOpenState: boolean
}

export const FilterCard = ({ id, title, children, defaultOpenState }: PropsWithChildren<FilterCardProps>) => {
  const [isOpen, setIsOpen] = useState(defaultOpenState)

  const handleToggle = useCallback((id: string, state: boolean) => {
    setIsOpen(state)
    saveCookie(`catalog-filter-${id}`, String(state))
  }, [])

  return (
    <Card className={cn('shadow-lg p-4 lg:w-[280px] select-none')}>
      <Collapsible
        open={isOpen}
        onOpenChange={() => {
          handleToggle(id, !isOpen)
        }}
      >
        <CollapsibleTrigger asChild>
          <CardHeader className="hover:bg-gray-50 p-2 rounded-xl cursor-pointer">
            <div className="flex items-center justify-between">
              <span className="font-bold">{title}</span>
              <ButtonArrow arrowDirection={isOpen ? 'up' : 'down'} />
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="p-0 pt-6 max-h-72 overflow-auto">{children}</CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}
