'use client'

import { useRout<PERSON> } from 'next/navigation'
import React, { useCallback, useMemo, useState } from 'react'

import { FullScreenLoader } from '@/src/app/(store)/[...dynamic]/(catalog)/CatalogProducts/Filters/components/FullScreenLoader'
import { FilterType } from '@/src/features/filters/FilterType'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { FilterCard } from '@features/filters/FilterCard'
import { AppAppliedFilterFragment, AppAvailableFilterFragment, FilterRenderType } from '@lib/_generated/graphql_sdk'

import { ActiveFilter } from './components/ActiveFilter'
import { LucideChevronDown, LucideChevronUp } from 'lucide-react'

interface FiltersProps {
  applied: AppAppliedFilterFragment[]
  available: AppAvailableFilterFragment[]
}

interface IActiveFilter {
  filter: {
    requestVar: string
    label: string
  }
  value: {
    label: string
    value: string
  }
}

enum FilterAction {
  ADD = 'add',
  REPLACE = 'replace',
  REMOVE = 'remove',
}

export const Filters = ({ available, applied }: FiltersProps) => {
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  const activeFilters = useMemo(
    (): IActiveFilter[] =>
      applied.flatMap((appliedFilter) => {
        const filterOptions = available.find((filter) => filter.requestVar === appliedFilter.requestVar)?.options || []
        // if (filterOptions)
        return filterOptions
          .filter((option) => appliedFilter.value.split(',').includes(option.value))
          .map((option) => ({
            filter: {
              requestVar: appliedFilter.requestVar,
              label: appliedFilter.label,
            },
            value: {
              label: option.label,
              value: option.value,
            },
          }))
      }),
    [applied, available]
  )

  const toggleFilter = useCallback(
    (requestVar: string, value: string, filterAction: FilterAction) => {
      setLoading(true)
      const url = new URL(window.location.href)
      const searchParams = url.searchParams
      searchParams.delete('p')

      if (filterAction == FilterAction.ADD) {
        const existingValues = new Set(searchParams.get(requestVar)?.split(',') || [])
        existingValues.add(value)
        searchParams.set(requestVar, Array.from(existingValues).join(','))
      } else if (filterAction == FilterAction.REMOVE) {
        const updatedValues = (searchParams.get(requestVar)?.split(',') || []).filter((v) => v !== value)
        updatedValues.length ? searchParams.set(requestVar, updatedValues.join(',')) : searchParams.delete(requestVar)
      } else if (filterAction == FilterAction.REPLACE) {
        searchParams.set(requestVar, value)
      }

      router.push(`${url.pathname}?${searchParams.toString()}`, { scroll: false })
      setTimeout(() => {
        setLoading(false)
      }, 500)
    },
    [router]
  )

  const clearFilters = useCallback(() => {
    router.push(window.location.pathname, { scroll: false })
  }, [router])

  const [isOpen, setIsOpen] = useState(false)

  const toggleFilters = () => {
    if (isOpen) {
      setIsOpen(false)
      document.body.style.overflow = 'auto'
    } else {
      setIsOpen(true)
      document.body.style.overflow = 'hidden'
    }
  }

  return (
    <>
      {loading && <FullScreenLoader />}
      <div
        className={cn('flex flex-col', '', 'lg:static lg:z-0 lg:pt-0', {
          'bg-black/70 fixed right-0 bottom-0 left-0 top-0 z-40 pt-[150px]': isOpen,
        })}
      >
        <div className={cn('hidden flex-col gap-4 p-5', 'lg:flex lg:p-0', 'overflow-auto', { flex: isOpen })}>
          {activeFilters.length > 0 && (
            <FilterCard title="Активни филтри" id="active-filters" defaultOpenState>
              <div className="flex flex-col gap-3">
                <div className="flex flex-wrap gap-1">
                  {activeFilters.map((filter, _) => (
                    <ActiveFilter
                      key={`${filter.filter.requestVar}-${filter.value.value}`}
                      label={filter.value.label}
                      onClick={() => toggleFilter(filter.filter.requestVar, filter.value.value, FilterAction.REMOVE)}
                    />
                  ))}
                </div>
                <Button variant="link" size="sm" className="text-black" onClick={clearFilters}>
                  <Text className="text-xs tracking-wider">Изчисти всички филтри</Text>
                </Button>
              </div>
            </FilterCard>
          )}
          {available.map((filter) => {
            const splitter = filter.type === FilterRenderType.Slider ? '-' : ','
            const value = applied.find((o) => o.requestVar === filter.requestVar)?.value?.split(splitter) || []
            return (
              <FilterType
                key={filter.requestVar}
                filter={filter}
                type={filter.type}
                value={value}
                onChange={(value, checked) => {
                  let action = checked ? FilterAction.ADD : FilterAction.REMOVE
                  if (filter.type === FilterRenderType.Slider) {
                    action = FilterAction.REPLACE
                  }
                  toggleFilter(filter.requestVar, value, action)
                }}
              />
            )
          })}
        </div>
        <div
          className={cn('my-4 flex flex-col px-5 self-stretch lg:hidden z-30', {
            'fixed bottom-0 right-0 left-0': !isOpen,
          })}
        >
          <Button
            size="xl"
            onClick={toggleFilters}
            className="shadow-lg rounded-xl hover:bg-primary leading-none tracking-normal"
          >
            {isOpen ? (
              <div className="flex w-full">
                <LucideChevronDown />
                <div className="flex-1">
                  <Text className="normal-case">Затвори и приложи филтрите</Text>
                </div>
              </div>
            ) : (
              <div className="flex w-full">
                <LucideChevronUp />
                <div className="flex-1">
                  <Text className="normal-case">Отвори филтри</Text>
                </div>
              </div>
            )}
          </Button>
        </div>
      </div>
    </>
  )
}
