'use client'

import * as SliderPrimitive from '@radix-ui/react-slider'
import { useEffect, useRef, useState } from 'react'
import { useDebounceCallback } from 'usehooks-ts'

import { Input } from '@components/theme/ui/input'
import { LucideChevronLeft, LucideChevronRight } from 'lucide-react'

interface RangeSliderProps {
  min: number
  max: number
  value: [number, number]
  onChangeAction: (range: [number, number]) => void
}

export const RangeSlider: React.FC<RangeSliderProps> = ({ min, max, value, onChangeAction }) => {
  const [range, setRange] = useState<[number, number]>([
    Math.round(value[0] > min ? value[0] : min),
    Math.round(value[1] < max ? value[1] : max),
  ])

  const sliderRef = useRef<HTMLDivElement>(null)

  const debounced = useDebounceCallback((r) => {
    onChangeAction(r)
  }, 800)

  const handleValueChange = (newRange: [number, number]) => {
    if (newRange[1] > newRange[0]) {
      setRange(newRange)
      debounced(newRange)
    }
  }

  // Handle manual input changes
  const handleInputChange = (index: 0 | 1, newValue: number) => {
    const parsedValue = Math.min(Math.max(parseInt(String(newValue), 10) || min, min), max)

    if (index === 0) {
      // Ensure lower value doesn't exceed higher value
      if (parsedValue < range[1]) {
        handleValueChange([parsedValue, range[1]])
      }
    } else {
      // Ensure higher value doesn't fall below lower value
      if (parsedValue > range[0]) {
        handleValueChange([range[0], parsedValue])
      }
    }
  }

  // Touch-specific workaround based on Radix UI GitHub issues
  useEffect(() => {
    // This helps prevent touch events from being blocked by the parent elements
    const handleTouchStart = (e: TouchEvent) => {
      e.preventDefault()
      e.stopPropagation()
    }

    const sliderElement = sliderRef.current
    if (sliderElement) {
      // Add touch handlers to improve mobile behavior
      sliderElement.addEventListener('touchstart', handleTouchStart, { passive: false })

      return () => {
        sliderElement.removeEventListener('touchstart', handleTouchStart)
      }
    }
  }, [])

  return (
    <div ref={sliderRef}>
      <div className="flex justify-between py-2">
        <span className="text-sm text-gray-400">
          <span className="font-bold">{range[0]}</span> лв.
        </span>
        <span className="text-sm text-gray-400">
          <span className="font-bold">{range[1]}</span> лв.
        </span>
      </div>

      {/* Slider Component */}
      <div className="py-4">
        <SliderPrimitive.Root
          className="relative flex items-center w-full h-10 touch-none"
          value={range}
          onValueChange={handleValueChange}
          min={min}
          max={max}
          step={1}
          aria-label="Price Range"
          defaultValue={[min, max]}
          minStepsBetweenThumbs={1}
        >
          <SliderPrimitive.Track className="relative flex-grow h-2 bg-gray-200 rounded-full">
            <SliderPrimitive.Range className="absolute h-full bg-primary rounded-full" />
          </SliderPrimitive.Track>

          {/* Larger thumbs for better touch targets */}
          <SliderPrimitive.Thumb
            className="flex items-center justify-center w-7 h-7 bg-white text-primary border-2 border-primary rounded-full focus:outline-none shadow-md"
            aria-label="Lower price"
          >
            <LucideChevronLeft size={18} />
          </SliderPrimitive.Thumb>
          <SliderPrimitive.Thumb
            className="flex items-center justify-center w-7 h-7 bg-white text-primary border-2 border-primary rounded-full focus:outline-none shadow-md"
            aria-label="Higher price"
          >
            <LucideChevronRight size={18} />
          </SliderPrimitive.Thumb>
        </SliderPrimitive.Root>
      </div>

      {/* Input Fields */}
      <div className="flex justify-evenly items-center mt-2">
        <Input
          type="number"
          value={range[0]}
          onChange={(e) => handleInputChange(0, Number(e.target.value))}
          className="rounded-full border border-gray-300"
          min={min}
          max={range[1] - 1}
        />
        <span className="px-3">-</span>
        <Input
          type="number"
          value={range[1]}
          onChange={(e) => handleInputChange(1, Number(e.target.value))}
          className="rounded-full border border-gray-300"
          min={range[0] + 1}
          max={max}
        />
      </div>
    </div>
  )
}
