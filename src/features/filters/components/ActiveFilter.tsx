import { LucideX } from 'lucide-react'
import React from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { Tooltip, TooltipContent, TooltipTrigger } from '@components/theme/ui/tooltip'

interface ActiveFilterProps {
  label: string
  onClick: () => void
}

export const ActiveFilter: React.FC<ActiveFilterProps> = ({ label, onClick }) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div
          className={cn(
            'inline-flex items-center rounded-full border py-0.5 text-xs font-semibold transition-colors border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80',
            'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
            'group',
            'cursor-pointer'
          )}
          onClick={onClick}
        >
          <Button variant="ghost" size="icon" className="p-1">
            <LucideX size={15} className="p-0" />
          </Button>
          <span className="pl-0 pr-2.5">{label}</span>
        </div>
      </TooltipTrigger>
      <TooltipContent className="bg-black/70">
        <Text className="text-xs">Премахни филтъра</Text>
      </TooltipContent>
    </Tooltip>
  )
}
