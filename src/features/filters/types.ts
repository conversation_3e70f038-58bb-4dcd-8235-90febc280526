import { AppliedFilter, AttributeOption, AvailableFilter, Filters } from '@lib/_generated/graphql_sdk'

export interface ActiveFilter {
  requestVar: string
  value: string
}

export type FiltersState = {
  booted: boolean
  available: AvailableFilter[]
  applied: AppliedFilter[]
}

export type FiltersActions = {
  addFilter: (requestVar: AvailableFilter['requestVar'], value: AttributeOption['value']) => void
  removeFilter: (type: string, value: string) => void
  init: (filters: Filters) => void
  clearFilters: () => void
}

export type FiltersStore = FiltersState & FiltersActions
