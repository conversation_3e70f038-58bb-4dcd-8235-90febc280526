import { CheckedState } from '@radix-ui/react-checkbox'
import React from 'react'

import { FilterCard } from '@/src/features/filters/FilterCard'
import { RangeSlider } from '@/src/features/filters/RangeSlider'
import { cn } from '@components/lib/utils'
import { Card } from '@components/theme/ui/card'
import { Checkbox } from '@components/theme/ui/checkbox'
import { AvailableFilter, FilterRenderType } from '@lib/_generated/graphql_sdk'
import { getCookieValue } from '@lib/utils/cookie'

interface FilterTypeProps {
  filter: AvailableFilter
  type: FilterRenderType
  value?: string[]
  onChange: (value: string, checked: CheckedState) => void
}

export const FilterType: React.FC<FilterTypeProps> = ({ filter, type, value, onChange }) => {
  const cookieValue = getCookieValue(`catalog-filter-${filter.requestVar}`)
  const defaultOpenState = cookieValue === 'true' || cookieValue === true

  switch (type) {
    case FilterRenderType.List:
      return (
        <FilterCard title={filter.label} id={filter.requestVar} defaultOpenState={defaultOpenState}>
          <div className="flex flex-col gap-1">
            {filter.options.map((option, index) => (
              <div
                key={index}
                className={cn(
                  'flex items-center gap-1 px-2 py-1 rounded-xl',
                  value?.includes(option.value) ? 'hover:bg-primary/10' : 'hover:bg-gray-100/60'
                )}
              >
                <Checkbox
                  key={index}
                  id={`option-${option.value}`}
                  className={cn(
                    'w-6 h-6 rounded-md bg-gray-100 shadow-none',
                    value?.includes(option.value) ? 'border-primary' : 'border-gray-300'
                  )}
                  checked={value?.includes(option.value)}
                  onCheckedChange={(checked) => onChange(option.value, checked)}
                />
                <label htmlFor={`option-${option.value}`} className="text-sm cursor-pointer w-full">
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        </FilterCard>
      )
    case FilterRenderType.Slider:
      const min = filter.options.find((option) => option.label === 'min')?.value
      const max = filter.options.find((option) => option.label === 'max')?.value
      return (
        <FilterCard key={`${min}-${max}`} title={filter.label} id={filter.requestVar} defaultOpenState>
          <RangeSlider
            min={Number(min)}
            max={Number(max)}
            value={(value?.map(Number) as [number, number]) || [Number(min), Number(max)]}
            onChangeAction={(range) => {
              onChange(`${range[0]}-${range[1]}`, true)
            }}
          />
        </FilterCard>
      )
    default:
      return <Card>Unsupported filter type</Card>
  }
}
