'use client'

import { useCallback, useEffect } from 'react'

import { refreshAuthToken } from '@features/customer/customer.actions'
import { useCustomerStore } from '@features/customer/customer.store'
import { AuthCookieName, ClientCookie } from '@features/cart/ClientCookie'
import { isCartTokenExpired, parseToken, tokenWillExpireSoon } from '@features/cart/cookie.helpers'
import { CookieCartClaims } from '@features/cart/types'

export function AuthHandler() {
  const setAuthenticated = useCustomerStore((status) => status.setAuthenticated)

  const checkAndRefreshToken = useCallback(async (): Promise<boolean> => {
    const token = ClientCookie.getToken(AuthCookieName)
    if (!token) {
      setAuthenticated(false)
      return false
    }

    const claims = parseToken<CookieCartClaims>(token)
    if (!claims) {
      setAuthenticated(false)
      return false
    }

    if (isCartTokenExpired(claims)) {
      setAuthenticated(false)
      return false
    }

    if (tokenWillExpireSoon(claims)) {
      // const refreshResult = await
      refreshAuthToken()
        .then((refreshResult) => {
          if (refreshResult) {
            setAuthenticated(true, refreshResult.id, refreshResult.token)
            return true
          }
        })
        .catch((error) => {
          return false
        })
    }
    return true
  }, [setAuthenticated])

  useEffect(() => {
    checkAndRefreshToken()

    // Set up periodic token check (every 5 minutes)
    const intervalId = setInterval(checkAndRefreshToken, 5 * 60 * 1000)

    return () => clearInterval(intervalId)
  }, [checkAndRefreshToken])

  return null
}
