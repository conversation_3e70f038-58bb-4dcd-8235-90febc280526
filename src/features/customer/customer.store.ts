import Cookies from 'universal-cookie'
import { create } from 'zustand'

import { GraphQLBackend } from '@lib/api/graphql'
import { handleAPIError } from '@lib/types/isAPIError'
import { showInfoToast, showSuccessToast } from '@lib/utils/toaster'
import { ICustomerStore } from '@features/customer/customer.types'
import { AuthCookieName } from '@features/cart/ClientCookie'
import { parseToken } from '@features/cart/cookie.helpers'
import { CookieAuthClaims } from '@features/cart/types'
import { CustomerDataFullFragment } from '@lib/_generated/graphql_sdk'
import { gtagTrack } from '@/src/components/molecules/GDPR/GtagTrack'
import { trackAddToWishlist } from '@lib/fbp/faceBookPixelHelper'

const cookies = new Cookies()

export function CustomerStore() {
  const token = cookies.get(AuthCookieName)
  const initialCustomer = token ? parseToken<CookieAuthClaims>(token) : null

  return create<ICustomerStore>((set, get) => ({
    id: initialCustomer?.customer_id,
    token,
    loading: true,
    customer: null,
    authenticated: false,
    wishlistSkus: [],
    orders: [],
    initCustomer: ({ data, wishlistSkus }) => {
      set({ customer: data, loading: false, authenticated: !!data, wishlistSkus })
    },
    updateCustomerPassword: async (oldPassword: string, newPassword: string) => {
      try {
        set({ loading: true })
        const response = await GraphQLBackend.UpdateCustomerPassword(
          { oldPassword, newPassword },
          { 'x-auth-customer': cookies.get('token') }
        )
        if (response) {
          console.log({ response })
          showSuccessToast({
            title: 'Паролата е променена успешно!',
            description: 'Паролата е променена успешно',
          })
          set({ loading: false })
          return true
        } else {
          return false
        }
      } catch (e) {
        set({ loading: false })
        handleAPIError(e)
        return false
      }
    },
    updateCustomerData: async (data) => {
      try {
        set({ loading: true })
        const response = await GraphQLBackend.UpdateCustomer({ data }, { 'x-auth-customer': cookies.get('token') })
        if (response.customerUpdateInfo) {
          showSuccessToast({
            description: 'Профилът е успешно обновен',
          })
          set({
            customer: response.customerUpdateInfo,
            loading: false,
          })
          return true
        }
        return false
      } catch (e) {
        set({ loading: false })
        handleAPIError(e)
        return false
      }
    },
    createCustomerAddress: async (data) => {
      try {
        set({ loading: true })
        const response = await GraphQLBackend.CreateCustomerAddress(
          {
            data,
          },
          {
            'x-auth-customer': cookies.get('token'),
          }
        )
        if (response) {
          const { customerAddressAdd } = response
          // find the newly added address by comparing the response and the existing addresses
          const addressID = customerAddressAdd.find(
            (address) => !get().customer?.addresses.find((addr) => addr.id === address.id)
          )?.id
          set({
            customer: {
              ...((get().customer || {}) as CustomerDataFullFragment),
              addresses: customerAddressAdd,
              defaultBillingAddressID: data.isDefaultBilling ? addressID : get().customer?.defaultBillingAddressID,
              defaultShippingAddressID: data.isDefaultShipping ? addressID : get().customer?.defaultShippingAddressID,
            },
          })
          showSuccessToast({
            title: 'Успешно добавен адрес',
            description: 'Адресът е добавен успешно',
          })
          set({ loading: false })
          return true
        }
        return false
      } catch (e) {
        set({ loading: false })
        handleAPIError(e)
        return false
      }
    },
    updateCustomerAddress: async (data, addressID) => {
      try {
        set({ loading: true })
        const response = await GraphQLBackend.UpdateCustomerAddress(
          { data, addressID },
          { 'x-auth-customer': cookies.get('token') }
        )
        if (response) {
          const { customerAddressUpdate } = response
          set({
            customer: {
              ...((get().customer || {}) as CustomerDataFullFragment),
              addresses: customerAddressUpdate,
              defaultBillingAddressID: data.isDefaultBilling ? addressID : get().customer?.defaultBillingAddressID,
              defaultShippingAddressID: data.isDefaultShipping ? addressID : get().customer?.defaultShippingAddressID,
            },
          })
          showSuccessToast({
            description: 'Адресът е успешно обновен',
          })
          set({ loading: false })
          return true
        }
        return false
      } catch (e) {
        set({ loading: false })
        handleAPIError(e)
        return false
      }
    },
    deleteCustomerAddress: async (addressID) => {
      try {
        set({ loading: true })
        const response = await GraphQLBackend.DeleteCustomerAddress(
          { addressID },
          { 'x-auth-customer': cookies.get('token') }
        )
        if (response) {
          const { customerAddressRemove } = response
          set({
            customer: {
              ...((get().customer || {}) as CustomerDataFullFragment),
              addresses: customerAddressRemove,
            },
          })
          showSuccessToast({
            description: 'Адресът е успешно изтрит',
          })
          set({ loading: false })
          return true
        }
        return false
      } catch (e) {
        set({ loading: false })
        handleAPIError(e)
        return false
      }
    },
    setAuthenticated: (isAuthenticated, id, token) => {
      set({
        authenticated: isAuthenticated,
        id: id ? id : get().id,
        token: token ? token : get().token,
      })
    },
    signIn: async (email: string, password: string, recaptchaToken: string) => {
      try {
        set({ loading: true })
        const loginResponse = await GraphQLBackend.CustomerLogin(
          { email, password },
          { 'x-captcha-token': recaptchaToken }
        )
        if (loginResponse) {
          const cookie = new Cookies()
          const parsedToken = parseToken<CookieAuthClaims>(loginResponse.customerLogin.token)
          if (!parsedToken) {
            throw new Error('Invalid token')
          }
          cookie.set(AuthCookieName, loginResponse.customerLogin.token, {
            path: '/',
            expires: new Date(1000 * parsedToken?.exp),
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
          })
          showSuccessToast({
            title: 'Добре дошли!',
            description: 'Вие успешно влязохте в системата',
          })
          set({
            loading: false,
            authenticated: true,
            id: parsedToken.customer_id,
            token: loginResponse.customerLogin.token,
          })

          gtagTrack({
            eventName: 'login',
            properties: {
              method: 'Form',
            },
          })
          return true
        }
        return false
      } catch (e) {
        set({ loading: false })
        handleAPIError(e)
        return false
      }
    },
    signUp: async (data, token) => {
      try {
        set({ loading: true })
        const response = await GraphQLBackend.CustomerRegister({ data }, { 'x-captcha-token': token })
        if (response.customerRegister) {
          set({ loading: false })
          return true
        }
      } catch (e) {
        set({ loading: false })
        handleAPIError(e)
      }
      return false
    },
    addToWishlist: async (sku: string, price?: number, currency?: string, options?: { fbContentId?: string }) => {
      const { token, authenticated } = get()
      if (token && authenticated) {
        try {
          set({ loading: true })
          const response = await GraphQLBackend.WishlistAdd({ sku }, { 'x-auth-customer': token })
          if (response) {
            showSuccessToast({
              title: 'Успешно добавен продукт в любими',
              description: 'Продуктът е добавен успешно',
            })
            trackAddToWishlist({
              content_ids: [options?.fbContentId || sku],
              contents: [{ id: options?.fbContentId || sku, quantity: 1 }],
              value: price,
              currency: currency,
            })

            gtagTrack({
              eventName: 'add_to_wishlist',
              properties: {
                currency: currency,
                value: price,
                items: [
                  {
                    item_id: options?.fbContentId || sku,
                    item_name: 'Product ' + sku,
                    price: price,
                    quantity: 1,
                  },
                ],
              },
            })
            console.log({ response })
            set({ loading: false, wishlistSkus: response.customerWishlistAdd.skus })
            return true
          }
          return false
        } catch (e) {
          set({ loading: false })
          handleAPIError(e)
          return false
        }
      }
      return false
    },
    removeFromWishlist: async (sku: string) => {
      const { token, authenticated } = get()
      if (token && authenticated) {
        try {
          set({ loading: true })
          const response = await GraphQLBackend.WishlistRemove({ sku }, { 'x-auth-customer': token })
          if (response) {
            showSuccessToast({
              title: 'Успешно премахнат продукт от любими',
              description: 'Продуктът е премахнат успешно',
            })
            set({ loading: false, wishlistSkus: response.customerWishlistRemove.skus })
            return true
          }
          return false
        } catch (e) {
          set({ loading: false })
          handleAPIError(e)
          return false
        }
      }
      return false
    },
    logoutCustomer: async () => {
      try {
        set({ loading: true })
        const authToken = get().token
        if (authToken) {
          const response = await GraphQLBackend.CustomerLogout({}, { 'x-auth-customer': authToken })
          return true
        }
        showInfoToast({
          title: 'Потребителят не е вписан',
          description: 'Моля, впишете се отново',
        })
        return true
      } catch (e) {
        handleAPIError(e)
        set({ loading: false })
        return false
      } finally {
        const cookie = new Cookies()
        cookie.remove(AuthCookieName, { path: '/' })
        set({ loading: false, customer: undefined, authenticated: false })
      }
    },
    getCustomerOrders: async () => {
      try {
        const response = await GraphQLBackend.GetCustomerOrders({}, { 'x-auth-customer': cookies.get('token') })
        if (response) {
          set({ orders: response.customerData.orders })
          return true
        }
        return false
      } catch (e) {
        handleAPIError(e)
        return false
      }
    },
  }))
}

export const useCustomerStore = CustomerStore()
