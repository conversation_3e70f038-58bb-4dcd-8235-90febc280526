'use server'

import { cookies } from 'next/headers'

import { AuthCookieName } from '@features/cart/ClientCookie'
import { ServerGraphQLBackend } from '@lib/api/graphql_server'

export async function refreshAuthToken(): Promise<{ id: number; token: string } | null> {
  try {
    const cookieStore = await cookies()
    const authToken = cookieStore.get(AuthCookieName)?.value

    // Only attempt to refresh if we have a token
    if (authToken) {
      const response = await ServerGraphQLBackend.CustomerAuth()
      if (response?.customerLoginRefresh?.token) {
        cookieStore.set(AuthCookieName, response.customerLoginRefresh.token, {
          path: '/',
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
        })
        return {
          id: Number(response.customerLoginRefresh.id),
          token: response.customerLoginRefresh.token,
        }
      }
    }
    return null
  } catch (error) {
    return null
  }
}

export async function deleteToken(): Promise<boolean> {
  try {
    const cookieStore = await cookies()
    cookieStore.delete(AuthCookieName)
    return true
  } catch (error) {
    return false
  }
}
