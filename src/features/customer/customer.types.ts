import {
  CustomerAddressInput,
  CustomerDataFullFragment,
  CustomerRegistrationData,
  CustomerUpdateInput,
  OrderFullFragment,
} from '@lib/_generated/graphql_sdk'

export interface ICustomerStore {
  // state
  loading: boolean
  customer: CustomerDataFullFragment | null
  id?: number
  token?: string
  authenticated: boolean
  wishlistSkus: string[]
  orders: OrderFullFragment[]
  // actions
  initCustomer: ({ data, wishlistSkus }: { data?: CustomerDataFullFragment; wishlistSkus: string[] }) => void
  updateCustomerPassword: (oldPassword: string, newPassword: string) => Promise<boolean>
  updateCustomerData: (data: CustomerUpdateInput) => Promise<boolean>
  createCustomerAddress: (data: CustomerAddressInput) => Promise<boolean>
  updateCustomerAddress: (data: CustomerAddressInput, id: string) => Promise<boolean>
  deleteCustomerAddress: (addressID: string) => Promise<boolean>
  setAuthenticated: (isAuthenticated: boolean, id?: number, token?: string) => void
  signIn: (email: string, name: string, recaptchaToken: string) => Promise<boolean>
  signUp: (data: CustomerRegistrationData, recaptchaToken: string) => Promise<boolean>
  logoutCustomer: () => Promise<boolean>
  addToWishlist: (
    sku: string,
    price?: number,
    currency?: string,
    options?: { fbContentId?: string }
  ) => Promise<boolean>
  removeFromWishlist: (sku: string) => Promise<boolean>
  getCustomerOrders: () => Promise<boolean>
}
