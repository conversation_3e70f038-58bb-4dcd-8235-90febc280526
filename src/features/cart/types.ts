import { CartAddMutation, GalleryImage, Image, Product, ProductPrice } from '@lib/_generated/graphql_sdk'

export type CookieCartClaims = {
  cart_id: string
  crt: number
  exp: number
  products_count: number
  salt: string
  skus: string
}

export type CookieAuthClaims = {
  created: number
  customer_id: number
  exp: number
  group_id: number
  name: string
}

export type Token = string

export interface IClientCartCookie {
  getToken: (token: Token) => string | null
  setToken: (token: Token) => void
  removeToken: (token: Token) => void
  isValid: (token: Token | null) => boolean
}

export type IServerCartCookie = {
  [K in keyof IClientCartCookie]: (
    ...args: Parameters<IClientCartCookie[K]>
  ) => Promise<ReturnType<IClientCartCookie[K]>>
}

export interface CartProduct {
  id: Product['id']
  name: Product['name']
  description: Product['shortDescription']
  labels: Product['labels']
  brand: Product['brand']
  price: ProductPrice
  sku: Product['sku']
  url: Product['urlKey']
  image: GalleryImage['image'] | null
}
