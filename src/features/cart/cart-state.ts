import { create } from 'zustand'

import paymentMethods from '@/src/app/(store)/_components/Footer/PaymentMethods'
import { ClientCookie } from '@features/cart/ClientCookie'
import {
  CartFullFragment,
  ClientInput,
  ShippingInput,
  AvailableShippingMethodFragment,
  AvailablePaymentMethodFragment,
  NewOrderInput,
  CreateOrderMutation,
  PlaceOrderResponse,
  PlaceOrderFragment,
} from '@lib/_generated/graphql_sdk'
import { GraphQLBackend } from '@lib/api/graphql'
import { handleAPIError } from '@lib/types/isAPIError'
import { showSuccessToast, showErrorToast } from '@lib/utils/toaster'
import Cookies from 'universal-cookie'
import { gtagTrack } from '@/src/components/molecules/GDPR/GtagTrack'

export interface CartState extends Omit<CartFullFragment, 'id'> {
  // Override the id to be a number
  id: number
  // Personal data
  personalLoading: boolean
  personalError: string | null
  personalInfo: ClientInput | null
  // Shipping methods
  shippingPrices: {
    data: AvailableShippingMethodFragment[]
    loading: boolean
    error: string | null
  }
  // Payment methods
  paymentsList: {
    data: AvailablePaymentMethodFragment[]
    loading: boolean
    error: string | null
  }
  paymentSave: {
    loading: boolean
    error: string | null
  }
  // UI state properties
  ready: boolean
  loading: boolean
  error: string | null

  // Cart actions
  initCart: () => Promise<void>
  addItem: (productSku: string, quantity: number) => Promise<boolean>
  updateItem: (productSku: string, quantity: number) => Promise<boolean>
  removeItem: (productSku: string) => Promise<boolean>
  applyCouponCode: (couponCode: string) => Promise<boolean>
  removeCouponCode: () => Promise<boolean>
  resetCart: () => void
  // Checkout client
  saveClientData: (clientData: ClientInput) => Promise<boolean>
  // Checkout shipping
  getShippingPrices: (
    deliveryData: ShippingInput
  ) => Promise<{ status: boolean; data: AvailableShippingMethodFragment[] }>
  clearShippingPrices: () => void
  saveShippingMethod: (code: string, shippingData: ShippingInput) => Promise<boolean>
  // Checkout payment
  getPaymentMethods: () => Promise<boolean>
  savePaymentMethod: (paymentMethod: string) => Promise<boolean>
  // Checkout order
  createOrder: (orderData: NewOrderInput) => Promise<{ status: boolean; data: PlaceOrderFragment | null }>
}

const cookies = new Cookies()

const createEmptyCartState = (): Omit<
  CartState,
  | 'initCart'
  | 'addItem'
  | 'updateItem'
  | 'removeItem'
  | 'applyCouponCode'
  | 'removeCouponCode'
  | 'resetCart'
  | 'saveClientData'
  | 'getShippingPrices'
  | 'clearShippingPrices'
  | 'saveShippingMethod'
  | 'getPaymentMethods'
  | 'createOrder'
  | 'savePaymentMethod'
> => ({
  id: 0,
  personalLoading: false,
  personalError: null,
  personalInfo:null,
  // Shipping methods
  shippingPrices: {
    data: [],
    loading: false,
    error: null,
  },
  // Payment methods
  paymentsList: {
    data: [],
    loading: false,
    error: null,
  },
  paymentSave: {
    loading: false,
    error: null,
  },
  // UI state properties
  ready: false,
  loading: true,
  error: null,

  // Default cart properties
  token: '',
  storeCode: '',
  currency: '',
  couponCode: '',
  note: '',
  paymentMethod: '',
  customer: {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    invoice: null,
  },
  items: [],
  shipping: {
    availableMethods: [],
    hasFreeShipping: false,
    freeShippingAfter: null,
    minAmountForFreeShippingMessage: null,
    selectedMethod: '',
    availableIn: [],
    address: null,
  },
  totals: [],
})

const mapResponse = (response: CartFullFragment) => {
  return {
    ...response,
    id: Number(response.id),
    ready: true,
    items: response.items.map((item) => ({
      ...item,
      product: item.product,
    })),
  }
}

const ensureValidToken = async (): Promise<string> => {
  const token = ClientCookie.getToken('cartToken')

  if (ClientCookie.isValid(token) && token?.length) {
    return token
  }

  try {
    // Create a new cart if no valid token exists
    const response = await GraphQLBackend.GetEmptyCart()
    const newToken = response.getNewCart.token
    ClientCookie.setToken(newToken)
    return newToken
  } catch (error) {
    console.error('Failed to create a new cart', error)
    handleAPIError(error)
    throw new Error('Could not create a new cart')
  }
}

export const useCartStore = create<CartState>((set, get) => ({
  ...createEmptyCartState(),

  initCart: async () => {
    set({ loading: true, error: null })

    try {
      const token = ClientCookie.getToken('cartToken')

      if (ClientCookie.isValid(token) && token?.length) {
        const response = await GraphQLBackend.GetCartById({ cart: token })

        if (response?.getCart) {
          ClientCookie.setToken(response.getCart.token)
          set(() => ({
            ...mapResponse(response.getCart),
            loading: false,
          }))
          return
        }
      }

      // If we reach here, either there was no valid token or the cart wasn't found
      set({
        ...createEmptyCartState(),
        ready: true,
        loading: false,
      })
    } catch (error) {
      ClientCookie.removeToken('cartToken')
      set({
        ...createEmptyCartState(),
        ready: true,
        loading: false,
        error: null,
      })
    }
  },

  addItem: async (productSku: string, quantity: number) => {
    if (!productSku || quantity <= 0) {
      set({ error: 'Invalid product or quantity' })
      return false
    }

    set({ loading: true, error: null })

    try {
      const validToken = await ensureValidToken()
      const response = await GraphQLBackend.CartAdd({
        cartToken: validToken,
        sku: productSku,
        quantity,
      })
      const addedItem = response.cartItemAdd.items.find((item) => item.sku === productSku)
      const price = addedItem?.price?.value || 0
      const currency = addedItem?.price?.currency || 'BGN'

      if (response?.cartItemAdd) {
        ClientCookie.setToken(response.cartItemAdd.token)
        set(() => ({
          ...mapResponse(response.cartItemAdd),
          loading: false,
        }))
        showSuccessToast({
          title: 'Product added',
          description: 'Successfully added to your cart',
        })

        // Track add_to_cart event for GA4
        gtagTrack({
          eventName: 'add_to_cart',
          properties: {
            currency: currency,
            value: price,
            items: [
              {
                item_id: addedItem?.product?.id || 'error no id',
                item_name: addedItem?.product?.name || 'Unknown Product',
                price: price,
                quantity: quantity,
              },
            ],
          },
        })
        return true
      }

      throw new Error('Failed to add item to cart')
    } catch (error) {
      console.error('Failed to add product to cart', error)
      set({
        loading: false,
        error: 'Could not add this product to your cart',
      })
      handleAPIError(error)
      return false
    }
  },

  updateItem: async (productSku: string, quantity: number) => {
    if (!productSku || quantity < 0) {
      set({ error: 'Invalid product or quantity' })
      return false
    }

    set({ loading: true, error: null })

    try {
      const validToken = await ensureValidToken()
      const response = await GraphQLBackend.CartUpdate({
        cartToken: validToken,
        sku: productSku,
        quantity,
      })

      if (response?.cartItemUpdate) {
        ClientCookie.setToken(response.cartItemUpdate.token)
        set(() => ({
          ...mapResponse(response.cartItemUpdate),
          loading: false,
        }))
        return true
      }

      throw new Error('Failed to update item in cart')
    } catch (error) {
      console.error('Failed to update product in cart', error)
      set({
        loading: false,
        error: 'Could not update this product in your cart',
      })
      handleAPIError(error)
      return false
    }
  },

  removeItem: async (productSku: string) => {
    if (!productSku) {
      set({ error: 'Invalid product' })
      return false
    }

    set({ loading: true, error: null })

    try {
      // Find the item before removing it to track the event
      const currentState = get()
      const itemToRemove = currentState.items.find((item) => item.sku === productSku)

      const validToken = await ensureValidToken()
      const response = await GraphQLBackend.CartRemove({
        cartToken: validToken,
        sku: productSku,
      })

      if (response?.cartItemRemove) {
        ClientCookie.setToken(response.cartItemRemove.token)
        set(() => ({
          ...mapResponse(response.cartItemRemove),
          loading: false,
        }))

        // Track remove_from_cart event for GA4 if item was found
        if (itemToRemove) {
          gtagTrack({
            eventName: 'remove_from_cart',
            properties: {
              currency: itemToRemove.price?.currency || 'BGN',
              value: (itemToRemove.price?.value || 0) * itemToRemove.baseQty,
              items: [
                {
                  item_id: itemToRemove.product?.id || 'unknown',
                  item_name: itemToRemove.product?.name || 'Unknown Product',
                  price: itemToRemove.price?.value || 0,
                  quantity: itemToRemove.baseQty,
                },
              ],
            },
          })
        }

        return true
      }

      throw new Error('Failed to remove item from cart')
    } catch (error) {
      console.error('Failed to remove product from cart', error)
      set({
        loading: false,
        error: 'Could not remove this product from your cart',
      })
      handleAPIError(error)
      return false
    }
  },

  applyCouponCode: async (couponCode: string) => {
    if (!couponCode) {
      set({ error: 'Invalid coupon code' })
      return false
    }

    set({ loading: true, error: null })

    try {
      const validToken = await ensureValidToken()
      const response = await GraphQLBackend.ApplyCouponCode(
        {
          cartToken: validToken,
          couponCode,
        },
        { 'x-auth-customer': cookies.get('token') }
      )

      if (response?.cartApplyCoupon) {
        ClientCookie.setToken(response.cartApplyCoupon.token)
        set(() => ({
          ...mapResponse(response.cartApplyCoupon),
          loading: false,
        }))
        showSuccessToast({ description: 'Discount code applied' })
        return true
      }

      throw new Error('Failed to apply coupon code')
    } catch (error) {
      console.error('Failed to apply coupon code', error)
      set({
        loading: false,
        error: 'Could not apply this coupon code',
      })
      handleAPIError(error)
      return false
    }
  },

  removeCouponCode: async () => {
    set({ loading: true, error: null })

    try {
      const validToken = await ensureValidToken()
      const response = await GraphQLBackend.ApplyCouponCode(
        {
          cartToken: validToken,
          couponCode: '',
        },
        { 'x-auth-customer': cookies.get('token') }
      )

      if (response?.cartApplyCoupon) {
        ClientCookie.setToken(response.cartApplyCoupon.token)
        set(() => ({
          ...mapResponse(response.cartApplyCoupon),
          loading: false,
        }))
        showSuccessToast({ description: 'Discount code removed' })
        return true
      }

      throw new Error('Failed to remove coupon code')
    } catch (error) {
      console.error('Failed to remove coupon code', error)
      set({
        loading: false,
        error: 'Could not remove the coupon code',
      })
      handleAPIError(error)
      return false
    }
  },

  saveClientData: async (clientData: ClientInput) => {
    if (!clientData) {
      set({ personalError: 'Invalid client data' })
      return false
    }

    set({ personalLoading: true, personalError: null })

    try {
      const validToken = await ensureValidToken()
      const response = await GraphQLBackend.CartSaveClient({
        cartToken: validToken,
        data: clientData,
      })

      if (response?.cartSaveClient) {
        ClientCookie.setToken(response.cartSaveClient.token)
        set(() => ({
          ...mapResponse(response.cartSaveClient),
          personalLoading: false,
          personalInfo:clientData
        }))
        showSuccessToast({ description: 'Клиентските данни са запазени' })
        return true
      }

      throw new Error('Failed to save customer data')
    } catch (error) {
      console.error('Failed to save customer data', error)
      set({
        personalLoading: false,
        personalError: 'Could not save customer data',
        personalInfo:null,
      })
      handleAPIError(error)
      return false
    }
  },

  getShippingPrices: async (shippingData: ShippingInput) => {
    if (!shippingData) {
      // set({ shippingError: "Invalid delivery data" });
      return {
        status: false,
        data: [],
      }
    }

    set({
      shippingPrices: {
        data: [],
        loading: true,
        error: null,
      },
    })

    try {
      const validToken = await ensureValidToken()
      const response = await GraphQLBackend.GetShippingMethods({
        cartToken: validToken,
        data: shippingData,
      })

      if (response?.cartAvailableShippingMethods) {
        set(() => ({
          shippingPrices: {
            data: response.cartAvailableShippingMethods,
            loading: false,
            error: null,
          },
        }))
        return {
          status: true,
          data: response.cartAvailableShippingMethods,
        }
      }
      throw new Error('Няма налични методи за доставка до този адрес. Моля изберете друг адрес или офис.')
    } catch (error) {
      console.error('Failed to get shipping methods', error)
      set({
        shippingPrices: {
          data: [],
          loading: false,
          error: 'Няма налични методи за доставка до този адрес. Моля изберете друг адрес или офис.',
        },
      })
      handleAPIError(error)
      return {
        status: false,
        data: [],
      }
    }
  },

  clearShippingPrices: () => {
    set(() => ({
      shippingPrices: {
        data: [],
        loading: false,
        error: null,
      },
    }))
  },

  saveShippingMethod: async (code: string, shippingData: ShippingInput) => {
    // if (!shippingData) {
    //   set({ shippingError: "Invalid shipping data" });
    //   return false;
    // }

    // set({ shippingLoading: true, shippingError: null });

    try {
      const validToken = await ensureValidToken()
      const response = await GraphQLBackend.CartSaveShippingMethod({
        cartToken: validToken,
        data: shippingData,
        code,
      })

      if (response?.cartSaveShipping) {
        ClientCookie.setToken(response.cartSaveShipping.token)
        set(() => ({
          ...mapResponse(response.cartSaveShipping),
          shippingLoading: false,
        }))
        showSuccessToast({ description: 'Информация за доставка е запазена' })
        return true
      }

      throw new Error('Failed to save shipping method')
    } catch (error) {
      console.error('Failed to save shipping method', error)
      // set({
      //   shippingLoading: false,
      //   shippingError: "Could not save shipping method"
      // });
      handleAPIError(error)
      return false
    }
  },

  getPaymentMethods: async () => {
    set({
      paymentsList: {
        data: [],
        loading: true,
        error: null,
      },
    })

    try {
      const validToken = await ensureValidToken()
      const response = await GraphQLBackend.GetPaymentMethods({
        cartToken: validToken,
      })

      if (response?.cartAvailablePaymentMethods) {
        set(() => ({
          paymentsList: {
            data: response.cartAvailablePaymentMethods,
            loading: false,
            error: null,
          },
        }))
        return true
      }
      throw new Error('Failed to get payment methods')
    } catch (error) {
      console.error('Failed to get payment methods', error)
      set({
        paymentsList: {
          data: [],
          loading: false,
          error: 'Could not get payment methods',
        },
      })
      handleAPIError(error)
      return false
    }
  },

  createOrder: async (orderData: NewOrderInput) => {
    if (!orderData) {
      showErrorToast({ description: 'Липсват данни за поръчката' })
    }

    try {
      const validToken = await ensureValidToken()
      const response = await GraphQLBackend.CreateOrder(
        {
          cartToken: validToken,
          data: orderData,
        },
        { 'x-auth-customer': cookies.get('token') }
      )

      if (response?.placeOrder) {
        set(() => ({
          // ...mapResponse(response.placeOrder),
          loading: false,
        }))
        return { status: true, data: response.placeOrder }
      }

      throw new Error('Failed to create order')
    } catch (error) {
      console.error('Failed to create order', error)
      handleAPIError(error)
      return { status: false, data: null }
    }
  },

  savePaymentMethod: async (paymentMethod: string) => {
    if (!paymentMethod) {
      set({ paymentSave: { loading: false, error: 'Invalid payment method' } })
      return false
    }

    set({ paymentSave: { loading: true, error: null } })

    try {
      const validToken = await ensureValidToken()
      const response = await GraphQLBackend.CartSavePaymentMethod({
        cartToken: validToken,
        code: paymentMethod,
      })

      if (response?.cartSavePayment) {
        ClientCookie.setToken(response.cartSavePayment.token)
        set(() => ({
          ...mapResponse(response.cartSavePayment),
          paymentLoading: false,
        }))
        return true
      }

      throw new Error('Failed to save payment method')
    } catch (error) {
      console.error('Failed to save payment method', error)
      set({
        paymentSave: {
          loading: false,
          error: 'Could not save payment method',
        },
      })
      handleAPIError(error)
      return false
    }
  },

  resetCart: () => {
    ClientCookie.removeToken('cartToken')
    set({
      ...createEmptyCartState(),
      ready: true,
    })
  },
}))
