import { useMemo } from 'react'

import { useCartStore } from '@features/cart/cart-state'
import { CartTotal, CartTotalCode } from '@lib/_generated/graphql_sdk'

export const useCart = () => {
  const { totals } = useCartStore()

  const cartTotals = useMemo(() => {
    return totals.reduce(
      (acc, cur) => {
        acc[cur.code as CartTotalCode] = cur
        return acc
      },
      {} as Partial<Record<Partial<CartTotalCode>, CartTotal>>
    )
  }, [totals])

  return {
    cartTotals,
  }
}
