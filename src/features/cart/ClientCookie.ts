import Cookies from 'universal-cookie'

import { validateToken } from '@features/cart/cookie.helpers'
import { IClientCartCookie } from '@features/cart/types'

export const AuthCookieName: string = 'token'
export const AuthTokenHeader = 'x-auth-customer'

export const ClientCookie: IClientCartCookie = {
  getToken: (token) => {
    const cookie = new Cookies()
    const cartToken = cookie.get(token)
    return cartToken ?? null
  },

  setToken: (token) => {
    try {
      const cookie = new Cookies()
      cookie.set('cartToken', token, {
        expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      })
    } catch (error) {
      console.error('Failed to set cart token:', error)
    }
  },

  removeToken: (tokenName = 'cartToken') => {
    try {
      const cookie = new Cookies()
      cookie.remove(tokenName, {
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      })
    } catch (error) {
      console.error('Failed to remove cart token:', error)
    }
  },

  isValid: (token) => validateToken(token),
}
