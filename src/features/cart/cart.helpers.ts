import { ClientCookie } from '@features/cart/ClientCookie'
import { CartProduct } from '@features/cart/types'
import { StoreCartItem } from '@lib/_generated/graphql_sdk'
import { GraphQLBackend } from '@lib/api/graphql'

export const ensureValidToken = async (): Promise<string> => {
  const token = ClientCookie.getToken('cartToken')
  console.log('ensureValidToken token = ', token)
  if (!ClientCookie.isValid(token) || !token) {
    console.log('!ClientCookie.isValid(token)', !ClientCookie.isValid(token))
    console.log('token', token)
    const newToken = await GraphQLBackend.GetEmptyCart()
    ClientCookie.setToken(newToken.getNewCart.token)
    return newToken.getNewCart.token
  }
  return token
}

export function toCartProduct(product: StoreCartItem['product']): CartProduct {
  return {
    id: product.id,
    name: product.name,
    description: product.shortDescription,
    labels: product.labels,
    brand: product.brand,
    price: product.price,
    sku: product.sku,
    url: product.urlKey,
    image: product.gallery.length > 0 ? (product.gallery.find((image) => image.position === 0)?.image ?? null) : null,
  }
}

// export const mapCartResponse = (response: CartAddMutation['addToCart'] | CartUpdateMutation['updateCart']) => {
//   return ({
//     token: response.token,
//     items: response.items.map(({baseQty, product}) => ({
//       quantity: baseQty,
//       product: toCartProduct(product as SimpleProduct),
//     })),
//   })
// }
