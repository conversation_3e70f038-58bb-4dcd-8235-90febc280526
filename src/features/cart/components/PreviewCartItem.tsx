import { CircleX, LucideLoaderCircle } from 'lucide-react'
import React from 'react'

import Img from '@atoms/Img'
import Text from '@atoms/Text'
import { CounterManaged } from '@components/molecules/CounterManaged'
import { Button } from '@components/theme/ui/button'
import { useCartStore } from '@features/cart/cart-state'
import { getProductMainImage } from '@features/product/helpers'
import { CartItemFragment, SimpleProductViewFragment } from '@lib/_generated/graphql_sdk'
import { ProductPrice } from '@components/molecules/ProductPrice'

export const PreviewCartItem = ({ item }: { item: CartItemFragment }) => {
  const image = getProductMainImage(item.product.image, item.product.gallery)
  const [loading, setLoading] = React.useState(false)
  const removeItem = useCartStore((state) => state.removeItem)
  const cartLoading = useCartStore((state) => state.loading)

  return (
    <div className="flex items-center justify-between py-4 border-b border-gray-200 hover:bg-gray-50 group/item">
      <div className="flex items-center relative">
        <div className="w-8/12 relative">
          <Button
            disabled={cartLoading}
            variant="tertiary"
            size="icon"
            className="w-6 h-6 p-0 lg:hidden group-hover/item:block absolute -top-2 -right-2 z-30"
            aria-label="Remove item"
            onClick={async () => {
              setLoading(true)
              removeItem(item.product.sku).then(() => {
                setLoading(false)
              })
            }}
          >
            {loading && <LucideLoaderCircle className="animate-spin" />}
            {!loading && <CircleX className="text-gray-500" />}
          </Button>

          <div className="w-full h-full aspect-square relative z-10">
            {image && <Img fill src={image.src} alt={image.alt || ''} className="object-contain rounded-lg" />}
          </div>
        </div>
        <div className="ml-4">
          <Text className="text-sm cursor-default">{item.product.name}</Text>
        </div>
      </div>
      <div className="pl-4 flex flex-col gap-3 items-center">
        <CounterManaged
          sku={item.product.sku}
          value={item.baseQty}
          min={1}
          max={(item.product as SimpleProductViewFragment).stock.qty}
        />
        <ProductPrice variant="cartPreviewItem" data={{ price: item.rowTotal }} />
      </div>
    </div>
  )
}
