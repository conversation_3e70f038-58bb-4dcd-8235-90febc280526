'use client'

import { LucideLoaderCircle } from 'lucide-react'
import React, { useEffect } from 'react'

import { useAsyncRoutePush } from '@/src/hooks/useAsyncRoutePush'
import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { Card, CardContent, CardFooter, CardHeader } from '@components/theme/ui/card'
import { useCartStore } from '@features/cart/cart-state'
import { PreviewCartItem } from '@features/cart/components/PreviewCartItem'
import { useCart } from '@features/cart/useCart'
import { PropsWithClassName } from '@lib/types/ClassName'
import { ProductPrice } from '@components/molecules/ProductPrice'

export const CartPreview = ({
  className,
  open,
  onRedirect,
}: PropsWithClassName & { open: boolean; onRedirect?: () => void }) => {
  const { loading, items, initCart } = useCartStore()
  const { cartTotals } = useCart()
  const [redirecting, setRedirecting] = React.useState(false)
  const routePush = useAsyncRoutePush()

  useEffect(() => {
    console.log('Cart Preview')
    initCart()
  }, [initCart])

  const hasItems = items.length > 0

  return (
    <Card
      className={cn(
        'absolute top-full hidden flex-col bg-white shadow-xl border-none z-20',
        'left-0 right-0',
        'md:left-auto md:w-[400px] max-h-[calc(100vh-210px)]', // 210px header + mobile filters buttons
        { flex: open },
        className
      )}
    >
      <CardHeader className={cn('hidden md:flex bg-primary rounded-b-xl py-4 px-5')}>
        <div className="flex justify-between items-center">
          <Text className="text-primary-foreground font-bold">Количка</Text>
          {hasItems && (
            <div className="relative">
              <ProductPrice
                variant="cartPreview"
                data={{ price: cartTotals.GRANT_TOTAL?.amount || { value: 0, currency: 'BGN' } }}
              />
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-4 max-h-[370px] overflow-auto">
        {!hasItems && loading && (
          <div className="flex flex-col items-center gap-2 justify-center py-5">
            <LucideLoaderCircle className="animate-spin" />
            <Text className="cursor-default">Зареждане...</Text>
          </div>
        )}
        {!loading && !hasItems && (
          <div className="flex justify-center py-5">
            <Text className="cursor-default">Няма добавени продукти</Text>
          </div>
        )}
        {items.map((item) => (
          <PreviewCartItem item={item} key={item.id} />
        ))}
      </CardContent>
      {hasItems && (
        <CardFooter className="flex justify-center pt-4">
          <Button
            disabled={loading}
            onClick={() => {
              setRedirecting(true)
              routePush('/cart').finally(() => {
                setRedirecting(false)
                onRedirect?.()
              })
            }}
          >
            {redirecting && <LucideLoaderCircle className="animate-spin" />}Завършване на поръчка
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}
