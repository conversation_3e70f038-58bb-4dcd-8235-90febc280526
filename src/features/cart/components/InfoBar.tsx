import React, { PropsWithChildren, ReactElement, ReactNode } from 'react'

import { cn } from '@components/lib/utils'
import Truck from '@icons/minimalistic/static/truck.inline.svg'

interface InfoBarProps {
  icon: ReactNode
  label: ReactNode
  text?: ReactNode
  className?: string
}

export const InfoBar: React.FC<InfoBarProps> = ({ label, text, className }) => (
  <div className={cn('grid grid-cols-24 py-4 bg-background rounded-xl my-3 items-center', className)}>
    <div className="col-span-8 sm:col-span-5 md:col-span-4 lg:col-span-3 xl:col-span-2 flex items-center sm:items-start justify-center">
      <Truck className="h-10 w-fit" />
    </div>
    <div className="flex flex-col col-span-16 md:col-span-20 lg:col-span-21 xl:col-span-22">
      <div className="flex md:items-center flex-col md:flex-row md:justify-between">
        {label}
        {text && <div className="text-xs pr-5">{text}</div>}
      </div>
    </div>
  </div>
)
