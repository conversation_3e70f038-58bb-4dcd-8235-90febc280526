import { cookies } from 'next/headers'

import { validateToken } from '@features/cart/cookie.helpers'
import { IServerCartCookie } from '@features/cart/types'

export const ServerCookie: IServerCartCookie = {
  getToken: async (token) => {
    // Force a new cookie store instance each time to prevent caching
    const cookieStore = await cookies()
    const cookieName = token || 'cartToken'
    const cartToken = cookieStore.get(cookieName)?.value
    return cartToken ?? null
  },

  setToken: async (token) => {
    try {
      const cookieStore = await cookies()
      cookieStore.set('cartToken', token, {
        expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      })
    } catch (error) {
      console.error('Failed to set cart token:', error)
    }
  },

  isValid: async (token) => validateToken(token),
  removeToken: async (token) => {
    try {
      const cookieStore = await cookies()
      cookieStore.delete('cartToken')
    } catch (error) {
      console.log(error)
    }
  },
}
