import { CookieAuthClaims, CookieCartClaims } from '@features/cart/types'

export function getDecodedTokenString(token: string): string {
  const segments = token.split('.')
  if (segments.length !== 3) throw new Error('Token structure incorrect')

  const base64Url = segments[1] || ''
  if (!base64Url) throw new Error('Token structure incorrect')

  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
  if (typeof window !== 'undefined') {
    return decodeURIComponent(
      window
        .atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
  } else {
    return Buffer.from(token.split('.')[1], 'base64').toString()
  }
}

export function parseToken<T>(token: string): T | null {
  try {
    const decodedPayload = getDecodedTokenString(token)
    if (!decodedPayload) throw new Error('Failed to decode token')

    return JSON.parse(decodedPayload)
  } catch (error) {
    return null
  }
}

export function validateToken(token: string | null): boolean {
  if (!token || !token.length) return false
  const claims = parseToken<CookieCartClaims | CookieAuthClaims>(token)
  if (!claims) return false
  let timestamp = claims.exp
  if (timestamp.toString().length < 13) {
    timestamp *= 1000
  }
  return timestamp > Date.now()
}

export function isCartTokenExpired(token: CookieCartClaims): boolean {
  let timestamp = token.exp
  if (timestamp.toString().length < 13) {
    timestamp *= 1000
  }

  // dif in minutes
  // const diff = Math.floor((timestamp - Date.now()) / 60000)
  // console.log('Token active for minutes:', diff, isExpired, timestamp, Date.now())

  return timestamp < Date.now()
}

export function tokenWillExpireSoon(token: CookieCartClaims): boolean {
  const created = token.crt * 1000
  const expirationTime = token.exp * 1000

  const length = expirationTime - created
  const left = expirationTime - Date.now()

  // if left 20% of length
  return left <= length * 0.2
}
