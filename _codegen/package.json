{"name": "codegen", "version": "1.0.0", "description": "Generate Graphql Stuff", "main": "index.js", "scripts": {"generate": "graphql-codegen", "get-schema": "get-graphql-schema https://praktis2-demo.pfgbulgaria.com/graphql > /app/schema.graphql", "dev-schema": "get-graphql-schema http://api:9420/graphql > /app/schema.graphql", "get-schema:dev": "get-graphql-schema https://praktis2-demo.pfgbulgaria.com/graphql > ../schema.graphql", "get-schema:local": "get-graphql-schema http://localhost:9420/graphql > ../schema.graphql", "generate:dev": "graphql-codegen -c codegen-demo.yml", "generate:local": "graphql-codegen -c codegen-local.yml"}, "author": "", "license": "MIT", "devDependencies": {"@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/typescript": "^4.0.7", "@graphql-codegen/typescript-graphql-request": "^6.2.0", "@graphql-codegen/typescript-operations": "^4.0.1", "get-graphql-schema": "^2.1.2", "typescript": "^5.4.5"}, "dependencies": {"graphql": "^16.8.1", "graphql-request": "^6.1.0", "prettier": "^3.3.0"}}