import type { Config } from 'tailwindcss'
export const ThemeColorsArray = ['primary', 'background', 'paragraph'] as const

export type ThemeColors = (typeof ThemeColorsArray)[number] | string

const config: Config = {
  darkMode: ['class'],
  content: [
    './src/components/**/*.{ts,tsx}',
    './src/app/**/*.{ts,tsx}',
    './src/features/**/*.{ts,tsx}',
    './style/_dynamic_classes.ts',
  ],
  theme: {
    screens: {
      'phone': {
        max: '600px',
      },
      'tablet': {
        min: '601px',
        max: '1279px',
      },
      'tabletXl': {
        min: '860px',
        max: '1279px',
      },
      'minTablet': '601px',
      'desktop': '1280px',
      'desktopLg': '1480px',
      'desktopXl': '1680px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
      '3xl': '1920px',
      '4xl': '2560px',
      '5xl': '3440px',
      '6xl': '3840px',
    },
    extend: {
      skew: {
        '26': '26deg',
      },
      colors: {
        accent: {
          DEFAULT: '#EE7F00',
          foreground: '#FFFFFF',
          background: '#FF0000',
        },
        destructive: {
          DEFAULT: '#cd0000',
          foreground: '#FFFFFF',
        },
        primary: {
          DEFAULT: '#EE7F00',
          foreground: '#FFFFFF',
        },
        secondary: {
          DEFAULT: '#F5F5F5',
          foreground: '#2D3132',
        },
        tertiary: {
          DEFAULT: '#FFFFFF',
          foreground: '#2D3132',
        },
        background: {
          DEFAULT: '#f3f3f3',
          dark: '#2D3132',
          darker: '#282C2D',
        },
        paragraph: {
          DEFAULT: '#2D3132',
          muted: '#999999',
        },
        white: '#FFFFFF',
        black: '#000000',
        newsletter: {
          orange: '#F39200',
          yellow: '#EEC800',
        },
        card: {
          DEFAULT: '#FFFFFF',
          foreground: '#2D3132',
        },
      },
      borderColor: {
        input: '#f3f3f3',
      },
      ringColor: {
        DEFAULT: '#EE7F00',
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
        '110': '110',
      },
      fontWeight: {
        normal: '400',
        bold: '700',
      },
      backgroundSize: {
        '70%': '70%',
      },
      backgroundImage: {
        'footer-logo': "url('/images/bg/logo-background.png')",
        '404-image': "url('/images/bg/wall-painting-bg.png')",
        'triangle-up': "url('/icons/triangle-up.inline.svg')",
        'triangle-down': "url('/icons/triangle-down.inline.svg')",
        'triangle-right': "url('/icons/triangle-right.inline.svg')",
        'triangle-left': "url('/icons/triangle-left.inline.svg')",
        'triangle-up-fill': "url('/images/icons/triangle-up-fill.inline.svg')",
        'triangle-down-fill': "url('/images/icons/triangle-down-fill.inline.svg')",
        'triangle-left-fill': "url('/images/icons/triangle-left-fill.inline.svg')",
        'triangle-right-fill': "url('/images/icons/triangle-right-fill.inline.svg')",
        'arrow-down': "url('/icons/arrow-down.inline.svg')",
        'arrow-up': "url('/icons/arrow-up.inline.svg')",
        'lock-calc-white': "url('/images/icons/lock-calc-white.svg')",
        'lock-calc-black': "url('/images/icons/lock-calc-black.svg')",
        'black-to-transparent-gradient': 'linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 58.35%)',
      },
      keyframes: {
        'swipeRight': {
          '0%': {
            transform: 'translateX(100%)',
            opacity: '0',
          },
          '100%': {
            transform: 'translateX(0%)',
            opacity: '1',
          },
        },
        'swipeDown': {
          '0%': {
            opacity: '0',
            position: 'sticky',
            transform: 'translateY(-134px)',
            top: '0',
          },
          '100%': {
            opacity: '1',
            position: 'sticky',
            transform: 'translateY(0)',
            top: '0',
          },
        },
        'swipeUp': {
          '0%': {
            opacity: '1',
            position: 'sticky',
            transform: 'translateY(0)',
            top: '0',
          },
          '100%': {
            opacity: '0',
            position: 'sticky',
            transform: 'translateY(-134px)',
            top: '0',
          },
        },
        'accordion-down': {
          from: {
            transform: 'scaleY(0)',
            transformOrigin: 'top',
            height: '0',
          },
          to: {
            transform: 'scaleY(1)',
            transformOrigin: 'top',
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'swipeDown': 'swipeDown 0.5s ease-out forwards',
        'swipeUp': 'swipeUp 0.5s ease-out forwards',
      },
      gridTemplateColumns: {
        '24': 'repeat(24, minmax(0, 1fr))',
      },
      gridColumn: {
        'span-13': 'span 13 / span 13',
        'span-14': 'span 14 / span 14',
        'span-15': 'span 15 / span 15',
        'span-16': 'span 16 / span 16',
        'span-17': 'span 17 / span 17',
        'span-18': 'span 18 / span 18',
        'span-19': 'span 19 / span 19',
        'span-20': 'span 20 / span 20',
        'span-21': 'span 21 / span 21',
        'span-22': 'span 22 / span 22',
        'span-23': 'span 23 / span 23',
        'span-24': 'span 24 / span 24',
      },
      gridColumnStart: {
        '13': '13',
        '14': '14',
        '15': '15',
        '16': '16',
        '17': '17',
        '18': '18',
        '19': '19',
        '20': '20',
        '21': '21',
        '22': '22',
        '23': '23',
        '24': '24',
      },
      width: {
        '1/8': '12.5%',
        '2/8': '25%',
        '3/8': '37.5%',
        '4/8': '50%',
        '5/8': '62.5%',
        '6/8': '75%',
        '7/8': '87.5%',
        '8/8': '100%',
      },
      fontSize: {
        '3xs': [
          '.5rem',
          {
            lineHeight: '0.625rem',
          },
        ],
        '2xs': [
          '.625rem',
          {
            lineHeight: '0.75rem',
          },
        ],
        'xs': [
          '.75rem',
          {
            lineHeight: '1rem',
          },
        ],
        'sm': [
          '.875rem',
          {
            lineHeight: '1.25rem',
          },
        ],
        'base': [
          '1rem',
          {
            lineHeight: '1.25rem',
          },
        ],
        'lg': [
          '1.125rem',
          {
            lineHeight: '1.75rem',
          },
        ],
        'xl': [
          '1.25rem',
          {
            lineHeight: '1.75rem',
          },
        ],
        '2xl': [
          '1.5rem',
          {
            lineHeight: '2rem',
          },
        ],
        '3xl': [
          '1.875rem',
          {
            lineHeight: '2.25rem',
          },
        ],
        '4xl': [
          '2.25rem',
          {
            lineHeight: '2.5rem',
          },
        ],
        '5xl': [
          '3rem',
          {
            lineHeight: '3rem',
          },
        ],
        '6xl': [
          '3.75rem',
          {
            lineHeight: '3.75rem',
          },
        ],
      },
      borderRadius: {
        'none': '0',
        'xs': '0.0625rem',
        'sm': '0.125rem',
        'base': '0.25rem',
        'md': '0.375rem',
        'lg': '0.5rem',
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.375rem',
        '4xl': '1.5rem',
        '5xl': '2rem',
        'full': '99999px',
      },
      order: {
        0: '0',
        13: '13',
        14: '14',
        15: '15',
        16: '16',
        17: '17',
        18: '18',
        19: '19',
        20: '20',
        21: '21',
        22: '22',
        23: '23',
        24: '24',
      },
    },
    fontFamily: {
      sans: [
        'Montserrat, "Montserrat Fallback"',
        {
          fontFeatureSettings: "'ss02' on",
        },
      ],
    },
  },
  plugins: [
    function ({ addUtilities }: { addUtilities: (utilities: Record<string, any>) => void }) {
      const newUtilities = {
        '.scrollbar-hide': {
          /* Firefox */
          'scrollbar-width': 'none',
          /* IE and Edge */
          '-ms-overflow-style': 'none',
          /* Safari and Chrome */
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        },
      }
      addUtilities(newUtilities)
    },
  ],
  corePlugins: {
    preflight: true,
  },
  safelist: [
    'debug-outline',
    'max-w-screen-sm',
    'max-w-screen-md',
    'max-w-screen-lg',
    'max-w-screen-xl',
    'max-w-screen-2xl',
    'max-w-screen-3xl',
    'col-span-22',
  ],
}

export default config
