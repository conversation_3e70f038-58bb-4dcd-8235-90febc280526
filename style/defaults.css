@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply text-base bg-background text-paragraph;
    font-weight: 400;
    /*line-height: 138.8%;*/
    @apply leading-tight;
  }

  body {
    @apply bg-background min-h-screen relative;
    font-family: '__Montserrat_ee4939'; /* TODO to be removed fix font on local env */
  }

  select {
    @apply border border-zinc-300 rounded-3xl text-xs py-2.5 font-bold pr-8 pl-4 appearance-none bg-triangle-down bg-white bg-no-repeat bg-[right_1rem_center] cursor-pointer;
  }

  .accordion {
    @apply bg-background rounded-2xl py-6 px-8 mb-6 font-bold cursor-pointer bg-triangle-down-fill bg-no-repeat bg-[right_1rem_center];
  }

  .accordion.active {
    @apply bg-triangle-up-fill;
  }

  .accordion.active + .home-accordion-panel {
    @apply !flex px-6 pt-0 pb-10 animate-accordion-down;
  }

  .hero-slide.swiper-slide {
    flex-shrink: 1 !important;
  }
}

@layer utilities {
  .hide-second-plus-sm > div .separator-plus {
    display: flex;
  }

  @media (max-width: 639px) {
    .hide-second-plus-sm > div:nth-child(even) .separator-plus {
      display: none;
    }
  }

  @media (min-width: 640px) and (max-width: 767px) {
    .hide-second-plus-sm > div:nth-child(3n) .separator-plus {
      display: none;
    }
  }

  @media (min-width: 768px) and (max-width: 1279px) {
    .hide-second-plus-sm > div:nth-child(4n) .separator-plus {
      display: none;
    }
  }
}
