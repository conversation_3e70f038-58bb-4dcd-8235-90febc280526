@tailwind base;

@layer base {
  .debug-outline div {
    outline: 1px solid #f00 !important;
  }
  .debug-outline span {
    outline: 1px solid #0066ff !important;
  }
  .debug-outline img {
    outline: 1px solid #25b000 !important;
  }

  .spinner {
    animation: rotate 2s linear infinite;
    z-index: 200;
  }

  .spinner .path {
    stroke-linecap: round;
    animation: dash 1.5s ease-in-out infinite;
  }

  @keyframes rotate {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes dash {
    0% {
      stroke-dasharray: 1, 150;
      stroke-dashoffset: 0;
    }
    50% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -35;
    }
    100% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -124;
    }
  }

  .full-cover {
    @apply absolute top-0 right-0 left-0 bottom-0;
  }

  .no-focus-outline {
    @apply focus:ring-0 focus:border-none focus-visible:outline-none;
  }

  .col {
    @apply flex flex-col;
  }

  .row {
    @apply flex flex-row;
  }

  .card {
    @apply bg-white shadow rounded-md;
    @apply p-4 w-fit;
  }

  .newsletter-bg {
    margin: -1px 0;  /* Fixes chrome bug */
    background: linear-gradient(to bottom, transparent 50%, #2D3132 50%);
  }
}
