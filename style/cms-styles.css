.flexibleforms_form input {
  @apply border-2 rounded-full  p-2
}
.flexibleforms_form textarea {
  @apply border-2 rounded-xl  p-2
}
.flexibleforms_form h3 {
  @apply text-xl font-bold py-4
}
.flexibleforms_form .form-list {
  @apply list-none flex gap-6 flex-wrap;
}
.flexibleforms_form .options-list {
  @apply list-none flex  flex-col py-2;
}
.flexibleforms_form .d-inline-block {
  @apply inline-block
}
.flexibleforms_form button {
  @apply bg-primary p-2 px-4 rounded-full text-base
}
.raw-content p {
  @apply mb-5;
}

.raw-content a {
  @apply text-primary underline;
}

.static-button {
  @apply bg-primary text-white font-bold rounded-3xl py-3 px-6 cursor-pointer;
}

.raw-content a.static-button {
  @apply text-white no-underline font-medium uppercase text-2xs tracking-widest inline-block;
}


@media (max-width: 639px) {
  a.static-button {
    @apply text-xs;
  }
}

.raw-content h2 {
  @apply text-3xl mb-2;
}

.raw-content ul {
  @apply list-disc pl-6;
}

.raw-content ul li {
  @apply mb-1;
}


.html-content ul {
  @apply list-disc pl-6 mb-4;
}

.html-content ul li {
  @apply text-paragraph mb-2;
}

.html-content p {
  @apply text-paragraph mb-4;
}

.html-content h2 {
  @apply text-3xl font-bold mt-12 !mb-6;
}

.html-content h3 {
  @apply text-2xl font-bold mt-12 mb-4;
}

.html-content a {
  @apply text-primary normal-case tracking-normal font-normal text-sm underline
}

.html-content .perks {
  @apply p-1 m-0 text-xs;
}

@media (max-width: 350px) {
  .html-content .perks {
    @apply max-w-[320px];
  }
}