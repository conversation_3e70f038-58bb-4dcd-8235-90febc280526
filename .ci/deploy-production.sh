#!/bin/bash
set -eo pipefail

# Script for deploying to remote server via BitBucket pipeline
# This script uses direct SSH connection (no VPN needed)

# Check if all required parameters are provided
if [ $# -ne 4 ]; then
    echo "Usage: $0 <REMOTE_USER> <REMOTE_HOST> <REMOTE_PATH> <REMOTE_BRANCH>"
    echo "Example: $0 ubuntu example.com /var/www/app master"
    exit 1
fi

# Extract parameters
REMOTE_USER="$1"
REMOTE_HOST="$2"
REMOTE_PATH="$3"
REMOTE_BRANCH="$4"

# SSH key path (consider using environment variable)
SSH_KEY="${SSH_KEY_PATH:-$HOME/.ssh/stoyan_atanasoff_pfgbulgaria_com}"

# Validate SSH key exists
if [ ! -f "${SSH_KEY}" ]; then
    echo "Error: SSH key not found at ${SSH_KEY}"
    echo "Please ensure the SSH key is properly configured in repository variables"
    exit 1
fi

echo "Starting deployment process..."
echo "Target: ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}"

# Create log directory (optional, since we're using pipeline logging)
mkdir -p logs

# Test SSH connectivity first
echo "Testing SSH connection..."
ssh -i "${SSH_KEY}" -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=10 -o BatchMode=yes ${REMOTE_USER}@${REMOTE_HOST} exit
if [ $? -ne 0 ]; then
    echo "Error: Failed to establish SSH connection"
    exit 1
fi

# Now proceed with deployment
echo "Connecting to remote server via SSH..."
ssh -i "${SSH_KEY}" -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=30 ${REMOTE_USER}@${REMOTE_HOST} << EOF
    # Enable strict error handling
    set -eo pipefail
    # Print commands and their arguments as they are executed
    set -x

    # Step 1: Go to frontend directory
    echo "Changing to directory: ${REMOTE_PATH}"
    cd ${REMOTE_PATH}

    # Step 2: Switch to master branch
    echo "Switching to master branch..."
    git checkout ${REMOTE_BRANCH}

    # Step 3: Pull latest changes
    echo "Pulling latest changes..."
    git pull --rebase

    # Step 4: Change to parent directory
    echo "Changing to parent directory..."
    cd ..

    # Step 5: Rebuild frontend
    echo "Rebuilding frontend..."
    ./store-cli rebuild frontend

    # Step 6: Wait for 3 seconds
    echo "Waiting for rebuild to complete..."
    sleep 3

    # Step 7: Reload frontend
    echo "Reloading frontend..."
    ./store-cli reload frontend

    echo "Deployment completed successfully!"
EOF

SSH_RESULT=$?

# Check if SSH command was successful
if [ $SSH_RESULT -eq 0 ]; then
    echo "✅ Deployment script executed successfully."
    echo "🚀 Application is now live!"
    exit 0
else
    echo "❌ Deployment failed with exit code $SSH_RESULT!"
    echo "Please check the logs above for more details."
    exit 1
fi