#!/bin/bash
set -euo pipefail

# ─── CONFIGURATION ─────────────────────────────────────────────────────────────
REMOTE_PATH="${1:-${REMOTE_PATH:-/home/<USER>/stores/praktis.bg/v2.praktis.bg/frontend}}"
REMOTE_BRANCH="${2:-${REMOTE_BRANCH:-develop}}"
REBUILD_TIMEOUT="${REBUILD_TIMEOUT:-60}"
SLEEP_DURATION="${SLEEP_DURATION:-3}"
STORE_CLI="${STORE_CLI_PATH:-$REMOTE_PATH/../store-cli}"
SSH_REMOTE="${SSH_REMOTE:-pfg@localhost}"  # SSH user@host to forward socket

# ─── LOGGING COLORS ───────────────────────────────────────────────────────────
RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'; BLUE='\033[0;34m'; NC='\033[0m'
log(){ echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"; }
error(){ echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2; }
success(){ echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"; }
warning(){ echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"; }
trap 'if [ $? -ne 0 ]; then error "Script failed"; fi' EXIT

# ─── VALIDATION ────────────────────────────────────────────────────────────────
log "Validating environment..."
[[ -d "$REMOTE_PATH" ]] || { error "Directory '$REMOTE_PATH' not found"; exit 1; }
command -v git >/dev/null || { error "git not found"; exit 1; }
[[ -x "$STORE_CLI" ]] || { error "store-cli not found/executable at '$STORE_CLI'"; exit 1; }
command -v ssh >/dev/null || { error "ssh not found"; exit 1; }
success "Environment validation passed"

# ─── DOCKER SOCKET FORWARDING ──────────────────────────────────────────────────
log "Forwarding Docker socket via SSH..."

SSH_SOCK="/tmp/docker.sock"
rm -f "$SSH_SOCK"

# Forward the host's Docker socket into this short, absolute path
ssh -nNT \
  -o StreamLocalBindUnlink=yes \
  -L "$SSH_SOCK:/var/run/docker.sock" \
  "$SSH_REMOTE" &
SSH_PID=$!

export DOCKER_HOST="unix://$SSH_SOCK"
log "DOCKER_HOST set to $DOCKER_HOST"


# ─── DEPLOY FUNCTION ───────────────────────────────────────────────────────────
deploy() {
  log "Deploying: $REMOTE_PATH@$REMOTE_BRANCH"
  cd "$REMOTE_PATH"
  [[ -d .git ]] || { error "Not a Git repo: '$REMOTE_PATH'"; exit 1; }

  if [[ -n "$(git status --porcelain)" ]]; then
    warning "Uncommitted changes detected"
    git status --short
  fi

  git fetch --all || { error "git fetch failed"; exit 1; }
  git checkout "$REMOTE_BRANCH" || { error "git checkout failed"; exit 1; }
  git pull --rebase origin "$REMOTE_BRANCH" || { error "git pull failed"; exit 1; }

  log "Starting rebuild with Docker"
  cd "$REMOTE_PATH/.."
  pwd
  ./store-cli rebuild frontend || { error "rebuild failed"; exit 1; }

#  log "Waiting up to ${REBUILD_TIMEOUT}s for rebuild to finish"
#  timeout "$REBUILD_TIMEOUT" bash -c \
#    'while pgrep -f "store-cli.*rebuild" >/dev/null; do sleep 1; done'

  log "Pausing ${SLEEP_DURATION}s"
  sleep "$SLEEP_DURATION"

  log "Reloading frontend"
  ./store-cli reload frontend || { error "reload failed"; exit 1; }

  success "Deployment completed ✅"
}

# ─── MAIN ──────────────────────────────────────────────────────────────────────
log "=== Shell Runner Deployment Script ==="
log "Using store-cli: $STORE_CLI"
deploy

kill "$SSH_PID" || true
rm -f "$SSH_SOCK"

success "Docker socket tunnel closed"
