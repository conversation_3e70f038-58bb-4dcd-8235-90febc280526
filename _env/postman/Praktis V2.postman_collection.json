{"info": {"_postman_id": "ce39157a-ea6b-4b09-be2d-c11869cc8357", "name": "Praktis V2", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "25933882"}, "item": [{"name": "Static Data", "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Side Section", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "fragment AppMenu on Menu {\n  categories {\n    url\n    thumbnail\n    name\n    sideSection {\n        imageUrl\n        rawHtml\n    }\n  }\n}\n\nquery GetStoreStaticData {\n    getStaticContent {\n    menu {\n      ...AppMenu\n    }\n  }\n}\n", "variables": "{\n    \"username\": \"pfg_admin\",\n    \"password\": \"pfg_admin\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Full static data", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "fragment AppMenu on Menu {\n  categories {\n    url\n    thumbnail\n    name\n    sideSection\n    children {\n      name\n      url\n    }\n  }\n}\n\nfragment AppContacts on StoreContacts {\n  general {\n    phone\n  }\n  onlineStore {\n    phone\n  }\n}\n\nfragment AppFooter on Footer {\n  columns {\n    links {\n      ...AppLink\n    }\n    title\n  }\n  appLinks {\n    android\n    ios\n  }\n  contacts {\n    ...AppContacts\n  }\n  social {\n    facebook\n    viber\n    youtube\n  }\n}\n\nfragment AppLink on Link {\n  href\n  text\n  title\n}\n\nquery GetStoreStaticData {\n    getStaticContent {\n    menu {\n      ...AppMenu\n    }\n    footer {\n      ...AppFooter\n    }\n    messages {\n      privacy\n      newsletter\n    }\n    store {\n      baseUrl\n      contacts {\n        ...AppContacts\n      }\n    }\n  }\n}\n", "variables": "{\n    \"username\": \"pfg_admin\",\n    \"password\": \"pfg_admin\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "CMS Block", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetBlock($id: String!) {\n    getStaticBlock(identifier: $id) {\n        content\n        identifier\n        title\n    }\n}", "variables": "{\n    \"id\": \"асдасд\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "GDPR", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetStatic {\n    getStaticContent {\n        apiKeys {\n            facebookLoginAppId\n            googleLoginClientId\n            googleMaps\n            googleRecaptchaKey\n        }\n        messages {\n            newsletter\n        }\n       gdpr {\n        __typename\n        rootId\n        clarityId\n        pixelId\n        gtagId\n        extraGTagsIds\n        modal {\n            __typename\n            title\n            content \n            cookieGroups {\n                __typename\n                id\n                title\n                content\n                cookiePatterns\n                grants\n                vendors {\n                    __typename\n                    keys\n                    config {\n                        __typename\n                        id\n                        name\n                        url \n                        cookieCount\n                        cookieDetails {\n                            __typename\n                            id\n                            name\n                            type\n                            description\n                            expiration\n                        }\n                    }\n                }\n            }\n        }\n       }\n    }\n}", "variables": "{\n    \"id\": \"асдасд\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Brands", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetBrands($featured: Boolean!) {\n    getBrands(featured: $featured) {\n        name\n        url {\n            href\n            text\n        }\n        image {\n            alt\n            src\n            title\n        }\n    }\n}", "variables": "{\n    \"featured\": false\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Brands V2", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetBrands($featured: Boolean!) {\n    getBrandsV2(featured: $featured) {\n        name\n        url {\n            href\n            text\n        }\n        image {\n            alt\n            src\n            title\n        }\n    }\n}", "variables": "{\n    \"featured\": false\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Static", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "fragment Cat on MenuItem {\n    id\n    url\n    thumbnail  \n    name\n    children {\n        id\n        url\n        thumbnail\n        name\n        sideSection {\n            imageUrl\n            rawHtml\n        }\n        children {\n            id\n            url\n            thumbnail\n            name\n        }\n    }\n}\nquery GetStatic {\n    getStaticContent {\n        menu {\n            categories {\n                ...Cat \n            }\n        }\n        footer {\n            columns {\n                links {\n                    href\n                    text\n                }\n                title\n            }\n            appLinks {\n                android\n                ios\n            }\n            contacts {\n                general {\n                    phone\n                }\n                onlineStore {\n                    phone\n                }\n            }\n            social {\n                facebook\n                viber\n                youtube\n            }\n        }\n        store {\n            contacts {\n                general {\n                    phone\n                }\n                onlineStore {\n                    phone\n                }\n            }\n        }\n    }\n}", "variables": "{\n    \"username\": \"pfg_admin\",\n    \"password\": \"pfg_admin\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "GetStatic - Footer only", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetStatic {\n    getStaticContent {\n        footer {\n            columns {\n                links {\n                    href\n                    text\n                }\n                title\n            }\n            appLinks {\n                android\n                ios\n            }\n            contacts {\n                general {\n                    phone\n                }\n                onlineStore {\n                    phone\n                }\n            }\n            social {\n                facebook\n                viber\n                youtube\n            }\n        }\n    }\n}", "variables": "{\n    \"username\": \"pfg_admin\",\n    \"password\": \"pfg_admin\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Static Config Messages", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetStatic {\n    getStaticContent {\n        messages {\n            newsletter\n            sendInquiryMessage\n        }\n    }\n}", "variables": "{\n    \"username\": \"pfg_admin\",\n    \"password\": \"pfg_admin\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Router", "item": [{"name": "Catalog", "item": [{"name": "TESTS", "item": [{"name": "Test - New Catalog Indexes", "item": [{"name": "Banner", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    data {\n      ... on CatalogPage {\n        category {\n            __typename\n            ... on Category {\n                banner {\n                    image {\n                        alt\n                        title\n                        src\n                    }\n                    url\n                }\n            }\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"stroitelstvo/lepila-i-silikoni/silikoni\",\n    \"query\": []\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Filters", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    data {\n      ... on CatalogPage {\n        layout\n        state {\n          filters {\n            applied {\n              label\n              value\n              requestVar\n              attributeCode\n            }\n            available {\n              position\n              type\n              requestVar\n              attributeCode\n              label\n              options {\n                label\n                value\n              }\n            }\n          }\n          pager {\n            page\n            pageSize\n            totalItems\n            totalPages\n          }\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"stroitelstvo/lepila-i-silikoni/silikoni\",\n    \"query\": [\n        {\n            \"name\": \"price\",\n            \"value\": \"13-27\"\n        }\n    ]\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Filters Copy", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    data {\n      ... on CatalogPage {\n        layout\n        state {\n          filters {\n            applied {\n              label\n              value\n              requestVar\n              attributeCode\n            }\n            available {\n              position\n              type\n              requestVar\n              attributeCode\n              label\n              options {\n                label\n                value\n              }\n            }\n          }\n          pager {\n            page\n            pageSize\n            totalItems\n            totalPages\n          }\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"stroitelstvo/lepila-i-silikoni/silikoni\",\n    \"query\": [\n        {\n            \"name\": \"price\",\n            \"value\": \"13-27\"\n        },\n        {\n            \"name\": \"p\",\n            \"value\": 2\n        }\n    ]\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Filters - v2", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    data {\n      ... on CatalogPage {\n        layout\n        state {\n          filters {\n            applied {\n              label\n              value\n              requestVar\n              attributeCode\n            }\n            available {\n              attributeCode\n              label\n            }\n          }\n          pager {\n            page\n            pageSize\n            totalItems\n            totalPages\n          }\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"gradina/gradinska-tehnika-i-oburudvane/rezachki\",\n    \"query\": [\n    ]\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Sort Price", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    data {\n      ... on CatalogPage {\n        products {\n            __typename\n            ... on SimpleProduct {\n                id,\n                sku\n                price {\n                    price {\n                        currency\n                        value\n                    }\n                    special {\n                        currency\n                        value\n                    }\n                }\n            }\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"gradina/gradinska-tehnika-i-oburudvane/rezachki\",\n    \"query\": [\n        {\n            \"name\": \"p\",\n            \"value\": 1\n        },\n        {\n            \"name\": \"sort\",\n            \"value\": \"price\"\n        },\n        {\n            \"name\": \"order\",\n            \"value\": \"desc\"\n        }\n    ]\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Get Catalog State", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    data {\n      ... on CatalogPage {\n        layout\n        state {\n            availableSorts {\n                dir\n                label\n                value\n            }\n          filters {\n            applied {\n              label\n              value\n              requestVar\n              attributeCode\n            }\n            available {\n              position\n              type\n              requestVar\n              attributeCode\n              label\n              options {\n                label\n                value\n              }\n            }\n          }\n          pageSize\n          sort {\n            value\n            dir\n          }\n        }\n        category {\n          id\n          name\n          url\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"banya/granitogres\",\n    \"query\": [\n        {\"name\": \"price\",\"value\": \"1699-4582.8\"},\n        {\"name\": \"sort\",\"value\": \"best_value\"},\n        {\"name\": \"order\",\"value\": \"desc\"},\n        {\"name\": \"limit\",\"value\": \"48\"}\n    ]\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "get product prices", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    data {\n      ... on CatalogPage {\n        products {\n          ...ProductCard\n        }\n      }\n    }\n  }\n}\n\nfragment ProductCard on SimpleProduct {\n  id\n  sku\n  name\n  urlKey\n  image {\n    alt\n    src\n  }\n  price {\n    price {\n      currency\n      value\n    }\n    special {\n      currency\n      value\n    }\n  }\n  energyLabel {\n      image {\n          src\n      }\n      infoUrl\n      labelUrl\n  }\n  labels {\n      buyCheap\n      freeDelivery\n      fromBrochure\n      other\n  }\n}", "variables": "{\n    \"url\": \"podovi-i-stenni-pokritiya/meki-nastilki\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Category", "item": [{"name": "Get Category - Banners", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    data {\n      ... on CatalogPage {\n        layout\n        category {\n          id\n          name\n          url\n          banner {\n            image {\n                title\n                alt\n                src\n            }\n            url\n          }\n          widgets {\n            __typename\n          }\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"gradina\",\n    \"query\": []\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Special Categories", "item": [{"name": "Get Category - Product Copy", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    data {\n      ... on CatalogPage {\n        products {\n          ...ProductCard\n        }\n      }\n    }\n  }\n}\n\nfragment ProductCard on SimpleProduct {\n  id\n  sku\n  name\n  urlKey\n  image {\n    alt\n    src\n  }\n  price {\n    price {\n      currency\n      value\n    }\n    special {\n      currency\n      value\n    }\n  }\n  energyLabel {\n      image {\n          src\n      }\n      infoUrl\n      labelUrl\n  }\n  labels {\n      buyCheap\n      freeDelivery\n      fromBrochure\n      other\n  }\n}", "variables": "{\n    \"url\": \"kupi-izgodno\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get -discounted", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    data {\n      ... on CatalogPage {\n        products {\n          ...ProductCard\n        }\n      }\n    }\n  }\n}\n\nfragment ProductCard on SimpleProduct {\n  id\n  sku\n  name\n  urlKey\n  image {\n    alt\n    src\n  }\n  price {\n    price {\n      currency\n      value\n    }\n    special {\n      currency\n      value\n    }\n  }\n  energyLabel {\n      image {\n          src\n      }\n      infoUrl\n      labelUrl\n  }\n  labels {\n      buyCheap\n      freeDelivery\n      fromBrochure\n      other\n  }\n}", "variables": "{\n    \"url\": \"spetsialni-predlozheniya\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get -discounted filters", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    data {\n      ... on CatalogPage {\n        layout\n        state {\n          filters {\n            applied {\n              label\n              value\n              requestVar\n              attributeCode\n            }\n            available {\n              position\n              type\n              requestVar\n              attributeCode\n              label\n              options {\n                label\n                value\n              }\n            }\n          }\n          pager {\n            page\n            pageSize\n            totalItems\n            totalPages\n          }\n          sort {\n            value\n            dir\n          }\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"spetsialni-predlozheniya\",\n    \"query\": [\n        {\n            \"name\": \"price\",\n            \"value\": \"13-27\"\n        },\n                {\n            \"name\": \"brand\",\n            \"value\": \"951\"\n        }\n    ]\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Get Category - Filters", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    data {\n      ... on CatalogPage {\n        layout\n        state {\n          filters {\n            applied {\n              label\n              value\n              requestVar\n              attributeCode\n            }\n            available {\n              position\n              type\n              requestVar\n              attributeCode\n              label\n              options {\n                label\n                value\n              }\n            }\n          }\n          pager {\n            page\n            pageSize\n            totalItems\n            totalPages\n          }\n          sort {\n            value\n            dir\n          }\n        }\n        category {\n          id\n          name\n          url\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"mashini-i-instrumenti/darvoobrabotvashti-instrumenti/tsirkulyari-nastolni\",\n    \"query\": [\n        {\n            \"name\": \"brand\",\n            \"value\": 4531\n        }\n    ]\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Category - Filters Applied", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    data {\n      ... on CatalogPage {\n        layout\n        state {\n          filters {\n            applied {\n              label\n              value\n              requestVar\n              attributeCode\n            }\n            available {\n              position\n              type\n              requestVar\n              attributeCode\n              label\n              options {\n                label\n                value\n              }\n            }\n          }\n        }\n\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"dom/perdeta-i-kornizi\",\n    \"query\": [\n        {\n            \"name\": \"brand\",\n            \"value\": 5271\n        }\n    ]\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Category - Breadcrumbs", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    breadcrumbs {\n      ...IBreadCrumb\n    }\n  }\n}\n\nfragment IBreadCrumb on Breadcrumb {\n  label\n  url\n  siblings {\n    label\n    url\n  }\n  children {\n      label\n      url\n  }\n}\n\n\n", "variables": "{\n    \"url\": \"gradina\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Category - Landing", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    breadcrumbs {\n      label\n      url\n    }\n    status {\n      redirectUrl\n      statusCode\n    }\n    data {\n      __typename\n      ... on CatalogPage {\n        layout\n        category {\n          ...IAppCategory\n          widgets {\n            ... on CategoryLinkWidget {\n              image {\n                alt\n                mobileSrc\n                src\n                title\n              }\n              title\n              url {\n                href\n                text\n                title\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\nfragment IAppCategory on Category {\n  id\n  name\n  url\n  description\n  banner {\n    image {\n      title\n      src\n      mobileSrc\n    }\n    url\n  }\n  image {\n    alt\n    src\n  }\n  icon {\n    alt\n    src\n  }\n}\n\n\n", "variables": "{\n    \"url\": \"dom/edra-tehnika-za-doma\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Category - Product", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    data {\n      ... on CatalogPage {\n        products {\n          ...ProductCard\n        }\n      }\n    }\n  }\n}\n\nfragment ProductCard on SimpleProduct {\n  id\n  sku\n  name\n  urlKey\n  image {\n    alt\n    src\n  }\n  price {\n    price {\n      currency\n      value\n    }\n    special {\n      currency\n      value\n    }\n  }\n  energyLabel {\n      image {\n          src\n      }\n      infoUrl\n      labelUrl\n  }\n  labels {\n      buyCheap\n      freeDelivery\n      fromBrochure\n      other\n  }\n}", "variables": "{\n    \"url\": \"podovi-i-stenni-pokritiya/meki-nastilki\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Category - Product Prices", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    data {\n      ... on CatalogPage {\n        products {\n          ...ProductCard\n        }\n      }\n    }\n  }\n}\n\nfragment ProductCard on SimpleProduct {\n  id\n  sku\n  name\n  urlKey\n  price {\n    price {\n      currency\n      value\n    }\n    special {\n      currency\n      value\n    }\n  }\n}", "variables": "{\n    \"url\": \"stroitelstvo/suho-stroitelstvo/gipskarton\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Product", "item": [{"name": "Get  Product", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    status {\n      redirectUrl\n      statusCode\n    }\n    data {\n      __typename\n      ... on ProductPage {\n        product {\n          ...ProductData\n        }\n      }\n    }\n  }\n}\n\nfragment ProductData on SimpleProduct {\n  id\n  sku\n  name\n  shortDescription\n  description\n}", "variables": "{\n    \"url\": \"vertikalen-boyler-atlantic-genius-steatite-wi-fi-80l-1800w\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get  Stores", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    status {\n      redirectUrl\n      statusCode\n    }\n    data {\n      __typename\n      ... on ProductPage {\n        product {\n          ...ProductData\n        }\n      }\n    }\n  }\n}\n\nfragment ProductData on SimpleProduct {\n  id\n  skuAvailability {\n    available\n    sample\n    store {\n        ID\n    }\n  }\n}", "variables": "{\n    \"url\": \"benzinova-motofreza-raider-rd-t13-2-1-skorosti-5-2kw\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get  Stock", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    data {\n      __typename\n      ... on ProductPage {\n        product {\n            ... on SimpleProduct {\n                stock {\n                    blockForSale\n                    hasImages\n                    inStock\n                    manageStock\n                    qty\n                    zeronBlockedDelivery\n                    zeronSiteStatus\n                }\n                measures {\n                    base\n                    secondary\n                    secondaryQty\n                }\n            }\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"sadilo-za-lukovitsi-bradas-de-luxe\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Product Gallery", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    status {\n      redirectUrl\n      statusCode\n    }\n    data {\n      __typename\n      ... on ProductPage {\n        product {\n          ...ProductData\n        }\n      }\n    }\n  }\n}\n\nfragment ProductData on SimpleProduct {\n  id\n  sku\n  name\n\n  image {\n    alt\n    src\n  }\n  gallery {\n    position\n    image {\n      alt\n      src\n    }\n  }\n}", "variables": "{\n    \"url\": \"benzinova-motofreza-ziel-bk-80-5-2kw-brazdir\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get  Product - related", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    status {\n      redirectUrl\n      statusCode\n    }\n    data {\n      __typename\n      ... on ProductPage {\n        product {\n            ...ProductData\n        }\n        boughtTogether {\n            ...ProductData\n        }\n        widgets {\n            identifier\n            tabs {\n                title\n                products {\n                    __typename\n                }\n            }\n            title\n        }\n      }\n    }\n  }\n}\n\nfragment ProductData on SimpleProduct {\n  id\n  sku\n  name\n  price {\n    price {\n      currency\n      value\n    }\n    special {\n      currency\n      value\n    }\n    specialFrom\n    specialTo\n  }\n}", "variables": "{\n    \"url\": \"akumulatoren-verizhen-trion-ziel-zl6601-12v-s-bateriya-i-zaryadno\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get  Product Static Blocks", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    status {\n      redirectUrl\n      statusCode\n    }\n    data {\n      __typename\n      ... on ProductPage {\n        staticBlocks {\n          content\n          identifier\n          title\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"led-plafoniera-ziel-lighting-shiny-star-24w-6500k-ip40\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Product Availabilaty", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    data {\n      __typename\n      ... on ProductPage {\n        product {\n          ... on SimpleProduct {\n            skuAvailability {\n                available\n                sample\n                store {\n                    name\n                    location {\n                        lat\n                        lng\n                    }\n                }\n            }\n          }\n        }\n    }\n  }\n}\n}", "variables": "{\n    \"url\": \"vertika<PERSON>-boy<PERSON>-eldom-spectra-sv08044-80l-3000w-s\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Breadcrumbs", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    breadcrumbs {\n      label\n      url\n      children {\n        label\n        url\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"vertika<PERSON>-boy<PERSON>-eldom-spectra-sv08044-80l-3000w-s\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get  Product - Blocks", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    data {\n      ... on ProductPage {\n        staticBlocks {\n          content\n          identifier\n          title\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"laminiran-parket-terraclick-t-73b-lizbon-8mm-ac3-31\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Brand / Splash Page", "item": [{"name": "Get Brand", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    status {\n      redirectUrl\n      statusCode\n    }\n    data {\n      __typename\n      ... on CatalogPage {\n        layout \n        state {\n            availableSorts {\n                dir\n                label\n                value\n            }\n            filters {\n                applied {\n                    attributeCode\n                    label\n                    requestVar\n                    value\n                }\n                available {\n                    attributeCode\n                    label\n                    options {\n                        label\n                        value\n                    }\n                    position\n                    requestVar\n                    type\n                }\n            }\n            pager {\n                page\n                pageSize\n                totalItems\n            }\n            sort {\n                dir\n                value\n            }\n        }\n        products {\n            __typename\n            ... on SimpleProduct {\n                id\n                brand {\n                    image {\n                        alt\n                        src\n                    }\n                    name\n                    url {\n                        href\n                        text\n                        title\n                    }\n                }\n            }\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"tesy\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Brand - APP", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    status {\n      redirectUrl\n      statusCode\n    }\n    data {\n      __typename\n      ... on CatalogPage {\n        layout \n        state {\n            availableSorts {\n                dir\n                label\n                value\n            }\n            filters {\n                applied {\n                    attributeCode\n                    label\n                    requestVar\n                    value\n                }\n                available {\n                    attributeCode\n                    label\n                    options {\n                        label\n                        value\n                    }\n                    position\n                    requestVar\n                    type\n                }\n            }\n            pager {\n                page\n                pageSize\n                totalItems\n            }\n            sort {\n                dir\n                value\n            }\n        }\n        products {\n            __typename\n            ... on SimpleProduct {\n                id\n                brand {\n                    image {\n                        alt\n                        src\n                    }\n                    name\n                    url {\n                        href\n                        text\n                        title\n                    }\n                }\n            }\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"cat-powertools\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Brand - Filters", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    data {\n      ... on CatalogPage {\n        layout\n        state {\n          filters {\n            applied {\n              label\n              value\n              requestVar\n              attributeCode\n            }\n            available {\n              position\n              type\n              requestVar\n              attributeCode\n              label\n              options {\n                label\n                value\n              }\n            }\n          }\n          pager {\n            page\n            pageSize\n            totalItems\n          }\n          sort {\n            value\n            dir\n          }\n        }\n        category {\n          __typename\n        }\n      }\n    }\n  }\n}", "variables": "{\n    \"url\": \"tesy\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "CMS", "item": [{"name": "Get <PERSON>", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetPage($id: ID!) {\n  getStaticPage(pageId: $id) {\n    identifier\n    title\n    content\n  }\n}\n", "variables": "{\n    \"id\": \"18\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Block", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetBlock {\n  getStaticBlock(identifier: \"contacts_info\") {\n    identifier\n    title\n    content\n  }\n}\n", "variables": "{\n    \"id\": \"18\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Full Router - every route", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRouteData($url: String!, $query: [QueryParam]) {\n  route(url: $url, query: $query) {\n    ...DynamicRouteData\n  }\n}\n\nfragment DynamicRouteData on Page {\n  breadcrumbs {\n    label\n    url\n    children {\n      label\n      url\n    }\n  }\n  status {\n    redirectUrl\n    statusCode\n  }\n  data {\n    ...DynamicPageData\n  }\n}\n\nfragment DynamicPageData on PageData {\n  __typename\n  ... on CatalogPage {\n    ...CatalogPageData\n  }\n  ... on CMSPage {\n    ...CMSPageData\n  }\n  ... on ProductPage {\n    ...ProductPageData\n  }\n  ... on ErrorData {\n    code\n    message\n  }\n}\n\nfragment AppFilterState on CatalogState {\n  filters {\n    applied {\n      label\n      value\n      requestVar\n      attributeCode\n    }\n    available {\n      position\n      type\n      requestVar\n      attributeCode\n      label\n      options {\n        label\n        value\n      }\n    }\n  }\n  pageSize\n  sort {\n    value\n    dir\n  }\n}\n\nfragment AppImage on Image {\n  src\n  mobileSrc\n  alt\n  title\n}\n\nfragment AppLink on Link {\n  href\n  text\n  title\n}\n\nfragment AppPrice on Price {\n  value\n  currency\n}\n\nfragment AppBrand on Brand {\n  name\n  image {\n    ...AppImage\n  }\n  url {\n    ...AppLink\n  }\n}\n\nfragment AppCategoryView on Category {\n  id\n  name\n  url\n  description\n  image {\n    ...AppImage\n  }\n  icon {\n    ...AppImage\n  }\n  banner {\n    image {\n      ...AppImage\n    }\n    url\n  }\n  widgets {\n    ...AppCategoryViewWidget\n  }\n}\n\nfragment AppCategoryViewWidget on CategoryWidget {\n  __typename\n  ... on CategoryLinkWidget {\n    title\n    url {\n      href\n      text\n      title\n    }\n    image {\n      alt\n      src\n      title\n      mobileSrc\n    }\n  }\n}\n\nfragment AppProductPrice on ProductPrice {\n  price {\n    ...AppPrice\n  }\n  special {\n    ...AppPrice\n  }\n}\n\nfragment AppEnergyLabel on EnergyLabel {\n  image {\n    ...AppImage\n  }\n  infoUrl\n  labelUrl\n}\n\nfragment AppLabels on MagentoLabels {\n  warrantyMonths\n  buyCheap\n  freeDelivery\n  fromBrochure\n  other\n}\n\nfragment AppProductCard on Product {\n  __typename\n  ... on SimpleProduct {\n    id\n    sku\n    name\n    urlKey\n    image {\n      ...AppImage\n    }\n    price {\n      ...AppProductPrice\n    }\n    energyLabel {\n      ...AppEnergyLabel\n    }\n    labels {\n      ...AppLabels\n    }\n  }\n  ... on BundleProduct {\n    id\n    sku\n    name\n    urlKey\n    image {\n      ...AppImage\n    }\n    price {\n      ...AppProductPrice\n    }\n    labels {\n      ...AppLabels\n    }\n  }\n}\n\nfragment AppProductView on SimpleProduct {\n  id\n  sku\n  name\n  gallery {\n    image {\n      ...AppImage\n    }\n    position\n  }\n  measures {\n    base\n    secondary\n    secondaryQty\n  }\n  price {\n    ...AppProductPrice\n  }\n  description\n  shortDescription\n  brand {\n    ...AppBrand\n  }\n  energyLabel {\n    ...AppEnergyLabel\n  }\n  labels {\n    ...AppLabels\n  }\n}\n\nfragment AppBundleItem on SimpleProduct {\n  id\n  sku\n  name\n  measures {\n    base\n    secondary\n    secondaryQty\n  }\n  image {\n    ...AppImage\n  }\n  price {\n    ...AppProductPrice\n  }\n}\n\nfragment AppBundleProductView on BundleProduct {\n  id\n  sku\n  name\n  gallery {\n    image {\n      ...AppImage\n    }\n    position\n  }\n  price {\n    ...AppProductPrice\n  }\n  description\n  shortDescription\n  brand {\n    ...AppBrand\n  }\n  labels {\n    ...AppLabels\n  }\n  bundled {\n    ...AppBundleItem\n  }\n}\n\n\nfragment CatalogPageData on CatalogPage {\n  __typename\n  layout\n  state {\n    ...AppFilterState\n  }\n  category {\n    ...AppCategoryView\n  }\n  products {\n    ...AppProductCard\n  }\n}\n\nfragment CMSPageData on CMSPage {\n  __typename\n  links {\n    ...AppLink\n  }\n  identifier\n  title\n  content\n}\n\nfragment ProductView on Product {\n  __typename\n  ... on SimpleProduct {\n    ...AppProductView\n  }\n  ... on BundleProduct {\n    ...AppBundleProductView\n  }\n}\n\nfragment AppProductsSliderWidget on ProductsSliderWidget {\n  identifier\n  title\n  tabs {\n    title\n    products {\n      ...AppProductCard\n    }\n  }\n}\n\nfragment StaticBlock on CMSBlock {\n  title\n  identifier\n  content\n}\n\nfragment ProductPageData on ProductPage {\n  __typename\n  product {\n    ...ProductView\n  }\n  widgets {\n    ...AppProductsSliderWidget\n  }\n  boughtTogether {\n    ...AppProductCard\n  }\n  staticBlocks {\n    ...StaticBlock\n  }\n}\n", "variables": "{\n    \"url\": \"vertika<PERSON>-boy<PERSON>-eldom-spectra-sv08044-80l-3000w-s\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Meta", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "fragment SiteMeta on PageMeta {\n    title\n    robots\n    description\n    keywords\n    image {\n        src\n    }\n    keywords\n    canonicalUrl\n}\n\nquery GetRouteMeta($url: String!, $query: [QueryParam]) {\n  routeMeta(url: $url, query: $query) {\n    ...SiteMeta\n  }\n}\n", "variables": "{\n    \"url\": \"vertika<PERSON>-boy<PERSON>-eldom-spectra-sv08044-80l-3000w-s\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Category FULL", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    data {\n      ... on CatalogPage {\n        ...AppCatalog\n      }\n    }\n  }\n}\n\nfragment AppCatalog on CatalogPage {\n    state {\n        filters {\n            applied {\n                attributeCode\n                label\n                requestVar\n                value\n            }\n            available {\n                attributeCode\n                label\n                options {\n                    label\n                    value\n                }\n                position\n                requestVar\n            }\n        }\n        pageSize\n        sort {\n            dir\n            value\n        }\n    }\n    category {\n        ...IAppCategory\n        widgets {\n            ... on CategoryLinkWidget {\n                ...ICategoryPreviewWidget\n            }\n        }\n    }\n}\n\n\nfragment IAppCategory on Category {\n  id\n  name\n  url\n  description\n  banner {\n    image {\n      title\n      src\n      mobileSrc\n    }\n    url\n  }\n  image {\n    alt\n    src\n  }\n  icon {\n    alt\n    src\n  }\n}\n\nfragment ICategoryPreviewWidget on CategoryLinkWidget {\n  title\n  url {\n    href\n  }\n  image {\n    src\n  }\n}\n", "variables": "{\n    \"url\": \"dom/drebna-byala-tehnika/sokoiztis<PERSON>chki\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Static Page", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n  route(url: $url) {\n    breadcrumbs {\n      label\n      url\n    }\n    status {\n      redirectUrl\n      statusCode\n    }\n    data {\n      __typename\n      ... on CMSPage {\n          content\n          identifier\n          links {\n              href\n              text\n          }\n      }\n    }\n  }\n}\n\n\n", "variables": "{\n    \"url\": \"about\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Redirect Response", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetRoute($url: String!) {\n    route(url: $url) {\n        breadcrumbs {\n            label\n            url\n        }\n        status {\n            error\n            statusCode\n            redirectUrl\n        }\n        data {\n            __typename\n        }\n    }\n}", "variables": "{\n    \"url\": \"dom/otoplenie-i-ventilatsiya/ventilatsiya/ventilatori-na-stoyka/\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Stores", "item": [{"name": "Get Available stores", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetAvailableStores {\n  availableStores {\n    name\n    identity\n    address\n    phone\n    gallery {\n        alt\n        src\n        title\n        mobileSrc\n        position\n        isPrimary\n    }\n  }\n}\n", "variables": "{\n    \"url\": \"gradina\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Schedule", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetStore($uri: String!) {\n  getStore(identity: $uri) {\n    store {\n        businessHours {\n            close\n        }\n    }\n  }\n}\n", "variables": "{\n    \"uri\": \"mebeli-targovski-tsentar-stara-zagora\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Store Data", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "fragment AppStore on PraktisStore {\n    name\n    identity\n    address\n    city\n    email\n    phone\n    virtualTour\n    gallery {\n        alt\n        src\n        title\n        mobileSrc\n        isPrimary\n    }\n    location {\n        lat\n        lng\n    } \n    businessHours {\n        open\n        close\n        day\n    }\n    services {\n        iconUrl\n        name\n        url\n    }\n\n    descriptionArea {\n        backgroundImage\n        content\n        formID\n        title\n    }\n\n    noticeArea {\n        message\n        title\n        type\n    }\n\n    transportInformation\n\n    metaDescription\n    metaKeywords\n    metaTitle\n}\n\nquery GetStore($uri: String!) {\n  getStore(identity: $uri) {\n    messageBlock\n    store {\n        ...AppStore\n    }\n  }\n}\n", "variables": "{\n    \"uri\": \"praktis-plovdiv\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Search", "item": [{"name": "Search Input", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query SearchField($text: String!) {\n  search(searchQuery: $text) {\n    categories {\n        name\n        url\n    }\n    popularTerms \n    totalItems\n    products {\n        __typename\n        ... on SimpleProduct {\n            measures {\n                base\n                secondary\n                secondaryQty\n            }\n            name\n            urlKey\n            image {\n                src\n            }\n            labels {\n                buyCheap\n                freeDelivery\n                fromBrochure\n                other\n                warrantyMonths\n            }\n            energyLabel {\n                image {\n                    src\n                }\n                infoUrl\n            }\n            brand {\n                name\n                image {\n                    src\n                    title\n                }\n                url {\n                    href\n                    text\n                    title\n                }\n            }\n            price {\n                price {\n                    value\n                    currency\n                }\n                special {\n                    currency\n                    value\n                }\n                specialFrom\n                specialTo\n            }\n            image {\n                title\n                src\n            }\n        }\n    }\n  }\n}", "variables": "{\n    \"text\": \"резачка\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Search Page - FIlters", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query SearchPage($text: String!) {\n  searchPage(searchQuery: $text) {\n    status {\n        error\n        redirectUrl\n        statusCode\n    }\n    state {\n        filters {\n            applied {\n                attributeCode\n                label\n                requestVar\n                value\n            }\n            available {\n                attributeCode\n                label\n                options {\n                    label\n                    value\n                }\n                position\n                requestVar\n                type\n            }\n        }\n        availableSorts {\n            dir\n            label\n            value\n        }\n        pager {\n            page\n            pageSize\n            totalItems\n            totalPages\n        }\n        sort {\n            dir\n            value\n        }\n    }\n    title\n  }\n}\n", "variables": "{\n    \"text\": \"бойлер\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Search Page - Items", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query SearchPage($text: String!) {\n  searchPage(searchQuery: $text) {\n    status {\n        error\n        redirectUrl\n        statusCode\n    }\n    title\n    data {\n      categories {\n        name\n        url\n      }\n      popularTerms\n      products {\n        __typename\n        ... on SimpleProduct {\n          measures {\n            base\n            secondary\n            secondaryQty\n          }\n          name\n          urlKey\n          image {\n            src\n          }\n          labels {\n            buyCheap\n            freeDelivery\n            fromBrochure\n            other\n            warrantyMonths\n          }\n          energyLabel {\n            image {\n              src\n            }\n            infoUrl\n          }\n          brand {\n            name\n            image {\n              src\n              title\n            }\n            url {\n              href\n              text\n              title\n            }\n          }\n          price {\n            price {\n              value\n              currency\n            }\n            special {\n              currency\n              value\n            }\n            specialFrom\n            specialTo\n          }\n          image {\n            title\n            src\n          }\n        }\n      }\n    }\n  }\n}\n", "variables": "{\n    \"text\": \"бойлер\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Credit Calculator", "item": [{"name": "TBI Data", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query Credit($sku: String!) {\n  getCreditCalculatorTBIBank(sku: $sku) {\n    ...CalData\n  }\n}\n\n\nfragment CalData on TbiConfiguration {\n  tbi_minstojnost\n  tbi_minstojnost_bnpl\n  tbi_maxstojnost\n  tbi_maxstojnost_bnpl\n  tbi_zaglavie\n  tbi_custom_button_status\n  tbi_purcent_default\n  reklama\n  tbi_5m_purcent_default\n  tbi_5m\n  tbi_5m_categories\n  tbi_5m_manufacturers\n  tbi_5m_min\n  tbi_5m_max\n  tbi_5m_pv\n  tbi_4m\n  tbi_4m_categories\n  tbi_4m_manufacturers\n  tbi_4m_min\n  tbi_4m_max\n  tbi_4m_pv\n  tbi_6m\n  tbi_6m_categories\n  tbi_6m_manufacturers\n  tbi_6m_min\n  tbi_6m_max\n  tbi_6m_pv\n  tbi_9m\n  tbi_9m_categories\n  tbi_9m_manufacturers\n  tbi_9m_min\n  tbi_9m_max\n  tbi_9m_pv\n  tbi_12m\n  tbi_12m_categories\n  tbi_12m_manufacturers\n  tbi_12m_min\n  tbi_12m_max\n  tbi_12m_pv\n  tbi_3m_purcent\n  tbi_4m_purcent\n  tbi_6m_purcent\n  tbi_7m_purcent\n  tbi_9m_purcent\n  tbi_11m_purcent\n  tbi_12m_purcent\n  tbi_15m_purcent\n  tbi_18m_purcent\n  tbi_24m_purcent\n  tbi_30m_purcent\n  tbi_36m_purcent\n  tbi_42m_purcent\n  tbi_48m_purcent\n  tbi_54m_purcent\n  tbi_60m_purcent\n  tbi_over_5000\n  tbi_status\n  tbi_bnpl\n  tbi_button_status\n  tbi_button_text_visible\n  tbi_button_kvadrat\n  tbi_is_direct\n  tbi_is_cart\n  tbi_eur\n}", "variables": "{\n    \"sku\": \"35587046\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "BNP Pariba", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query Credit($sku: String!, $downPayment: Float!, $qty: Int!) {\n  getCreditCalculatorBNPParibas(sku: $sku, downPayment: $downPayment, qty: $qty) {\n    schemeId\n    variants {\n        apr\n        correctDownpaymentAmount\n        id\n        installmentAmount\n        maturity\n        nir\n        pricingSchemeId\n        pricingSchemeName\n        totalRepaymentAmount\n    }\n  }\n}", "variables": "{\n    \"sku\": \"35587046\",\n    \"downPayment\": 0,\n    \"qty\": 1\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Sales", "item": [{"name": "Tests", "item": [{"name": "Customer Order", "item": [{"name": "1. <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_user_token', jsonData.data.customerLogin.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-captcha-token", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CustomerLogin($u: String!, $p: String!) {\n    customerLogin(email: $u, password: $p) {\n        id\n        token\n        defaultAddress {\n            id\n            city\n        }\n        addresses {\n            id\n            city\n        }\n        invoices {\n            id\n            city\n        }\n        defaultInvoice {\n            id\n            city\n        }\n        orders {\n            incrementId\n            note\n        }\n    }\n}", "variables": "{\n    \"u\": \"<EMAIL>\",\n    \"p\": \"SecurePassword123!\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "2. <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.getNewCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}, {"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetEmptyCart {\n  getNewCart {\n    token\n  }\n}", "variables": "{\n    \"cart\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjIjoxNzM3MzIwMTAzLCJjYXJ0X2lkIjoiIiwiZXhwIjoxNzM3NDA2NTAzLCJwcm9kdWN0c19jb3VudCI6MCwic2FsdCI6IiJ9.I2aE7n_T9PznrU9DKk3atjYnbovaYtoP5i7lqW499mY\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "3. add item", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.cartItemAdd.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}, {"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CartItemAdd($cart: String!, $sku: String!) {\n  cartItemAdd(cartToken: $cart, item: {\n    baseQty: 1,\n    sku: $sku\n  }) {\n    id\n    token\n    items {\n      ...AppCartItem\n    }\n    totals {\n      ...AppCartTotal\n    }\n  }\n}\n\nfragment AppCartItem on StoreCartItem {\n  id\n  sku\n  labels {\n    freeShipping\n    usePallet\n  }\n  product {\n    __typename\n    ... on SimpleProduct {\n      id\n      sku\n    }\n  }\n  baseQty\n}\n\nfragment AppCartTotal on CartTotal {\n  amount {\n    currency\n    value\n  }\n  code\n  label\n  order\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"sku\": \"35587046\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "4. get", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.getNewCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}, {"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetCart($cart: String!) {\n  getCart(cartToken: $cart) {\n    ...AppCart\n  }\n}\n\nfragment AppCart on StoreCart {\n  token\n  id\n  storeCode\n  currency\n  couponCode\n  note\n  customer {\n    ...AppOrderCustomer\n  }\n  items {\n    ...AppCartItem\n  }\n  shipping {\n    availableMethods\n    availableIn {\n        ID\n        name\n    }\n    minAmountForFreeShippingMessage\n    hasFreeShipping\n    freeShippingAfter\n    address {\n        ...AppShippingAddress\n    }\n  }\n  paymentMethod\n  totals {\n    code\n    label\n    amount {\n      ...AppPrice\n    }\n    order\n  }\n}\n\nfragment AppCartItem on StoreCartItem {\n  id\n  sku\n  labels {\n    freeShipping\n    usePallet\n  }\n  product {\n    __typename\n    ... on SimpleProduct {\n      id\n      sku\n    }\n  }\n  baseQty\n}\n\nfragment AppOrderCustomer on OrderCustomer {\n  firstName\n  lastName\n  email\n  phone\n  invoice {\n    type\n    address\n    city\n    company {\n      eik\n      mol\n      name\n      vat\n    }\n    individual {\n      egn\n      name\n      vat\n    }\n  }\n}\n\nfragment AppShippingAddress on ShippingAddress {\n  id\n  skus\n  method\n  city\n  postCode\n  street\n  officeCode\n  storeCode\n}\n\nfragment AppPrice on Price {\n  currency\n  value\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "5. save client", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.getNewCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}, {"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: ClientInput!) {\n  cartSaveClient(cartToken: $cart, data: $data) {\n    id\n    token\n    customer {\n        ...AppOrderCustomer\n    }\n  }\n}\n\nfragment AppOrderCustomer on OrderCustomer {\n  firstName\n  lastName\n  email\n  phone\n  invoice {\n    type\n    address\n    city\n    company {\n      eik\n      mol\n      name\n      vat\n    }\n    individual {\n      egn\n      name\n      vat\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"data\": {\n        \"firstName\": \"sdfdsf\",\n        \"lastName\": \"<PERSON><PERSON>\",\n        \"phone\": \"+359877123456\",\n        \"email\": \"<EMAIL>\",\n        \"registerOnOrder\": false,\n        \"password\": \"\"\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "6. get shipping", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.getNewCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}, {"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: ShippingInput!) {\n  cartAvailableShippingMethods(cartToken: $cart, data: $data) {\n    __typename\n    method {\n        __typename\n        code\n        name\n    }\n    errorMsg\n    price {\n        currency\n        value\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"data\": {\n        \"type\": \"ECONT_TO_OFFICE\",\n        \"city\": \"Пловдив\",\n        \"officeCode\": \"4046\",\n        \"cityId\": \"33\",\n        \"postCode\": \"4000\",\n        \"address\": \"Пловдив кв. Захарна фабрика ул. Йоан Екзарх №17\"\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "7. save shipping", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.getNewCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}, {"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: ShippingInput!, $code: String!) {\n  cartSaveShipping(cartToken: $cart, shippingMethodCode: $code,data: $data) {\n    shipping {\n        address {\n            id\n            skus\n            officeCode\n            storeCode\n            city\n            postCode\n            street\n        }\n    }\n    totals {\n        amount {\n            value\n            currency\n        }\n        code\n        label\n        order\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"code\": \"praktis_weightpriceshipping_weight_price\",\n    \"data\":  {\n        \"address\": \" Пловдив кв. Захарна фабрика ул. Йоан Екзарх №17\",\n        \"city\": \"Пловдив\",\n        \"cityId\": \"33\",\n        \"officeCode\": \"4046\",\n        \"postCode\": \"4000\",\n        \"type\": \"ECONT_TO_OFFICE\",\n        \"storeCode\": null\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "8. save cart payment", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.getNewCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}, {"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation SavePaymnetMethod($cart: String!, $paymentMethodCode: String!) {\n  cartSavePayment(cartToken: $cart, paymentMethodCode: $paymentMethodCode) {\n    id\n    token \n    paymentMethod\n    totals {\n        amount {\n            currency\n            value\n        }\n        code\n        label\n        order\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"paymentMethodCode\": \"extensa_rbb\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "9. place order", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.getNewCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}, {"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: NewOrderInput!) {\n  placeOrder(cartToken: $cart, data: $data) {\n    orderNumber\n    order {\n        incrementId\n        note\n        paymentMethod {\n            code\n            name\n        }\n        shippingMethod {\n            code\n            name\n        }\n    }\n    status {\n        code\n        label\n    }\n    redirect {\n        data {\n            key\n            value\n        }\n        url \n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"data\": {\n            \"client\": {\n                \"email\": \"<EMAIL>\",\n                \"firstName\": \"Стоян\",\n                \"lastName\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n                \"password\": \"\",\n                \"phone\": \"0883555204\",\n                \"registerOnOrder\": false\n            },\n            \"note\": \"коментар\",\n            \"paymentMethodCode\": \"extensa_rbb\",\n            \"promoCode\": \"\",\n            \"shipping\": {\n                \"address\": \" Пловдив кв. Захарна фабрика ул. Йоан Екзарх №17\",\n                \"city\": \"Пловдив\",\n                \"cityId\": \"33\",\n                \"officeCode\": \"4046\",\n                \"postCode\": \"4000\",\n                \"type\": \"ECONT_TO_OFFICE\"\n            },\n            \"shippingMethodCode\": \"praktis_weightpriceshipping_weight_price\"\n        }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "8.2. save cаche payment", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.getNewCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}, {"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation SavePaymnetMethod($cart: String!, $paymentMethodCode: String!) {\n  cartSavePayment(cartToken: $cart, paymentMethodCode: $paymentMethodCode) {\n    id\n    token \n    paymentMethod\n    totals {\n        amount {\n            currency\n            value\n        }\n        code\n        label\n        order\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"paymentMethodCode\": \"extensa_rbb\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "9.2. place order cache", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.getNewCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}, {"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: NewOrderInput!) {\n  placeOrder(cartToken: $cart, data: $data) {\n    orderNumber\n    order {\n        incrementId\n        note\n        paymentMethod {\n            code\n            name\n        }\n        shippingMethod {\n            code\n            name\n        }\n    }\n    status {\n        code\n        label\n    }\n    redirect {\n        data {\n            key\n            value\n        }\n        url \n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"data\": {\n            \"client\": {\n                \"email\": \"<EMAIL>\",\n                \"firstName\": \"Стоян\",\n                \"lastName\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n                \"password\": \"\",\n                \"phone\": \"0883555204\",\n                \"registerOnOrder\": false\n            },\n            \"note\": \"коментар\",\n            \"paymentMethodCode\": \"cashondelivery\",\n            \"promoCode\": \"\",\n            \"shipping\": {\n                \"address\": \" Пловдив кв. Захарна фабрика ул. Йоан Екзарх №17\",\n                \"city\": \"Пловдив\",\n                \"cityId\": \"33\",\n                \"officeCode\": \"4046\",\n                \"postCode\": \"4000\",\n                \"type\": \"ECONT_TO_OFFICE\"\n            },\n            \"shippingMethodCode\": \"praktis_weightpriceshipping_weight_price\"\n        }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}]}, {"name": "Order", "item": [{"name": "Tests", "item": [{"name": "1. Save Client - With Invoice and Account", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.addToCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: ClientInput!) {\n  cartSaveClient(cartToken: $cart, data: $data) {\n    id\n    token\n    customer {\n        ...AppOrderCustomer\n    }\n  }\n}\n\nfragment AppOrderCustomer on OrderCustomer {\n  firstName\n  lastName\n  email\n  phone\n  invoice {\n    type\n    address\n    city\n    company {\n      eik\n      mol\n      name\n      vat\n    }\n    individual {\n      egn\n      name\n      vat\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"data\": {\n        \"firstName\": \"sdfdsf\",\n        \"lastName\": \"<PERSON><PERSON>\",\n        \"phone\": \"+359877123456\",\n        \"email\": \"<EMAIL>\",\n        \"registerOnOrder\": true,\n        \"password\": \"<EMAIL>\",\n        \"invoice\": {\n            \"type\": \"personal\",\n            \"city\": \"Sofia\",\n            \"address\": \"123 Main Street, Apt 4B\",\n            \"individual\": {\n                \"name\": \"<PERSON>\",\n                \"egn\": \"**********\",\n                \"vat\": \"BG**********\"\n            }\n        }\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "1. Create Order with customer registration", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.addToCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: NewOrderInput!) {\n  placeOrder(cartToken: $cart, data: $data) {\n    orderNumber\n    order {\n        incrementId\n        note\n        paymentMethod {\n            code\n            name\n        }\n        shippingMethod {\n            code\n            name\n        }\n    }\n    status {\n        code\n        label\n    }\n    redirect {\n        data {\n            key\n            value\n        }\n        url \n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"data\": {\n        \"client\": {\n            \"firstName\": \"sdfdsf\",\n            \"lastName\": \"<PERSON><PERSON>\",\n            \"phone\": \"+359877123456\",\n            \"email\": \"<EMAIL>\",\n            \"registerOnOrder\": true,\n            \"password\": \"<EMAIL>\",\n            \"invoice\": {\n                \"type\": \"personal\",\n                \"city\": \"Sofia\",\n                \"address\": \"123 Main Street, Apt 4B\",\n                \"individual\": {\n                    \"name\": \"<PERSON>\",\n                    \"egn\": \"**********\",\n                    \"vat\": \"BG**********\"\n                }\n            }\n        },\n        \"note\": \"коментар\",\n        \"paymentMethodCode\": \"banktransfer\",\n        \"promoCode\": \"\",\n        \"shipping\": {\n            \"address\": \" Пловдив кв. Захарна фабрика ул. Йоан Екзарх №17\",\n            \"city\": \"Пловдив\",\n            \"cityId\": \"33\",\n            \"officeCode\": \"1042\",\n            \"postCode\": \"4000\",\n            \"type\": \"ECONT_TO_OFFICE\"\n        },\n        \"shippingMethodCode\": \"praktis_weightpriceshipping_weight_price\"\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "3. Ship - TO OFFICE", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: ShippingInput!, $code: String!) {\n  cartSaveShipping(cartToken: $cart, shippingMethodCode: $code,data: $data) {\n    shipping {\n        address {\n            id\n            skus\n            officeCode\n            storeCode\n            city\n            postCode\n            street\n        }\n    }\n    totals {\n        amount {\n            value\n            currency\n        }\n        code\n        label\n        order\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"code\": \"praktis_weightpriceshipping_weight_price\",\n    \"data\": {\n        \"type\": \"ECONT_TO_OFFICE\",\n        \"city\": \"София\",\n        \"cityId\": \"41\",\n        \"postCode\": \"1000\",\n        \"officeCode\": \"10068\",\n        \"address\": \"ul. Antim 1 53\"\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "1. <PERSON> - Regular", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.addToCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: ClientInput!) {\n  cartSaveClient(cartToken: $cart, data: $data) {\n    id\n    token\n    customer {\n        ...AppOrderCustomer\n    }\n  }\n}\n\nfragment AppOrderCustomer on OrderCustomer {\n  firstName\n  lastName\n  email\n  phone\n  invoice {\n    type\n    address\n    city\n    company {\n      eik\n      mol\n      name\n      vat\n    }\n    individual {\n      egn\n      name\n      vat\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"data\": {\n        \"firstName\": \"sdfdsf\",\n        \"lastName\": \"<PERSON><PERSON>\",\n        \"phone\": \"+359877123456\",\n        \"email\": \"<EMAIL>\",\n        \"registerOnOrder\": false,\n        \"password\": \"\"\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "2. Get Shipping Methods", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.addToCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: ShippingInput!) {\n  cartAvailableShippingMethods(cartToken: $cart, data: $data) {\n    __typename\n    method {\n        __typename\n        code\n        name\n    }\n    errorMsg\n    price {\n        currency\n        value\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"data\": {\n        \"type\": \"ECONT_TO_OFFICE\",\n        \"city\": \"Пловдив\",\n        \"officeCode\": \"1042\",\n        \"cityId\": \"33\",\n        \"postCode\": \"4000\",\n        \"address\": \"Пловдив кв. Захарна фабрика ул. Йоан Екзарх №17\"\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "3. Save Shipping Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.addToCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: ShippingInput!, $code: String!) {\n  cartSaveShipping(cartToken: $cart, shippingMethodCode: $code,data: $data) {\n    shipping {\n        address {\n            id\n            skus\n            officeCode\n            storeCode\n            city\n            postCode\n            street\n        }\n    }\n    totals {\n        amount {\n            value\n            currency\n        }\n        code\n        label\n        order\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"code\": \"praktis_weightpriceshipping_weight_price\",\n    \"data\":  {\n        \"address\": \" Пловдив кв. Захарна фабрика ул. Йоан Екзарх №17\",\n        \"city\": \"Пловдив\",\n        \"cityId\": \"33\",\n        \"officeCode\": \"4046\",\n        \"postCode\": \"4000\",\n        \"type\": \"ECONT_TO_OFFICE\",\n        \"storeCode\": null\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "4. Get Payment methods", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!) {\n  cartAvailablePaymentMethods(cartToken: $cart) {\n    code\n    extraContent\n    name\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "5. Save Payment", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation SavePaymnetMethod($cart: String!, $paymentMethodCode: String!) {\n  cartSavePayment(cartToken: $cart, paymentMethodCode: $paymentMethodCode) {\n    id\n    token \n    paymentMethod\n    totals {\n        amount {\n            currency\n            value\n        }\n        code\n        label\n        order\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"paymentMethodCode\": \"extensa_rbb\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "5. Create Order", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $data: NewOrderInput!) {\n  placeOrder(cartToken: $cart, data: $data) {\n    orderNumber\n    order {\n        incrementId\n        note\n        paymentMethod {\n            code\n            name\n        }\n        shippingMethod {\n            code\n            name\n        }\n    }\n    status {\n        code\n        label\n    }\n    redirect {\n        data {\n            key\n            value\n        }\n        url \n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"data\": {\n            \"client\": {\n                \"email\": \"<EMAIL>\",\n                \"firstName\": \"Стоян\",\n                \"lastName\": \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n                \"password\": \"\",\n                \"phone\": \"0883555204\",\n                \"registerOnOrder\": false\n            },\n            \"note\": \"коментар\",\n            \"paymentMethodCode\": \"extensa_rbb\",\n            \"promoCode\": \"\",\n            \"shipping\": {\n                \"address\": \" Пловдив кв. Захарна фабрика ул. Йоан Екзарх №17\",\n                \"city\": \"Пловдив\",\n                \"cityId\": \"33\",\n                \"officeCode\": \"4046\",\n                \"postCode\": \"4000\",\n                \"type\": \"ECONT_TO_OFFICE\"\n            },\n            \"shippingMethodCode\": \"praktis_weightpriceshipping_weight_price\"\n        }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Cart New Empty", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.getNewCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}, {"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetEmptyCart {\n  getNewCart {\n    token\n  }\n}", "variables": "{\n    \"cart\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjIjoxNzM3MzIwMTAzLCJjYXJ0X2lkIjoiIiwiZXhwIjoxNzM3NDA2NTAzLCJwcm9kdWN0c19jb3VudCI6MCwic2FsdCI6IiJ9.I2aE7n_T9PznrU9DKk3atjYnbovaYtoP5i7lqW499mY\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Cart Get", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.getCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query GetCart($cart: String!) {\n  getCart(cartToken: $cart) {\n    ...AppCart\n  }\n}\n\nfragment AppCart on StoreCart {\n  token\n  id\n  storeCode\n  currency\n  couponCode\n  note\n  customer {\n    ...AppOrderCustomer\n  }\n  items {\n    ...AppCartItem\n  }\n  shipping {\n    availableMethods\n    availableIn {\n        ID\n        name\n    }\n    minAmountForFreeShippingMessage\n    hasFreeShipping\n    freeShippingAfter\n    address {\n        ...AppShippingAddress\n    }\n  }\n  paymentMethod\n  totals {\n    code\n    label\n    amount {\n      ...AppPrice\n    }\n    order\n  }\n}\n\nfragment AppCartItem on StoreCartItem {\n  id\n  sku\n  labels {\n    freeShipping\n    usePallet\n  }\n  product {\n    __typename\n    ... on SimpleProduct {\n      id\n      sku\n    }\n  }\n  baseQty\n}\n\nfragment AppOrderCustomer on OrderCustomer {\n  firstName\n  lastName\n  email\n  phone\n  invoice {\n    type\n    address\n    city\n    company {\n      eik\n      mol\n      name\n      vat\n    }\n    individual {\n      egn\n      name\n      vat\n    }\n  }\n}\n\nfragment AppShippingAddress on ShippingAddress {\n  id\n  skus\n  method\n  city\n  postCode\n  street\n  officeCode\n  storeCode\n}\n\nfragment AppPrice on Price {\n  currency\n  value\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Cart Item Add", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.cartItemAdd.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CartItemAdd($cart: String!, $sku: String!) {\n  cartItemAdd(cartToken: $cart, item: {\n    baseQty: 1,\n    sku: $sku\n  }) {\n    id\n    token\n    items {\n      ...AppCartItem\n    }\n    totals {\n      ...AppCartTotal\n    }\n  }\n}\n\nfragment AppCartItem on StoreCartItem {\n  id\n  sku\n  labels {\n    freeShipping\n    usePallet\n  }\n  product {\n    __typename\n    ... on SimpleProduct {\n      id\n      sku\n    }\n  }\n  baseQty\n}\n\nfragment AppCartTotal on CartTotal {\n  amount {\n    currency\n    value\n  }\n  code\n  label\n  order\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"sku\": \"35587046\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Cart Item Remove", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.cartItemRemove.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CartItemRemove($cart: String!) {\n  cartItemRemove(cartToken: $cart, sku: \"35580276\") {\n    id\n    token\n    items {\n        id\n        baseQty\n        product {\n            ... on SimpleProduct {\n                id\n                name\n            }\n        }\n    }\n  }\n}", "variables": "{\n    \"cart\": \"{{_cart_token}}\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Cart Item Update", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.addToCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!, $sku: String!) {\n  cartItemUpdate(cartToken: $cart, item: {\n    baseQty: 1\n    sku: $sku\n  }) {\n    id\n    token\n    items {\n        id\n        baseQty\n        product {\n            ... on SimpleProduct {\n                id\n                name\n            }\n        }\n    }\n    totals {\n        label\n        amount {\n            value\n        }\n    \n    }\n  }\n}", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"sku\": \"35580276\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Cart Apply Promo", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.addToCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CartApplyCoupon($cart: String!, $code: String!) {\n  cartApplyCoupon(cartToken: $cart, couponCode: $code) {\n    id\n    token\n    couponCode\n    totals {\n        ...AppCartTotal\n    }\n  }\n}\n\nfragment AppCartTotal on CartTotal {\n        amount {\n            currency\n            value\n        }\n        code\n        label\n        order\n}", "variables": "{\n    \"cart\": \"{{_cart_token}}\",\n    \"code\": \"TESTTEST\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Cart Recalculate Totals", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_cart_token', jsonData.data.addToCart.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation Update($cart: String!) {\n  cartCalculateTotals(cartToken: $cart) {\n    id\n    totals {\n        amount {\n            currency\n            value\n        }\n        code\n        label \n        order\n    }\n  }\n}\n", "variables": "{\n    \"cart\": \"{{_cart_token}}\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}]}, {"name": "Customer", "item": [{"name": "Auth/Token", "item": [{"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_user_token', jsonData.data.customerLoginRefresh.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CustomerLoginRefresh {\n    customerLoginRefresh {\n        id\n        token\n    }\n}", "variables": ""}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Customer logout", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CustomerLogout {\n    customerLogout \n}", "variables": ""}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_user_token', jsonData.data.customerLogin.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-captcha-token", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CustomerLogin($u: String!, $p: String!) {\n    customerLogin(email: $u, password: $p) {\n        id\n        token\n    }\n}", "variables": "{\n    \"u\": \"<EMAIL>\",\n    \"p\": \"SecurePassword123!\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Register", "item": [{"name": "Register Regular", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-captcha-token", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation RegisterCustmer($data: CustomerRegistrationData!) {\n    customerRegister(data: $data) {\n        create\n        requireConfirmation\n    }\n}", "variables": "{\n    \"data\": {\n        \"email\": \"<EMAIL>\",\n        \"firstname\": \"Test\",\n        \"lastname\": \"PFGTEst\",\n        \"password\": \"SecurePassword123!\",\n        \"newsletterSubscribe\": false\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Personal invoice", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-captcha-token", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation RegisterCustmer($data: CustomerRegistrationData!) {\n    customerRegister(data: $data) {\n        create\n        requireConfirmation\n    }\n}", "variables": "{\n    \"data\": {\n        \"email\": \"<EMAIL>\",\n        \"firstname\": \"Test\",\n        \"lastname\": \"PFGTEst\",\n        \"password\": \"SecurePassword123!\",\n        \"newsletterSubscribe\": false,\n        \"invoice\": {\n            \"type\": \"personal\",\n            \"city\": \"Sofia\",\n            \"address\": \"123 Main Street, Apt 4B\",\n            \"individual\": {\n                \"name\": \"<PERSON>\",\n                \"egn\": \"**********\",\n                \"vat\": \"BG**********\"\n            }\n        }\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Company Invoice", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-captcha-token", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation RegisterCustmer($data: CustomerRegistrationData!) {\n    customerRegister(data: $data) {\n        create\n        requireConfirmation\n    }\n}", "variables": "{\n    \"data\": {\n        \"email\": \"<EMAIL>\",\n        \"firstname\": \"Test\",\n        \"lastname\": \"PFGTEst\",\n        \"password\": \"SecurePassword123!\",\n        \"newsletterSubscribe\": false,\n        \"invoice\": {\n            \"type\": \"company\",\n            \"city\": \"Sofia\",\n            \"address\": \"123 Main Street, Apt 4B\",\n            \"company\": {\n                \"name\": \"Acme Corporation Ltd.\",\n                \"mol\": \"<PERSON>\",\n                \"eik\": \"*********\",\n                \"vat\": \"BG*********\"\n            }\n        }\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Account", "item": [{"name": "Address", "item": [{"name": "Add", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation AddCustomerAddress($address: ShippingInput!) {\n  customerAddressAdd(address: $address) {\n    id\n    firstName\n    lastName\n    city\n    postCode\n    street\n    officeCode\n    storeCode\n  }\n}", "variables": "{\n    \"address\": {\n      \"type\": \"ECONT_TO_ADDRESS\",\n      \"cityId\": \"SOF\",\n      \"city\": \"Sofia\",\n      \"postCode\": \"1000\",\n      \"address\": \"ul. Vitosha 42, et. 3, ap. 7\",\n      \"officeCode\": null,\n      \"storeCode\": null\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Edit", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation AddCustomerAddress($id: ID!, $address: ShippingInput!) {\n  customerAddressUpdate(addressID: $id, data: $address) {\n    id\n    firstName\n    lastName\n    city\n    postCode\n    street\n    officeCode\n    storeCode\n  }\n}", "variables": "{\n    \"id\": 71634,\n    \"address\": {\n      \"type\": \"ECONT_TO_ADDRESS\",\n      \"cityId\": \"SOF\",\n      \"city\": \"Sofia\",\n      \"postCode\": \"1000\",\n      \"address\": \"ul. Vitosha 42, et. 3, ap. 8\",\n      \"officeCode\": null,\n      \"storeCode\": null\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Remove", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation AddCustomerAddress($addressID: ID!) {\n  customerAddressRemove(addressID: $addressID) {\n    id\n    firstName\n    lastName\n    city\n    postCode\n    street\n    officeCode\n    storeCode\n  }\n}", "variables": "{\n    \"addressID\": 14907\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Invoice", "item": [{"name": "Add", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation AddCustomerAddress($data: InvoiceInput!) {\n  customerInvoiceAdd(data: $data) {\n    id\n    type\n    city\n    address\n    company {\n        eik\n        mol\n        name\n        vat\n    }\n    individual {\n        egn\n        name\n        vat\n    }\n  }\n}", "variables": "{\n    \"data\": {\n        \"type\": \"personal\",\n        \"city\": \"Sofia\",\n        \"address\": \"123 Main Street, Apt 4B\",\n        \"individual\": {\n            \"name\": \"<PERSON>\",\n            \"egn\": \"**********\",\n            \"vat\": \"BG**********\"\n        }\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Edit", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation AddCustomerAddress($id: ID!, $address: ShippingInput!) {\n  customerAddressUpdate(addressID: $id, data: $address) {\n    id\n    firstName\n    lastName\n    city\n    postCode\n    street\n    officeCode\n    storeCode\n  }\n}", "variables": "{\n    \"id\": 71634,\n    \"address\": {\n      \"type\": \"ECONT_TO_ADDRESS\",\n      \"cityId\": \"SOF\",\n      \"city\": \"Sofia\",\n      \"postCode\": \"1000\",\n      \"address\": \"ul. Vitosha 42, et. 3, ap. 8\",\n      \"officeCode\": null,\n      \"storeCode\": null\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Remove", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation AddCustomerAddress($addressID: ID!) {\n  customerAddressRemove(address: $addressID) {\n    id\n    firstName\n    lastName\n    city\n    postCode\n    street\n    officeCode\n    storeCode\n  }\n}", "variables": "{\n    \"address\": {\n      \"type\": \"ECONT_TO_ADDRESS\",\n      \"cityId\": \"SOF\",\n      \"city\": \"Sofia\",\n      \"postCode\": \"1000\",\n      \"address\": \"ul. Vitosha 42, et. 3, ap. 7\",\n      \"officeCode\": null,\n      \"storeCode\": null\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Get Customer", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query CustomerData {\n    customerData {\n        ...Data\n    }\n}\n\nfragment Data on Customer {\n    id\n    token\n    firstName\n    lastName\n    email\n    isSubscribed\n    defaultAddressID\n    defaultAddress {\n        ...AddressData\n    }\n    addresses {\n        ...AddressData\n    }\n    defaultInvoiceID\n    defaultInvoice {\n        ...InvoiceData\n    }\n    invoices {\n        ...InvoiceData\n    }\n    orders {\n        ...OrderData\n    }\n}\n\nfragment AddressData on CustomerAddress {\n    id\n    city\n    postCode\n    street\n    officeCode\n    storeCode\n    firstName\n    lastName\n    phone\n}\n\nfragment InvoiceData on CustomerInvoice {\n    id\n    type\n    city\n    address\n    company {\n        eik\n        mol\n        name\n        vat\n    }\n    individual {\n        egn\n        name\n        vat\n    }\n}\n\nfragment OrderData on StoreOrder {\n    incrementId\n    status {\n        code\n        label\n    }\n    createAt\n    items {\n        id\n        baseQty\n    }\n}", "variables": ""}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Update Customer Password", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CustomerLogin($o: String!, $n: String!) {\n    customerUpdatePassword(oldPassword: $o, newPassword: $n) {\n        id\n    }\n}", "variables": "{\n    \"o\": \"<EMAIL>\",\n    \"n\": \"todor.mitev@pfgbulgaria.com222\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Update Customer Info", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdateCustomer($data: CustomerUpdateInput!) {\n    customerUpdateInfo(data: $data) {\n        firstName\n        lastName\n        isSubscribed\n        defaultAddressID\n        defaultInvoiceID\n    }\n}", "variables": "{\n    \"data\": {\n        \"firstName\": \"test1\",\n        \"lastName\": \"test2\",\n        \"newsletterSubscribe\": false\n    }\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Wishlist", "item": [{"name": "Get Wishlist", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "query Wishlis {\n    customerWishlist {\n        skus\n        products {\n            __typename\n            ... on SimpleProduct {\n                ...WishlistProduct\n            }\n        }\n    }\n}\n\nfragment WishlistProduct on SimpleProduct {\n    id\n    name\n    sku\n    price {\n        price {\n            value\n        }\n        special {\n            value\n        }\n    }\n    image {\n        alt\n        src\n    }\n}", "variables": "{\n    \"sku\": \"3497014\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Wishlist Add", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation WishlistAdd($sku: String!) {\n    customerWishlistAdd(sku: $sku) {\n        skus\n        products {\n            __typename\n            ... on SimpleProduct {\n                ...WishlistProduct\n            }\n        }\n    }\n}\n\nfragment WishlistProduct on SimpleProduct {\n    id\n    name\n    sku\n    price {\n        price {\n            value\n        }\n        special {\n            value\n        }\n    }\n    image {\n        alt\n        src\n    }\n}", "variables": "{\n    \"sku\": \"3497014\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Wishlist Remove", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-auth-customer", "value": "{{_user_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation WishlistAdd($sku: String!) {\n    customerWishlistRemove(sku: $sku) {\n        skus\n        products {\n            __typename\n            ... on SimpleProduct {\n                ...WishlistProduct\n            }\n        }\n    }\n}\n\nfragment WishlistProduct on SimpleProduct {\n    id\n    name\n    sku\n    price {\n        price {\n            value\n        }\n        special {\n            value\n        }\n    }\n    image {\n        alt\n        src\n    }\n}", "variables": "{\n    \"sku\": \"3497014\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}]}, {"name": "News/Blog", "item": [{"name": "Get News", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text", "disabled": true}], "body": {"mode": "graphql", "graphql": {"query": "fragment PostPreview on BlogPost {\n    urlKey\n    title\n    summary\n    previewImageUrl\n    publishedAt\n}\n\nquery GetBlogPosts($page: Int!) {\n    getBlogPosts(page: $page, size: 10) {\n        currentPage\n        totalPages\n        posts {\n            ...PostPreview\n        }\n    }\n    getFeaturedBlogPost {\n        ...PostPreview\n    }\n}", "variables": "{\n    \"page\": 1\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Featured Post", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text", "disabled": true}], "body": {"mode": "graphql", "graphql": {"query": "fragment PostPreview on BlogPost {\n    urlKey\n    title\n    summary\n    previewImageUrl\n    publishedAt\n}\n\nquery GetFeaturedBlogPost {\n    getFeaturedBlogPost {\n        ...PostPreview\n    }\n}", "variables": ""}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}, {"name": "Get Blog post", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "X-Captcha-Response", "value": "{{_captcha_token}}", "type": "text", "disabled": true}], "body": {"mode": "graphql", "graphql": {"query": "query GetBlogPost ($identifier: String!) {\n    getBlogPost(identifier: $identifier) {\n        urlKey\n        title\n        content\n        mainImageUrl\n        publishedAt\n    }\n}", "variables": "{\n    \"identifier\": \"polzi-ot-pokupka-na-upotrebyavan-kardan\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "Messages", "item": [{"name": "Send Inquery", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-captcha-token", "value": "{{_captcha_token}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation SendInquery {\n    storeSendInquiry(input: {\n        email: \"test\",\n        message: \"test\",\n        name: \"test\",\n        phone: \"test\",\n        store: \"test\",\n        type: Personal\n    })\n}", "variables": "{\n    \"page\": 1\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Set New Cart Token\", function () {", "    var jsonData = pm.response.json();", "    pm.environment.set('_user_token', jsonData.data.customerLogin.token)", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "x-captcha-token", "value": "{{_captcha_token}}"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CustomerLogin($u: String!, $p: String!) {\n    customerLogin(email: $u, password: $p) {\n        id\n        token\n        defaultAddress {\n            id\n            city\n        }\n        addresses {\n            id\n            city\n        }\n        invoices {\n            id\n            city\n        }\n        defaultInvoice {\n            id\n            city\n        }\n        orders {\n            incrementId\n            note\n        }\n    }\n}", "variables": "{\n    \"u\": \"<EMAIL>\",\n    \"p\": \"SecurePassword123!\"\n}"}}, "url": {"raw": "{{_graph_url}}", "host": ["{{_graph_url}}"]}}, "response": []}]}