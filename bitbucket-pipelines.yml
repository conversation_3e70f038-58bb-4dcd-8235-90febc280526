# bitbucket-pipelines.yml
image: ubuntu:22.04

definitions:
  steps:
    - step: &ssh-deploy-staging
        name: SSH Deploy
        runs-on:
          - self.hosted
          - linux.shell
        script:
          - chmod +x /home/<USER>/stores/praktis.bg/v2.praktis.bg/frontend/.ci/deploy.sh
          - /home/<USER>/stores/praktis.bg/v2.praktis.bg/frontend/.ci/deploy.sh /home/<USER>/stores/praktis.bg/v2.praktis.bg/frontend develop

    - step: &ssh-deploy-production
        name: SSH Deploy
        runs-on:
          - self.hosted
          - linux.shell
        script:
          # Install required packages
          - apt-get update && apt-get install -y openssh-client iputils-ping

          # Setup SSH
          - echo -e "Host *\n\tStrictHostKeyChecking no\n\tUserKnownHostsFile=/dev/null" > ~/.ssh/config
          - chmod 600 ~/.ssh/config

          # Execute the direct SSH deployment script
          - chmod +x deploy-production.sh
          - bash ./deploy-production.sh

    - step: &deploy-to-production
        <<: *ssh-deploy-production
        name: Deploy to Production
        deployment: production

    - step: &deploy-to-staging
        <<: *ssh-deploy-staging
        name: Deploy to Staging
        deployment: staging

pipelines:
  branches:
    master:
      - step: *deploy-to-production

    develop:
      - step: *deploy-to-staging