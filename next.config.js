/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    NEXT_PUBLIC_IMAGE_DOMAIN: process.env.NEXT_PUBLIC_IMAGE_DOMAIN ?? 'https://praktis.bg',
    NEXT_PUBLIC_FRONTEND_GRAPTHQL_ENDPOINT:
      process.env.NEXT_PUBLIC_FRONTEND_GRAPTHQL_ENDPOINT ?? 'https://praktis.bg/graphql',
    NEXT_PUBLIC_SEARCHANISE_API_KEY: process.env.NEXT_PUBLIC_SEARCHANISE_API_KEY ?? '',
    NEXT_PUBLIC_SEARCHANISE_RESULT_URL:
      process.env.NEXT_PUBLIC_SEARCHANISE_RESULT_URL ?? 'https://praktis.bg/searchanise/result',
    NEXT_IMAGE_PROXY: process.env.IMAGE_PROXY ?? '',
    NEXT_IMAGE_USERNAME: process.env.IMAGE_USERNAME ?? '',
    NEXT_IMAGE_PASSWORD: process.env.IMAGE_PASSWORD ?? '',
    NEXT_PUBLIC_TBI_BANK_URL: process.env.NEXT_PUBLIC_TBI_BANK_URL ?? '',
  },
  productionBrowserSourceMaps: false,
  images: {
    minimumCacheTTL: 86400, // 1 day
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'praktis2-demo.pfgbulgaria.com',
        port: '',
      },
      {
        protocol: 'http',
        hostname: 'praktis.localhost',
        port: '',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
      },
      {
        protocol: 'https',
        hostname: 'praktis.bg',
        port: '',
      },
      { protocol: 'https', hostname: 'img.youtube.com', port: '' },
    ],
  },
  webpack(config) {
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule) => rule.test?.test?.('.inline.svg'))

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.inline.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.inline.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
        use: ['@svgr/webpack'],
      }
    )

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.inline.svg$/i

    return config
  },
  experimental: {
    reactCompiler: true,
  },
  turbopack: {
    rules: {
      '*.inline.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
  typescript: {
    ignoreBuildErrors: false,
  },
  reactStrictMode: false,
}

module.exports = nextConfig
