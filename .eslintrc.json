{
  "extends": ["next/core-web-vitals"],
  "plugins": ["react-compiler", "import"],
  "rules": {
    "react-compiler/react-compiler": "error"
    //    "import/order": [
    //      "error",
    //      {
    //        "groups": ["builtin", "external", "internal", "parent", "sibling", "index"],
    //        "newlines-between": "always",
    //        "alphabetize": { "order": "asc", "caseInsensitive": true }
    //      }
    //    ]
  }
}
