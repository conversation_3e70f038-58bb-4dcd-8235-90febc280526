{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "sourceMap": true, "plugins": [{"name": "next"}], "baseUrl": "./", "paths": {"@/*": ["./*"], "@components/*": ["./src/components/*"], "@features/*": ["./src/features/*"], "@lib/*": ["./src/lib/*"], "@atoms/*": ["./src/components/atoms/*"], "@context/*": ["./src/components/context/*"], "@hooks/*": ["./src/components/hooks/*"], "@icons/*": ["./public/icons/*"], "@images/*": ["./public/images/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "recaptcha.d.ts"], "exclude": ["node_modules", "_codegen"]}