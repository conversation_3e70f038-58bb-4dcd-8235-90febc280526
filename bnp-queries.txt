1. Pricing Schemes Query Test

Query: getBNPPricingSchemes

query GetBNPPricingSchemes($goodTypeIds: String!, $principal: Float!, $downPayment: Float!) {
  getBNPPricingSchemes(
    goodTypeIds: $goodTypeIds
    principal: $principal
    downPayment: $downPayment
  ) {
    id
    name
  }
}

Test Variables:

Scenario 1: Valid Parameters

{
  "goodTypeIds": "1,2,3",
  "principal": 2500.00,
  "downPayment": 500.00
}

Expected Response:

{
  "data": {
    "getBNPPricingSchemes": [
      {
        "id": "1",
        "name": "Standard Leasing 12-60 months"
      },
      {
        "id": "2",
        "name": "Express Leasing 6-36 months"
      }
    ]
  }
}

2. Pricing Variants Query Test

Query: getCreditCalculatorBNPParibas

query GetCreditCalculatorBNPParibas($sku: String!, $downPayment: Float!, $qty: Int!) {
  getCreditCalculatorBNPParibas(
    sku: $sku
    downPayment: $downPayment
    qty: $qty
  ) {
    schemeId
    variants {
      id
      apr
      correctDownpaymentAmount
      installmentAmount
      maturity
      nir
      pricingSchemeId
      pricingSchemeName
      totalRepaymentAmount
    }
  }
}

Scenario 1: SKU 360242 with 20% Down Payment

{
  "sku": "360242",
  "downPayment": 100.00,
  "qty": 1
}

Expected Response:

{
  "data": {
    "getCreditCalculatorBNPParibas": [
      {
        "schemeId": "1",
        "variants": [
          {
            "id": "101",
            "apr": "12.50",
            "correctDownpaymentAmount": "500.00",
            "installmentAmount": "85.67",
            "maturity": "24",
            "nir": "11.20",
            "pricingSchemeId": "1",
            "pricingSchemeName": "Standard Leasing 24 months",
            "totalRepaymentAmount": "2556.08"
          }
        ]
      }
    ]
  }
}

3. Loan Calculation Query Test

Query: calculateBNPLoan

query CalculateBNPLoan($goodTypeIds: String!, $principal: Float!, $downPayment: Float!, $pricingVariantId: Int!) {
  calculateBNPLoan(
    goodTypeIds: $goodTypeIds
    principal: $principal
    downPayment: $downPayment
    pricingVariantId: $pricingVariantId
  ) {
    apr
    correctDownpaymentAmount
    installmentAmount
    maturity
    nir
    pricingSchemeId
    pricingSchemeName
    pricingVariantId
    processingFeeAmount
    totalRepaymentAmount
  }
}

Scenario 1: Calculate Loan for Variant 101

{
  "goodTypeIds": "1,2,3",
  "principal": 2500.00,
  "downPayment": 500.00,
  "pricingVariantId": 101
}

Expected Response:

{
  "data": {
    "calculateBNPLoan": {
      "apr": "12.50",
      "correctDownpaymentAmount": "500.00",
      "installmentAmount": "85.67",
      "maturity": "24",
      "nir": "11.20",
      "pricingSchemeId": "1",
      "pricingSchemeName": "Standard Leasing 24 months",
      "pricingVariantId": "101",
      "processingFeeAmount": "50.00",
      "totalRepaymentAmount": "2556.08"
    }
  }
}

4. Payment Data Mutation Test

Mutation: cartSaveBNPPayment

mutation CartSaveBNPPayment($cartToken: String!, $paymentData: BNPPaymentInput!) {
  cartSaveBNPPayment(
    cartToken: $cartToken
    paymentData: $paymentData
  ) {
    id
    items {
      id
      sku
      baseQty
      price {
        amount
        currency
      }
    }
    paymentMethod {
      code
      title
    }
    totals {
      code
      title
      value {
        amount
        currency
      }
    }
  }
}

Test Variables:
Scenario 1: Complete Customer Data - Individual

{
  "cartToken": "test_cart_token_12345",
  "paymentData": {
    "goodTypeIds": "1,2,3",
    "principal": 2500.00,
    "downPayment": 500.00,
    "pricingVariantId": 101,
    "customerData": {
      "firstName": "Иван",
      "lastName": "Петров",
      "phone": "+359888123456",
      "email": "<EMAIL>",
      "address": "ул. Витоша 15",
      "city": "София",
      "postCode": "1000",
      "egn": "8001011234"
    }
  }
}

Expected Response:

{
  "data": {
    "cartSaveBNPPayment": {
      "id": "12345",
      "items": [
        {
          "id": "1",
          "sku": "360242",
          "baseQty": 1,
          "price": {
            "amount": 2500.00,
            "currency": "BGN"
          }
        }
      ],
      "paymentMethod": {
        "code": "stenik_leasingjetcredit",
        "title": "BNP Paribas Leasing"
      },
      "totals": [
        {
          "code": "subtotal",
          "title": "Subtotal",
          "value": {
            "amount": 2500.00,
            "currency": "BGN"
          }
        }
      ]
    }
  }
}

5. Application Submission Mutation Test

Mutation: submitBNPApplication

mutation SubmitBNPApplication($orderNumber: String!) {
  submitBNPApplication(orderNumber: $orderNumber)
}

Test Variables:

Scenario 1: Valid Order Number

{
  "orderNumber": "100000123"
}


6. Complete End-to-End Test Workflow
Step 1: Get Pricing Schemes

curl -X POST https://praktis2-demo.pfgbulgaria.com/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query GetBNPPricingSchemes($goodTypeIds: String!, $principal: Float!, $downPayment: Float!) { getBNPPricingSchemes(goodTypeIds: $goodTypeIds, principal: $principal, downPayment: $downPayment) { id name } }",
    "variables": {
      "goodTypeIds": "1,2,3",
      "principal": 2500.00,
      "downPayment": 500.00
    }
  }'

Step 2: Get Pricing Variants for SKU 360242

curl -X POST https://praktis2-demo.pfgbulgaria.com/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query GetCreditCalculatorBNPParibas($sku: String!, $downPayment: Float!, $qty: Int!) { getCreditCalculatorBNPParibas(sku: $sku, downPayment: $downPayment, qty: $qty) { schemeId variants { id apr installmentAmount maturity totalRepaymentAmount } } }",
    "variables": {
      "sku": "360242",
      "downPayment": 500.00,
      "qty": 1
    }
  }'

Step 3: Calculate Loan for Selected Variant

curl -X POST https://praktis2-demo.pfgbulgaria.com/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query CalculateBNPLoan($goodTypeIds: String!, $principal: Float!, $downPayment: Float!, $pricingVariantId: Int!) { calculateBNPLoan(goodTypeIds: $goodTypeIds, principal: $principal, downPayment: $downPayment, pricingVariantId: $pricingVariantId) { apr installmentAmount maturity totalRepaymentAmount } }",
    "variables": {
      "goodTypeIds": "1,2,3",
      "principal": 2500.00,
      "downPayment": 500.00,
      "pricingVariantId": 101
    }
  }'

Step 4: Save Payment Data to Cart

curl -X POST https://praktis2-demo.pfgbulgaria.com/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mutation CartSaveBNPPayment($cartToken: String!, $paymentData: BNPPaymentInput!) { cartSaveBNPPayment(cartToken: $cartToken, paymentData: $paymentData) { id paymentMethod { code title } } }",
    "variables": {
      "cartToken": "test_cart_token_12345",
      "paymentData": {
        "goodTypeIds": "1,2,3",
        "principal": 2500.00,
        "downPayment": 500.00,
        "pricingVariantId": 101,
        "customerData": {
          "firstName": "Иван",
          "lastName": "Петров",
          "phone": "+359888123456",
          "email": "<EMAIL>",
          "address": "ул. Витоша 15",
          "city": "София",
          "postCode": "1000"
        }
      }
    }
  }'

Step 5: Submit Application After Order Placement

curl -X POST https://praktis2-demo.pfgbulgaria.com/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mutation SubmitBNPApplication($orderNumber: String!) { submitBNPApplication(orderNumber: $orderNumber) }",
    "variables": {
      "orderNumber": "100000123"
    }
  }'