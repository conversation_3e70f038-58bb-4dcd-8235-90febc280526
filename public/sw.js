function performRequestReturnTextResponse(source, url, eventName, method) {
  fetch(url, { method: method ? 'get' : 'post' })
    .then((r) => r.text())
    .then((_raw) => {
      source.postMessage({
        name: eventName,
        data: _raw,
      })
    })
    .catch(function (error) {
      console.log('Request failed', error)
    })
}

addEventListener('message', (event) => {
  if (!event.data) {
    return
  }

  if (event.data.name === 'get_header') {
    performRequestReturnTextResponse(event.source, event.data.url, 'get_header')
  }
})
