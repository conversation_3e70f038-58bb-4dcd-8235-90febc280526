{"name": "praktis.bg", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "ts_check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "lint": "next lint", "cssbuild": "tailwindcss -i style/_main.css -o style/_auto.css", "code_format": "./node_modules/eslint/bin/eslint.js --fix", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write ."}, "engines": {"node": ">=20.0.0"}, "dependencies": {"@babel/standalone": "^7.27.0", "@hookform/resolvers": "^4.1.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.7", "@react-oauth/google": "^0.12.1", "@vis.gl/react-google-maps": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "dompurify": "^3.2.4", "formik": "^2.4.5", "graphql": "^16.8.1", "graphql-request": "^6.1.0", "graphql-tag": "^2.12.6", "lucide-react": "^0.469.0", "next": "15.3.1", "photoswipe": "^5.4.4", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "schema-dts": "^1.1.5", "sonner": "^2.0.1", "swiper": "^11.1.15", "tailwind-merge": "^2.5.3", "universal-cookie": "^7.2.0", "usehooks-ts": "^3.1.0", "yup": "^1.2.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/babel__standalone": "^7.1.9", "@types/invariant": "^2.2.37", "@types/node": "^20.16.10", "@types/react": "19.0.1", "@types/react-dom": "19.0.2", "autoprefixer": "^10.4.19", "babel-plugin-react-compiler": "^0.0.0-experimental-938cd9a-20240601", "encoding": "^0.1.13", "eslint": "8.57.0", "eslint-config-next": "15.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-compiler": "19.0.0-beta-201e55d-20241215", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8.4.47", "prettier": "^3.3.3", "tailwindcss": "^3.4.17", "typescript": "^5.6.2"}, "overrides": {"@types/react": "19.0.1", "@types/react-dom": "19.0.2"}}